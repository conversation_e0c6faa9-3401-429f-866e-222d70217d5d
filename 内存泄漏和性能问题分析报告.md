# 电子开盘系统内存泄漏和性能问题分析报告

## 项目概述
- **项目名称**: 电子开盘系统 (dzkp_cddc)
- **技术栈**: Spring Boot 2.0.3 + Vue.js 2.x + WebSocket + Redis + RabbitMQ + MySQL
- **部署方式**: 多服务实例并发运行
- **问题现象**: 压力测试后内存未释放，CPU周期性上涨，内存持续增长

## 问题分析

### 1. 后端Java应用内存泄漏问题

#### 1.1 定时任务相关问题

**问题描述**: 
- 系统中存在多个定时任务，每分钟执行一次 (`@Scheduled(cron = "0 * * * * ?")`)
- 定时任务中大量使用 `CompletableFuture` 异步处理，但缺乏适当的资源管理

**具体问题**:

1. **CompletableFuture 资源泄漏** (`OrderTask.java:212-259`)
```java
CompletableFuture updateOrderEffectiveFuture = CompletableFuture.supplyAsync(() -> {
    updateOrderEffective(now, startTime);
    return null;
}, taskExecutor);
```
- 创建了多个 CompletableFuture 但没有调用 `.join()` 或 `.get()` 等待完成
- 异步任务可能在主任务结束后仍在运行，导致线程和内存无法释放

2. **Redis 分布式锁未正确释放**
```java
Boolean lock = redisTemplate.opsForValue().setIfAbsent("AUTO_ORDER_LOCK", new Date());
```
- 使用 Redis 锁但在异常情况下可能未正确释放
- 锁的过期时间设置不当可能导致死锁

#### 1.2 线程池配置问题

**配置分析** (`ThreadPoolTaskExcutorConfig.java`):
```properties
spring.task.execution.pool.core-size=32
spring.task.execution.pool.max-size=64
spring.task.execution.pool.queue-capacity=1000
spring.task.execution.pool.keep-alive=10000
```

**问题**:
- 核心线程数设置为32，在多实例部署时可能过高
- 队列容量1000，在高并发下可能导致大量任务堆积
- `setAllowCoreThreadTimeOut(false)` 导致核心线程永不销毁

#### 1.3 数据库连接池问题

**Druid 连接池配置** (`application.properties`):
```properties
spring.datasource.dynamic.datasource.master.druid.initial-size=100
spring.datasource.dynamic.datasource.master.druid.min-idle=100
spring.datasource.dynamic.datasource.master.druid.max-active=3000
```

**问题**:
- 初始连接数100过高，多实例部署时会占用大量数据库连接
- 最大连接数3000在压力测试时可能导致连接池耗尽
- 连接泄漏检测机制不完善

#### 1.4 WebSocket 连接管理问题

**问题分析** (`WebSocketServer.java`):
```java
private static CopyOnWriteArraySet<WebSocketServer> webSocketSet = new CopyOnWriteArraySet<WebSocketServer>();
```

**问题**:
- 使用静态集合存储 WebSocket 连接，连接断开时可能未正确清理
- 心跳检测机制存在，但重连逻辑可能导致连接对象累积
- 多实例部署时，每个实例都维护自己的连接集合，可能导致内存浪费

### 2. 前端内存泄漏问题

#### 2.1 WebSocket 连接问题

**问题分析** (`Web/src/screen/index.vue:362-444`):
```javascript
var heartCheck = {
    timeout: 55000,
    serverTimeoutObj: null,
    reset: function() {
        clearInterval(this.serverTimeoutObj);
        return this;
    },
    start: function() {
        this.serverTimeoutObj = setInterval(function() {
            // 心跳逻辑
        }, this.timeout)
    }
}
```

**问题**:
- WebSocket 重连时可能创建多个连接实例
- 定时器 `setInterval` 在页面销毁时可能未正确清理
- 事件监听器未在组件销毁时移除

#### 2.2 定时器和事件监听器泄漏

**问题分析**:
1. **倒计时组件** (`countDown.vue:67`)
```javascript
let timer = setInterval(function(){
    // 倒计时逻辑
}, 1000);
```

2. **自动滚动组件** (`autoScrollTop.vue:61`)
```javascript
that.timer = setInterval(function() {
    that.moveTop();
}, 50);
```

**问题**:
- 多个组件使用 `setInterval` 但在组件销毁时未调用 `clearInterval`
- 事件监听器添加后未在适当时机移除

### 3. 中间件配置问题

#### 3.1 Redis 配置
```properties
spring.redis.database=1
spring.redis.host=cd-dbserver.redis.cnceccloud.cn
spring.redis.port=30101
```
- 缺少连接池配置，可能导致连接泄漏
- 未设置合适的超时时间

#### 3.2 RabbitMQ 配置
```properties
spring.rabbitmq.listener.concurrency=10
spring.rabbitmq.listener.max-concurrency=20
spring.rabbitmq.listener.prefetch=5
```
- 消费者并发数在多实例部署时可能过高
- 消息处理异常时可能导致消息堆积

## 解决方案建议

### 1. 后端优化方案

#### 1.1 定时任务优化
```java
// 正确的 CompletableFuture 使用方式
CompletableFuture<Void> allTasks = CompletableFuture.allOf(
    updateOrderEffectiveFuture,
    createOrderFuture,
    closeActivityFuture
);

try {
    allTasks.get(30, TimeUnit.SECONDS); // 设置超时时间
} catch (Exception e) {
    log.error("定时任务执行异常", e);
    // 取消未完成的任务
    updateOrderEffectiveFuture.cancel(true);
    createOrderFuture.cancel(true);
    closeActivityFuture.cancel(true);
}
```

#### 1.2 线程池配置优化
```properties
# 根据实例数量调整
spring.task.execution.pool.core-size=8
spring.task.execution.pool.max-size=16
spring.task.execution.pool.queue-capacity=200
spring.task.execution.pool.keep-alive=60000
# 允许核心线程超时
spring.task.execution.pool.allow-core-thread-timeout=true
```

#### 1.3 数据库连接池优化
```properties
# 减少初始连接数
spring.datasource.dynamic.datasource.master.druid.initial-size=10
spring.datasource.dynamic.datasource.master.druid.min-idle=10
spring.datasource.dynamic.datasource.master.druid.max-active=100
# 增加连接泄漏检测
spring.datasource.dynamic.datasource.master.druid.remove-abandoned=true
spring.datasource.dynamic.datasource.master.druid.remove-abandoned-timeout=300
spring.datasource.dynamic.datasource.master.druid.log-abandoned=true
```

#### 1.4 WebSocket 连接管理优化
```java
@OnClose
public void onClose() {
    webSocketSet.remove(this);
    WebSocketServer.onlineCount.decrementAndGet();
    // 清理相关资源
    if (this.session != null) {
        try {
            this.session.close();
        } catch (IOException e) {
            log.error("关闭WebSocket会话异常", e);
        }
    }
    log.info("WebSocket连接关闭，当前在线人数：{}", onlineCount);
}
```

### 2. 前端优化方案

#### 2.1 WebSocket 连接优化
```javascript
// 在组件销毁时清理资源
beforeDestroy() {
    if (this.socket) {
        this.socket.close();
        this.socket = null;
    }
    if (this.heartCheck && this.heartCheck.serverTimeoutObj) {
        clearInterval(this.heartCheck.serverTimeoutObj);
    }
}
```

#### 2.2 定时器清理
```javascript
// 在组件中正确管理定时器
data() {
    return {
        timers: [] // 统一管理定时器
    }
},
methods: {
    addTimer(timer) {
        this.timers.push(timer);
    }
},
beforeDestroy() {
    // 清理所有定时器
    this.timers.forEach(timer => {
        clearInterval(timer);
        clearTimeout(timer);
    });
    this.timers = [];
}
```

### 3. 监控和诊断建议

#### 3.1 JVM 监控
```bash
# 添加 JVM 参数进行内存监控
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/logs/heapdump/
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-Xloggc:/logs/gc.log
```

#### 3.2 应用监控
- 集成 Micrometer + Prometheus 进行应用指标监控
- 添加自定义指标监控线程池状态、连接池状态
- 监控定时任务执行时间和频率

#### 3.3 数据库监控
- 启用 Druid 监控页面
- 监控慢SQL和连接池状态
- 设置连接泄漏告警

## 总结

该项目的内存泄漏和性能问题主要集中在以下几个方面：

1. **定时任务管理不当**: CompletableFuture 未正确等待完成，导致异步任务累积
2. **资源配置过高**: 线程池、连接池配置在多实例部署时资源占用过多
3. **WebSocket 连接管理**: 前后端 WebSocket 连接清理不彻底
4. **前端定时器泄漏**: 组件销毁时未清理定时器和事件监听器

建议按照上述解决方案逐步优化，并建立完善的监控体系，确保系统在高并发场景下的稳定性。
