import axios from '@/axios'

//删除客户
export function deleteOpUsers(data) {
    return axios.service({
        url: axios.api + '/opUser/deleteOpUsers',
        method: 'post',
        data: data
    })
}

//置业顾问模板
export function downLoadExcel(params) {
    // return axios.api + '/opUser/downLoadExcelConsultant';
    return axios.service({
        url: axios.api + `/opUser/downLoadExcelConsultant`,
        method: 'get',
        params,
        responseType: 'blob'
    })
}

//下载客户
export function downLoadCus(params) {
    // return axios.api + `/opUser/downLoadExcel?activityId=${id}`
    return axios.service({
        url: axios.api + `/opUser/downLoadExcel`,
        method: 'get',
        params,
        responseType: 'blob'
    })
}

//批量客户导入
export function importCustomerExcel(data) {
    return axios.service({
        url: axios.api + '/opUser/importCustomerExcel',
        method: 'post',
        data: data
    })
}

//批量置业顾问导入
export function importSaleExcel(data) {
    return axios.service({
        url: axios.api + '/opUser/importSaleExcel',
        method: 'post',
        data: data
    })
}

//置业顾问经理添加界面角色下拉菜单
export function managerList(params) {
    return axios.service({
        url: axios.api + '/opUser/managerList',
        method: 'get',
        params
    })
}

//客户列表
export function opUserPageList(params) {
    return axios.service({
        url: axios.api + '/opUser/opUserPageList',
        method: 'get',
        params
    })
}
// 新增修改用户房源白名单数据
export function saveUserHouseResource(data) {
    return axios.service({
        url: axios.api + '/opUser/saveUserHouseResource',
        method: 'post',
        data
    })
}
// 递归查询房源信息根据活动ID
export function selectHouseResourceListByActivityId(params) {
    return axios.service({
        url: axios.appApi + '/house/selectHouseResourceListByActivityId',
        method: 'get',
        params
    })
}
// 根据用户id查询用户白名单房源列表
export function getUserHouseResourceListByUserId(params) {
    return axios.service({
        url: axios.api + '/opUser/getUserHouseResourceListByUserId',
        method: 'get',
        params
    })
}

//置业顾问列表
export function opUserSalePageList(params) {
    return axios.service({
        url: axios.api + '/opUser/opUserSalePageList',
        method: 'get',
        params
    })
}

//角色下拉列表
export function roleName(params) {
    return axios.service({
        url: axios.api + '/opUser/roleName',
        method: 'get',
        params
    })
}

//新增客户
export function saveOpUser(data) {
    return axios.service({
        url: axios.api + '/opUser/saveOpUser',
        method: 'post',
        data: data
    })
}

//编辑客户
export function updateOpUser(data) {
    return axios.service({
        url: axios.api + '/opUser/updateOpUser',
        method: 'post',
        data: data
    })
}

//编辑客户展示
export function updateOpUserShow(params) {
    return axios.service({
        url: axios.api + '/opUser/updateOpUserShow',
        method: 'get',
        params
    })
}

//可选房数
export function getSelHouseNum(params) {
    return axios.service({
        url: axios.api + '/opUser/selecCount',
        method: 'get',
        params
    })
}

//认筹单数
export function getRecognizeNum(params) {
    return axios.service({
        url: axios.api + '/opUser/bookingNum',
        method: 'get',
        params
    })
}

//获取置业顾问下拉列表
export function getAdviserOption(params) {
    return axios.service({
        url: axios.api + '/opUser/saleName',
        method: 'get',
        params
    })
}

//判断是否可修改客户信息
export function judgeIsChangeCusInfo(params) {
    return axios.service({
        url: axios.api + '/opUser/canModify',
        method: 'get',
        params
    })
}
