import Vue from 'vue'
import $http from "axios"

Vue.prototype.$http = $http;
function savepic(url) {
    let a = document.createElement('a');
    a.href = url;
    a.style = 'display:none;';
    a.download = "file.jpg";
    a.target = "_blank";
    document.body.appendChild(a);
    a.click();
}
function clearCookie(name) {
    var exp = new Date();
    exp.setTime(exp.getTime() - 1);
    var cval = getCookie(name);
    if (cval != null)
        document.cookie = name + "=" + cval + ";expires=" + exp.toGMTString();
}

function getCookie(name) {
    var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
    if (arr = document.cookie.match(reg))
        return unescape(arr[2]);
    else
        return null;
}

function setCookie(name, value) {
    var Days = 30;
    var exp = new Date();
    exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
    document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString();
}

export default{
    clearCookie,
    getCookie,
    setCookie,
    savepic
}