import axios from '@/axios'


// 活动分析--客户分析
export function actAnalysis(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityAnalysisCustomer',
    method: 'get',
    params
  })
}

// 签到统计导出
export function sginInListExport(params) {
  // return axios.api + '/opActivity/sginInListExport?' + params;
  return axios.service({
    url: axios.api + `/opActivity/sginInListExport`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 活动分析--客户分析--导出
export function actAnalysisExport(params) {
  // return axios.api + '/opActivity/activityAnalysisCustomerExcelExport?' + params;
  return axios.service({
    url: axios.api + `/opActivity/activityAnalysisCustomerExcelExport`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 热度分析--房源收藏详情
export function actFavorite(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityFavoriteDetails',
    method: 'get',
    params
  })
}

// 热度分析--房源收藏详情导出
export function actFavoriteExport(params) {
  return axios.api + '/opActivity/activityFavoriteDetailsExcelExport?activityIdNow=' + params.id;
}

// 房源热度分析，房源列表导出
export function exportHouseList(params) {
  // let url = axios.api + '/opActivity/activityHouseResImport'
  // let index = 0;
  // for(let key in params){
  //   index += 1;
  //   if(index == 1){
  //     url += '?'+key+'='+params[key]
  //   }else{
  //     url += '&'+key+'='+params[key]
  //   }
  // }
  // window.open(url);
  return axios.service({
    url: axios.api + `/opActivity/activityHouseResImport`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

//公用导出方法
export function commonExport(url, params) {
  let eUrl = axios.api + url;
  let index = 0;
  for (let key in params) {
    index += 1;
    if (index == 1) {
      eUrl += '?' + key + '=' + params[key]
    } else {
      eUrl += '&' + key + '=' + params[key]
    }
  }
  window.open(eUrl);
}

// 开盘数据页面--客户
export function houseOpenCustomer(params) {
  return axios.service({
    url: axios.api + '/opActivity/houseOpenCustomer',
    method: 'get',
    params
  })
}

// 开盘数据页面--房源
export function houseOpenHouse(params) {
  return axios.service({
    url: axios.api + '/opActivity/houseOpenHouse',
    method: 'get',
    params
  })
}

// 热度分析--房源
export function activityHouseRes(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityHouseRes',
    method: 'get',
    params
  })
}

// 房间号
export function getroomNum(params) {
  return axios.service({
    url: axios.api + '/opOrder/roomNum',
    method: 'get',
    params
  })
}

//热度分析 房源收藏详情
export function activityFavoriteDetails(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityFavoriteDetails',
    method: 'get',
    params
  })
}

// 热度分析--户型
export function activityHouseType(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityHouseType',
    method: 'get',
    params
  })
}

//签到统计
export function sginInList(params) {
  return axios.service({
    url: axios.api + '/opActivity/sginInList',
    method: 'get',
    params
  })
}
