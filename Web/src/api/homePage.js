import axios from '@/axios'

// 是否管理员
export function isAdmin() {
  return axios.service({
    url: axios.api + '/sysRole/isAdmin',
    method: 'get'
  })
}

// 项目简要信息
export function projectInfo(params) {
  return axios.service({
    url: axios.api + '/opActivity/projectInfo',
    method: 'get',
    params
  })
}

// 活动状态
export function activityStatus(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityStatus',
    method: 'get',
    params
  })
}

// 右上角活动列表切换
export function myActivityList(params) {
  return axios.service({
    url: axios.api + '/opActivity/myActivityList',
    method: 'get',
    params
  })
}

// 获取权限列表
export function myActivityRole(params) {
  return axios.service({
    url: axios.api + '/opActivity/myActivityRole',
    method: 'get',
    params
  })
}