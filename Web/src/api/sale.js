import axios from '@/axios'

//释放房源
export function deleteOrder(data) {
  return axios.service({
    url: axios.api + '/opOrder/deleteOrderY',
    method: 'post',
    data
  })
}

export function deleteOrderN(data) {
  return axios.service({
    url: axios.api + '/opOrder/deleteOrderN',
    method: 'post',
    data
  })
}

// 户型列表导出
export function downloadHouseTypeList(params) {
  // return axios.api + `/opOrder/downloadHouseTypeList?activityIdNow=${params.id}&isRegular=${params.isRegular}`;
  return axios.service({
    url: axios.api + `/opOrder/downloadHouseTypeList`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

//订单导入
export function importSaleExcel(data){
  return axios.service({
    url: axios.api + '/opOrder/importOrder',
    method: 'post',
    data
  })
}

// 订单导出
export function downLoadOrderExcel(params) {
  // console.log(params);
  // let index = 0;
  // let url = axios.api + '/opOrder/downLoadOrderExcel'
  // for(let key in params){
  //   index += 1;
  //   if(index==1){
  //     url += '?'+key+'='+params[key]
  //   }else{
  //     url += '&'+key+'='+params[key]
  //   }
  // }
  // return url;
  //return axios.api + '/opOrder/downLoadOrderExcel?activityIdNow=' + params.id;
  return axios.service({
    url: axios.api + `/opOrder/downLoadOrderExcel`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 置业顾问销售数据导出
export function downloadSaleData(params) {
  // return axios.api + `/opOrder/downloadSaleData?activityIdNow=${params.id}&isRegular=${params.isRegular}&roleName=${params.roleName}`;
  return axios.service({
    url: axios.api + `/opOrder/downloadSaleData`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 订单列表
export function opOrderPageList(params) {
  return axios.service({
    url: axios.api + '/opOrder/opOrderPageList',
    method: 'get',
    params
  })
}

// 房间号
export function roomNum(params) {
  return axios.service({
    url: axios.api + '/opOrder/roomNum',
    method: 'get',
    params
  })
}

// 置业顾问销售数据
export function saleData(params) {
  return axios.service({
    url: axios.api + '/opOrder/saleData',
    method: 'get',
    params
  })
}

// 销售户型列表
export function salesHouseTypeList(params) {
  return axios.service({
    url: axios.api + '/opOrder/salesHouseTypeList',
    method: 'get',
    params
  })
}

// 销售总览
export function salesOverview(params) {
  return axios.service({
    url: axios.api + '/opOrder/salesOverview',
    method: 'get',
    params
  })
}

export function downLoadOrderModel(params) {//导出订单模板
  // return axios.api + '/opOrder/moudlDownload'
  return axios.service({
    url: axios.api + `/opOrder/moudlDownload`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}
