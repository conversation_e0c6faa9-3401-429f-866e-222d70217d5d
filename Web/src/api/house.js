import axios from '@/axios'

//获取楼栋
export function getBuilding(params) {
  return axios.service({
    url: axios.api + `/opHousingResources/buildingName`,
    method: 'get',
    params
  })
}

//获取单元
export function getFloor(params) {
  return axios.service({
    url: axios.api + `/opHousingResources/unitName`,
    method: 'get',
    params
  })
}

//获取楼层
export function getCell(params) {
  return axios.service({
    url: axios.api + `/opHousingResources/unitFloorNumber`,
    method: 'get',
    params
  })
}

//产品类型
export function getRoomType(params) {
  return axios.service({
    url: axios.api + `/opHousingResources/roomType`,
    method: 'get',
    params
  })
}

//房源列表
export function getHouseList(params) {
  return axios.service({
    url: axios.api + `/opHousingResources/opHousingResourcesPageList`,
    method: 'get',
    params
  })
}
//房源白名单列表
export function getHouseWhiteList(data) {
  return axios.service({
    url: axios.api + `/opHousingResources/selectWhiteHouseResourceListByCondition`,
    method: 'post',
    data
  })
}

//房源白名单批量导入
export function importWhiteExcel(data) {
  return axios.service({
    url: axios.api + `/opHousingResources/importWhiteExcel`,
    method: 'post',
    data
  })
}

//销控列表操作
export function updateSaleControl(params) {
  return axios.service({
    url: axios.api + `/opHousingResources/updateSaleControlFlag`,
    method: 'get',
    params
  })
}


//批量导入
export function setImport(data) {
  return axios.service({
    url: axios.api + `/opHousingResources/importExcel`,
    method: 'post',
    data
  })
}

//下载模板
export function downLoadExcel(params) {
  return axios.service({
    url: axios.api + `/opHousingResources/downLoadExcel`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 配置房源下载模板
export function exportHourseResources(params) {
  return axios.service({
    url: axios.api + `/opHousingResources/exportHourseResources`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 房源白名单下载模板
export function downLoadWhiteExcel(params) {
  return axios.service({
    url: axios.api + `/opHousingResources/downLoadWhiteExcel`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

//房源白名单导出
export function downLoadWhiteListExcel(data) {
  return axios.service({
    url: axios.api + `/opHousingResources/exportWhiteHouseResourceListByCondition`,
    method: 'post',
    data,
    responseType: 'blob'
  })
}

//删除房源
export function delResources(data) {
  return axios.service({
    url: axios.api + `/opHousingResources/deleteOpHousingResources`,
    method: 'post',
    data
  })
}

//新增房源
export function newHouse(data) {
  return axios.service({
    url: axios.api + `/opHousingResources/saveOpHousingResources`,
    method: 'post',
    data
  })
}

//编辑房源
export function updateHouse(data) {
  return axios.service({
    url: axios.api + `/opHousingResources/updateOpHouseType`,
    method: 'post',
    data
  })
}

// 编辑房源展示
export function updateHouseShow(params) {
  return axios.service({
    url: axios.api + `/opHousingResources/updateOpHousingResourcesShow`,
    method: 'get',
    params
  })
}

// ==============================================================================
// 删除户型
export function deleteOpHouseType(data) {
  return axios.service({
    url: axios.api + '/opHouseType/deleteOpHouseType',
    method: 'post',
    data: data
  })
}

// 户型列表
export function opHouseTypePageList(params) {
  return axios.service({
    url: axios.api + '/opHouseType/opHouseTypePageList',
    method: 'get',
    params
  })
}

// 新增户型
export function saveOpHouseType(data) {
  return axios.service({
    url: axios.api + '/opHouseType/saveOpHouseType',
    method: 'post',
    data: data
  })
}

// 同步户型
export function updataAll(params) {
  return axios.service({
    url: axios.api + '/opHouseType/updataAll',
    method: 'get',
    params
  })
}

// 编辑户型
export function updateIopHouseTypeShow(params) {
  return axios.service({
    url: axios.api + '/opHouseType/updateIopHouseTypeShow',
    method: 'get',
    params
  })
}

// 编辑户型
export function updateOpHouseType(data) {
  return axios.service({
    url: axios.api + '/opHouseType/updateOpHouseType',
    method: 'post',
    data: data
  })
}

// 上传图片
export function uploadFile(data) {
  return axios.service({
    url: axios.api + '/opHouseType/uploadFile',
    method: 'post',
    data: data
  })
}

// 新增编辑户型
export function saveOrUpdateHouseType(data) {
  return axios.service({
    url: axios.api + '/opHouseType/saveOrUpdateHouseType',
    method: 'post',
    data: data
  })
}

// 条查批次列表
export function selectBatchListByCondition(data) {
  return axios.service({
    url: axios.api + `/batch/selectBatchListByCondition`,
    method: 'post',
    data
  })
}

// 新增修改批次
export function saveOrUpdateBatch(data) {
  return axios.service({
    url: axios.api + '/batch/saveOrUpdateBatch',
    method: 'post',
    data: data
  })
}

// 批量删除批次
export function deleteBatchByIds(data) {
  return axios.service({
    url: axios.api + '/batch/deleteBatchByIds?ids=' + data,
    method: 'post',
  })
}