import axios from '@/axios'

// 获取角色详情
export function sysRoleGet(params) {
  return axios.service({
    url: axios.api + '/sysRole/get',
    method: 'get',
    params
  })
}

// 获取角色列表
export function sysRoleList(params) {
  return axios.service({
    url: axios.api + '/sysRole/list',
    method: 'get',
    params
  })
}

// 删除角色
export function sysRoleDel(data) {
  return axios.service({
    url: axios.api + '/sysRole/removeRoles',
    method: 'post',
    data: data
  })
}

// 保存角色详情
export function sysRoleSave(data) {
  return axios.service({
    url: axios.api + '/sysRole/saveRole',
    method: 'post',
    data: data
  })
}


//-----------------------------------------------------------------------------------
// 项目区域
export function projectArea(params) {
  return axios.service({
    url: axios.api + '/opProjectSale/projectArea',
    method: 'get',
    params
  })
}

// 项目城市
export function projectCity(params) {
  return axios.service({
    url: axios.api + '/opProjectSale/projectCity',
    method: 'get',
    params
  })
}

// 查询条件项目
export function projectConditionList(params) {
  return axios.service({
    url: axios.api + '/opProjectSale/projectConditionList',
    method: 'get',
    params
  })
}

// 项目列表
export function projectPageList(params) {
  return axios.service({
    url: axios.api + '/opProjectSale/projectPageList',
    method: 'get',
    params
  })
}

// 获取项目详情
export function getProjectInfo(params) {
  return axios.service({
    url: axios.api + '/opProjectShare/projectPageList',
    method: 'get',
    params
  })
}


//-----------------------------------------------------------------------------------
// 活动详情
export function activityDetails(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityDetails',
    method: 'post',
    params
  })
}

// 获取大屏基本数据
export function getScreenBaseData(params) {
  return axios.service({
    url: axios.api + '/opHousingResources/getOpHousingBaseData',
    method: 'get',
    params
  })
}

// 活动增加角色初始化参数
export function activityRoleInit(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityRoleInit',
    method: 'get',
    params
  })
}

// 活动下角色列表
export function activityRoleList(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityRoleList',
    method: 'get',
    params
  })
}

// 开启 / 关闭活动
export function openActivity(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityStatus',
    method: 'post',
    params
  })
}

// 删除活动
export function delActivity(params) {
  return axios.service({
    url: axios.api + '/opActivity/delActivity',
    method: 'post',
    params
  })
}

// 删除活动角色
export function delActivityRole(params) {
  return axios.service({
    url: axios.api + '/opActivity/delActivityRole',
    method: 'post',
    params
  })
}

// 活动列表
export function activityList(params) {
  return axios.service({
    url: axios.api + '/opActivity/projectPageList',
    method: 'get',
    params
  })
}

// 活动列表所有分页
export function activityPageList(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityPage',
    method: 'get',
    params
  })
}

// 保存活动
export function saveActivity(data) {
  return axios.service({
    url: axios.api + '/opActivity/saveActivity',
    method: 'post',
    data: data
  })
}

// 保存活动角色
export function saveActivityRole(data) {
  return axios.service({
    url: axios.api + '/opActivity/saveActivityRole',
    method: 'post',
    data: data
  })
}

// ----------------------------------------------------------------------------------
// 通过组织获取用户信息
export function findUserByOrgId(params) {
  return axios.service({
    url: axios.api + '/opActivity/findUserByOrgId',
    method: 'get',
    params
  })
}

// 通过名字获取用户信息
export function findUserByName(params) {
  return axios.service({
    url: axios.api + '/opActivity/findUsersByNameOrCode',
    method: 'get',
    params
  })
}

//开启||关闭活动
export function openOrCloseActivity(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityStatus',
    method: 'post',
    params
  })
}

//同步项目简要信息
export function syncProjectInfo(params) {
  return axios.service({
    url: axios.api + '/opProjectShare/updataProject',
    method: 'post',
    params
  })
}

//判断角色名称编码是否重复
export function judgeNameCodeRpete(params) {
  return axios.service({
    url: axios.api + '/sysRole/hasRoleCode',
    method: 'get',
    params
  })
}

//获取活动详情
export function getOpenActivityDetail(params) {
  return axios.service({
    url: axios.api + '/opActivity/activityDetails',
    method: 'post',
    params
  })
}


//--------------------------------------------------------------
export function getDate() {
  return axios.service({
    url: axios.api + '/opActivity/getDate',
    method: 'get'
  })
}
//修改大屏楼栋布局参数
export function updateScreen(data) {
  return axios.service({
    url: axios.api + `/opActivity/updateScreenSetStrByActivityId`,
    method: 'post',
    data
  })
}

//生成二维码
export function buildQrCode(params) {
  return axios.service({
    url: axios.appApi + '/common/twoCode',
    method: 'post',
    params
  })
}
//下载二维码
export function downloadQrCode(params) {
  let url = axios.appApi + '/common/downloadRqImg';
  let index = 0;
  for (let k in params) {
    index += 1;
    if (index == 1) {
      url += '?' + k + '=' + params[k]
    } else {
      url += '&' + k + '=' + params[k]
    }
  }
  let a = document.createElement('a');
  a.href = url;
  a.style.display = 'none;';
  a.download = "file.jpg";
  a.target = "_blank";
  document.body.appendChild(a);
  a.click();
}

// 查看项目详情表根据项目id
export function getProjectShareByProjectId(params) {
  return axios.service({
    url: axios.api + '/opProjectShare/getProjectShareByProjectId',
    method: 'get',
    params
  })
}

// 更改项目详情表
export function updateProjectShare(data) {
  return axios.service({
    url: axios.api + '/opProjectShare/updateProjectShare',
    method: 'post',
    data
  })
}

export function saveActivityLottery(data) {
  return axios.service({
    url: axios.api + '/opActivity/saveActivityLottery',
    method: 'post',
    data: data
  })
}