/* eslint-disable */
import {
  resourcesList
} from "@/api/screen";
import autoScrollTop from "@/components/autoScrollTop.vue";

export default {
  name: 'th-screen',
  components: {
    autoScrollTop,
  },
  props: {
    act: {
      type: Object
    },
    params: {
      type: Object,
      default: {
        rowNum: 20,
        columnNum: 20,
        timerRun: 3,
        isTiling: false
      }
    },
    pageSet: {
      type: Object,
      default: {
        pageRun: 6,
        isPage: true
      }
    },
    selected: '',
  },
  data() {
    return {
      list: [],
      initData: {
        build: [],
        unit: [],
        data: []
      },
      buildList: [],
      unitList: [],
      houseList: [],
      timer: '',
      pageNum: 1,
      showPageNum: 1,
      allColumn: 0,
      pageTimer: '',
      selectIds: [],
    }
  },
  render(h) {
    // return h('div', { class: 'th-screen' },[
    //   <table class="th-table__body" cellspacing="1" cellpadding="0" border="0">
    //     <thead>
    //       <tr>{
    //         this._l(this.buildList, (b, $bi) => [
    //           <th colspan={b.bNum}  class="building-name"  >{b.buildingName}</th>
    //         ])
    //       }
    //     </tr>
    //       <tr>{
    //         this._l(this.unitList, (u, $ui) => [
    //           <th colspan={u.uNum}   class="unit-name" >{u.unitName}</th>
    //         ])
    //       }
    //       </tr>
    //     </thead>
    //     <tbody class="select-house-tbody">{
    //         this._l(this.houseList, (hl, $hli) => [
    //         <tr class={$hli >= this.params.rowNum?'screen-rowhide':''}>{
    //             this._l(hl, (row, $index) => [
    //             <td class={this.selectIds.length > 0 && this.selectIds.indexOf(row ? String(row.id) : -1) >= 0 ?"screen-selected":row?'':'screen-disabled'}>{row ? row.roomNum : ""}</td>
    //             ])
    //           }</tr>
    //         ])
    //       }</tbody>
    //   </table>
    // ]);

    const renderTable = () => {
      return h('table', { class: 'th-table__body', attrs: { cellspacing: 1, cellpadding: 0, border: 0 } }, [
        h('thead', [
          h('tr', this._l(this.buildList, (b, $bi) =>
            h('th', { attrs: { colspan: b.bNum }, class: 'building-name' }, b.buildingName)
          )),
          h('tr', this._l(this.unitList, (u, $ui) =>
            h('th', { attrs: { colspan: u.uNum }, class: 'unit-name' }, u.unitName)
          ))
        ]),
        h('tbody', { class: 'select-house-tbody' }, this._l(this.houseList, (hl, $hli) =>
          h('tr', { class: $hli >= this.params.rowNum ? 'screen-rowhide' : '' },
            this._l(hl, (row, $index) =>
              h('td', {
                class: this.selectIds.length > 0 && this.selectIds.indexOf(row ? String(row.id) : -1) >= 0
                  ? "screen-selected"
                  : row ? '' : 'screen-disabled'
              }, row ? row.roomNum : "")
            )
          )
        ))
      ])
    }
    return h('div', { class: 'th-screen' }, [
      this.params.isTiling
        ? h('autoScrollTop', [renderTable()])
        : renderTable()
    ])
  },
  watch: {
    "act.id"(v) {
      if (v)
        resourcesList({
          activityIdNow: v
        }).then(res => {
          if (res.code == 200) {
            this.list = res.data;
            this.initHouse();
          }
        });
    },
    selected(val) {
      this.selectIds = val;
    },
    "params": {
      handler() {
        clearTimeout(this.pageTimer);
        this.pageTimer = '';
        if (this.showPageNum == 1)
          this.clearTimer();
        else this.showPageNum = 1
      }, deep: true
    },
    "pageSet": {
      handler() {
        clearTimeout(this.pageTimer);
        this.pageTimer = '';
        this.clearTimer();
      }, deep: true
    },
    "showPageNum"() {
      this.clearTimer();
    }
  },
  methods: {
    clearTimer() {
      clearTimeout(this.timer);
      this.timer = '';
      this.buildList = [];
      this.unitList = [];
      this.houseList = [];
      this.update()
    },
    initHouse() {
      if (this.params.isTiling) { //平铺展示
        let r = 0
        this.houseList = [];
        this.list.forEach((e, i) => {
          if (i == (r + 1) * this.params.columnNum) r++;
          if (!this.houseList[r]) this.houseList[r] = [];
          this.houseList[r].push(e);
        })
        return;
      }

      let buildingName = "";
      let unitName = "";
      let currentFloor = "";

      let bNum = 0;
      let uNum = 0;
      let fNum = 0;

      let l = JSON.parse(JSON.stringify(this.list));

      l.push({
        currentFloor: "",
        unitName: "",
        buildingName: ""
      })
      l.forEach((e, i) => {
        if (e.buildingName + e.unitName + e.currentFloor != currentFloor && currentFloor != "") {
          let num = i - fNum;
          fNum = i;
          uNum = uNum > num ? uNum : num;
        }
        if (e.buildingName + e.unitName != unitName && unitName != "") {
          this.initData.unit.push({
            buildingName: buildingName,
            unitName: unitName.replace(buildingName, ''),
            uNum: uNum
          })
          bNum += uNum;
          uNum = 0;
        }
        if (e.buildingName != buildingName && buildingName != "") {
          this.initData.build.push({
            buildingName: buildingName,
            bNum: bNum
          })
          bNum = 0;
        }
        buildingName = e.buildingName;
        unitName = e.buildingName + e.unitName;
        currentFloor = e.buildingName + e.unitName + e.currentFloor
      })

      this.update();
    },
    update() {
      if (this.params.isTiling) { //平铺展示
        this.houseList = [];
        let r = 0
        this.list.forEach((e, i) => {
          if (i == (r + 1) * this.params.columnNum) r++;
          if (!this.houseList[r]) this.houseList[r] = [];
          this.houseList[r].push(e);
        })
        return;
      }

      console.log(this.initData)

      let scn = this.params.columnNum * this.showPageNum - this.params.columnNum;
      let ecn = this.params.columnNum * this.showPageNum;

      // 处理显示 楼栋
      let bl = JSON.parse(JSON.stringify(this.initData.build));
      let showNum = 0;
      this.allColumn = 0;
      this.buildList = bl.filter((i, index) => {
        this.allColumn += i.bNum;
        if (i.bNum == 0) return false;
        if (showNum >= ecn) { // 超出显示范围;
          return false;
        }

        let _s = showNum;
        showNum += i.bNum;
        if (showNum > scn) { // 需要显示范围；
          let _b = i.bNum;
          if (_s < scn) {
            _b -= scn - _s; // 显示长 - （分页起始长 - 累计长）
          }
          if (showNum > ecn) {
            _b -= showNum - ecn; // 显示长 - （累计长 - 分页结束长）
          }
          i.bNum = _b;
          return true;
        }
        return false;
      });

      // 处理显示单元
      let ul = JSON.parse(JSON.stringify(this.initData.unit));
      showNum = 0;
      this.unitList = ul.filter((i, index) => {
        if (i.uNum == 0) return false;
        if (showNum >= ecn) { // 超出显示范围;
          return false;
        }

        let _s = showNum;
        showNum += i.uNum;
        if (showNum > scn) { // 需要显示范围；
          let _b = i.uNum;
          if (_s < scn) {
            _b -= scn - _s; // 显示长 - （分页起始长 - 累计长）
          }
          if (showNum > ecn) {
            _b -= showNum - ecn; // 显示长 - （累计长 - 分页结束长）
          }
          i.uNum = _b;
          return true;
        }
        return false;
      });

      console.log(this.buildList, this.unitList);

      let dl = [];
      let sNum = 0;
      let cNum = showNum - scn > this.params.columnNum ? this.params.columnNum : showNum - scn;
      this.unitList.forEach((e, ui) => {
        // 原因在这里，可能是之前写的时候没有想到会有这么多房间数据
        let ul = this.list.filter(i => i.buildingName == e.buildingName && i.unitName == e.unitName);
        let currentFloor = "";
        let row = -1;
        let r2 = 0;
        ul.forEach(f => {
          // 根据currentFloor字段判断是否换行
          if (f.currentFloor != currentFloor) {
            currentFloor = f.currentFloor;
            row++; // 行数
            r2 = sNum;
            this.houseList[row] = this.houseList[row] || [];
            for (let r = 0; r < cNum; r++) {
              this.houseList[row][r] = this.houseList[row][r] || null;
            }
          }
          // 上面的代码，如果单元名称不切换的话，房间列表也不会切换
          if (ul.length > cNum * 2 && row == 0) { // 单行并且数据超过两页，不会切换单元名称
            this.houseList[row] = [...this.list].splice(
              (this.showPageNum - 1) * cNum,
              cNum
            );
          } else if (r2 < cNum) {
            this.houseList[row][r2++] = f;
          }
        })
        sNum += e.uNum;
      });
      this.rowRuning();
      if (this.pageSet.isPage && !this.pageTimer) {
        this.pageNum = Math.ceil(this.allColumn / this.params.columnNum);
        if (this.pageNum > 1) this.pageRuning();
        else this.$emit('getPager', { pageNum: this.showPageNum, pageSize: this.pageNum })
      }
    },
    rowRuning() {
      if (this.houseList.length > this.params.rowNum) {
        var _this = this;
        this.timer = setTimeout(_ => {
          _this.houseList.push(_this.houseList[0]);
          _this.houseList.splice(0, 1);
          _this.rowRuning();
        }, this.params.timerRun * 1000);
      }
    },
    pageRuning() {
      let _this = this;
      this.pageTimer = setTimeout(_ => {
        _this.showPageNum = _this.pageNum == _this.showPageNum ? 1 : (_this.showPageNum + 1);
        _this.pageRuning();
        _this.$emit('getPager', { pageNum: _this.showPageNum, pageSize: _this.pageNum })
      }, this.pageSet.pageRun * 1000);
    }
  },
  created() {
    resourcesList({
      activityIdNow: this.act.id
    }).then(res => {
      if (res.code == 200) {
        this.list = res.data;
        this.initHouse();
        this.selectIds = this.selected;
      }
    });
  }
}
