export default {
  name: 'th-panel',
  props: {
    title: {
      type: String,
      default: '标题',
    },
    noTitle: {
      type: Boolean,
      default: false
    },
    isDialog: {
      type: Boolean,
      default: false
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  render(h) {
    if (this.isDialog)
      return h('div', {
        'class': ['th-panel-dialog', this.visible ? '' : 'th-panel-dialog--disabled']
      }, [this.initPanel(h), h('div', {
        'class': 'th-panel--modal'
      })]);
    else
      return this.initPanel(h);
  },
  methods: {
    //初始化面板
    initPanel(h) {
      return h('div', {
        'class': 'th-panel'
      }, [this.panelHeader(h),
        h('div', {
          'class': ['th-panel-content']
        }, this.$slots.default),
        this.panelFooter(h)
      ]);
    },
    //面板头部
    panelHeader(h) {
      if (!this.noTitle || this.$slots['th-panel-header'] || this.isDialog) {
        return h('div', {
          'class': ['th-panel-header']
        }, [
          !this.noTitle ? h('span', {
            'class': ['th-panel-header--title']
          }, this.title) : h(),
          this.$slots['th-panel-header'] || this.isDialog ? h('div', {
            'class': ['th-panel-header--right']
          }, [this.$slots['th-panel-header'], this.isDialog ? h('i', {
            'class': ['el-icon-close', 'th-panel-dialog--close'],
            on: {
              click: this.hide
            }
          }) : null]) : null
        ])
      }
    },
    //面板底部
    panelFooter(h) {
      if (this.$slots['th-panel-footer']) {
        return h('div', {
          'class': ['th-panel-footer']
        }, [
          this.$slots['th-panel-footer']
        ])
      }
    },
    hide() {
      this.$emit('update:visible', false);
    },
  }
}
