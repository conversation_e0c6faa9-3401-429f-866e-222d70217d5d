export default {
  name:'th-search',
  data() {
    return {
      isOpen: true,
      isMore: false
    }
  },
  render(h) {
    var _this = this;
    return h('div', {
      'class': 'th-search'
    }, [
      h('div', {
        'class': ['th-search-tli']
      }, [
        h('div', {
          'class': ['th-search-term']
        }, this.renderTerm(h))
      ]),
      h('div', {
          'class': ['th-search-more', this.isMore ? '' : 'th-search-more--disabled']
        },
        [(<hr />), (<span><i on-click={_ => _this.isOpen = !_this.isOpen} class={_this.isOpen ? "el-icon-caret-bottom" : "el-icon-caret-top"} ></i></span>)]
      )
    ]);
  },
  methods: {
    renderTerm(h) {
      let _l = [];
      if (this.$slots.default && this.$slots.default.length > 0) {
        this.$slots.default.forEach(e => {
          if (e.tag == 'th-trem')
            _l.push(h('div', {
              'class': 'th-input'
            }, [
              h('span', {
                'class': 'th-input__title'
              }, e.data.attrs.title || ''),
              e.children
            ]))
        });
      }
      this.isMore = _l.length > 4;
      _l = _l.slice(0, this.isOpen ? _l.length > 3 ? 3 : _l.length : _l.length);
      _l.push(
        h('div', {
          'class': 'th-search-btn'
        }, this.$slots['search-btn']))  
      return _l;
    }
  }
}
