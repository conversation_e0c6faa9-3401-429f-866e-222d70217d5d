/* Automatically generated by './build/bin/build-entry.js' */
import '@/assets/scss/comp.scss';
import Search from '@/assets/js/comp/search.js';
import Panel from '@/assets/js/comp/panel.js';
import Button from '@/assets/js/comp/button.js';

const components = [
  Search,
  Panel,
  Button
];

const install = function (Vue, opts = {}) {

  components.forEach(component => {
    Vue.component(component.name, component);
  });
};

export default {
  install
}
