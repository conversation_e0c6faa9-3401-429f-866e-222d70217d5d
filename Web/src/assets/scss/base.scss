@charset 'utf-8';
// 本地图标
@import '../al_font/iconfont.css';
// 重置element-ui
$--color-primary: #e63f3c !default;

/* 改变 icon 字体路径变量，必需 */

$--font-path: '~element-ui/lib/theme-chalk/fonts';
// $--font-path: '../el_font';
@import "~element-ui/packages/theme-chalk/src/index";
// @import "../el_font/index.css";
* {
    padding: 0;
    margin: 0;
}

html,
body {
    height: 100vh;
    width: 100vw;
    overflow-x: hidden;
}

body {
    background: #efefef;
    font-family: MicrosoftYaHei;
    // font-size: 0;
    color: #000000;
}

a {
    text-decoration: none;
}

ul,
li {
    list-style: none;
}

.dzkp-main {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-flow: column;
    overflow: hidden;
    .sale-header {
        width: 100vw;
        height: 67px;
        line-height: 67px;
        flex: none;
        display: flex;
        z-index: 100;
        justify-content: space-between;
        background: url('../img/<EMAIL>') center no-repeat no-repeat;
        -webkit-box-shadow: 1px 2px 4px 0 rgba(0, 0, 0, 0.1);
        box-shadow: 1px 2px 4px 0 rgba(0, 0, 0, 0.1);
        .header-left {
            margin-left: 20px;
            flex: none;
            img {
                // vertical-align: top;
                // width: 155px;
                // margin-top: 16px;
                //height: 67px;
                //object-fit: contain;
                height: auto;
                max-width: 235px;
                margin-top: 18px;
                margin-left: -14px;
            }
            span {
                font-size: 24px;
                color: #b4b4b4;
                letter-spacing: 0;
                margin-left: 85px;
                font-weight: 700;
            }
        }
        .header-right {
            font-size: 12px;
            margin-right: 20px;
            flex: 1;
            text-align: right;
            display: flex;
            justify-content: flex-end;
            .update-activity,
            h3 {
                flex: none;
            }
            .update-activity {
                display: flex;
                justify-content: flex-start;
                width: 400px;
                line-height: 44px;
                height: 44px;
                margin-top: 12px;
                span {
                    color: #000;
                }
                .el-select {
                    width: 240px;
                }
            }
            .el-dropdown {
                line-height: 12px;
            }
            span {
                color: red;
                i.el-icon-arrow-down {
                    font-size: 10px;
                    margin-left: 5px;
                    font-weight: 800;
                }
            }
        }
    }
    .sale-container {
        display: flex;
        flex: 1;
        .sale-aside {
            background-color: #fff;
            box-shadow: rgba(0, 0, 0, 0.1) 1px 2px 4px 0px;
            flex: none;
            h2 {
                position: relative;
                height: 83px;
                line-height: 83px;
                font-size: 18px;
                letter-spacing: 0;
                padding: 0 20px;
            }
        }
        .sale-main {
            flex: 1;
            display: flex;
            flex-flow: column;
            min-width: 1080px;
            overflow-x: auto;
            .el-breadcrumb {
                line-height: 36px;
                margin: 5px 0 0 20px;
                color: #606266;
                border-bottom: thin solid #dadada;
            }
            .sale-main-page {
                // flex: 1;
                // height: 0;
                margin: 5px 0 5px 20px;
                // padding-right: 20px;
                overflow-y: auto;
                height: calc(100vh - 120px);
                border-bottom-left-radius: 5px;
                border-bottom-right-radius: 5px;
            }
        }
    }
}