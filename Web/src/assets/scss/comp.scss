@charset 'utf-8';

.th-button {
  min-width: 70px;
  height: 22px;
  padding: 0 5px;
  cursor: pointer;
  display: inline-block;
  background: #fff;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, .2);
  border-radius: 100px;
  text-align: center;
  line-height: 22px;
  color: #e63f3c;
  border: none;
  outline: none;
  font-size: 12px;
  margin: 0 5px;
}

.th-search {
  border-radius: 4px;
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
  padding: 5px 0;

  .th-search-tli {
    display: flex;
    padding-right: 4%;
    flex-wrap: wrap;
    justify-content: space-between;

    .th-search-term {
      display: flex;
      flex-wrap: wrap;
      font-size: 12px;
      flex: 1;

      .th-input {
        flex: 0 0 25%;
        display: flex;
        height: 40px;
        line-height: 40px;

        .th-input__title {
          flex: none;
          text-align: right;
          padding-right: 5px;
          width: 100px;
          overflow: hidden;
        }
      }

      .th-search-btn {
        flex: 1;
        text-align: right;
        line-height: 40px;
      }
    }

  }

  .th-search-more {
    position: relative;
    text-align: center;
    margin: 10px 0;

    hr {
      background-color: #e6e6e6;
      height: 1px;
      border: none;
    }

    span {
      display: inline-block;
      position: absolute;
      top: -11px;
      background: #fff;
      cursor: pointer;
    }

    i {
      font-size: 18px;
      color: #E63F3C;
    }
  }

  .th-search-more--disabled {
    display: none;
  }
}

.th-panel {
  border-radius: 4px;
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
  margin-top: 10px;

  .th-panel-header {
    height: 44px;
    line-height: 44px;
    border-bottom: 1px solid rgb(230, 230, 230);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;

    span.th-panel-header--title {
      flex: none;
      height: 16px;
      line-height: 16px;
      padding-left: 5px;
      border-left: 2px solid red;
      display: inline-block;
      font-size: 14px;
      font-weight: 700;
    }

    .th-panel-header--right {
      flex: 1;
      text-align: right;
    }
  }

  .th-panel-header--disable {
    display: none;
  }

  .th-panel-footer {
    text-align: right;
    height: 44px;
    line-height: 44px;
    border-top: 1px solid #e6e6e6;
    padding: 0 10px;
  }

}

.th-panel-dialog {
  position: fixed;
  z-index: 2004;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;

  .th-panel {
    width: 50%;
    margin: 0 auto;
    margin-top: 15vh;
    position: fixed;
    z-index: 2004;
    max-height: calc(50vh + 88px);
    top: 0;
    right: 0;
    left: 0;

    .th-panel-dialog--close {
      cursor: pointer;
    }

    .th-panel-content {
      max-height: 50vh;
      overflow-y: auto;
      height: calc(100% - 44px);
    }
  }

  width: 100vw;
  height: 100vh;

  .th-panel--modal {
    z-index: 2000;
    width: 100%;
    height: 100%;
    opacity: 0.5;
    background: #000;
  }
}

.th-panel-dialog--disabled {
  display: none;
}
