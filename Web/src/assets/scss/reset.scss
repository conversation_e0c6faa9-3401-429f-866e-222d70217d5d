@charset 'utf-8';

// 重置element UI 样式
.el-table {
    thead {
        font-family: MicrosoftYaHei-Bold;
        font-size: 12px;
        color: #000000;
        letter-spacing: 0;
    }

    td,
    th {
        background: #fff;
        border: none;
        padding: 0 !important;
        height: 30px;
        line-height: 30px;
        font-family: MicrosoftYaHei;
        font-size: 12px;
        color: #000000;
        letter-spacing: 0;
    }

    th {
        background: #f1f4f8 !important;
    }

    tr.el-table__row--striped td {
        background: #F1F4F8 !important;
    }

    tr.el-table__row.active td {
        background: red !important;
        color: #fff !important;
    }

    .cell {
        line-height: inherit;
    }
}

.el-select {
    width: 100%;
}

.el-input__inner {
    height: 24px !important;
    line-height: 24px !important;
}

.login-form .el-form-item {
    width: 100%;
    margin-bottom: 24px;
    .el-input__inner {
        height: 50px !important;
    }
  }

.el-range-editor.el-input__inner {
    padding: 0 10px;
}

.el-range-editor .el-range__close-icon,
.el-range-editor .el-range__icon,
.el-range-editor .el-range-separator {
    line-height: 22px;
}

.pager-container .left-container .el-select__caret.el-input__icon {
    line-height: 28px !important;
}

.exchange-open-type {
    position: fixed;
    right: 20px;
    top: 70px;
    .reset-select-lineheight {
        line-height: 40px !important;
    }
}

.login-type {
    >>>#tab-first {
      color: #333;
      line-height: 0;
      font-size: 14px;
      border-bottom-color: #409eff;
      border-bottom-width: 2px;
    }
  
    >>>.el-tabs__header {
      margin-bottom: 15px
    }
  
    >>>.el-input__inner {
      height: 50px;
      line-height: 54px;
      font-size: 12px
    }
  }

  .numer_input {
    max-width: 300px;
    width: 100%;
    text-align: left !important;
  }
  .numer_input .el-input__inner{
    text-align: left !important;
  }
  .numer_input .el-input-number::placeholder {
    text-align: left;
  }
  
  .numer_input .el-input__inner::-webkit-input-placeholder {
    text-align: left;
  
  }
  
  .numer_input .el-input__inner::-moz-placeholder {
    text-align: left;
  }