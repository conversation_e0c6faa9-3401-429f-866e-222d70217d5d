!function(d){var e,n='<svg><symbol id="icon--queren" viewBox="0 0 1024 1024"><path d="M512.83060878 49.38097549a463.78576891 463.78576891 0 1 0 463.78576892 463.78576895 463.78576891 463.78576891 0 0 0-463.78576892-463.78576895z m172.50568235 390.2587568s-164.72878683 194.42238788-185.93850189 217.75307442a26.86563908 26.86563908 0 0 1-41.00544906 0L340.32492646 540.03238347a36.05651557 36.05651557 0 0 1 0-45.95438258 24.74466755 24.74466755 0 0 1 38.88447755 0l97.5646892 97.5646892 168.97072985-197.2503499a24.74466755 24.74466755 0 0 1 38.88447754 0 36.76350606 36.76350606 0 0 1 0 45.9543826z" fill="" ></path></symbol><symbol id="icon-jinggao-" viewBox="0 0 1024 1024"><path d="M469.333 469.333V768a42.667 42.667 0 0 0 85.334 0V469.333a42.667 42.667 0 0 0-85.334 0z m42.667-128a64 64 0 1 0-64-64 64 64 0 0 0 64 64z m0 640A469.333 469.333 0 1 1 981.333 512 469.333 469.333 0 0 1 512 981.333z"  ></path></symbol></svg>',t=(e=document.getElementsByTagName("script"))[e.length-1].getAttribute("data-injectcss");if(t&&!d.__iconfont__svg__cssinject__){d.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(e){console&&console.log(e)}}!function(e){if(document.addEventListener)if(~["complete","loaded","interactive"].indexOf(document.readyState))setTimeout(e,0);else{var t=function(){document.removeEventListener("DOMContentLoaded",t,!1),e()};document.addEventListener("DOMContentLoaded",t,!1)}else document.attachEvent&&(n=e,o=d.document,i=!1,a=function(){i||(i=!0,n())},(c=function(){try{o.documentElement.doScroll("left")}catch(e){return void setTimeout(c,50)}a()})(),o.onreadystatechange=function(){"complete"==o.readyState&&(o.onreadystatechange=null,a())});var n,o,i,a,c}(function(){var e,t;(e=document.createElement("div")).innerHTML=n,n=null,(t=e.getElementsByTagName("svg")[0])&&(t.setAttribute("aria-hidden","true"),t.style.position="absolute",t.style.width=0,t.style.height=0,t.style.overflow="hidden",function(e,t){t.firstChild?function(e,t){t.parentNode.insertBefore(e,t)}(e,t.firstChild):t.appendChild(e)}(t,document.body))})}(window);