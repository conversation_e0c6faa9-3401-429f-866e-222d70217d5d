<template>
    <div>
        <!-- <th-panel noTitle class="opening_table">
            <table class="sale_overs">
                <tr>
                    <td>房源总数</td>
                    <td>{{openHouse.openData.totalCount}} 套</td>
                    <td>客户总人数</td>
                    <td>{{openHouse.loginData.totalUserCount}} 人</td>
                    <td>房源总数</td>
                    <td>{{openHouse.openData.totalCount}} 套</td>
                </tr>
                <tr>
                    <td>房源面积</td>
                    <td>{{openHouse.openData.totalAreaCount}} km²</td>
                    <td>历史已登录</td>
                    <td>{{openHouse.loginData.hisLoginUserCount}} 人</td>
                    <td>房源面积</td>
                    <td>{{openHouse.openData.totalCount}} Km²</td>
                </tr>
                <tr>
                    <td>户型总数</td>
                    <td>{{openHouse.openData.totalHouseType}} 个</td>
                    <td>历史未登录</td>
                    <td>{{openHouse.loginData.hisNoLoginUserCount}} 人</td>
                    <td>户型总数</td>
                    <td>{{openHouse.openData.totalCount}} 个</td>
                </tr>
                <tr>
                    <td>房源总价</td>
                    <td>{{openHouse.openData.totalPriceCount}} 万元</td>
                    <td>今日已登录</td>
                    <td>{{openHouse.loginData.todayLoginUserCount}} 人</td>
                    <td>房源总价</td>
                    <td>{{openHouse.openData.totalCount}} 万元</td>
                </tr>
                <tr>
                    <td>平均单价</td>
                    <td>{{openHouse.openData.avgPrice}} 万元/m</td>
                    <td>今日未登录</td>
                    <td>{{openHouse.loginData.todayNoLoginUserCount}} 人</td>
                    <td>平均单价</td>
                    <td>{{openHouse.openData.totalCount}} 万元/m</td>
                </tr>
            </table>
        </th-panel> -->
        <div class="open-data-form-container analysis_open_list">
          <th-panel noTitle>
            <formLine label="房源总数" :value="openHouse.openData.totalCount" unit="套" :noborder="true"></formLine>
            <formLine label="房源面积" :value="openHouse.openData.totalAreaCount" unit="m²"></formLine>
            <formLine label="户型总数" :value="openHouse.openData.totalHouseType" unit="个"></formLine>
            <formLine label="房源总价" :value="openHouse.openData.totalPriceCount" unit="万元"></formLine>
            <formLine label="平均单价" :value="openHouse.openData.avgPrice" unit="元/m²"></formLine>
          </th-panel>
          <th-panel noTitle>
            <formLine label="客户总人数" :value="openHouse.loginData.totalUserCount" unit="人" :noborder="true"></formLine>
            <formLine label="历史已登录" :value="openHouse.loginData.hisLoginUserCount" unit="人"></formLine>
            <formLine label="历史未登录" :value="openHouse.loginData.hisNoLoginUserCount" unit="人"></formLine>
            <formLine label="今日已登录" :value="openHouse.loginData.todayLoginUserCount" unit="人"></formLine>
            <formLine label="今日未登录" :value="openHouse.loginData.todayNoLoginUserCount" unit="人"></formLine>
          </th-panel>
          <th-panel noTitle>
            <formLine v-for="(item,index) in openHouse.roomFavoriteData" :key="index" :label="item.name" :value="item.count" unit="套" :noborder="index==0?true:false"></formLine>
          </th-panel>
        </div>
        <div class="analysis_open_list">
            <th-panel title="客户历史登录">
                <div class="echarts_pie" ref="oldLogin"></div>
            </th-panel>
            <th-panel title="客户今日登录">
                <div class="echarts_pie" ref="newLogin"></div>
            </th-panel>
            <th-panel title="房源热度">
                <div class="echarts_pie" ref="houseHot"></div>
            </th-panel>
        </div>
    </div>
</template>

<script >
let echarts = require("echarts/lib/echarts");
// 引入柱状图组件
require("echarts/lib/chart/pie");
// 引入提示框和title组件
require("echarts/lib/component/tooltip");
require("echarts/lib/component/legend");
import { houseOpenHouse, houseOpenCustomer } from "@/api/analysis";
import formLine from '@/components/formLine'
export default {
  data() {
    return {
      key: "value",
      openHouse: {
        loginData:{},
        openData:{},
        roomFavoriteData:[]
      },
      hotColorArr:['#F57C00','#673AB7','#7993D9','#F8E71C','#8BC34A'],
      openCustomer: {}
    };
  },
  components:{
    formLine
  },
  methods: {
    init() {
      let p = { activityIdNow: this.$router.actId };
      houseOpenHouse(p).then(res => {
        if (res.code == 200){
         this.openHouse = res.data;
         new Promise((resolve,reject) => {
             this.openHouse.roomFavoriteData.map((item,index) => {
               item.sortNum = item.name.replace(/[^0-9]/ig,"") != ''?Number(item.name.replace(/[^0-9]/ig,"")):0;
               if(index == this.openHouse.roomFavoriteData.length-1){
                 resolve(index)
               }
             })
         }).then(() => {
           this.openHouse.roomFavoriteData.sort((a,b) => {
             return b.sortNum - a.sortNum;
           })
         })
         // this.openHouse.openData.avgPrice = (Number(this.openHouse.openData.totalPriceCount.replace(/,/g,''))/Number(this.openHouse.openData.totalAreaCount)).toFixed(2);//totalPriceCount/totalAreaCount  replace(/,/g,'')
         this.openHouse.openData.avgPrice = (this.openHouse.openData.totalPriceCount.replace(',', '') * 10000 / this.openHouse.openData.totalAreaCount).toFixed(2)


          this.setEcharts(
              this.$refs.newLogin,
              "客户今日登录",
              ["今日已登录", "今日未登录"],
              [
                {
                  value: res.data.loginData.todayLoginUserCount,
                  name: "今日已登录",
                  itemStyle: { color: "#F4B63A" }
                },
                {
                  value: res.data.loginData.todayNoLoginUserCount,
                  name: "今日未登录",
                  itemStyle: { color: "#bab7b7" }
                }
              ]
          );
          this.setEcharts(
            this.$refs.oldLogin,
            "客户历史登录",
            ["历史已登录", "历史未登录"],
            [
              {
                value: res.data.loginData.hisLoginUserCount,
                name: "历史已登录",
                itemStyle: { color: "#97C7FF" }
              },
              {
                value: res.data.loginData.hisNoLoginUserCount,
                name: "历史未登录",
                itemStyle: { color: "#bab7b7" }
              }
            ]
          );
          let arr = new Array();
          let option = new Array();
          for(let i=0;i<res.data.roomFavoriteData.length;i++){
            if(res.data.roomFavoriteData[i].name != '(无)' && res.data.roomFavoriteData[i].count != 0){
              arr.push(res.data.roomFavoriteData[i].name);
              let obj = {
                value: res.data.roomFavoriteData[i].count,
                name: res.data.roomFavoriteData[i].name,
                itemStyle: { color: this.hotColorArr[i]?this.hotColorArr[i]:this.getRandowColor() }
              }
              option.push(obj);
            }
          }
          this.setEcharts(
            this.$refs.houseHot,
            "房源热度",
            arr,
            option
          )
      }

      });
    //   houseOpenCustomer(p).then(res => {
    //     if (res.code == 200) {
    //       this.openCustomer = res.data;
    //       this.setEcharts(
    //         this.$refs.newLogin,
    //         "客户今日登录",
    //         ["今日已登录", "今日未登录"],
    //         [
    //           {
    //             value: res.data.todayLogin,
    //             name: "今日已登录",
    //             itemStyle: { color: "#F4B63A" }
    //           },
    //           {
    //             value: res.data.todayNoLogin,
    //             name: "今日未登录",
    //             itemStyle: { color: "#E6E6E6" }
    //           }
    //         ]
    //       );
    //       this.setEcharts(
    //         this.$refs.oldLogin,
    //         "客户历史登录",
    //         ["历史已登录", "历史未登录"],
    //         [
    //           {
    //             value: res.data.historyLogin,
    //             name: "历史已登录",
    //             itemStyle: { color: "#97C7FF" }
    //           },
    //           {
    //             value: res.data.historyNoLogin,
    //             name: "历史未登录",
    //             itemStyle: { color: "#E6E6E6" }
    //           }
    //         ]
    //       );
    //     }
    //   });
    },
    getRandowColor(){
        return '#'+Math.floor(Math.random()*0xffffff).toString(16);
    },
    renderHotChart(){

    },

    setEcharts(r, title, legend, data) {
      let myChart = echarts.init(r);
      myChart.setOption({
        tooltip: {
          trigger: "item",
          formatter: "{b}: {c} ({d}%)"
        },
        legend: {
          x: "center",
          y: "bottom",
          itemGap: 20,
          padding: 20,
          itemHeight:10,
          icon: "circle",
          data: legend
        },
        series: [
          {
            name: title,
            type: "pie",
            radius: ["40%", "50%"],
            hoverAnimation: false,
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: "center"
              }
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            data: data
          }
        ]
      });
    }
  },
  created() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.open-data-form-container{
  .th-panel {
    flex: 1;
    height: auto!important;
    min-width: 280px;
  }
}
.analysis_open_list {
  font-size: 14px;
  display: flex;
  justify-content: space-between;

  .th-panel {
    flex: 1;
    height: 350px;
    min-width: 280px;
    line-height: 40px;
  }

  .th-panel:nth-child(2) {
    margin: 10px 10px 0 10px;
  }

  .echarts_pie {
    height: 300px;
    width: 100%;
  }
}
.opening_table {
  padding: 10px;

  .sale_overs {
    width: 100%;
    border-collapse: collapse;
    line-height: 30px;

    tr {
      border: 1px solid #eee;
      td {
        padding: 0 20px;
      }
    }
  }

  .sale_overs tr td:nth-child(2n + 1) {
    background: #eee;
    text-align: center;
  }

  .sale_overs tr td:nth-child(2n) {
    width: 20%;
    text-align: right;
  }
}
</style>
<style scoped>
.analysis_open_list >>> .th-panel-content {
  padding: 5px 10px;
}
</style>
