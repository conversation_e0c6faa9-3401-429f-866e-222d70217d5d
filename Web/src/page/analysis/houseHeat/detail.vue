<template>
    <div class=''>
        <th-panel title="收藏详情">
            <template v-slot:th-panel-header>
                <th-button v-has="'houseHeat_04'" @click="exportCollectDetailList">导出数据</th-button>
            </template>
            <el-table ref="houseHeatDetailRef" :data="searchData.records" stripe v-loading="loading">
                <el-table-column type="index" label="序号" width="" />
                <el-table-column prop="name" label="客户姓名" show-overflow-tooltip/>
                <el-table-column prop="tel" label="电话号码" />
                <el-table-column prop="idCard" label="身份证号" />
                <el-table-column prop="saleName" label="置业顾问" />
            </el-table>
            <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
            <!-- <wcg-page :total="searchData.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
        </th-panel>
    </div>
</template>

<script >
import axios from '@/axios'
import {activityFavoriteDetails,commonExport} from '@/api/analysis.js'
export default {
    data() {
        return {
            searchModel:{houseId:this.$route.query.houseId},
            searchData:{},
            buildingList: [],
            floorList: [],
            cellList: [],
            loading:false,
            page:{
                pageNum:1,
                pageSize:10,
                total:0
            }
        }
    },
    methods:{
        exportCollectDetailList(){
            commonExport('/opActivity/activityFavoriteDetailsExcelExport',this.searchModel);
        },
        handleSearch(pageNum, pageSize) {
            this.loading = true;
            this.searchModel.pageNum = pageNum;
            this.searchModel.pageSize = pageSize;
            this.searchModel.houseId = this.$route.query.houseId;
            this.searchData = {};
            activityFavoriteDetails(this.searchModel)
                .then(res => {
                if (res.code == 200) {
                    this.searchData = res.data;
                    this.page.total = res.data.total;
                }
                this.loading = false;
                })
                .catch(err => {
                this.loading = false;
                });
        },
        changePageNum(val){
            this.page.pageNum = val;
            this.handleSearch(val,this.page.pageSize);
        },
        changePageSize(val){
            this.page.pageSize = val;
            this.handleSearch(1,val);
        },
        refresh(){
            this.handleSearch(this.page.pageNum,this.page.pageSize);
        },
        handleExport(){
            window.location.href = axios.api + '/opActivity/activityFavoriteDetailsExcelExport?houseId='+this.$route.query.houseId
        }
        
    },
    created(){
        this.refresh();
    }
};
</script>

<style lang="scss" scoped>
</style>