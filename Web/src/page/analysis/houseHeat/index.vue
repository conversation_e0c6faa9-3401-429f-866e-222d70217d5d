<template>
    <div class=''>
        <th-search>
            <th-trem title="房源名称">
                <el-input v-model="searchModel.houseName"></el-input>
            </th-trem>
            <th-trem title="楼栋">
                <el-select v-model="searchModel.buildingName" filterable placeholder="请选择">
                <el-option v-for="(item,index) in buildingList" :key="index" :label="item.buildingName" :value="item.buildingName">
                </el-option>
                </el-select>
            </th-trem>
            <th-trem title="单元">
                <el-select v-model="searchModel.unitName" :disabled="searchModel.buildingName?false:true" filterable placeholder="请选择">
                <el-option v-for="item in floorList" :key="item.unitName" :label="item.unitName" :value="item.unitName">
                </el-option>
                </el-select>
            </th-trem>
            <th-trem title="房间号">
                <el-select v-model="searchModel.roomNum" :disabled="searchModel.unitName?false:true" filterable placeholder="请选择">
                <el-option v-for="item in cellList" :key="item.roomNum" :label="item.roomNum" :value="item.roomNum">
                </el-option>
                </el-select>
            </th-trem>
            
            <th-trem title="收藏数量">
                <el-input v-model="searchModel.minFavo"></el-input> &nbsp;-&nbsp;<el-input v-model="searchModel.maxFavo"></el-input>
            </th-trem>
            <!-- <th-trem title="至">
                <el-input v-model="searchModel.hourseType"></el-input>
            </th-trem> -->
            <template v-slot:search-btn>
                <th-button v-has="'houseHeat_01'" @click="search">查询</th-button>
                <th-button v-has="'houseHeat_02'" @click="handleReset">重置</th-button>
            </template>
        </th-search>
        <th-panel title="房源列表">
            <template v-slot:th-panel-header>
                <th-button v-has="'houseHeat_03'" @click="exportList">导出</th-button>
            </template>
            <el-table ref="houseHeatRef" :data="searchData.records" stripe v-loading="loading" tooltip-effect="dark"><!--@row-click="goDetail"-->
                <el-table-column type="index" label="序号" width="55" />
                <el-table-column prop="buildingName" label="楼栋" width="" show-overflow-tooltip/>
                <el-table-column prop="unitName" label="单元" width="" />
                <el-table-column prop="currentFloor" label="楼层" width="" />
                <el-table-column prop="roomNum" label="房间号"  width=""/>
                <el-table-column prop="houseName" label="房源名称" width="" show-overflow-tooltip />
                <el-table-column prop="favoNum" label="房源收藏次数" width="150">
                    <template slot-scope="scope">
                        <span style="color:blue;cursor:pointer" @click.stop="goDetail(scope.row)"> {{scope.row.favoNum}} </span>
                    </template>
                </el-table-column>   
            </el-table>
            <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
            <!-- <wcg-page :total="searchData.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
        </th-panel>
    </div>
</template>

<script >
import {
  getBuilding,
  getFloor,
} from "@/api/house.js";
import {activityHouseRes,getroomNum,exportHouseList} from '@/api/analysis.js'
export default {
    data() {
        return {
            searchModel:{activityIdNow: this.$router.actId},
            searchData:{},
            buildingList: [],
            floorList: [],
            cellList: [],
            loading:false,
            page:{
                pageNum:1,
                pageSize:10,
                total:0
            }
        }
    },
    methods:{
        handleSearch(pageNum, pageSize) {
            this.loading = true;
            this.searchModel.pageNum = pageNum;
            this.searchModel.pageSize = pageSize;
            this.searchData = {};
            activityHouseRes(this.searchModel)
                .then(res => {
                    if (res.code == 200) {
                        this.searchData = res.data;
                        this.page.total = res.data.total;
                    }
                    this.loading = false;
                })
                .catch(err => {
                    this.loading = false;
                });
        },
        exportList() {
            this.loading = true;
            exportHouseList(this.searchModel).then(res => {
                let blob = new Blob([res.data]); // 设置文件类型excel
                let url = URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
                // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                let a = document.createElement("a");
                a.href = url;
                a.download = '房源热度分析导出表.xlsx'
                a.click();
                // 释放这个临时的对象url
                URL.revokeObjectURL(url);
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
        changePageNum(val) {
            this.page.pageNum = val;
            this.handleSearch(val, this.page.pageSize);
        },
        changePageSize(val) {
            this.page.pageSize = val;
            this.handleSearch(1, val);
        },
        refresh() {
            this.handleSearch(this.page.pageNum, this.page.pageSize);
        },
        search() {
            this.handleSearch(1, this.page.pageSize);
        },
        goDetail(row) {
            console.log(row);
            this.$router.push({ path: 'HouseHeatDetail', query: { houseId: row.houseId } })
        },
        handleReset() {
            this.searchModel = {
                activityIdNow: this.$router.actId
            };
            this.search();
        },
    },
    created(){
        this.handleSearch(this.page.pageNum,this.page.pageSize);
        getBuilding({ activityIdNow: this.$router.actId }).then(res => {
            if (res.code == 200) this.buildingList = res.data;
        });
    },
    watch: {
        "searchModel.buildingName"(n) {
        this.$set(this.searchModel, "unitName", "");
        //this.$set(this.searchModel, "unitFloorNumber", "");
        getFloor({
            activityIdNow: this.$router.actId,
            buildingName: n
        }).then(res => {
            if (res.code == 200) this.floorList = res.data;
        });
        },
        "searchModel.unitName"(n) {
        if (n) {
            //this.$set(this.searchModel, "unitFloorNumber", "");
            getroomNum({
            activityIdNow: this.$router.actId,
            buildingName: this.searchModel.buildingName,
            unitName: n
            }).then(res => {
            if (res.code == 200) this.cellList = res.data;
            });
        }
        }
  },
};
</script>

<style lang="scss" scoped>
</style>