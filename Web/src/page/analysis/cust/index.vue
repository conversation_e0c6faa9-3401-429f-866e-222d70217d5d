<template>
    <div>
        <th-search>
            <th-trem title="客户姓名：">
                <el-input v-model="searchModel.name" placeholder="搜索客户姓名"></el-input>
            </th-trem>
            <th-trem title="手机号码：">
                <el-input v-model="searchModel.tel" placeholder="搜索电话号码"></el-input>
            </th-trem>
            <th-trem title="身份证号：">
                <el-input v-model="searchModel.idCard" placeholder="搜索身份证号"></el-input>
            </th-trem>
            <th-trem title="登录状态：">
                <el-select v-model="searchModel.loginNum" filterable placeholder="请选择">
                    <el-option label="历史未登录客户" :value="1"></el-option>
                    <el-option label="历史已登录客户" :value="2"></el-option>
                    <el-option label="今日未登录客户" :value="3"></el-option>
                    <el-option label="今日已登陆客户" :value="4"></el-option>
                    <el-option label="历史已登录无订单客户" :value="5"></el-option>
                </el-select>
            </th-trem>
            <th-trem title="有无订单：">
                <el-select v-model="searchModel.orderNum" filterable placeholder="请选择">
                    <el-option label="有" :value="0"></el-option>
                    <el-option label="无" :value="1"></el-option>
                </el-select>
            </th-trem>
            <th-trem title="置业顾问：">
                <el-input v-model="searchModel.saleName" placeholder="搜索置业顾问"></el-input>
            </th-trem>
            <template v-slot:search-btn>
                <th-button v-has="'cust_01'" @click="search">查询</th-button>
                <th-button v-has="'cust_02'" @click="handleReset">重置</th-button>
            </template>
        </th-search>
        <th-panel title="客户分析列表">
            <template v-slot:th-panel-header>
                <th-button v-has="'cust_03'" @click="handleDownload()">导出</th-button>
            </template>
            <el-table ref="orderListRef" :data="searchData.records" stripe v-loading="loading">
                <el-table-column label="序号" type="index" width="50" align="center" />
                <el-table-column label="客户姓名" prop="name" />
                <el-table-column label="手机号码" prop="tel" />
                <el-table-column label="身份证号" prop="idCard" />
                <el-table-column label="登录时间" prop="lastLoginDate" />
                <el-table-column label="有无订单" prop="orderYN" />
                <el-table-column label="置业顾问" prop="saleName" />
            </el-table>
            <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
            <!-- <wcg-page :total="searchData.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
        </th-panel>
    </div>
</template>

<script >
import { actAnalysis, actAnalysisExport } from "@/api/analysis";
export default {
  data() {
    return {
      searchModel: {},
      searchData: {},
      loading: false,
      page:{
        pageNum:1,
        pageSize:10,
        total:10
      }
    };
  },
  watch: {},
  methods: {
    handleReset() {
      this.searchModel = {};
      this.search();
    },
    handleDownload() {
      // let link = document.createElement("a");
      // link.style.display = "none";
     
      // link.href = actAnalysisExport(params);
      // link.target = "_blank";
      // document.body.appendChild(link);
      // link.click();
      // 导出模板
      this.loading = true;
       actAnalysisExport(this.searchModel).then(res=>{
          let blob = new Blob([res.data]); // 设置文件类型excel
          let url = URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement("a");
          a.href = url;
          a.download = '活动分析客户导出表.xlsx'
          a.click();
          // 释放这个临时的对象url
          URL.revokeObjectURL(url);
          this.loading = false;
          this.handleReset();
        }).catch(() => {
          this.loading = false;
        })
    },
    handleSearch(pageNum, pageSize) {
      this.loading = true;
      this.searchModel.pageNum = pageNum || 1;
      this.searchModel.pageSize = pageSize || 20;
      this.searchModel.activityIdNow = this.$router.actId;
      this.searchData = {};
      actAnalysis(this.searchModel)
        .then(res => {
          if (res.code == 200) {
            this.searchData = res.data;
            this.page.total = res.data.total; 
            this.loading = false;
          }
        })
        .catch(err => {
          this.loading = false;
        });
    },
    changePageNum(val){
      this.page.pageNum = val;
      this.handleSearch(val,this.page.pageSize);
    },
    changePageSize(val){
      this.page.pageSize = val;
      this.handleSearch(1,val);
    },
    search(){
      this.handleSearch(1,this.page.pageSize);
    },
    refresh(){
      this.handleSearch(this.page.pageNum,this.page.pageSize);
    }
  },
  created() {
    this.handleSearch(this.page.pageNum,this.page.pageSize);
  }
};
</script>

<style lang="scss" scoped>
</style>