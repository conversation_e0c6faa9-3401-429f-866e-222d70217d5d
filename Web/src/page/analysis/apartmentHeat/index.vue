<template>
    <div class=''>
        <th-search>
            <th-trem title="户型名称">
                <el-input v-model="searchModel.houseType"></el-input>
            </th-trem>
            <template v-slot:search-btn>
                <th-button v-has="'apartmentHeat_01'" @click="search">查询</th-button>
                <th-button v-has="'apartmentHeat_02'" @click="handleReset">重置</th-button>
            </template>
        </th-search>
        <th-panel title="户型列表">
            <el-table ref="houseHeatRef" :data="searchData.records" stripe v-loading="loading">
                <el-table-column type="index" label="序号" />
                <el-table-column prop="houseType" label="户型名称" show-overflow-tooltip/>
                <el-table-column prop="totilHouseRes" label="房源总数（套）"/>
                <el-table-column prop="num" label="收藏次数（次）"/>
            </el-table>
            <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
            <!-- <wcg-page :total="searchData.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
        </th-panel>
    </div>
</template>

<script >
import {activityHouseType} from '@/api/analysis.js'
export default {
    data() {
        return {
            searchModel:{activityIdNow: this.$router.actId},
            searchData:{},
            loading:false,
            page:{
                pageNum:1,
                pageSize:10,
                total:0,
            }
        }
    },
    methods:{
        handleSearch(pageNum, pageSize) {
            this.loading = true;
            this.searchModel.pageNum = pageNum;
            this.searchModel.pageSize = pageSize;
            this.searchData = {};
            activityHouseType(this.searchModel)
                .then(res => {
                if (res.code == 200) {
                    this.searchData = res.data;
                    this.page.total = res.data.total;
                }
                this.loading = false;
                })
                .catch(err => {
                this.loading = false;
                });
        },
        changePageNum(val){
            this.page.pageNum = val;
            this.handleSearch(val,this.page.pageSize);
        },
        changePageSize(val){
            this.page.pageSize = val;
            this.handleSearch(1,val);
        },
        refresh(){
            this.handleSearch(this.page.pageNum,this.page.pageSize);
        },
        search(){
            this.handleSearch(1,this.page.pageSize);
        },
        goDetail(row,event,column){
            console.log(row);
            this.$router.push({path:'HouseHeatDetail',query:{houseId:row.houseId}})
        },
        handleReset() {
            this.searchModel = {
                activityIdNow: this.$router.actId
            };
            this.search();
        },
    },
    created(){
        this.handleSearch(this.page.pageNum,this.page.pageSize);
    },
    
};
</script>

<style lang="scss" scoped>
</style>