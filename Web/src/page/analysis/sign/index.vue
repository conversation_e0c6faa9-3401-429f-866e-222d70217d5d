<template>
    <div class=''>
        <th-search>
            <th-trem title="客户姓名">
                <el-input v-model="searchModel.name"></el-input>
            </th-trem>
            <th-trem title="手机号码">
                <el-input v-model="searchModel.tel"></el-input>
            </th-trem>
            <th-trem title="身份证号">
                <el-input v-model="searchModel.idCard"></el-input>
            </th-trem>
            <th-trem title="是否签到">
                <el-select v-model="searchModel.sginOrNot" filterable placeholder="请选择">
                    <el-option v-for="item in options" :key="item.label" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </th-trem>
            <th-trem title="置业顾问">
                <el-input v-model="searchModel.saleName"></el-input>
            </th-trem>
            <template v-slot:search-btn>
                <th-button v-has="'sign_01'" @click="search">查询</th-button>
                <th-button v-has="'sign_02'" @click="handleReset">重置</th-button>
            </template>
        </th-search>
        <th-panel title="签到列表">
            <template v-slot:th-panel-header>
                <th-button v-has="'sign_03'" @click="exportList" >导出名单</th-button>
            </template>
            <el-table ref="houseHeatRef" :data="searchData.records" stripe v-loading="loading">
                <el-table-column type="selection" width="55" />
                <el-table-column type="index" label="序号" width="" />
                <el-table-column prop="name" label="客户姓名" width="" />
                <el-table-column prop="tel" label="手机号码" width="" />
                <el-table-column prop="idCard" label="身份证号" width="" />
                <el-table-column prop="sginyn" label="是否签到"  width=""/>
                <el-table-column prop="sginInDate" label="签到时间" width=""  />
                <el-table-column prop="saleName" label="置业顾问" width="" />
                
            </el-table>
             <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
            <!-- <wcg-page :total="searchData.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
        </th-panel>
    </div>
</template>

<script >
import axios from '@/axios'
import {sginInList,sginInListExport} from '@/api/analysis.js'
export default {
    data() {
        return {
            searchModel:{activityId: this.$router.actId},
            searchData:{},
            loading:false,
            options:[{label:'是',value:'y'},{label:'否',value:'n'}],
            page:{
                pageNum:1,
                pageSize:10,
                total:10
            }
        }
    },
    methods:{
        handleSearch(pageNum, pageSize) {
            this.loading = true;
            this.searchModel.pageNum = pageNum || 1;
            this.searchModel.pageSize = pageSize || 20;
            this.searchData = {};
            sginInList(this.searchModel)
                .then(res => {
                if (res.code == 200) {
                    this.searchData = res.data;
                    this.page.total = res.data.total;
                }
                this.loading = false;
                })
                .catch(err => {
                this.loading = false;
                });
        },
        handleReset() {
            this.searchModel = {
                activityId: this.$router.actId
            };
            this.search()
        },
        changePageNum(val){
            this.pageNum = val;
            this.handleSearch(val,this.page.pageSize);
        },
        changePageSize(val){
            this.page.pageSize = val;
            this.handleSearch(1,val);
        },
        refresh(){
            this.handleSearch(this.page.pageNum,this.page.pageSize);
        },
        search(){
            this.handleSearch(1,this.page.pageSize);
        },
        exportList(){//导出名单
        //   let params = Object.keys(this.searchModel).map(k=>{return `${k}=${this.searchModel[k]}&`}).join('')
        //     window.location.href = axios.api + '/opActivity/sginInListExport?' + params
            this.loading = true;
            sginInListExport(this.searchModel).then(res=>{
            let blob = new Blob([res.data]); // 设置文件类型excel
            let url = URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
            // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement("a");
            a.href = url;
            a.download = '签到统计导出表.xlsx'
            a.click();
            // 释放这个临时的对象url
            URL.revokeObjectURL(url);
            this.loading = false;
            this.handleReset();
            }).catch(() => {
            this.loading = false;
            })
        },
    },
    created(){
        this.handleSearch(this.page.pageNum,this.page.pageSize);
        
    },
    watch: {
        "searchModel.buildingName"(n) {
        this.$set(this.searchModel, "unitName", "");
        this.$set(this.searchModel, "unitFloorNumber", "");
        getFloor({
            activityIdNow: this.$router.actId,
            buildingName: n
        }).then(res => {
            if (res.code == 200) this.floorList = res.data;
        });
        },
        "searchModel.unitName"(n) {
        if (n) {
            this.$set(this.searchModel, "unitFloorNumber", "");
            getroomNum({
            activityIdNow: this.$router.actId,
            buildingName: this.searchModel.buildingName,
            unitName: n
            }).then(res => {
            if (res.code == 200) this.cellList = res.data;
            });
        }
        }
  },
};
</script>

<style lang="scss" scoped>
</style>