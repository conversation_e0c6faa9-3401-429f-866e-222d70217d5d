<template>
  <div>
    <th-panel title="活动信息" v-loading="loading">

      <template v-slot:th-panel-header>
        <th-button v-has="'activity_01'" :loading="loading" @click="handleSave" :disabled="isDisabled">保存</th-button>
        <th-button v-has="'activity_02'" :loading="loading" @click="routerBack">返回</th-button>
      </template>

      <el-form ref="activityForm" :model="activityData" label-width="230px" :rules="rules">
        <el-form-item label="开盘活动名称:" prop="activityName">
          <el-input v-model="activityData.activityName" placeholder="用户姓名"></el-input>
          <span class="form-remark">{{ activityData.activityName ? activityData.activityName.length : 0 }}/20</span>
        </el-form-item>
        <el-form-item label="模拟开盘设置:" prop="isSimulation">
          <el-radio-group v-model="activityData.isSimulation" @change="judgeIsSimulation">
            <el-radio label="n">不进行</el-radio>
            <el-radio label="y">进行</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="activityData.isSimulation == 'y'" label="模拟开盘时间:" prop="simulationEnd">
          <div style="height:40px;line-height:40px">
            <el-date-picker v-model="simTime" type="datetimerange" range-separator="至" start-placeholder="开始时间"
              end-placeholder="结束日期" style="width: 100%;" value-format="yyyy-MM-dd HH:mm:00" :picker-options="setSpo">
            </el-date-picker>
          </div>
          <span class="form-remark">* 正式开盘前，您仅可以组织所有用户进行一场模拟开盘，模拟开盘一切操作与正式开盘完全相同</span>
        </el-form-item>

        <el-form-item label="正式开盘时间:" prop="formalEnd">
          <div style="height:40px;line-height:40px">
            <el-date-picker v-model="forTime" type="datetimerange" range-separator="至" start-placeholder="开始时间"
              end-placeholder="结束日期" style="width: 100%;" value-format="yyyy-MM-dd HH:mm:00" :picker-options="setFpo">
            </el-date-picker>
          </div>
          <span class="form-remark">* 您仅可进行一次开盘活动，活动开始后将不可再修改开盘时间，<span
              style="color:#e63f3c">开盘前三分钟所有数据不可修改</span></span>
        </el-form-item>

        <!-- 不知道为什么就是必须要加一行，烦躁呀 -->
        <el-form-item style="display:none" prop="formalEnd"></el-form-item>

        <el-form-item label="下单客户退房:" prop="isRefunds">
          <el-radio-group v-model="activityData.isRefunds">
            <el-radio label="n">不允许</el-radio>
            <el-radio label="y">允许</el-radio>
          </el-radio-group>
          <span class="form-remark">* 若允许，则在开盘活动结束前且仍有其他房源可选的前提下，已选房用户可退掉订单重新选房；若不允许，则选定后不退不换。</span>
        </el-form-item>
        <el-form-item label="参与人数基数:" prop="joinNumber">
          <el-input-number class="input-number" v-model="activityData.joinNumber" :min="0" :step="1" :precision="0"
            placeholder="参与人数基数"></el-input-number>
          <span class="form-remark">* 开盘时投屏页面展示的“参与人数”，等于此次预设的人数基数+实际真实参与人数</span>
        </el-form-item>
        <el-form-item label="开盘前隐总价:" prop="openPriceHide">
          <el-radio-group v-model="activityData.openPriceHide">
            <el-radio label="n">否</el-radio>
            <el-radio label="y">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开盘前隐单价:" prop="unitPriceHide">
          <el-radio-group v-model="activityData.unitPriceHide">
            <el-radio label="n">否</el-radio>
            <el-radio label="y">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开盘后隐总价:" prop="afterOpenPriceHide">
          <el-radio-group v-model="activityData.afterOpenPriceHide">
            <el-radio label="n">否</el-radio>
            <el-radio label="y">是</el-radio>
          </el-radio-group>
          <span class="form-remark">* 若允许，则在开盘后隐藏总价信息，仅隐藏房源列表中总价，点击进入房间详情总价不隐藏。</span>
        </el-form-item>
        <el-form-item label="客户选房上限:" prop="buyNumber">
          <el-input v-model.number="activityData.buyNumber" placeholder="客户选房上限"></el-input>
          <span class="form-remark">* 您可控制每位购房者，在前台最多选定的房源数量，默认为1</span>
        </el-form-item>
        <el-form-item label="选房成功提示:" prop="successTips">
          <el-input v-model="activityData.successTips" placeholder="选房成功提示"></el-input>
          <span class="form-remark">{{ activityData.successTips ? activityData.successTips.length : 0 }}/40 *
            文案将出现在选房成功用户的提示弹出界面上</span>
        </el-form-item>
        <el-form-item label="客户收藏上限:" prop="collectionLimit">
          <el-input v-model.number="activityData.collectionLimit" placeholder="客户收藏上限"></el-input>
          <span class="form-remark">* 您可控制每位购房者，在前台最多收藏的房源数量</span>
        </el-form-item>
        <el-form-item label="身份证号验证:" prop="isIdVal">
          <el-radio-group v-model="activityData.isIdVal">
            <el-radio label="n">否</el-radio>
            <el-radio label="y">是</el-radio>
          </el-radio-group>
          <span class="form-remark">* 身份证号验证与短信验证必须至少验证一项</span>
        </el-form-item>
        <el-form-item label="短信验证:" prop="isMessageVal">
          <el-radio-group v-model="activityData.isMessageVal">
            <el-radio label="n">否</el-radio>
            <el-radio label="y">是</el-radio>
          </el-radio-group>
          <span class="form-remark">* 身份证号验证与短信验证必须至少验证一项</span>
        </el-form-item>
        <el-form-item label="售楼处电话号:" prop="salesTel">
          <el-input v-model="activityData.salesTel" placeholder="售楼处电话号"></el-input>
          <span class="form-remark"></span>
        </el-form-item>
        <el-form-item label="短信提醒信息:" prop="isMessageRemind">
          <el-radio-group v-model="activityData.isMessageRemind">
            <el-radio label="n">不发送</el-radio>
            <el-radio label="y">发送</el-radio>
          </el-radio-group>
          <span class="form-remark">* 若使用该功能，所有客户将在开盘前收到登录系统的短信提醒</span>
        </el-form-item>
        <el-form-item v-if="activityData.isMessageRemind == 'y'" class="messageH" prop="messageHandM">
          开盘前 <el-input v-model="activityData.messageH" style="flex:0;margin:0px 10px;" />小时 <el-input
            v-model="activityData.messageM" style="flex:0;margin:0px 10px;" /> 分钟发送登录提醒短信给所有客户，短信模板如下：
        </el-form-item>
        <el-form-item v-if="activityData.isMessageRemind == 'y'" label="正式选房提醒:" prop="formalMessage">
          <el-input type="textarea" :autosize="{ minRows: 1, maxRows: 4 }" v-model="activityData.formalMessage"
            placeholder="温馨提示：XXX（项目名称）模拟/正式开盘将于XX年XX月XX日XX：XX开始，请您尽快登录叁石云小程序选房（微信小程序搜索：泰禾集团叁石云），预祝您抢房成功！"></el-input>
        </el-form-item>
        <el-form-item v-if="activityData.isMessageRemind == 'y' && activityData.isSimulation == 'y'" label="模拟选房提醒:"
          prop="simulationMessage">
          <el-input type="textarea" :autosize="{ minRows: 1, maxRows: 4 }" v-model="activityData.simulationMessage"
            placeholder="温馨提示：XXX（项目名称）模拟/正式开盘将于XX年XX月XX日XX：XX开始，请您尽快登录叁石云小程序选房（微信小程序搜索：泰禾集团叁石云），预祝您抢房成功！"></el-input>
        </el-form-item>
        <el-form-item label="线上选房须知:" prop="shouldKnow">
          <el-input type="textarea" :autosize="{ minRows: 10, maxRows: 10 }" v-model="activityData.shouldKnow"
            placeholder="请输入在线选房须知，用户必须确认同意该须知后，方可参加在线选房"></el-input>
        </el-form-item>
        <!-- <el-form-item label="自动房源已购:">
          <el-radio-group v-model="activityData.isAutoBuy">
            <el-radio label="n">否</el-radio>
            <el-radio label="y">是</el-radio>
          </el-radio-group>
          <span class="form-remark">* 规则待确认</span>
        </el-form-item> -->
        <el-form-item label="展示收款信息:" prop="showPriceMsg">
          <el-radio-group v-model="activityData.showPriceMsg">
            <el-radio label="n">否</el-radio>
            <el-radio label="y">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否现场签到:" prop="signRange">
          <div>
            <el-radio-group v-model="activityData.isSignIn">
              <el-radio label="n">否</el-radio>
              <el-radio label="y">是</el-radio>
            </el-radio-group>
            <span v-show="activityData.isSignIn == 'y'">有效签到范围：距离中心点半径 &nbsp;&nbsp;
              <el-input v-model="activityData.signRange" placeholder="" style="width:80px"></el-input> &nbsp;&nbsp;米
              &nbsp;&nbsp;<span class="form-remark">*
                如设置设置现场签到为是，开盘前十五分钟小程序强制验证签到状态，未签到人员不可进入选房；如签到范围设置为0，则只验证签到状态，不验证签到位置。</span></span>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="map-container" v-if="activityData.isSignIn == 'y'">
            <baiduMap :range="activityData.signRange" :displayPoint="activityData.centerCoordinate"
              @signRangeEvent="getSignPosition" />
          </div>
        </el-form-item>
        <el-form-item label="活动开始前:" prop="showPriceMinutes">
          <div>
            <el-input-number class="input-number" v-model="activityData.showPriceMinutes" @blur="changeShowPriceMinutes"
              :min="0" :step="1" :precision="0"></el-input-number>&nbsp;&nbsp;分钟，显示房源价格
            <span class="form-remark"> </span>
          </div>
        </el-form-item>
        <el-form-item class="check-box-area" v-if="showSplitSetStrLists.length > 0" label="小程序换行数:"
          prop="showSplitSetStrOptions">
          <div v-for="(item, index) in showSplitSetStrLists" class="check-box-div" :key="item.roomType">
            <el-checkbox class="check-box" v-model="showSplitSetStrLists[index].checkFlag"
              @change="showSplitSetStrLists[index].splitNum = null">{{ item.roomType }}</el-checkbox>
            <el-input-number v-if="showSplitSetStrLists[index].checkFlag" placeholder="请输入每几个换行" class="input-number"
              v-model="showSplitSetStrLists[index].splitNum" :min="0" :max="100000" :step="1"
              :precision="0"></el-input-number>
          </div>
        </el-form-item>
        <el-form-item label="活动选房模式:" prop="checkMode">
          <el-radio-group v-model="activityData.checkMode" @change="checkModeFun">
            <el-radio :label="0">抢购模式</el-radio>
            <el-radio :label="1">选房模式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="activityData.checkMode == 1" label="单用户选房时间:" prop="checkMinutes">
          <el-input-number class="input-number" v-model="activityData.checkMinutes" :min="1" :step="1"
            :precision="0"></el-input-number><span style="width: 80%;font-size: 14px;color: #606266;">秒</span>
        </el-form-item>
        <el-form-item class="dateList" v-if="activityData.checkMode == 1 && activityData.isSimulation == 'y'"
          label="该活动的模拟可选房区间列表:" prop="simulateDateList">
          <div v-for="(item, index) in activityData.simulateDateList" :key="index"
            style="width: 100%;display: flex;flex-wrap: nowrap;align-items: center;">
            <p style="min-width: 30px;">
              {{ index + 1 }}.
            </p>
            <el-date-picker style="margin-left: 10px;" v-model="item['beginDate']" type="datetime" placeholder="选择日期时间"
              default-time="12:00:00" value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
            <el-date-picker style="margin-left: 10px;" v-model="item['endDate']" type="datetime" placeholder="选择日期时间"
              default-time="12:00:00" value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
            <th-button @click="deleteSimulateDate(item, index)">删除</th-button>
          </div>
          <div>
            <th-button @click="addSimulateDate">+</th-button>
          </div>
        </el-form-item>
        <el-form-item class="dateList" v-if="activityData.checkMode == 1" label="该活动的正式可选房区间列表:" prop="dateList">
          <div v-for="(item, index) in activityData.dateList" :key="index"
            style="width: 100%;display: flex;flex-wrap: nowrap;align-items: center;">
            <p style="min-width: 30px;">
              {{ index + 1 }}.
            </p>
            <el-date-picker style="margin-left: 10px;" v-model="item['beginDate']" type="datetime" placeholder="选择日期时间"
              default-time="12:00:00" value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
            <el-date-picker style="margin-left: 10px;" v-model="item['endDate']" type="datetime" placeholder="选择日期时间"
              default-time="12:00:00" value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
            <th-button @click="deleteDate(item, index)">删除</th-button>
          </div>
          <div>
            <th-button @click="addDate">+</th-button>
          </div>
        </el-form-item>
        <el-form-item label="是否开启电子签:" prop="openElectronFlag">
          <el-radio-group v-model="activityData.openElectronFlag" @change="openElectronFlagChange">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="电子签模版id:" prop="templateId">
            <el-input v-model="activityData.templateId" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="是否按批次展示房源:" prop="batchFlag">
          <el-radio-group v-model="activityData.batchFlag">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开始选房短信通知:" prop="openCheckFlag">
          <el-radio-group v-model="activityData.openCheckFlag">
            <el-radio :label="1">发送</el-radio>
            <el-radio :label="0">不发送</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="每人选房开始时间前多少分钟发送:" prop="everyBodyBeforeMinutes">
          <el-input-number class="input-number" v-model="activityData.everyBodyBeforeMinutes" :min="0" :step="1"
            :precision="0"></el-input-number><span style="width: 80%;font-size: 14px;color: #606266;">分钟</span>
        </el-form-item>
        <div style="height:50px;"></div>
        <!-- <el-form-item label="活动图片:"  v-show="activityData.isSignIn=='y'"></el-form-item> -->
      </el-form>

    </th-panel>
  </div>
</template>

<script>
import { activityDetails, saveActivity } from "@/api/sys.js";
import baiduMap from '@/components/BMap'
export default {
  components: {
    baiduMap
  },
  data() {
    var fTimeCheck = (r, v, c) => {
      if (
        this.activityData.formalStart &&
        new Date(this.activityData.simulationEnd).getTime() >=
        new Date(this.activityData.formalStart).getTime() + 3600000
      ) {
        c(new Error("正式开始时间至少大于模拟结束时间1小时"));
      } else {
        c();
      }
    };
    var isIdOrMesCheck = (r, v, c) => {
      if (this.activityData.isIdVal == 'y' || this.activityData.isMessageVal == 'y')
        c();
      else c(new Error("身份证号验证与短信验证必须至少验证一项"));
    };
    var isMessageRemindCheck = (r, v, c) => {
      if (this.activityData.isMessageRemind == 0) c();
      else {
        if (!v) return c(new Error(r.message));
        c();
      }
    };
    var isSingRange = (r, v, c) => {
      if (this.activityData.isSignIn == 'y' && (v == "" || v == null)) {
        return c(new Error('请输入签到范围'))
      } else {
        c()
      }
    }
    var dateListCheck = (r, v, c) => {
      let bool = this.activityData.dateList.every(item => item.beginDate && item.endDate)
      if (bool && this.activityData.dateList.length) {
        c();
      } else if (!this.rules.dateList[0].required) {
        c();
      } else {
        c(new Error("请选择时间"));
      }
    };
    var simulateDateListCheck = (r, v, c) => {
      let bool = this.activityData.simulateDateList.every(item => item.beginDate && item.endDate)
      if (bool && this.activityData.simulateDateList.length) {
        c();
      } else if (!this.rules.simulateDateList[0].required) {
        c();
      } else {
        c(new Error("请选择时间"));
      }
    };
    return {
      loading: false,
      simTime: [],
      setSpo: {
        disabledDate: time => {
          return time.getTime() < new Date().getTime() - 86400000;
        }
      },
      forTime: [],
      baseActivityData: {},
      setFpo: {
        disabledDate: time => {
          return time.getTime() < new Date().getTime() - 86400000;
          //let sed = this.activityData.simulationEnd;
          // if (sed && new Date(sed).getTime() + 3600000 > new Date().getTime())
          //   return time.getTime() < new Date(sed).getTime() + 3600000;
          // else return time.getTime() < new Date().getTime() - 86400000;
        }
      },
      showSplitSetStrLists: [], // 展示换行数设置集合
      activityData: {},
      rules: {
        activityName: [
          { required: true, message: "开盘项目名称为必填项", trigger: "blur" },
          { max: 20, message: "开盘项目名称超出20个字符", trigger: "blur" }
        ],
        simulationEnd: [
          { required: true, message: "模拟开盘时间为必填项", trigger: "blur" }
        ],
        formalEnd: [
          { required: true, message: "正式开盘时间为必填项", trigger: "blur" },
          { validator: fTimeCheck, trigger: "blur" }
        ],
        isSimulation: [
          { required: true, message: "模拟开盘设置为必填项", trigger: "blur" },
        ],
        isRefunds: [
          { required: true, message: "下单客户退房为必填项", trigger: "blur" },
        ],
        joinNumber: [
          { required: true, message: "参与人数基数为必填项", trigger: "blur" },
        ],
        openPriceHide: [
          { required: true, message: "开盘前隐总价为必填项", trigger: "blur" },
        ],
        unitPriceHide: [
          { required: true, message: "开盘前隐单价为必填项", trigger: "blur" },
        ],
        afterOpenPriceHide: [
          { required: true, message: "开盘后隐总价为必填项", trigger: "blur" },
        ],
        buyNumber: [
          {
            type: "number",
            min: 1,
            required: true,
            message: "客户选房上限为数字必填项,最小值为1",
            trigger: "blur"
          }
        ],
        successTips: [
          { required: true, message: "选房成功提示为必填项", trigger: "blur" },
          { max: 40, message: "选房成功提示超出40个字符", trigger: "blur" }
        ],
        collectionLimit: [
          { required: true, message: "客户收藏上限为必填项", trigger: "blur" },
          {
            type: "number",
            min: 1,
            message: "客户收藏上限为数字类型,可以为空，最小值为1",
            trigger: "blur"
          }
        ],
        salesTel: [
          { required: true, message: "售楼处电话号码为必填项", trigger: "blur" },
        ],
        isMessageRemind: [
          { required: true, message: "短信提醒信息为必填项", trigger: "blur" },
        ],
        isIdVal: [{ validator: isIdOrMesCheck, trigger: "change" }],
        isMessageVal: [{ validator: isIdOrMesCheck, trigger: "change" }],
        // formalMessage: [
        //   { required: true, message: "正式选房提醒为必填项", trigger: "blur" }
        // ],
        // simulationMessage: [
        //   { required: true, message: "模拟选房提醒为必填项", trigger: "blur" }
        // ],
        shouldKnow: [
          { required: true, message: "线上选房须知为必填项", trigger: "blur" }
        ],
        showPriceMsg: [
          { required: true, message: "展示收款信息必填项", trigger: "change" }
        ],
        signRange: [
          { validator: isSingRange, trigger: "blur" }
        ],
        dateList: [
          { required: true, validator: dateListCheck, trigger: "change" }
        ],
        simulateDateList: [
          { required: true, validator: simulateDateListCheck, trigger: "change" }
        ],
        checkMinutes: [
          { required: true, message: "请输入单用户选房时间", trigger: "change" }
        ],
        // showPriceMinutes: [
        //   { required: true, message: "请填写时间", trigger: "blur" }
        // ]
        openElectronFlag: [
          { required: true, message: "请选择是否开启电子签", trigger: "change" }
        ],
        templateId: [
          { required: true, message: "请输入电子签模版id", trigger: "change" }
        ],
        batchFlag: [
          { required: true, message: "请选择是否按批次展示房源", trigger: "change" }
        ],
        openCheckFlag: [
          { required: true, message: "请选择开始选房短信通知", trigger: "change" }
        ]
      },
      isDisabled: false,
      isTimeChanged: false,
      _f: true
    };
  },
  watch: {
    //this.baseActivityData  formalStart  formalEnd   simulationStart  simulationEnd
    simTime(v) {
      if (v && v.length == 2) {
        this.activityData.simulationStart = v[0];
        this.activityData.simulationEnd = v[1];
        if ((this.activityData.simulationStart != this.baseActivityData.simulationStart) || (this.activityData.simulationEnd != this.baseActivityData.simulationEnd)) {
          this.isTimeChanged = true;
        } else {
          this.isTimeChanged = false;
        }
        if (this.activityData.checkMode == 1) {
          this.rules.simulateDateList[0].required = true;
        } else {
          this.rules.simulateDateList[0].required = false;
        }
      } else {
        this.activityData.simulationStart = null;
        this.activityData.simulationEnd = null;
        this.isTimeChanged = false;
        this.rules.simulateDateList[0].required = false;
      }
    },
    forTime(v) {
      if (v && v.length == 2) {
        this.activityData.formalStart = v[0];
        this.activityData.formalEnd = v[1];
        if ((this.activityData.formalStart != this.baseActivityData.formalStart) || (this.activityData.formalEnd != this.baseActivityData.formalEnd)) {
          this.isTimeChanged = true;
        } else {
          this.isTimeChanged = false;
        }
        if (this.activityData.checkMode == 1) {
          this.rules.dateList[0].required = true;
        } else {
          this.rules.dateList[0].required = false;
        }
      } else {
        this.activityData.formalStart = null;
        this.activityData.formalEnd = null;
        this.isTimeChanged = false;
        this.rules.dateList[0].required = false;
      }
    },
    "activityData.isSignIn"(val) {
      if (val == 'n') {
        this.$set(this.activityData, 'centerCoordinate', '');
      }
    },
  },
  methods: {
    openElectronFlagChange(val) {
      // 开启电子签标识,电子签模板id必填
      this.rules.templateId[0].required = this.activityData.openElectronFlag == 1;
      this.$refs["activityForm"].clearValidate()
    },
    judgeIsSimulation(val) {
      if (val == 'n') {
        this.simTime = null;
      } else {
        this.simTime = [];
      }
      // 进行模拟开盘设置，模拟开盘时间必填
      this.rules.simulationEnd[0].required = val == 'y';
      // 不进行模拟开盘设置，正式开盘时间必填
      this.rules.formalEnd[0].required = val == 'n';
    },
    changeShowPriceMinutes() {
      const val = this.activityData.showPriceMinutes;
      if (val === undefined) {
        this.$set(this.activityData, 'showPriceMinutes', 0)
      }
    },
    handleSave() {
      if (this.isTimeChanged) {
        let simStart = this.simTime ? new Date(this.simTime[0]).valueOf() : null
        let simEnd = this.simTime ? new Date(this.simTime[1]).valueOf() : null
        let forStart = this.forTime ? new Date(this.forTime[0]).valueOf() : null
        let forEnd = this.forTime ? new Date(this.forTime[1]).valueOf() : null
        let nowTime = new Date().valueOf();
        if (simStart && (simStart - nowTime) < 180000) {//15
          //模拟开始时间必须晚于当前时间1小时
          this.$message({ type: 'warning', message: '模拟开盘时间必须晚于当前时间3分钟及以上!' });
          return;
        } else if (simStart && simEnd && simStart == simEnd) {
          //模拟开始时间与模拟结束时间相等
          this.$message({ type: 'warning', message: '模拟开盘时间与模拟结束时间不能为同一时间!' });
          return;
        } else if (forStart && (forStart - nowTime) < 180000) {//15
          //正式活动开始时间必须晚于当前时间一小时
          this.$message({ type: 'warning', message: '正式开盘时间必须晚于当前时间3分钟及以上!' });
          return;
        } else if (forStart && forEnd && forStart == forEnd) {
          //正式活动开始时间与结束时间相等
          this.$message({ type: 'warning', message: '正式开盘时间与正式结束时间不能为同一时间!' });
          return;
        } else if (forStart && simEnd && (forStart - simEnd) < 3600000) {
          //正式开始时间距离模拟结束时间不足一小时
          this.$message({ type: 'warning', message: '正式开盘时间必须晚于模拟开盘结束时间1小时及以上!' });
          return;
        }
      }
      if (this.activityData.isSignIn == 'y' && !this.activityData.centerCoordinate) {
        this.$message({
          type: 'warning',
          message: '您选择了现场签到，但未在地图上标注签到地点，请在地图上标注签到地点!'
        })
        return;
      }

      if (this.isDisabled) {
        return
      } else {
        let _f = true;
        this.$refs["activityForm"].validate(valid => {
          if (valid) {
            this.$set(this.activityData, 'formalCheckTimeStr', JSON.stringify(this.activityData.dateList));
            this.$set(this.activityData, 'checkTimeStr', JSON.stringify(this.activityData.simulateDateList))
            this.$set(this.activityData, 'showSplitSetStr', JSON.stringify(this.showSplitSetStrLists));
            this.activityData.showPriceMinutes = this.activityData.showPriceMinutes || 0;
            let params = {
              ...this.activityData
            }
            delete params.dateList;
            delete params.simulateDateList
            this.loading = true
            saveActivity(params).then(res => {
              this.isDisabled = false;
              if (res.code == 200) {
                this.routerBack();
                this.$message({ type: "success", message: "保存成功" });
                this.isDisabled = false;
              } else this.$message({ type: "error", message: res.msg });
            }).finally(() => {
              this.loading = false
            });
          }
        });
      }
    },
    routerBack() {
      this.$router.push({ name: "main" });
    },
    initAct(d) {//centerCoordinate
      let _a = this.activityData;
      this.$set(this.activityData, "activityName", d.activityName);
      this.$set(this.activityData, "buyNumber", d.buyNumber || 1);
      this.$set(this.activityData, "collectionLimit", d.collectionLimit || 5);
      this.$set(this.activityData, "formalStart", d.formalStart);
      this.$set(this.activityData, "formalEnd", d.formalEnd);
      this.$set(this.activityData, "centerCoordinate", d.centerCoordinate || null);
      this.$set(this.activityData, "showPriceMinutes", d.showPriceMinutes || 0);
      if (d.formalStart && d.formalEnd)
        this.forTime = [d.formalStart, d.formalEnd];
      this.$set(this.activityData, "formalMessage", d.formalMessage || "温馨提示：XXX（项目名称）模拟/正式开盘将于XX年XX月XX日XX：XX开始，预祝您抢房成功！");
      this.$set(this.activityData, "id", d.id);
      this.$set(this.activityData, "isAutoBuy", d.isAutoBuy || "n");
      this.$set(this.activityData, "isIdVal", d.isIdVal || "y");
      this.$set(this.activityData, "isMessageRemind", d.isMessageRemind || "y");
      this.$set(this.activityData, "isMessageVal", d.isMessageVal || "y");
      this.$set(this.activityData, "isRefunds", d.isRefunds || "y");
      this.$set(this.activityData, "isSignIn", d.isSignIn || "y");
      this.$set(this.activityData, "isSimulation", d.isSimulation || "y");
      this.$set(this.activityData, "joinNumber", d.joinNumber || 0);
      this.$set(this.activityData, "messageH", d.messageH);
      this.$set(this.activityData, "messageM", d.messageM);
      this.$set(this.activityData, "openPriceHide", d.openPriceHide || "y");
      this.$set(this.activityData, "afterOpenPriceHide", d.afterOpenPriceHide || "y");
      this.$set(this.activityData, "salesTel", d.salesTel || "");
      this.$set(this.activityData, "shouldKnow", d.shouldKnow || "");
      this.$set(this.activityData, "signRange", d.signRange || 0);
      this.$set(this.activityData, "simulationMessage", d.simulationMessage || "温馨提示：XXX（项目名称）模拟/正式开盘将于XX年XX月XX日XX：XX开始，预祝您抢房成功！");
      this.$set(this.activityData, "simulationStart", d.simulationStart);
      this.$set(this.activityData, "simulationEnd", d.simulationEnd);
      if (d.simulationStart && d.simulationEnd)
        this.simTime = [d.simulationStart, d.simulationEnd];
      this.$set(this.activityData, "successTips", d.successTips || "");
      this.$set(this.activityData, "unitPriceHide", d.unitPriceHide || "y");
      this.$set(this.activityData, "showPriceMsg", d.showPriceMsg || 'n')
      this.$set(this.activityData, "checkMode", d.checkMode || 0)
      this.$set(this.activityData, "checkMinutes", d.checkMinutes || 1)
      this.$set(this.activityData, "formalCheckTimeStr", d.formalCheckTimeStr || '')
      this.$set(this.activityData, "checkTimeStr", d.checkTimeStr || '')

      this.$set(this.activityData, 'dateList', JSON.parse(d.formalCheckTimeStr) || [])
      this.$set(this.activityData, 'simulateDateList', JSON.parse(d.checkTimeStr) || [])


      this.$set(this.activityData, "openElectronFlag", d.openElectronFlag || 1)
      this.$set(this.activityData, "templateId", d.templateId || '')
      this.$set(this.activityData, "batchFlag", d.batchFlag || 0)
      this.$set(this.activityData,"openCheckFlag", d.openCheckFlag || 0)
      this.$set(this.activityData,"everyBodyBeforeMinutes", d.everyBodyBeforeMinutes || 0)

      this.rules.dateList[0].required = this.activityData.checkMode == 1 && this.forTime && this.forTime.length
      this.rules.simulateDateList[0].required = this.activityData.checkMode == 1 && this.simTime && this.simTime.length

      // 进行模拟开盘设置，模拟开盘时间必填
      this.rules.simulationEnd[0].required = this.activityData.isSimulation == 'y';
      // 不进行模拟开盘设置，正式开盘时间必填
      this.rules.formalEnd[0].required = this.activityData.isSimulation == 'n';

      // 开启电子签标识,电子签模板id必填
      this.rules.templateId[0].required = this.activityData.openElectronFlag == 1;
      this.$nextTick(()=>{
        this.$refs["activityForm"].clearValidate()
      })
    },
    getActData() {
      if (this.$router.actId)
        this.loading = true
      activityDetails({ id: this.$router.actId }).then(res => {
        if (res.code == 200 && res.data) {
          if (res.data.showSplitSetStr) {
            this.showSplitSetStrLists = JSON.parse(res.data.showSplitSetStr);
          }
          console.log(this.showSplitSetStrLists);
          this.initAct(res.data);
          this.baseActivityData = JSON.stringify(res.data);
          this.baseActivityData = JSON.parse(this.baseActivityData);
        }
      }).finally(() => {
        this.loading = false
      });
    },
    getSignPosition(val) {
      if (this.activityData.isSignIn == 'y') {
        this.$set(this.activityData, 'centerCoordinate', val.lng + ',' + val.lat);
        //console.log('position',this.activityData.centerCoordinate)
      }
    },
    disabledDate(value) {
      if (Date.now() <= value) {
        return true
      }
      return false
    },
    checkModeFun(val) {
      this.$set(this.activityData, "checkMinutes", 1)
      this.$set(this.activityData, "formalCheckTimeStr", '')
      this.$set(this.activityData, "dateList", [])
      this.$set(this.activityData, "checkTimeStr", '')
      this.$set(this.activityData, "simulateDateList", [])
      this.rules.checkMinutes[0].required = val == 1
      this.rules.dateList[0].required = val == 1 && this.forTime && this.forTime.length
      this.rules.simulateDateList[0].required = val == 1 && this.simTime && this.simTime.length
    },
    addDate() {
      let arr = this.activityData.dateList
      arr.push({
        uuid: this.getUID(),
        beginDate: null,
        endDate: null,
      })
      this.$set(this.activityData, 'dateList', arr || [])
    },
    deleteDate(row, currentIndex) {
      if (row.uuid) {
        let arr = this.activityData.dateList.filter(item => item.uuid !== row.uuid)
        this.$set(this.activityData, 'dateList', arr || [])
      } else {
        let arr = this.activityData.dateList.filter((item, index) => currentIndex !== index)
        this.$set(this.activityData, 'dateList', arr || [])
      }
    },
    addSimulateDate() {
      let arr = this.activityData.simulateDateList
      arr.push({
        uuid: this.getUID(),
        beginDate: null,
        endDate: null,
      })
      this.$set(this.activityData, 'simulateDateList', arr || [])
    },
    deleteSimulateDate(row, currentIndex) {
      if (row.uuid) {
        let arr = this.activityData.simulateDateList.filter(item => item.uuid !== row.uuid)
        this.$set(this.activityData, 'simulateDateList', arr || [])
      } else {
        let arr = this.activityData.simulateDateList.filter((item, index) => currentIndex !== index)
        this.$set(this.activityData, 'simulateDateList', arr || [])
      }
    },
    getUID() { // 获取唯一值
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },
  },
  created() {
    this.getActData();
  }
};
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 0px;

  &.is-error {
    margin-bottom: 20px;
  }
}

.form-remark {
  flex: 2;
  color: #ccc;
}

.input-number {
  >>>span {
    display: none;
  }

  >>>.el-input__inner {
    text-align: left;
    padding-left: 15px;
    padding-right: 10px;
  }
}

.check-box {
  width: 180px;
}
</style>
<style>
.el-scrollbar:nth-of-type(3) {
  display: none !important;
}

.el-time-spinner {
  text-align: center;
}
</style>
<style scoped>
.el-form-item>>>.el-form-item__content {
  display: flex;
  flex-wrap: wrap;
}

.el-form-item>>>.el-form-item__content>div {
  width: auto;
  flex: 1;
  margin-right: 20px;
}

.el-form-item>>>.el-form-item__content>div.check-box-div {
  width: 180px;
  flex: 0;
}

.el-form-item>>>.el-form-item__content>div .el-radio {
  line-height: 40px;
  height: 40px;
}

.messageH>>>.el-input {
  width: 100px !important;
}

.messageH>>>.el-input__inner {
  width: 100px !important;
}

.map-container {
  margin-left: 150px;
  width: 740px;
  height: 500px;
}

.dateList>>>.el-form-item__content {
  flex-direction: column;

}
</style>
