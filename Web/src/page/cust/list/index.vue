<template>
  <div v-loading.fullscreen.lock="loading">
    <th-search>
      <th-trem title="客户姓名：">
        <el-input v-model="searchModel.name" placeholder="搜索客户姓名"></el-input>
      </th-trem>
      <th-trem title="手机号码：">
        <el-input v-model="searchModel.tel" placeholder="搜索手机号码"></el-input>
      </th-trem>
      <th-trem title="身份证号：">
        <el-input v-model="searchModel.idCard" placeholder="搜索身份证号"></el-input>
      </th-trem>
      <th-trem title="可选房数：">
        <el-select v-model="searchModel.selectCount" placeholder="请选择">
          <el-option
            v-for="item in selHouseNumOption"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="置业顾问：">
        <el-input v-model="searchModel.saleName" placeholder="搜索置业顾问"></el-input>
      </th-trem>
      <th-trem title="认筹单数：">
        <el-select v-model="searchModel.bookingNum" placeholder="请选择">
          <el-option
            v-for="item in recognizeOption"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </th-trem>
      <template v-slot:search-btn>
        <th-button v-has="'custList_01'" @click="search">查询</th-button>
        <th-button v-has="'custList_02'" @click="handleReset">重置</th-button>
      </template>
    </th-search>
    <th-panel title="客户列表">
      <template v-slot:th-panel-header>
        <th-button v-has="'custList_03'" @click="handleCustDownload()">下载模板</th-button>
        <th-button v-has="'custList_04'" @click="handleCustImport()">批量导入</th-button>
        <input ref="uploadFile" type="file" accept='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel' name="file" style="display:none;" @change="fileChange($event)" />
        <th-button v-has="'custList_06'" @click="handleCustInfo()">新增客户</th-button>
        <th-button v-has="'custList_08'" @click="handleCustDel()">删除客户</th-button>
      </template>
      <el-table ref="CustListRef" :data="searchData.records" stripe tooltip-effect="dark">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="客户姓名" prop="name" width="80"/>
        <el-table-column label="手机号码" prop="tel" show-overflow-tooltip/>
        <el-table-column label="身份证号" prop="idCard" show-overflow-tooltip/>
        <el-table-column label="添加时间" prop="creationDate" show-overflow-tooltip/>
        <el-table-column label="认筹单数" width="80" prop="bookingNum" show-overflow-tooltip/>
        <el-table-column label="可选房数" width="80" prop="selectCount" show-overflow-tooltip/>
        <el-table-column label="置业顾问" width="80" prop="saleName" show-overflow-tooltip/>
        <el-table-column label="选房序号" width="80" prop="userSort" show-overflow-tooltip/>
        <el-table-column label="摇号批次名称" width="120" prop="batchNameStr" show-overflow-tooltip/>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <th-button v-has="'custList_07'" @click="handleCustInfo(scope.row)">修改</th-button>
            <th-button v-has="'custList_09'" @click="handleWhiteList(scope.row)">房源白名单</th-button>
          </template>
        </el-table-column>
      </el-table>
      <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
      <!-- <wcg-page :total="page.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
    </th-panel>
    <el-dialog
      :append-to-body="true"
      title="房源白名单列表"
      v-if="whiteListVisible"
      :visible.sync="whiteListVisible"
      width="50%"
      top="20vh">
      <tree-transfer
        :titles="title"
        v-model="model"
        ref="treeTransfer"
        node-key="houseResourceId"
        default-expand-all
        show-checkbox
        nodeCheck
        checkStrictly
        :props="defaultProps"
        :data="fromData"
      >
      </tree-transfer>
      <div slot="footer">
        <el-button @click="whiteListVisible = false">取 消</el-button>
        <el-button type="primary" @click="sureSaveList">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script >
import {
  deleteOpUsers,
  opUserPageList,
  downLoadCus,
  importCustomerExcel,
  getRecognizeNum,
  getSelHouseNum,
  judgeIsChangeCusInfo,
  selectHouseResourceListByActivityId,
  getUserHouseResourceListByUserId,
  saveUserHouseResource
} from "@/api/cust.js";
export default {
  data() {
    return {
      searchModel: {
        activityIdNow: this.$router.actId,
        roleNameSelect: "customer"
      },
      page: {
        total: 0,
        pageNum:1,
        pageSize:20,
      },
      searchData: {},
      loading: false,
      recognizeOption:[],
      selHouseNumOption:[],
      whiteListVisible: false,
      title: ["房源列表","已选房源"],
      model: [],
      fromData: [],
      defaultProps: {
        children: 'childList',
        label: 'houseName',
        disabled: 'disable'
      },
      userId: ''
    };
  },
  methods: {
    changePageNum(val){
      this.page.pageNum = val;
      this.handleSearch(val,this.page.pageSize);
    },
    changePageSize(val){
      this.page.pageSize = val;
      this.handleSearch(this.page.pageNum,val);
    },
    refresh(){
      this.handleSearch(this.page.pageNum,this.page.pageSize);
    },
    search(){
      this.handleSearch(1,this.page.pageSize);
    },
    handleReset() {
      this.searchModel = {
        activityIdNow: this.$router.actId,
        roleNameSelect: "customer"
      };
      this.handleSearch(1,this.page.pageSize);
    },
    handleSearch(pageNum, pageSize) {
      this.searchModel.pageNum = pageNum || 1;
      this.searchModel.pageSize = pageSize || 20;
      this.searchData = {};
      opUserPageList(this.searchModel).then(res => {
        if (res.code == 200) {
          this.searchData = res.data;
          this.page.total = this.searchData.total;
        }
      });
    },
    fileChange(e) {
      this.loading = true;
      let files = this.$refs.uploadFile.files;
      if (files && files.length > 0) {
        let formData = new FormData();
        formData.append("file", files[0]);
        formData.append("activityIdNow", this.$router.actId);
        importCustomerExcel(formData)
          .then(res => {
            this.loading = false;
            this.$refs.uploadFile.value = "";
            if(res.code == 200){
              this.$message({
                type:'success',
                message:res.msg
              })
              this.handleSearch();
            }else{
              this.$message({
                type:'error',
                message:res.msg
              })
            }
          })
          .catch(err => {
            this.loading = false;
            this.$refs.uploadFile.value = "";
          });
      }
    },
    handleCustDownload() {
      // 导出模板
      this.loading = true;
      downLoadCus({ activityId: this.$router.actId }).then(res => {
        let blob = new Blob([res.data]); // 设置文件类型excel
        let url = URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
        // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement("a");
        a.href = url;
        a.download = '客户导入表.xlsx'
        a.click();
        // 释放这个临时的对象url
        URL.revokeObjectURL(url);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
    },
    handleCustImport() {
      let that = this;
      this.$confirm('是否导入数据,导入会覆盖当前已有信息,确认打开文件选择页面', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true
      }).then(() => {
        that.$refs.uploadFile.click();
      }).catch(() => {

      });
    },
    //  房源白名单
    handleWhiteList(row) {
      this.userId = row.id
      this.whiteListVisible = true
      selectHouseResourceListByActivityId({activityId: this.$router.actId,userId: row.id}).then(res => {
        if (res.code == 200) {
          this.fromData = res.data
          getUserHouseResourceListByUserId({userId: row.id}).then(ures=> {
            if (ures.code == 200) {
              this.model = ures.data
              console.log(this.model)
            }
          })
        }
      })
    },
    sureSaveList() {
      console.log(this.model)
      let arr = []
      this.model.map(i=>{arr.push(i.houseResourceId || i)})
      saveUserHouseResource({
        activityId: this.$router.actId,
        houseResourceIdList: arr,
        userId: this.userId
      }).then(res => {
        if (res.code == 200) {
          this.whiteListVisible = false
          this.$message.success(res.msg)
          this.handleSearch(1,this.page.pageSize);
        }
      })
    },
    handleCustInfo(row) {
      let params = {
        activityId:this.$router.actId
      }
      judgeIsChangeCusInfo(params).then(res => {
        if(res.code == 200){
          if(row){
            this.$router.push({
              path: "/cust/custInfo",
              query: { id: row.id }
            });
          }else{
            this.$router.push({
              path: "/cust/custInfo",
              query: {  }
            });
          }
        }else{
          this.$message({
            type:'warning',
            message:res.msg
          })
        }
      })
    },
    handleCustDel() {
      let _l = this.$refs.CustListRef.store.states.selection;
      if (_l && _l.length > 0) {
        this.$confirm('确认删除选中的客户?','温馨提示',{center:true}).then(() => {
          let idList = [];
          _l.forEach(e => {
            idList.push(e.id);
          });
          deleteOpUsers(idList).then(res => {
            if (res.code == 200) {
              this.handleSearch();
              this.$message({ type: "success", message: "删除成功" });
            } else this.$message({ type: "error", message: res.msg });
          });
        }).catch(() => {
          return;
        })
      }else{
        this.$alert('当前未选中任何客户，请先选择需要删除的客户!','温馨提示',{center:true})
      }
    },
    getRecognizeNum(){
      let params = {
        activityId:this.searchModel.activityIdNow
      }
      getRecognizeNum(params).then((res) => {
        if(res.code == 200){
          this.recognizeOption = [];
          res.data.map((item) => {
            let obj = {
              label:item,
              value:item,
            }
            this.recognizeOption.push(obj);
          })
        }
      })
    },
    getSelHouseNum(){
      let params = {
        activityId:this.searchModel.activityIdNow
      }
      getSelHouseNum(params).then((res) => {
        if(res.code == 200){
          this.selHouseNumOption = [];
          res.data.map((item) => {
            let obj = {
              label:item,
              value:item,
            }
            this.selHouseNumOption.push(obj);
          })
        }
      })
    }
  },
  created() {
    this.handleSearch();
    this.getRecognizeNum();
    this.getSelHouseNum();
  }
};
</script>

<style lang="scss" scoped>
</style>
