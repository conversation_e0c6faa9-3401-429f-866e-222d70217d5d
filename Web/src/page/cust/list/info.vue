<template>
    <div v-loading.fullscreen.lock="loading">
        <th-panel title="客户详情">
            <template v-slot:th-panel-header>
                <th-button @click="handleUpdate">保存</th-button>
                <th-button @click="routerBack">返回</th-button>
            </template>
            <el-form :inline="true" :model="custData" :rules="rules" ref="ruleForm" label-width="120px">
                <el-form-item label="客户姓名" prop="name">
                    <el-input v-model="custData.name" placeholder="客户姓名"></el-input>
                </el-form-item>
                <el-form-item label="身份证号">
                    <el-input v-model="custData.idCard" placeholder="身份证号"></el-input>
                </el-form-item>
                <el-form-item label="手机号码" prop="tel">
                    <el-input v-model="custData.tel" placeholder="手机号码"></el-input>
                </el-form-item>
                <el-form-item label="置业顾问">
                    <!-- <el-input v-model="custData.saleName" readonly placeholder="置业顾问" :disabled="isDisabled" @click.native="handleAddUser"></el-input> -->
                    <el-select v-model="custData.saleId" placeholder="请选择">
                      <el-option
                        v-for="(item,index) in adviserOptions"
                        :key="index"
                        :label="item.saleName"
                        :value="item.saleId">
                      </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="可选房数" prop="selectCount">
                    <el-input v-model="custData.selectCount" placeholder="可选房数"></el-input>
                </el-form-item>
                <el-form-item label="认筹单数">
                    <el-input v-model="custData.bookingNum" placeholder="认筹单数"></el-input>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input type="textarea" v-model="custData.remark">
                    </el-input>
                </el-form-item>
              <el-form-item label="选房序号">
                <el-input v-model="custData.userSort" placeholder="选房序号"></el-input>
              </el-form-item>
              <el-form-item label="摇号批次名称" style="position: relative;">
                <el-input v-model="custData.batchNameStr" placeholder="摇号批次名称"></el-input>
                <span style="position:absolute;width: 200px;left: 0;bottom: -20px;font-size: 10px;color: red;">(多个用英文逗号分割)</span>
              </el-form-item>
            </el-form>
        </th-panel>

        <th-panel title="选择授权人员" isDialog :visible.sync="isVisible">
            <div class="select-user">
                <div class="select-user__panel">
                    <el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
                    <el-tree :data="orgTree" node-key="id" :default-expanded-keys="[3931]" :props="orgTreeProps" :filter-node-method="filterNode" ref="orgTreeRef" @node-click="handleNodeClick">
                    </el-tree>
                </div>

                <div class="select-user__panel">
                    <el-input placeholder="输入关键字进行过滤" v-model="filterName">
                        <el-button slot="append" icon="el-icon-search" @click="handleSelectUserName"></el-button>
                    </el-input>
                    <el-table :data="userList" stripe @row-click="handleUserRowClick" :row-class-name="rowClassName">
                        <el-table-column label="序号" type="index" width="50" align="center" />
                        <el-table-column label="用户名" prop="username" />
                        <el-table-column label="姓名" prop="name" />
                    </el-table>
                </div>
            </div>

            <template v-slot:th-panel-footer>
                <th-button @click="handleUserSelect()">确定</th-button>
                <th-button @click="isVisible = false">取消</th-button>
            </template>
        </th-panel>

    </div>
</template>

<script>
import {
  activityRoleInit,
  findUserByOrgId,
  findUserByName
} from "@/api/sys.js";
import { updateOpUserShow, updateOpUser, saveOpUser, getAdviserOption } from "@/api/cust.js";
export default {
  data() {
    return {
      loading: false,
      custData: {},
      isVisible: false,
      orgTreeProps: {
        children: "childs",
        label: "name"
      },
      orgTree: [],
      userList: [],
      filterText: "",
      filterName: "",
      selectUser: {},
      isDisabled:false,
      adviserOptions:[],
      rules: {
          name: [
            { required: true, message: '请输入客户姓名', trigger: 'blur' },
          ],
          tel: [
            { required: true, message: '请输入手机号码', trigger: 'blur' }
          ],
          selectCount: [
            { required: true, message: '请输可选房数', trigger: 'blur' }
          ]
        }
    };
  },
  watch: {
    $route: "handleGet"
  },
  methods: {
    handleAddUser() {
      if(this.isDisabled){
        return;
      }
      this.isVisible = true;
      if (this.orgTree.length == 0) {
        activityRoleInit().then(res => {
          if (res.code == 200) {
            this.orgTree = res.data.orgTree;
          }
        });
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleSelectUserName() {
      findUserByName({ username: this.filterName }).then(res => {
        if (res.code == 200) this.userList = res.data;
      });
    },
    handleNodeClick(d) {
      findUserByOrgId({ orgId: d.sid || "3931" }).then(res => {
        if (res.code == 200) this.userList = res.data;
      });
    },
    rowClassName({ row, rowIndex }) {
      return this.selectUser.username == row.username ? "active" : "";
    },
    handleUserRowClick(d) {
      this.selectUser = d;
    },
    handleUserSelect() {
      this.$set(this.custData, "saleName", this.selectUser.name);
      this.$set(this.custData, "saleId", this.selectUser.username);
      this.isVisible = false;
    },
    handleUpdate() {
      let isSave = true;
      this.$refs['ruleForm'].validate((valid) => {
          if (!valid) {
            isSave = false;
          }
      });
      if(isSave){
        this.loading = true;
        if (this.custData.id) {
          let that =this;
          if(this.custData.saleId){
            //this.custData.saleName = this.adviserOptions.filter(item => item.cstguid == that.custData.saleId)[0].name;
            this.$set(this.custData,'saleName',this.adviserOptions.filter(item => item.saleId == that.custData.saleId)[0].saleName)
          }
          updateOpUser(this.custData).then(res => {
            this.loading = false;
            if (res.code == 200) {
              // this.$router.push({
              //   path: "/cust/custInfo",
              //   query: { id: this.custData.id }
              // });
              this.$message({ type: "success", message: "保存成功" });
              this.routerBack();
            } else this.$message({ type: "error", message: res.msg });
          });
        } else {
          let that =this;
          if(this.custData.saleId){
            //this.custData.saleName = this.adviserOptions.filter(item => item.cstguid == that.custData.saleId)[0].name;
            this.$set(this.custData,'saleName',this.adviserOptions.filter(item => item.saleId == that.custData.saleId)[0].saleName)
          }
          this.custData.activityId = this.$router.actId;
          this.custData.roleCode = "customer";
          this.custData.roleName = "customer";
          saveOpUser(this.custData).then(res => {
            this.loading = false;
            if (res.code == 200) {
              // this.$router.push({
              //   path: "/cust/custInfo",
              //   query: { id: res.data.id }
              // });
              this.$message({ type: "success", message: "保存成功" });
              this.routerBack();
            } else this.$message({ type: "error", message: res.msg });
          });
        }
      }
    },
    handleGet() {
      if (this.$route.query.id)
        updateOpUserShow({ id: this.$route.query.id }).then(res => {
          if (res.code == 200) {
            this.custData = res.data;
            this.selectUser = {
              name: this.custData.saleName,
              username: this.custData.saleId
            };
          }
        });
    },
    routerBack: function() {
      this.$router.push({ name: "custList" });
    },
    getAdviserList(){
      let params = {
        activityId:this.$router.actId
      }
      getAdviserOption(params).then((res) => {
        if(res.code == 200){
          this.adviserOptions = res.data;
        }
      })
    }
  },
  created() {
    this.handleGet();
    this.getAdviserList();
    if(this.$route.query.id){
      this.isDisabled = true;
    }else{
      this.isDisabled = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.select-user {
  display: flex;
  justify-content: space-between;

  .select-user__panel {
    flex: 1;
    padding: 5px;
  }
  .select-user__panel > div:nth-child(2) {
    height: calc(50vh - 45px);
    margin-top: 5px;
    overflow: auto;
  }
}
</style>
