<template>
  <div v-loading.fullscreen.lock="loading">
    <th-search>
      <th-trem title="客户姓名：">
        <el-input v-model="searchModel.name" placeholder="搜索客户姓名"></el-input>
      </th-trem>
      <th-trem title="手机号码：">
        <el-input v-model="searchModel.tel" placeholder="搜索手机号码"></el-input>
      </th-trem>
      <template v-slot:search-btn>
        <th-button v-has="'adviser_01'" @click="search">查询</th-button>
        <th-button v-has="'adviser_02'" @click="handleReset">重置</th-button>
      </template>
    </th-search>
    <th-panel title="置业顾问/营销经理列表">
      <template v-slot:th-panel-header>
        <th-button v-has="'adviser_03'" @click="handleCustDownload()">下载模板</th-button>
        <th-button v-has="'adviser_04'" @click="handleCustImport()">批量导入</th-button>
        <input ref="uploadFile" type="file" accept='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel' name="file" style="display:none;" @change="fileChange($event)" />
        <th-button v-has="'adviser_06'" @click="handleCustInfo()">新增人员</th-button>
        <th-button v-has="'adviser_08'" @click="handleCustDel()">删除人员</th-button>
      </template>
      <el-table ref="CustListRef" :data="searchData.records" stripe>
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="姓名" prop="saleName" />
        <el-table-column label="手机号码" prop="tel" />
        <el-table-column label="添加时间" prop="creationDate" />
        <el-table-column label="关联客户数" prop="holdUserNum" />
        <el-table-column label="角色" prop="roleName" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <th-button  v-has="'adviser_07'" @click="handleCustInfo(scope.row.id)">修改</th-button>
          </template>
        </el-table-column>
      </el-table>
      <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
      <!-- <wcg-page :total="page.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
    </th-panel>
  </div>
</template>

<script >
import {
  deleteOpUsers,
  opUserSalePageList,
  downLoadExcel,
  importSaleExcel
} from "@/api/cust.js";
export default {
  data() {
    return {
      searchModel: {
        activityIdNow: this.$router.actId
      },
      page: {
        total: 0,
        pageNum:1,
        pageSize:20
      },
      searchData: {},
      loading: false
    };
  },
  methods: {
    handleReset() {
      this.searchModel = {
        activityIdNow: this.$router.actId
      };
      this.handleSearch(1, this.page.pageSize);
    },
    search(){
      this.handleSearch(1, this.page.pageSize);
    },
    changePageNum(val){
      this.page.pageNum = val;
      this.handleSearch(val, this.page.pageSize);
    },
    changePageSize(val){
      this.page.pageSize = val;
      this.handleSearch(this.page.pageNum, val);
    },
    refresh(){
      this.handleSearch(this.page.pageNum, this.page.pageSize);
    },
    handleSearch(pageNum, pageSize) {
      this.searchModel.pageNum = pageNum || 1;
      this.searchModel.pageSize = pageSize || 20;
      this.searchData = {};
      opUserSalePageList(this.searchModel).then(res => {
        if (res.code == 200) {
          this.searchData = res.data;
          this.page.total = this.searchData.total;
        }
      });
    },
    fileChange(e) {
      this.loading = true;
      let files = this.$refs.uploadFile.files;
      if (files && files.length > 0) {
        let formData = new FormData();
        formData.append("file", files[0]);
        formData.append("activityIdNow", this.$router.actId);
        importSaleExcel(formData)
          .then(res => {
            if(res.code == 200){
              this.loading = false;
              this.$message({
                type:'success',
                message:res.msg
              })
              this.$refs.uploadFile.value = "";
              this.handleSearch();
            }else{
              this.loading = false;
              this.$message({
                type:'error',
                message:res.msg
              })
              this.$refs.uploadFile.value = "";
            }
          })
          .catch(err => {
            this.loading = false;
            this.$refs.uploadFile.value = "";
          });
      }
    },
    handleCustDownload() {
      this.loading = true;
      downLoadExcel({}).then(res => {
        let blob = new Blob([res.data]); // 设置文件类型excel
        let url = URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
        // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement("a");
        a.href = url;
        a.download = '置业顾问/经理导入表.xlsx'
        a.click();
        // 释放这个临时的对象url
        URL.revokeObjectURL(url);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
    },
    handleCustImport() {
      let that = this;
      this.$confirm('是否导入数据,导入会覆盖当前已有信息,确认打开文件选择页面', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true
      }).then(() => {
        that.$refs.uploadFile.click();
      }).catch(() => {

      });
    },
    handleCustInfo(id) {
      this.$router.push({
        path: "/cust/adviserInfo",
        query: { id: id }
      });
    },
    handleCustDel() {
      let _l = this.$refs.CustListRef.store.states.selection;
      if (_l && _l.length > 0) {
        this.$confirm('确认删除选中的人员吗?','温馨提示',{center:true}).then(() => {
          let idList = [];
          _l.forEach(e => {
            idList.push(e.id);
          });
          deleteOpUsers(idList).then(res => {
            if (res.code == 200) {
              this.handleSearch();
              this.$message({ type: "success", message: "删除成功" });
            } else this.$message({ type: "error", message: res.msg });
          });
        }).catch(() => {

        })
      }else{
        this.$alert('当前未选中任何人员，请先选择需要删除的人员!','温馨提示',{center:true})
      }
    }
  },
  created() {
    this.handleSearch(this.page.pageNum,this.page.pageSize);
  }
};
</script>

<style lang="scss" scoped>
</style>