<template>
    <div v-loading.fullscreen.lock="loading">
        <th-panel title="置业顾问/营销经理详情">
            <template v-slot:th-panel-header>
                <th-button @click="handleUpdate">保存</th-button>
                <th-button @click="routerBack">返回</th-button>
            </template>
            <el-form :inline="true" :model="custData" :rules="rules" ref="ruleForm" label-width="120px">
                <el-form-item label="姓名" prop="name">
                    <el-input v-model="custData.name" placeholder="姓名"></el-input>
                </el-form-item>
                <el-form-item label="手机号码" prop="tel">
                    <el-input v-model="custData.tel" placeholder="手机号码"></el-input>
                </el-form-item>
                <el-form-item label="角色">
                <el-select v-model="custData.roleName" filterable placeholder="请选择">
                    <el-option value="置业顾问"></el-option>
                    <el-option value="营销经理"></el-option>
                </el-select>

                </el-form-item>
            </el-form>
        </th-panel>
    </div>
</template>

<script>
import { updateOpUserShow, updateOpUser, saveOpUser } from "@/api/cust.js";
export default {
  data() {
    return {
      loading: false,
      custData: {},
      rules: {
          name: [
            { required: true, message: '请输入姓名', trigger: 'blur' },
          ],
          tel: [
            { required: true, message: '请输入手机号码', trigger: 'blur' },
          ],
        }
    };
  },
  watch: {
    $route: "handleGet"
  },
  methods: {
    handleUpdate() {
      this.$refs['ruleForm'].validate((valid) => {
          if (!valid) {
            return
          } else {
            this.loading = true;
            if (this.custData.id) {
              updateOpUser(this.custData).then(res => {
                this.loading = false;
                if (res.code == 200) {
                  // this.$router.push({
                  //   path: "/cust/adviserInfo",
                  //   query: { id: this.custData.id }
                  // });
                  this.routerBack();
                  this.$message({ type: "success", message: "保存成功" });
                } else this.$message({ type: "error", message: res.msg });
              });
            } else {
              this.custData.activityId = this.$router.actId;
              saveOpUser(this.custData).then(res => {
                this.loading = false;
                if (res.code == 200) {
                  // this.$router.push({
                  //   path: "/cust/adviserInfo",
                  //   query: { id: res.data.id }
                  // });
                  this.routerBack();
                  this.$message({ type: "success", message: "保存成功" });
                } else this.$message({ type: "error", message: res.msg });
              });
            }   
          }
      });
    },
    handleGet() {
      if (this.$route.query.id)
        updateOpUserShow({ id: this.$route.query.id }).then(res => {
          if (res.code == 200) {
            this.custData = res.data;
            this.selectUser = {
              name: this.custData.saleName,
              username: this.custData.saleId
            };
          }
        });
    },
    routerBack: function() {
      this.$router.push({ name: "adviser" });
    }
  },
  created() {
    this.handleGet();
  }
};
</script>

<style lang="scss" scoped>
.select-user {
  display: flex;
  justify-content: space-between;

  .select-user__panel {
    flex: 1;
    padding: 5px;
  }
  .select-user__panel > div:nth-child(2) {
    height: calc(50vh - 45px);
    margin-top: 5px;
    overflow: auto;
  }
}
</style>