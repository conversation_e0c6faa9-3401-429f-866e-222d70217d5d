<template>
  <div v-loading.fullscreen.lock="loading">
    <th-panel title="户型详情">
      <template v-slot:th-panel-header>
        <th-button v-if="!isDisabled" @click="saveInfo">保存</th-button>
        <th-button @click="routerBack">返回</th-button>
      </template>
      <el-form ref="formRef" :model="apartmentData" label-width="120px" :rules="rules">
        <el-form-item label="户型名称" prop="houseName">
          <el-input v-model="apartmentData.houseName" placeholder="户型名称" :disabled="isDisabled"
            maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="户型面积" prop="houseArea">
          <!-- <el-input v-model="apartmentData.houseArea" placeholder="户型面积" :disabled="isDisabled" maxlength="50"
            @input="requiredDurationEvent($event, 'houseArea')"></el-input> -->
          <el-input-number class="numer_input" v-model="apartmentData.houseArea" :controls="false" placeholder="户型面积"
            :disabled="isDisabled" :precision="2" :max="999999999999999"></el-input-number>

        </el-form-item>
        <el-form-item label="户型代码" prop="houseCode">
          <el-input v-model="apartmentData.houseCode" placeholder="户型代码" :disabled="isDisabled"
            maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="户型图片" prop="houseImg">
          <el-upload v-loading="uploadLoading" class="avatar-uploader" :multiple="false" :action="action"
            :headers="myHeaders" :show-file-list="false" :on-success="handleAvatarSuccess"
            :before-upload="beforeUpload">
            <img v-if="apartmentData.houseImg" :src="apartmentData.houseImg" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <!-- <img :src="apartmentData.houseImg"> -->
          <!-- <el-input v-model="apartmentData.houseImg" placeholder="户型图片" disabled></el-input> -->
        </el-form-item>
        <el-form-item label="户型描述" prop="houseDescribe">
          <el-input type="textarea" v-model="apartmentData.houseDescribe" :disabled="isDisabled" maxlength="200">
          </el-input>
        </el-form-item>
      </el-form>
    </th-panel>
  </div>
</template>

<script>
import axios from '@/axios'
import { updateIopHouseTypeShow, saveOrUpdateHouseType } from "@/api/house.js";
export default {
  data() {
    return {
      loading: false,
      apartmentData: {},
      selectUser: {},
      uploadLoading: false,
      action: axios.api + '/opHouseType/uploadFile',
      myHeaders: {},
      rules: {
        houseName: [
          { required: true, message: '请输入户型名称', trigger: 'submit' },
        ],
        houseArea: [
          { required: true, message: '请输入户型面积', trigger: 'submit' },
        ],
        houseCode: [
          { required: true, message: '请输入户型代码', trigger: 'submit' },
        ],
        houseImg: [
          { required: true, message: '请上传户型图片', trigger: 'submit' },
        ],
        houseDescribe: [
          { required: true, message: '请输入户型描述', trigger: 'submit' },
        ]
      }
    };
  },
  watch: {
    $route: "handleGet"
  },
  computed: {
    isDisabled() {
      return this.$route.query.type == 'view'
    }
  },
  methods: {
    requiredDurationEvent(value, name) {
      let reg = new RegExp(`^\\d*(\\.?\\d{0,2})`, 'g')
      let valRes = (value + '').replace(/[^\d^\.]+/g, '').replace(/^0+(\d)/, '$1').replace(/^\./, '0.').match(reg)[0] || ''
      this.apartmentData[name] = valRes
    },
    handleGet() {
      if (this.$route.query.id) {
        this.loading = true
        updateIopHouseTypeShow({ id: this.$route.query.id }).then(res => {
          if (res.code == 200) {
            this.apartmentData = res.data;
            this.selectUser = {
              name: this.apartmentData.saleName,
              username: this.apartmentData.saleId
            };
            this.loading = false
          }
        });
      }
    },
    beforeUpload() {
      this.uploadLoading = true
      return true
    },
    handleAvatarSuccess(res, file) {
      this.apartmentData.houseImg = res.data
      this.uploadLoading = false
    },
    routerBack() {
      this.$router.push({ name: "apartment" });
    },
    saveInfo() {
      this.$refs["formRef"].validate(valid => {
        if (valid) {
          this.loading = true;
          saveOrUpdateHouseType({ ...this.apartmentData, activityId: this.$router.actId }).then(res => {
            if (res.code == 200) {
              this.$message({ type: "success", message: "保存成功" });
              this.loading = false;
              this.$router.push({ name: "apartment" });
            } else this.$message({ type: "error", message: res.msg });
          });
        }
      });
    }
  },
  created() {
    this.myHeaders['bus-user-token'] = sessionStorage.getItem('busUserToken');
    this.handleGet();
  }
};
</script>

<style lang="scss" scoped>
.el-form {
  padding: 20px;
}

.el-input {
  max-width: 300px;
}

.select-user {
  display: flex;
  justify-content: space-between;

  .select-user__panel {
    flex: 1;
    padding: 5px;
  }

  .select-user__panel>div:nth-child(2) {
    height: calc(50vh - 45px);
    margin-top: 5px;
    overflow: auto;
  }
}

.avatar-uploader {
  width: 180px;
  height: 180px;
}

>>>.avatar-uploader .el-upload {
  border: 1px solid #DCDFE6;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
