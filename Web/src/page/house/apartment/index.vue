<template>
  <div>
    <th-search>
      <th-trem title="户型名称：">
        <el-input v-model="searchModel.houseName" placeholder="搜索户型名称"></el-input>
      </th-trem>
      <th-trem title="户型面积：">
        <el-input v-model="searchModel.houseArea" placeholder="搜索户型面积"></el-input>
      </th-trem>
      <th-trem title="户型描述：">
        <el-input v-model="searchModel.houseDescribe" placeholder="搜索户型描述"></el-input>
      </th-trem>
      <template v-slot:search-btn>
        <th-button v-has="'apartment_01'" @click="search">查询</th-button>
        <th-button v-has="'apartment_02'" @click="handleReset">重置</th-button>
      </template>
    </th-search>
    <th-panel title="项目列表">
      <template v-slot:th-panel-header>
        <!-- <th-button v-has="'apartment_03'" @click="handleUpdataAll()">同步户型</th-button> -->
        <th-button v-has="'apartment_03'" @click="newApartmentType()">新增</th-button>
        <th-button v-has="'apartment_04'" @click="handleHtDel()">删除户型</th-button>
      </template>
      <el-table ref="CustListRef" :data="searchData.records" stripe v-loading="loading" @row-click="handleRowClick">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="序号" type="index" width="50" align="center" />
        <!--                <el-table-column label="户型代码" prop="houseCode" />-->
        <!--                <el-table-column label="户型结构" prop="houseType" />-->
        <el-table-column label="户型名称" prop="houseName" />
        <el-table-column label="户型面积（m²）" prop="houseArea" />
        <el-table-column label="户型描述" prop="houseDescribe" />
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <th-button @click.stop="editApartmentType(scope.row)">编辑</th-button>
          </template>
        </el-table-column>
      </el-table>
      <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum"
        @currentPageSize="changePageSize" @refreshEvent="refresh" />
      <!-- <wcg-page :total="page.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
    </th-panel>
  </div>
</template>

<script>
import {
  deleteOpHouseType,
  opHouseTypePageList,
  updataAll
} from "@/api/house.js";
export default {
  data() {
    return {
      searchModel: {
        activityIdNow: this.$router.actId
      },
      page: {
        total: 0,
        pageNum: 1,
        pageSize: 20
      },
      searchData: {},
      loading: false
    };
  },
  methods: {
    handleReset() {
      this.searchModel = {
        activityIdNow: this.$router.actId
      };
      this.search();
    },
    changePageNum(val) {
      this.page.pageNum = val;
      this.handleSearch(val, this.page.pageSize);
    },
    changePageSize(val) {
      this.page.pageSize = val;
      this.handleSearch(this.page.pageNum, val);
    },
    search() {
      this.handleSearch(1, this.page.pageSize);
    },
    refresh() {
      this.handleSearch(this.page.pageNum, this.page.pageSize);
    },
    handleSearch(pageNum, pageSize) {
      this.loading = true;
      this.searchModel.pageNum = pageNum || 1;
      this.searchModel.pageSize = pageSize || 20;
      this.searchData = {};
      opHouseTypePageList(this.searchModel)
        .then(res => {
          if (res.code == 200) {
            this.searchData = res.data;
            this.page.total = this.searchData.total;
          }
          this.loading = false;
        })
        .catch(err => {
          this.loading = false;
        });
    },
    handleUpdataAll() {
      updataAll({ activityId: this.$router.actId }).then(res => {
        if (res.code == 200) {
          this.$message({ type: "success", message: "同步成功" })
          this.handleSearch();
        } else {
          this.$message({ type: "error", message: res.msg })
        }
      });
    },
    newApartmentType() {
      this.$router.push({
        path: "/house/apartmentInfo",
        query: { type: 'add' }
      });
    },
    editApartmentType(row) {
      this.$router.push({
        path: "/house/apartmentInfo",
        query: { id: row.id, type: 'edit' }
      });
    },
    handleRowClick(r, c, e) {
      this.handleHtInfo(r.id);
    },
    handleHtInfo(id) {
      this.$router.push({
        path: "/house/apartmentInfo",
        query: { id: id, type: 'view' }
      });
    },
    handleHtDel() {
      let _l = this.$refs.CustListRef.store.states.selection;
      if (_l && _l.length > 0) {
        this.$confirm('确认删除选中户型?', '温馨提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          center: true
        }).then(() => {
          let idList = [];
          _l.forEach(e => {
            idList.push(e.id);
          });
          deleteOpHouseType(idList).then(res => {
            if (res.code == 200) {
              this.handleSearch();
              this.$message({ type: "success", message: "删除成功" });
            } else this.$message({ type: "error", message: res.msg });
          });
        }).catch(() => {
          return;
        })
      } else {
        this.$alert('当前未勾选任何户型，请先勾选需要删除的户型', '温馨提示', {
          confirmButtonText: '确定',
          center: true
        })
      }
    }
  },
  created() {
    this.handleSearch();
  }
};
</script>

<style lang="scss" scoped></style>
