<template>
  <div v-loading.fullscreen.lock="loadingAll">
    <th-search>
      <th-trem title="批次名称">
        <el-input v-model="searchModel.batchName"></el-input>
      </th-trem>
      <template v-slot:search-btn>
        <th-button v-has="'batchManagement_01'" @click="search">查询</th-button>
        <th-button v-has="'batchManagement_02'" @click="handleReset"
          >重置</th-button
        >
      </template>
    </th-search>
    <th-panel title="项目列表">
      <template v-slot:th-panel-header>
        <th-button v-has="'batchManagement_03'" @click="add">新增</th-button>
        <th-button v-has="'batchManagement_04'" @click="handleDel()"
          >批量删除</th-button
        >
      </template>
      <el-table
        ref="ListRef"
        :data="searchData.records"
        stripe
        v-loading="loading"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="55" />
        <el-table-column
          prop="priorityLevel"
          label="优先级"
          show-overflow-tooltip
        />
        <el-table-column
          prop="batchName"
          label="批次名称"
          show-overflow-tooltip
        />
        <el-table-column
          prop="syncFlag"
          label="是否同步下一批次"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ ["同步", "不同步"][scope.row.syncFlag] }}
          </template>
        </el-table-column>
        <el-table-column
          prop="creationDate"
          label="创建时间"
          show-overflow-tooltip
        />
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <th-button @click.stop="handleEdit(scope.row)">编辑</th-button>
          </template>
        </el-table-column>
      </el-table>
      <th-page
        :total="page.total"
        :pageNum="page.pageNum"
        :pageSize="page.pageSize"
        @currentPageNum="changePageNum"
        @currentPageSize="changePageSize"
        @refreshEvent="refresh"
      />
    </th-panel>
    <el-dialog
      :append-to-body="true"
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="30%"
      :show-close="false"
      top="30vh"
      :before-close="closeDialog"
    >
      <div>
        <el-form
          v-loading="dialogLoading"
          ref="detailFormRef"
          label-position="right"
          :rules="rules"
          :model="detailForm"
          label-width="140px"
        >
          <el-form-item label="优先级:" prop="priorityLevel">
            <el-input
              v-model.number="detailForm.priorityLevel"
              :maxlength="500"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="批次名称:" prop="batchName">
            <el-input v-model="detailForm.batchName" :maxlength="500">
            </el-input>
          </el-form-item>
          <el-form-item label="是否同步下一批次:" prop="syncFlag">
            <el-radio-group v-model="detailForm.syncFlag">
              <el-radio :label="0">同步</el-radio>
              <el-radio :label="1">不同步</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button :loading="dialogLoading" @click="closeDialog"
          >取 消</el-button
        >
        <el-button :loading="dialogLoading" type="primary" @click="saveDetail"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  selectBatchListByCondition,
  saveOrUpdateBatch,
  deleteBatchByIds
} from "@/api/house.js";
export default {
  data() {
    return {
      searchModel: {
        activityIdNow: this.$router.actId,
        activityId: this.$router.actId
      },
      dialogTitle: "",
      dialogVisible: false,
      dialogLoading: false,
      detailForm: {
        id: null,
        activityId: this.$router.actId,
        priorityLevel: null,
        batchName: null,
        syncFlag: 0
      },
      rules: {
        priorityLevel: [
          { required: true, message: "请输入", trigger: "blur" },
          {
            type: "number",
            message: "优先级为数字必填项",
            trigger: "submit"
          }
        ],
        batchName: [
          {
            required: true,
            message: "请输入",
            trigger: "submit"
          }
        ],
        syncFlag: [
          {
            required: true,
            message: "请选择",
            trigger: "submit"
          }
        ]
      },
      searchData: {},
      loading: false,
      loadingAll: false,
      page: {
        total: 0,
        pageNum: 1,
        pageSize: 20
      }
    };
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.detailForm = {
          id: null,
          activityId: this.$router.actId,
          priorityLevel: null,
          batchName: null,
          syncFlag: 0
        };
      }
    }
  },
  methods: {
    add() {
      this.dialogTitle = "新增批次";
      this.dialogVisible = true;
    },
    closeDialog() {
      this.dialogVisible = false;
    },
    handleEdit(row) {
      this.dialogTitle = "编辑批次";
      this.detailForm = { ...row };
      this.dialogVisible = true;
    },
    saveDetail() {
      this.$refs["detailFormRef"].validate(valid => {
        if (valid) {
          this.dialogLoading = true;
          saveOrUpdateBatch({
            ...this.detailForm
          })
            .then(res => {
              if (res.code == 200) {
                this.$message({ type: "success", message: "操作成功" });
                this.dialogVisible = false;
                this.handleSearch();
              } else this.$message({ type: "error", message: res.msg });
            })
            .finally(() => {
              this.dialogLoading = false;
            });
        }
      });
    },
    handleDel() {
      let _l = this.$refs.ListRef.store.states.selection;
      if (_l && _l.length > 0) {
        this.$confirm("确认删除选中的批次?", "温馨提示", { center: true })
          .then(() => {
            let ids = _l.map(item => item.id).join(",");
            deleteBatchByIds(ids).then(res => {
              if (res.code == 200) {
                this.$message({ type: "success", message: "删除成功" });
                this.handleSearch();
              } else this.$message({ type: "error", message: res.msg });
            });
          })
          .catch(() => {
            return;
          });
      } else {
        this.$alert("当前未选中任何批次，请先选择需要删除的批次!", "温馨提示", {
          center: true
        });
      }
    },
    handleReset() {
      this.searchModel = {
        activityIdNow: this.$router.actId
      };
      this.search();
    },
    changePageNum(val) {
      this.page.pageNum;
      this.handleSearch(val, this.page.pageSize);
    },
    changePageSize(val) {
      this.page.pageSize = val;
      this.handleSearch(this.page.pageNum, val);
    },
    search() {
      this.handleSearch(1, this.page.pageSize);
    },
    refresh() {
      this.handleSearch(this.page.pageNum, this.page.pageSize);
    },
    handleSearch(pageNum, pageSize) {
      this.loading = true;
      this.searchModel.pageNum = pageNum || 1;
      this.searchModel.pageSize = pageSize || 20;
      this.searchModel.activityId = this.$router.actId;
      this.searchData = {};
      selectBatchListByCondition(
        Object.assign(this.searchModel, { activityIdNow: undefined })
      )
        .then(res => {
          if (res.code == 200) {
            this.searchData = res.data;
            this.page.total = res.data.total;
          }
          this.loading = false;
        })
        .catch(err => {
          this.loading = false;
        });
    }
  },
  created() {
    this.handleSearch();
  }
};
</script>

<style lang="scss" scoped></style>
