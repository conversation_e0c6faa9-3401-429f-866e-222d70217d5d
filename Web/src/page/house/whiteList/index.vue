<template>
  <div v-loading.fullscreen.lock="loadingAll">
    <th-search>
      <th-trem title="楼栋">
        <el-select v-model="searchModel.buildingName" filterable placeholder="请选择">
          <el-option v-for="item in buildingList" :key="item.buildingName" :label="item.buildingName" :value="item.buildingName">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="单元">
        <el-select v-model="searchModel.unitName" :disabled="searchModel.buildingName?false:true" filterable placeholder="请选择">
          <el-option v-for="item in floorList" :key="item.unitName" :label="item.unitName" :value="item.unitName">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="楼层">
        <el-select v-model="searchModel.currentFloor" :disabled="searchModel.unitName?false:true" filterable placeholder="请选择">
          <el-option v-for="item in cellList" :key="item.currentFloor" :label="item.currentFloor" :value="item.currentFloor">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="房间号">
        <el-input v-model="searchModel.roomNum"></el-input>
      </th-trem>
      <th-trem title="户型名称">
        <el-input v-model="searchModel.hourseType"></el-input>
      </th-trem>
      <th-trem title="户型结构">
        <el-input v-model="searchModel.roomStru"></el-input>
      </th-trem>
      <th-trem title="房源名称">
        <el-input v-model="searchModel.houseName"></el-input>
      </th-trem>
      <th-trem title="产品类型">
        <el-select v-model="searchModel.roomType" placeholder="请选择">
          <el-option v-for="item in productTypeList" :key="item.id" :label="item.roomType" :value="item.roomType">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="用户名">
        <el-input v-model="searchModel.userName"></el-input>
      </th-trem>
      <th-trem title="用户手机号">
        <el-input v-model="searchModel.userMobile"></el-input>
      </th-trem>

      <template v-slot:search-btn>
        <th-button v-has="'whiteList_01'" @click="search">查询</th-button>
        <th-button v-has="'whiteList_02'" @click="handleReset">重置</th-button>
      </template>
    </th-search>
    <th-panel title="项目列表">
      <template v-slot:th-panel-header>
        <th-button v-has="'whiteList_04'" @click="handleSourceExportTemplate">下载模板</th-button>
        <th-button v-has="'whiteList_05'" @click="handleSourceImportTemplate">批量导入</th-button>
        <input ref="uploadFile" type="file" accept='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel' name="file" style="display:none;" @change="fileChange($event)" />
        <th-button v-has="'whiteList_03'" @click="handleSourceDownload">导出</th-button>
      </template>
      <el-table ref="SourceListRef" :data="searchData.records" stripe v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="55" />
        <el-table-column prop="userName" label="用户名" width="70" show-overflow-tooltip/>
        <el-table-column prop="userMobile" label="用户手机号" width="100" show-overflow-tooltip/>
        <el-table-column prop="buildingName" label="楼栋" width="70" show-overflow-tooltip/>
        <el-table-column prop="unitName" label="单元" width="70"/>
        <el-table-column prop="currentFloor" label="楼层" width="55"/>
        <el-table-column prop="roomNum" label="房间号" width="70"/>
        <el-table-column prop="houseName" label="房源名称" show-overflow-tooltip />
        <el-table-column prop="houserArea" label="房源面积(m²)" width="120" show-overflow-tooltip/>
        <el-table-column prop="totalPrice" label="房源总价(元)" width="120" show-overflow-tooltip/>
        <el-table-column prop="unitPrice" label="房源单价(元)" width="120" show-overflow-tooltip/>
        <el-table-column prop="discountUnitPrice" label="优惠单价(元)" width="120" show-overflow-tooltip/>
        <el-table-column prop="discountTotalPrice" label="优惠总价(元)" width="120" show-overflow-tooltip/>
        <el-table-column prop="roomType" label="产品类型" show-overflow-tooltip/>
        <el-table-column prop="hourseType" label="户型名称" show-overflow-tooltip/>
        <el-table-column prop="roomStru" label="户型结构" show-overflow-tooltip/>
        <el-table-column prop="createName" label="操作人" show-overflow-tooltip/>
        <el-table-column prop="createTime" label="操作时间" width="150" show-overflow-tooltip/>
      </el-table>
      <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
    </th-panel>
  </div>
</template>

<script >
  import {
    getBuilding,
    getFloor,
    getCell,
    getRoomType,
    getHouseWhiteList,
    setImport,
    importWhiteExcel,
    downLoadWhiteExcel,
    downLoadWhiteListExcel,
  } from "@/api/house.js";
  export default {
    data() {
      return {
        searchModel: {
          activityIdNow: this.$router.actId,
          activityId: this.$router.actId,
        },
        productTypeList: [],
        buildingList: [],
        floorList: [],
        cellList: [],
        searchData: {},
        loading: false,
        loadingAll: false,
        page:{
          total:0,
          pageNum:1,
          pageSize:20
        }
      };
    },
    watch: {
      "searchModel.buildingName"(n) {
        this.$set(this.searchModel, "unitName", "");
        this.$set(this.searchModel, "currentFloor", "");
        getFloor({
          activityIdNow: this.$router.actId,
          buildingName: n
        }).then(res => {
          if (res.code == 200) this.floorList = res.data;
        });
      },
      "searchModel.unitName"(n) {
        if (n) {
          this.$set(this.searchModel, "currentFloor", "");
          getCell({
            activityIdNow: this.$router.actId,
            buildingName: this.searchModel.buildingName,
            unitName: n
          }).then(res => {
            if (res.code == 200) this.cellList = res.data;
          });
        }
      }
    },
    methods: {
      handleSourceExportTemplate () {
        // 导出模板
        this.loading = true;
        delete this.searchModel.pageNum;
        delete this.searchModel.pageSize;
        downLoadWhiteExcel(this.searchModel).then(res=>{
          let blob = new Blob([res.data]); // 设置文件类型excel
          let url = URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement("a");
          a.href = url;
          a.download = '房源白名单模板.xlsx'
          a.click();
          // 释放这个临时的对象url
          URL.revokeObjectURL(url);
          this.loading = false;
          this.handleReset();
        }).catch(() => {
          this.loading = false;
        })
      },
      handleSourceImportTemplate () {
        // 批量导入
        let that = this;
        this.$confirm('是否导入数据,导入会覆盖当前已有信息,确认打开文件选择页面', '温馨提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          center:true
        }).then(() => {
          that.$refs.uploadFile.click();
        }).catch(() => {

        });
      },
      fileChange(e) {
        this.loadingAll = true;
        let files = this.$refs.uploadFile.files;
        if (files && files.length > 0) {
          let formData = new FormData();
          formData.append("file", files[0]);
          formData.append("activityIdNow", this.$router.actId);
          importWhiteExcel(formData)
            .then(res => {
              if(res.code == 200){
                this.loadingAll = false;
                this.$refs.uploadFile.value = "";
                this.$message({ type: "success", message: res.msg });
                this.handleSearch();
              }else{
                this.loadingAll = false;
                this.$refs.uploadFile.value = "";
                this.$message({ type: "error", message: res.msg });
              }
            })
            .catch(err => {
              this.loadingAll = false;
              this.$refs.uploadFile.value = "";
            });
        }
      },
      handleSourceDownload() {
        this.searchModel.activityId = this.$router.actId
        downLoadWhiteListExcel(Object.assign(this.searchModel,{activityIdNow:undefined})).then(res=>{
          let blob = new Blob([res.data]); // 设置文件类型excel
          let url = URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement("a");
          a.href = url;
          a.download = '房源白名单列表.xlsx'
          a.click();
          // 释放这个临时的对象url
          URL.revokeObjectURL(url);
        })
      },
      handleReset() {
        this.searchModel = {
          activityIdNow: this.$router.actId
        };
        this.search();
      },
      changePageNum(val){
        this.page.pageNum;
        this.handleSearch(val, this.page.pageSize)
      },
      changePageSize(val){
        this.page.pageSize = val;
        this.handleSearch(this.page.pageNum, val);
      },
      search(){
        this.handleSearch(1, this.page.pageSize)
      },
      refresh(){
        this.handleSearch(this.page.pageNum, this.page.pageSize)
      },
      handleSearch(pageNum, pageSize) {
        this.loading = true;
        this.searchModel.pageNum = pageNum || 1;
        this.searchModel.pageSize = pageSize || 20;
        this.searchModel.activityId = this.$router.actId
        this.searchData = {};
          getHouseWhiteList(Object.assign(this.searchModel,{activityIdNow:undefined}))
          .then(res => {
            if (res.code == 200) {
              this.searchData = res.data;
              this.page.total = res.data.total;
            }
            this.loading = false;
          })
          .catch(err => {
            this.loading = false;
          });
      },
    },
    created() {
      this.handleSearch();
      getBuilding({ activityIdNow: this.$router.actId }).then(res => {
        if (res.code == 200) this.buildingList = res.data;
      });
      getRoomType({ activityId: this.$router.actId }).then(res => {
        if (res.code == 200) this.productTypeList = res.data;
      });
    }
  };
</script>

<style lang="scss" scoped>
</style>
