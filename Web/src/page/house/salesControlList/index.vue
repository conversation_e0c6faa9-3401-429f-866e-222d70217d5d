<template>
  <div v-loading.fullscreen.lock="loadingAll">
    <th-search>
      <th-trem title="楼栋">
        <el-select v-model="searchModel.buildingName" filterable placeholder="请选择">
          <el-option v-for="item in buildingList" :key="item.buildingName" :label="item.buildingName" :value="item.buildingName">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="单元">
        <el-select v-model="searchModel.unitName" :disabled="searchModel.buildingName?false:true" filterable placeholder="请选择">
          <el-option v-for="item in floorList" :key="item.unitName" :label="item.unitName" :value="item.unitName">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="楼层">
        <el-select v-model="searchModel.currentFloor" :disabled="searchModel.unitName?false:true" filterable placeholder="请选择">
          <el-option v-for="item in cellList" :key="item.currentFloor" :label="item.currentFloor" :value="item.currentFloor">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="房间号">
        <el-input v-model="searchModel.roomNum"></el-input>
      </th-trem>
      <th-trem title="户型名称">
        <el-input v-model="searchModel.hourseType"></el-input>
      </th-trem>
      <th-trem title="户型结构">
        <el-input v-model="searchModel.roomStru"></el-input>
      </th-trem>
      <th-trem title="房源名称">
        <el-input v-model="searchModel.houseName"></el-input>
      </th-trem>
      <th-trem title="产品类型">
        <el-select v-model="searchModel.roomType" placeholder="请选择">
          <el-option v-for="item in productTypeList" :key="item.id" :label="item.roomType" :value="item.roomType">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="是否销控">
        <el-select v-model="searchModel.saleControlFlag" placeholder="请选择">
          <el-option label="是" value="y"></el-option>
          <el-option label="否" value="n"></el-option>
        </el-select>
      </th-trem>
      <th-trem title="已成单">
                <el-select v-model="searchModel.orderControlFlag" placeholder="请选择">
                  <el-option label="是" value="y"></el-option>
                  <el-option label="否" value="n"></el-option>
                </el-select>
              </th-trem>
      <template v-slot:search-btn>
        <th-button v-has="'salesControl_01'" @click="search">查询</th-button>
        <th-button v-has="'salesControl_02'" @click="handleReset">重置</th-button>
      </template>
    </th-search>
    <th-panel title="项目列表">
      <template v-slot:th-panel-header>
        <th-button v-has="'salesControl_03'" @click="handleControl(null,'y')">销控</th-button>
        <th-button v-has="'salesControl_04'" @click="handleControl(null,'n')">取消销控</th-button>
      </template>
      <el-table
        ref="SourceListRef"
        :data="searchData.records"
        stripe
        v-loading="loading"
        row-key="id"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" :reserve-selection="true" width="55" />
        <el-table-column type="index" label="序号" width="55" />
        <el-table-column prop="buildingName" label="楼栋" width="70" show-overflow-tooltip/>
        <el-table-column prop="unitName" label="单元" width="70"/>
        <el-table-column prop="currentFloor" label="楼层" width="55"/>
        <el-table-column prop="roomNum" label="房间号" width="70"/>
        <el-table-column prop="houseName" label="房源名称" show-overflow-tooltip />
        <el-table-column prop="houserArea" label="房源面积(m²)" width="120" show-overflow-tooltip/>
        <el-table-column prop="totalPrice" label="房源总价(元)" width="120" show-overflow-tooltip/>
        <el-table-column prop="unitPrice" label="房源单价(元)" width="120" show-overflow-tooltip/>
        <el-table-column prop="discountUnitPrice" label="优惠单价(元)" width="120" show-overflow-tooltip/>
        <el-table-column prop="discountTotalPrice" label="优惠总价(元)" width="120" show-overflow-tooltip/>
        <el-table-column prop="roomType" label="产品类型" show-overflow-tooltip/>
        <el-table-column prop="hourseType" label="户型名称" width="120" show-overflow-tooltip/>
        <el-table-column prop="roomStru" label="户型结构" width="120" show-overflow-tooltip/>
        <el-table-column prop="saleControlFlag" label="是否销控" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{scope.row.saleControlFlag=='y'?'是':scope.row.saleControlFlag=='n'?'否':''}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="saleControlUserName" label="销控人" show-overflow-tooltip/>
        <el-table-column prop="saleControlDate" label="销控时间" width="120" show-overflow-tooltip/>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <th-button @click="handleControl(scope.row)">
              <span>{{scope.row.saleControlFlag=='y'?'取消销控':'销控'}}</span>
            </th-button>
          </template>
        </el-table-column>
      </el-table>
      <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
    </th-panel>
  </div>
</template>

<script >
  import {
    getBuilding,
    getFloor,
    getCell,
    getRoomType,
    getHouseList,
    updateSaleControl
  } from "@/api/house.js";
  export default {
    data() {
      return {
        searchModel: {
          activityIdNow: this.$router.actId,
        },
        productTypeList: [],
        buildingList: [],
        floorList: [],
        cellList: [],
        searchData: {},
        loading: false,
        loadingAll: false,
        page:{
          total:0,
          pageNum:1,
          pageSize:20
        },
        selectedKeys: [],
      };
    },
    watch: {
      "searchModel.buildingName"(n) {
        this.$set(this.searchModel, "unitName", "");
        this.$set(this.searchModel, "currentFloor", "");
        getFloor({
          activityIdNow: this.$router.actId,
          buildingName: n
        }).then(res => {
          if (res.code == 200) this.floorList = res.data;
        });
      },
      "searchModel.unitName"(n) {
        if (n) {
          this.$set(this.searchModel, "currentFloor", "");
          getCell({
            activityIdNow: this.$router.actId,
            buildingName: this.searchModel.buildingName,
            unitName: n
          }).then(res => {
            if (res.code == 200) this.cellList = res.data;
          });
        }
      }
    },
    methods: {
      handleSelectionChange(val) {
        this.selectedKeys = val;
      },
      handleControl(row,flag){
        if(!row&&this.selectedKeys.length < 1) return this.$message.warning('请选择操作数据')
        const ids = this.selectedKeys.map(k=>k.id).join(',')
        updateSaleControl({
          activityId: this.$router.actId,
          saleControlFlag:row?row.saleControlFlag=='y'?'n':'y':flag,
          ids: row?row.id:ids
        }).then(res=>{
          if (res.code == 200) {
            this.$message.success('操作成功')
            this.search()
          }
        })
      },
      handleReset() {
        this.searchModel = {
          activityIdNow: this.$router.actId
        };
        this.search();
      },
      changePageNum(val){
        this.page.pageNum;
        this.handleSearch(val, this.page.pageSize)
      },
      changePageSize(val){
        this.page.pageSize = val;
        this.handleSearch(this.page.pageNum, val);
      },
      search(){
        this.handleSearch(1, this.page.pageSize)
      },
      refresh(){
        this.handleSearch(this.page.pageNum, this.page.pageSize)
      },
      handleSearch(pageNum, pageSize) {
        this.loading = true;
        this.searchModel.pageNum = pageNum || 1;
        this.searchModel.pageSize = pageSize || 20;
        this.searchData = {};
          getHouseList(this.searchModel)
          .then(res => {
            if (res.code == 200) {
              this.searchData = res.data;
              this.page.total = res.data.total;
            }
            this.$refs.SourceListRef.clearSelection()
            this.loading = false;
          })
          .catch(err => {
            this.loading = false;
          });
      },
    },
    created() {
      this.handleSearch();
      getBuilding({ activityIdNow: this.$router.actId }).then(res => {
        if (res.code == 200) this.buildingList = res.data;
      });
      getRoomType({ activityId: this.$router.actId }).then(res => {
        if (res.code == 200) this.productTypeList = res.data;
      });
    }
  };
</script>

<style lang="scss" scoped>
</style>
