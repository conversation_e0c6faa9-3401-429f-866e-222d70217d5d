<template>
  <div v-loading.fullscreen.lock="loading">
    <th-panel title="房源信息">
      <template v-slot:th-panel-header>
        <th-button @click="handleUpdate">保存</th-button>
        <th-button @click="routerBack">返回</th-button>
      </template>
      <el-form :model="sourceData" label-width="120px" :rules="rules">
        <el-form-item label="楼栋" prop="buildingName" required>
          <el-input v-model="sourceData.buildingName" placeholder="楼栋"></el-input>
        </el-form-item>
        <el-form-item label="单元" prop="unitName" required>
          <el-input v-model="sourceData.unitName" placeholder="单元"></el-input>
        </el-form-item>
        <el-form-item label="楼层" prop="currentFloor" required>
          <el-input v-model="sourceData.currentFloor" placeholder="楼层"></el-input>
        </el-form-item>
        <el-form-item label="房间号" prop="roomNum" required>
          <el-input v-model="sourceData.roomNum" placeholder="房间号"></el-input>
        </el-form-item>
        <el-form-item label="房源面积(㎡)" prop="houserArea" required>
          <el-input v-model="sourceData.houserArea" placeholder="房源面积"></el-input>
        </el-form-item>
        <el-form-item label="房源总价(元)" prop="totalPrice" required>
          <el-input v-model="sourceData.totalPrice" placeholder="房源总价"></el-input>
        </el-form-item>
        <el-form-item label="房源单价(元)" prop="unitPrice" required>
          <el-input v-model="sourceData.unitPrice" placeholder="房源单价"></el-input>
        </el-form-item>
        <el-form-item label="优惠总价(元)" prop="discountTotalPrice" required>
          <el-input v-model="sourceData.discountTotalPrice" placeholder="优惠总价"></el-input>
        </el-form-item>
        <el-form-item label="优惠单价(元)" prop="discountUnitPrice" required>
          <el-input v-model="sourceData.discountUnitPrice" placeholder="优惠单价"></el-input>
        </el-form-item>
        <el-form-item label="户型名称" prop="hourseType" required>
          <el-input v-model="sourceData.hourseType" placeholder="户型名称"></el-input>
        </el-form-item>
        <el-form-item label="户型结构" prop="roomStru" required>
          <el-input v-model="sourceData.roomStru" placeholder="户型结构"></el-input>
        </el-form-item>
        <el-form-item label="产品类型" prop="roomType" required>
          <el-input v-model="sourceData.roomType" placeholder="产品类型"></el-input>
        </el-form-item>
        <el-form-item label="房源名称" prop="houseName" required>
          <el-input v-model="sourceData.houseName" placeholder="房源名称"></el-input>
        </el-form-item>
        <el-form-item label="模拟批次名称" prop="batchNameSimulation" required>
          <el-input v-model="sourceData.batchNameSimulation" placeholder="模拟批次名称"></el-input>
        </el-form-item>
        <el-form-item label="正式批次名称" prop="batchNameFormal" required>
          <el-input v-model="sourceData.batchNameFormal" placeholder="正式批次名称"></el-input>
        </el-form-item>
      </el-form>
    </th-panel>
  </div>
</template>

<script>
import {
  updateHouseShow,
  newHouse,
  updateHouse,
  getRoomType
} from "@/api/house.js";
export default {
  data() {
    return {
      loading: false,
      sourceData: {},
      rules: {}
    };
  },
  watch: {
    $route: "handleGet"
  },
  methods: {
    handleGet() {
      if (this.$route.query.id)
        updateHouseShow({ id: this.$route.query.id }).then(res => {
          if (res.code == 200) this.sourceData = res.data;
        });
    },
    routerBack() {
      this.$router.push({ name: "source" });
    },
    handleUpdate() {
      if (!this.sourceData.buildingName) return this.$message.warning('请输入楼栋')
      if (!this.sourceData.unitName) return this.$message.warning('请输入单元')
      if (!this.sourceData.currentFloor) return this.$message.warning('请输入楼层')
      if (!this.sourceData.roomNum) return this.$message.warning('请输入房间号')
      if (!this.sourceData.houserArea) return this.$message.warning('请输入房源面积')
      if (!this.sourceData.totalPrice) return this.$message.warning('请输入房源总价')
      if (!this.sourceData.unitPrice) return this.$message.warning('请输入房源单价')
      if (!this.sourceData.discountTotalPrice) return this.$message.warning('请输入优惠总价')
      if (!this.sourceData.discountUnitPrice) return this.$message.warning('请输入优惠单价')
      if (!this.sourceData.hourseType) return this.$message.warning('请输入户型名称')
      if (!this.sourceData.roomStru) return this.$message.warning('请输入户型结构')
      if (!this.sourceData.roomType) return this.$message.warning('请输入产品类型')
      if (!this.sourceData.houseName) return this.$message.warning('请输入房源名称')
      this.loading = true;
      if (this.sourceData.id) {
        updateHouse(this.sourceData).then(res => {
          this.loading = false;
          if (res.code == 200) {
            this.$router.push({
              path: "/house/sourceInfo",
              query: { id: this.sourceData.id }
            });
            this.$message({ type: "success", message: "保存成功" });
          } else this.$message({ type: "error", message: res.msg });
        });
      } else {
        this.sourceData.activityId = this.$router.actId;
        newHouse(this.sourceData).then(res => {
          this.loading = false;
          if (res.code == 200) {
            this.$router.push({
              path: "/house/sourceInfo",
              query: { id: res.data.id }
            });
            this.$message({ type: "success", message: "保存成功" });
          } else this.$message({ type: "error", message: res.msg });
        });
      }
    }
  },
  created() {
    this.handleGet();
  }
};
</script>

<style lang="scss" scoped>
.el-form {
  padding: 20px;
}
.el-input {
  max-width: 300px;
}
.select-user {
  display: flex;
  justify-content: space-between;

  .select-user__panel {
    flex: 1;
    padding: 5px;
  }
  .select-user__panel > div:nth-child(2) {
    height: calc(50vh - 45px);
    margin-top: 5px;
    overflow: auto;
  }
}
</style>
