<template>
  <div>
    <th-search>
      <th-trem title="楼栋">
        <el-select v-model="searchData.buildingName" placeholder="请选择" @change="getChange('1')">
          <el-option v-for="item in buildingList" :key="item.buildingName" :label="item.buildingName" :value="item.buildingName">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="单元">
        <el-select v-model="searchData.unitName" placeholder="请选择" @change="getChange('2')">
          <el-option v-for="item in floorList" :key="item.unitName" :label="item.unitName" :value="item.unitName">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="楼层">
        <el-select v-model="searchData.unitFloorNumber" placeholder="请选择" @change="getChange('3')">
          <el-option v-for="item in cellList" :key="item.unitFloorNumber" :label="item.unitFloorNumber" :value="item.unitFloorNumber">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="房间号">
        <el-input v-model="searchData.roomNum"></el-input>
      </th-trem>
      <!-- <th-trem title="房源面积范围">
                <el-input v-model="searchData."></el-input>
            </th-trem>
            <th-trem title="房源总价范围">
                <el-input v-model="searchData."></el-input>
            </th-trem>
            <th-trem title="房源单价范围">
                <el-input v-model="searchData."></el-input>
            </th-trem> -->
      <th-trem title="户型名称">
        <el-input v-model="searchData.hourseType"></el-input>
      </th-trem>
      <th-trem title="户型结构">
        <el-input v-model="searchData.roomStru"></el-input>
      </th-trem>
      <th-trem title="房源名称">
        <el-input v-model="searchData.houseName"></el-input>
      </th-trem>
      <th-trem title="产品类型">
        <el-select v-model="searchData.roomType" placeholder="请选择">
          <el-option v-for="item in productTypeList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </th-trem>

      <template v-slot:search-btn>
        <th-button v-has="'source_01'" @click="search">查询</th-button>
        <th-button v-has="'source_02'" @click="reset">重置</th-button>
      </template>
    </th-search>
    <th-panel title="房源信息">
      <template v-slot:th-panel-header>
        <th-button v-has="'source_03'" @click="setDownLoadExcel">下载模板</th-button>
        <th-button v-has="'source_04'" @click="setImportPort">批量导入</th-button>
        <th-button v-has="'source_06'" @click="handleSourceInfo()">新增房源</th-button>
        <th-button v-has="'source_08'" @click="setDelResources">删除房源</th-button>
      </template>
      <el-table :data="tableData" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" />
        <el-table-column prop="buildingName" label="楼栋" />
        <el-table-column prop="unitName" label="单元" />
        <el-table-column prop="unitFloorNumber" label="楼层" />
        <el-table-column prop="roomNum" label="房间号" />
        <el-table-column prop="houseName" label="房源名称" show-overflow-tooltip />
        <el-table-column prop="houserArea" label="房源面积(m²)" />
        <el-table-column prop="totalPrice" label="房源总价(元)" />
        <el-table-column prop="unitPrice" label="房源单价(元)" />
        <el-table-column prop="roomType" label="产品类型" />
        <el-table-column prop="hourseType" label="户型名称" />
        <el-table-column prop="roomStru" label="户型结构" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <th-button @click="handleSourceInfo(scope.row.id)">修改</th-button>
          </template>
        </el-table-column>
      </el-table>
      <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
      <!-- <wcg-page :total="total" :floorList="tableData || []" @on-change="getHouseListPort"></wcg-page> -->
    </th-panel>
  </div>
</template>

<script>
import {
  getBuilding,
  getFloor,
  getCell,
  getRoomType,
  getHouseList,
  setImport,
  downLoadExcel,
  delResources
} from "@/api/house.js";
import axios from "@/axios";
import qs from "qs";
export default {
  data() {
    return {
      searchData: {},
      options: [],
      tableData: [],
      buildingList: [],
      floorList: [],
      cellList: [],
      productTypeList: [],
      selectedHouse: [],
      total: 0,
      page:{
        pageNum:1,
        pageSize:10,
        total:0
      }
    };
  },
  methods: {
    handleSourceInfo(id) {
      this.$router.push({
        path: "/house/sourceInfo",
        query: { id: id }
      });
    },
    getChange(index) {
      switch (index) {
        case "1":
          this.getFloorPort();
          break;
        case "2":
          this.getCellPort();
          break;
      }
    },
    reset() {
      this.searchData = Object.assign({}, {});
      this.search();
    },
    handleSelectionChange(val) {
      //获取复选框值
      console.log(val);
      this.selectedHouse = val;
    },
    getBuildingPort() {
      //楼栋
      getBuilding({ activityIdNow: "1" })
        .then(res => {
          console.log(res);
          if (res.code === 200) {
            this.buildingList = res.data;
          }
        })
        .catch(error => {});
    },
    getFloorPort() {
      //单元
      getFloor({
        activityIdNow: "1",
        buildingName: this.searchData.buildingName
      })
        .then(res => {
          console.log(res);
          if (res.code === 200) {
            this.floorList = res.data;
          }
        })
        .catch(error => {});
    },
    getCellPort() {
      //楼层
      getCell({
        activityIdNow: "1",
        buildingName: this.searchData.buildingName,
        unitName: this.searchData.unitName
      })
        .then(res => {
          console.log(res);
          if (res.code === 200) {
            this.cellList = res.data;
          }
        })
        .catch(error => {});
    },
    getRoomPort() {
      //产品类型
      getRoomType({
        activityId: "1",
        buildingName: this.searchData.buildingName,
        unitName: this.searchData.unitName,
        unitFloorNumber: this.searchData.unitFloorNumber
      })
        .then(res => {
          console.log(res);
          if (res.code === 200) {
            this.productTypeList = res.data.records;
          }
        })
        .catch(error => {});
    },
    getHouseListPort(pageNum, pageSize) {
      //房屋列表
      this.searchData.activityIdNow = "1";
      this.searchData.pageNum = pageNum || 1;
      this.searchData.pageSize = pageSize || 20;
      getHouseList(this.searchData)
        .then(res => {
          console.log(res);
          if (res.code === 200) {
            this.tableData = res.data.records;
            this.page.total = res.data.total;
          }
        })
        .catch(error => {});
    },
    changePageNum(val){
      this.page.pageNum = val;
      this.getHouseListPort(val,this.page.pageSize);
    },
    changePageSize(val){
      this.page.pageSize = val;
      this.getHouseListPort(1,val);
    },
    refresh(){
      this.getHouseListPort(this.page.pageNum,this.page.pageSize);
    },
    search(){
      this.getHouseListPort(1,this.page.pageSize);
    },
    setImportPort() {
      //批量导入
      let that = this;
      this.$confirm('是否导入数据,导入会覆盖当前已有信息,确认打开文件选择页面', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center:true
      }).then(() => {
        setImport(data)
        .then(res => {
          console.log(res);
          if (res.code === 200) {
          }
        })
        .catch(error => {});
      }).catch(() => {
                
      });
    },
    setDownLoadExcel() {
      //下载模板
      window.location.href = axios.api + "/opHousingResources/downLoadExcel";
    },
    setDelResources() {
      //删除房源
      // let data = { idList:arr }
      if(this.selectedHouse.length > 0){
        this.$confirm('确认删除选中的房源?','温馨提示',{center:true}).then(() => {
          let arr = [];
          for (let i in this.selectedHouse) {
            arr.push(this.selectedHouse[i].id);
          }
          delResources(arr).then(res => {
            if (res.code === 200) {
              this.$message.success("删除成功!");
              this.getHouseListPort(1,this.page.pageSize);
            }else{
              this.$message.error(res.msg);
            }
          }).catch(error => {

          });
        }).catch(() => {
          return;
        })
      }else{
        this.$alert('当前未选中任何房源，请先选中需要删除的房源!','温馨提示 ',{center:true})
      }
    }
  },
  mounted() {
    this.getBuildingPort();
    this.getHouseListPort(this.page.pageNum,this.page.pageSize);
  }
};
</script>

<style lang="scss" scoped>
</style>
