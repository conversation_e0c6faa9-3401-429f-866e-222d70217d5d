<template>
  <div v-loading.fullscreen.lock="loadingAll">
    <th-search>
      <th-trem title="楼栋">
        <el-select v-model="searchModel.buildingName" filterable placeholder="请选择">
          <el-option v-for="item in buildingList" :key="item.buildingName" :label="item.buildingName" :value="item.buildingName">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="单元">
        <el-select v-model="searchModel.unitName" :disabled="searchModel.buildingName?false:true" filterable placeholder="请选择">
          <el-option v-for="item in floorList" :key="item.unitName" :label="item.unitName" :value="item.unitName">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="楼层">
        <el-select v-model="searchModel.currentFloor" :disabled="searchModel.unitName?false:true" filterable placeholder="请选择">
          <el-option v-for="item in cellList" :key="item.currentFloor" :label="item.currentFloor" :value="item.currentFloor">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="房间号">
        <el-input v-model="searchModel.roomNum"></el-input>
      </th-trem>
      <!-- <th-trem title="房源面积范围">
                <el-input v-model="searchModel."></el-input>
            </th-trem>
            <th-trem title="房源总价范围">
                <el-input v-model="searchModel."></el-input>
            </th-trem>
            <th-trem title="房源单价范围">
                <el-input v-model="searchModel."></el-input>
            </th-trem> -->
      <th-trem title="户型名称">
        <el-input v-model="searchModel.hourseType"></el-input>
      </th-trem>
      <th-trem title="户型结构">
        <el-input v-model="searchModel.roomStru"></el-input>
      </th-trem>
      <th-trem title="房源名称">
        <el-input v-model="searchModel.houseName"></el-input>
      </th-trem>
      <th-trem title="产品类型">
        <el-select v-model="searchModel.roomType" placeholder="请选择">
          <el-option v-for="item in productTypeList" :key="item.id" :label="item.roomType" :value="item.roomType">
          </el-option>
        </el-select>
      </th-trem>

      <template v-slot:search-btn>
        <th-button v-has="'source_01'" @click="search">查询</th-button>
        <th-button v-has="'source_02'" @click="handleReset">重置</th-button>
      </template>
    </th-search>
    <th-panel title="项目列表">
      <template v-slot:th-panel-header>
        <th-button v-has="'source_03'" @click="handleSourceDownload"
          >下载模板</th-button
        >
        <th-button v-has="'source_04'" @click="handleSourceImport()"
          >批量导入</th-button
        >
        <th-button v-has="'source_09'" @click="handleSourceExport()"
          >导出</th-button
        >
        <input
          ref="uploadFile"
          type="file"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          name="file"
          style="display:none;"
          @change="fileChange($event)"
        />
        <th-button v-has="'source_06'" @click="handleHtInfo()"
          >新增房源</th-button
        >
        <th-button v-has="'source_08'" @click="handleHtDel">删除房源</th-button>
      </template>
      <el-table
        ref="SourceListRef"
        :data="searchData.records"
        stripe
        v-loading="loading"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="55" />
        <el-table-column
          prop="buildingName"
          label="楼栋"
          width="70"
          show-overflow-tooltip
        />
        <el-table-column prop="unitName" label="单元" width="70" />
        <el-table-column prop="currentFloor" label="楼层" width="55" />
        <el-table-column prop="roomNum" label="房间号" width="70" />
        <el-table-column
          prop="houseName"
          label="房源名称"
          show-overflow-tooltip
          width="120"
        />
        <el-table-column
          prop="houserArea"
          label="房源面积(m²)"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="totalPrice"
          label="房源总价(元)"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="unitPrice"
          label="房源单价(元)"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="discountUnitPrice"
          label="优惠单价(元)"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="discountTotalPrice"
          label="优惠总价(元)"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="roomType"
          label="产品类型"
          show-overflow-tooltip
        />
        <el-table-column
          prop="hourseType"
          label="户型名称"
          show-overflow-tooltip
        />
        <el-table-column
          prop="roomStru"
          label="户型结构"
          show-overflow-tooltip
        />
        <el-table-column
          prop="batchNameSimulation"
          label="模拟批次名称"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="batchNameFormal"
          label="正式批次名称"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <th-button @click="handleHtInfo(scope.row.id)">修改</th-button>
          </template>
        </el-table-column>
      </el-table>
      <th-page
        :total="page.total"
        :pageNum="page.pageNum"
        :pageSize="page.pageSize"
        @currentPageNum="changePageNum"
        @currentPageSize="changePageSize"
        @refreshEvent="refresh"
      />
      <!-- <wcg-page :total="searchData.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
    </th-panel>
  </div>
</template>

<script>
import {
  getBuilding,
  getFloor,
  getCell,
  getRoomType,
  getHouseList,
  setImport,
  downLoadExcel,
  exportHourseResources,
  delResources
} from "@/api/house.js";
export default {
  data() {
    return {
      searchModel: {
        activityIdNow: this.$router.actId
      },
      productTypeList: [],
      buildingList: [],
      floorList: [],
      cellList: [],
      searchData: {},
      loading: false,
      loadingAll: false,
      page:{
          total:0,
          pageNum:1,
          pageSize:20
      }
    };
  },
  watch: {
    "searchModel.buildingName"(n) {
      this.$set(this.searchModel, "unitName", "");
      this.$set(this.searchModel, "currentFloor", "");
      getFloor({
        activityIdNow: this.$router.actId,
        buildingName: n
      }).then(res => {
        if (res.code == 200) this.floorList = res.data;
      });
    },
    "searchModel.unitName"(n) {
      if (n) {
        this.$set(this.searchModel, "currentFloor", "");
        getCell({
          activityIdNow: this.$router.actId,
          buildingName: this.searchModel.buildingName,
          unitName: n
        }).then(res => {
          if (res.code == 200) this.cellList = res.data;
        });
      }
    }
  },
  methods: {
    handleSourceImport() {
      let that = this;
      this.$confirm('是否导入数据,导入会覆盖当前已有信息,确认打开文件选择页面', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center:true
      }).then(() => {
        that.$refs.uploadFile.click();
      }).catch(() => {

      });
    },
    handleSourceExport() {
      // 配置房源 导出功能
      this.loading = true;
      delete this.searchModel.pageNum;
      delete this.searchModel.pageSize;
      exportHourseResources(this.searchModel).then(res=>{
        let blob = new Blob([res.data]); // 设置文件类型excel
        let url = URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
        // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement("a");
        a.href = url;
        a.download = '配置房源列表.xlsx'
        a.click();
        // 释放这个临时的对象url
        URL.revokeObjectURL(url);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
    },
    fileChange(e) {
      this.loadingAll = true;
      let files = this.$refs.uploadFile.files;
      if (files && files.length > 0) {
        let formData = new FormData();
        formData.append("file", files[0]);
        formData.append("activityIdNow", this.$router.actId);
        setImport(formData)
          .then(res => {
            if(res.code == 200){
              this.loadingAll = false;
              this.$refs.uploadFile.value = "";
              this.$message({ type: "success", message: res.msg });
              this.handleSearch();
            }else{
              this.loadingAll = false;
              this.$refs.uploadFile.value = "";
              this.$message({ type: "error", message: res.msg });
            }
          })
          .catch(err => {
            this.loadingAll = false;
            this.$refs.uploadFile.value = "";
          });
      }
    },
    handleSourceDownload() {
      // 导出模板
      this.loading = true;
      downLoadExcel().then(res => {
        let blob = new Blob([res.data]); // 设置文件类型excel
        let url = URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
        // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement("a");
        a.href = url;
        a.download = '房源导入表.xlsx'
        a.click();
        // 释放这个临时的对象url
        URL.revokeObjectURL(url);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
    },
    handleReset() {
      this.searchModel = {
        activityIdNow: this.$router.actId
      };
      this.search();
    },
    changePageNum(val){
      this.page.pageNum;
      this.handleSearch(val, this.page.pageSize)
    },
    changePageSize(val){
      this.page.pageSize = val;
      this.handleSearch(this.page.pageNum, val);
    },
    search(){
      this.handleSearch(1, this.page.pageSize)
    },
    refresh(){
      this.handleSearch(this.page.pageNum, this.page.pageSize)
    },
    handleSearch(pageNum, pageSize) {
      this.loading = true;
      this.searchModel.pageNum = pageNum || 1;
      this.searchModel.pageSize = pageSize || 20;
      this.searchData = {};
      getHouseList(this.searchModel)
        .then(res => {
          if (res.code == 200) {
            this.searchData = res.data;
            this.page.total = res.data.total;
          }
          this.loading = false;
        })
        .catch(err => {
          this.loading = false;
        });
    },
    handleHtInfo(id) {
      this.$router.push({
        path: "/house/sourceInfo",
        query: { id: id }
      });
    },
    handleHtDel() {
      let _l = this.$refs.SourceListRef.store.states.selection;
      if (_l && _l.length > 0) {
        this.$confirm('确认删除选中房源?','温馨提示',{
          center:true
        }).then(() => {
          let idList = [];
          _l.forEach(e => {
            idList.push(e.id);
          });
          delResources(idList).then(res => {
            if (res.code == 200) {
              this.handleSearch();
              this.$message({ type: "success", message: "删除成功" });
            } else this.$message({ type: "error", message: res.msg });
          });
        }).catch(() => {

        })
      }else{
        this.$alert('当前未选中任何房源，请先勾选需要删除的房源!','温馨提示',{center:true})
      }
    }
  },
  created() {
    this.handleSearch();
    getBuilding({ activityIdNow: this.$router.actId }).then(res => {
      if (res.code == 200) this.buildingList = res.data;
    });
    getRoomType({ activityId: this.$router.actId }).then(res => {
      if (res.code == 200) this.productTypeList = res.data;
    });
  }
};
</script>

<style lang="scss" scoped>
</style>
