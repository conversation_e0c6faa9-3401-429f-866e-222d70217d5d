<template>
  <div class="dzkp-main">
    <!-- 头部 -->
    <div class="sale-header" id="homepage-header">
      <div class="header-left">
        <img src="@/assets/img/cdlogo.png" />
        <!--        <img src="@/assets/img/TahoeLogo.png" />-->
        <!-- <span>{{title[0] || '电子开盘管理系统'}}</span> -->
        <span>{{ '在线开盘管理系统' }}</span>
      </div>
      <div class="header-right">
        <div class="update-activity" v-if="actList.length > 0" v-show="isShowActivitySelector">
          <span>切换活动：</span>
          <el-select v-model="actId" filterable placeholder="请选择">
            <el-option v-for="item in actList" v-if="item" :key="item.activityId" :label="item.activityName"
              :value="item.activityId">
            </el-option>
          </el-select>
        </div>
        <h3>您好：
          <el-dropdown>
            <span class="el-dropdown-link">
              {{ $router.realName }}
              <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item icon="tahoe-icon icon-tuichu" @click.native="setCookieClear">退出系统</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </h3>
      </div>
    </div>
    <div class="sale-container">
      <!-- 菜单 -->
      <div class="sale-aside" @dblclick="isCollapse = !isCollapse">
        <h2 v-if="!isCollapse">在线开盘管理系统</h2>
        <sale-menu ref="saleMenuRef" :isCollapse="isCollapse"></sale-menu>
      </div>
      <!-- 页面 -->
      <div class="sale-main">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item v-for="t in title" :key="t">{{ t }}</el-breadcrumb-item>
        </el-breadcrumb>
        <router-view class="sale-main-page" id="mainPage" :key="actId" />
      </div>
    </div>

    <div class="clear"></div>
  </div>
</template>

<script>
import saleMenu from "@/components/menu";
import { busLogout } from '@/api/login'
export default {
  components: { saleMenu },
  data() {
    return {
      title: [],
      isCollapse: false,
      user: {},
      actId: this.$router.actId,
      actList: this.$router.actList,
      isShowActivitySelector: true
    };
  },
  watch: {
    $route: {
      handler: function (n) {
        this.getBreadcrumb(n.matched);
        if (n.path.indexOf('/sys/') == -1) {
          this.isShowActivitySelector = true;
        } else {
          this.isShowActivitySelector = false;
        }
      },
      deep: true
    },
    actId(v) {
      this.$router.actId = v;
      this.$router.options.setRoles();
    }
  },
  methods: {
    setCookieClear() {
      busLogout().then(res => {
        const { code } = res
        if (code === 200) {
          sessionStorage.removeItem('userInfo')
          sessionStorage.removeItem('busUserToken')
          this.$router.replace('/login')
        }
      })
      // if (location.hostname.indexOf("demo") > 0)
      //   window.location.href = 'http://ucsso.tzdc.com:9988/logout?sysId=WXKEFU&ReturnURL=' + location.href;
      // else
      //   window.location.href = 'http://ucsso.tzdc.com:9988/logout?sysId=WXKEFU&ReturnURL=' + location.href;
    },
    getBreadcrumb(n) {
      let d = JSON.parse(JSON.stringify(this.menus));
      let p = [];
      if (n && n.length > 0) {
        n.forEach(e => {
          if (e.name && d.length > 0) {
            let c = d.filter(i => i.name == e.name)[0];
            p.push(c.coment);
            if (c.children && c.children.length > 0) d = c.children;
          }
        });
      }
      this.title = p;
    }
  },
  created() {
    this.getBreadcrumb(this.$route.matched);
    if (this.$route.path.indexOf('/sys/') == -1) {
      this.isShowActivitySelector = true;
    } else {
      this.isShowActivitySelector = false;
    }
  },
  mounted() { }
};
</script>

<style scoped></style>
