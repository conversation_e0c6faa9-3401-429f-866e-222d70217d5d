<template>
    <div>
        <th-search>
            <th-trem title="置业顾问：">
                <el-input v-model="searchModel.roleName" placeholder="搜索置业顾问"></el-input>
            </th-trem>
            <template v-slot:search-btn>
                <th-button v-has="'adviserSale_01'" @click="search">查询</th-button>
                <th-button v-has="'adviserSale_02'" @click="handleReset">重置</th-button>
            </template>
        </th-search>
        <th-panel title="置业顾问列表">
            <template v-slot:th-panel-header>
                <th-button v-has="'adviserSale_03'" @click="handleDownload()">导出数据</th-button>
            </template>
            <el-table ref="CustListRef" :data="searchData.records" stripe v-loading="loading">
                <el-table-column label="序号" type="index" width="50" align="center" />
                <el-table-column label="置业顾问" prop="saleName" />
                <el-table-column label="成交销售额(元)" prop="salesVolume" />
                <el-table-column label="成交客户数(人)" prop="salesUserNum" />
                <el-table-column label="成交房源数(套)" prop="salesHouseNum" />
            </el-table>
            <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
            <!-- <wcg-page :total="searchData.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
        </th-panel>
        <div class="exchange-open-type">
            <el-select v-model="openDataType" placeholder="请选择开盘数据类型" class="reset-select-lineheight">
              <el-option key="formal" value="y" label="正式开盘数据"></el-option>
              <el-option key="simulation" value="n" label="模拟开盘数据"></el-option>
            </el-select>
        </div>
    </div>
</template>

<script >
import { saleData, downloadSaleData } from "@/api/sale";
export default {
  data() {
    return {
      searchModel: {},
      searchData: {},
      loading: false,
      openDataType:'y',
      page:{
        pageNum:1,
        pageSize:10,
        total:0
      }
    };
  },
  watch:{
    openDataType(){
      this.handleSearch(1,this.page.pageSize);
    }
  },
  methods: {
    handleReset() {
      this.searchModel = {};
      this.search()
    },
    handleDownload() {
      // let link = document.createElement("a");
      // link.style.display = "none";
      // link.href = downloadSaleData({ id: this.$router.actId, isRegular: this.openDataType, roleName:this.searchModel.roleName });
      // link.target = "_blank";
      // document.body.appendChild(link);
      // link.click();
      this.loading = true;
      downloadSaleData({  id: this.$router.actId, isRegular: this.openDataType, roleName:this.searchModel.roleName }).then(res=>{
          let blob = new Blob([res.data]); // 设置文件类型excel
          let url = URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement("a");
          a.href = url;
          a.download = '置业顾问销售数据导出信息.xlsx'
          a.click();
          // 释放这个临时的对象url
          URL.revokeObjectURL(url);
          this.loading = false;
          this.handleReset();
        }).catch(() => {
          this.loading = false;
        })
    },
    handleSearch(pageNum, pageSize) {
      this.loading = true;
      this.searchModel.pageNum = pageNum || 1;
      this.searchModel.pageSize = pageSize || 20;
      this.searchModel.activityIdNow = this.$router.actId;
      this.searchModel.isRegular = this.openDataType;
      this.searchData = {};
      saleData(this.searchModel)
        .then(res => {
          if (res.code == 200) {
            this.searchData = res.data;
            this.page.total = res.data.total;
            this.loading = false;
          }
        })
        .catch(err => {
          this.loading = false;
        });
    },
    changePageNum(val){
      this.page.pageNum = val;
      this.handleSearch(val,this.page.pageSize);
    },
    changePageSize(val){
      this.page.pageSize = val;
      this.handleSearch(1,val);
    },
    refresh(){
      this.handleSearch(this.page.pageNum,this.page.pageSize);
    },
    search(){
      this.handleSearch(1,this.page.pageSize);
    }
  },
  created() {
    this.handleSearch(this.page.pageNum,this.page.pageSize);
  }
};
</script>

<style lang="scss" scoped>
</style>
