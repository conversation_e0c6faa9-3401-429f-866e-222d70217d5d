<template>
    <div class="th-home-page">
        <th-panel noTitle>
            <table class="sale_overs">
                <tr>
                    <td>房源总数</td>
                    <td>{{salesData.totalNum}}套</td>
                </tr>
                <tr>
                    <td>已售总数</td>
                    <td>{{salesData.soldNum}}套</td>
                </tr>
                <tr>
                    <td>房源总面积</td>
                    <td>{{salesData.totalHouseResources}}㎡</td>
                </tr>
                <tr>
                    <td>已售总面积</td>
                    <td>{{salesData.soldHouseResources}}㎡</td>
                </tr>
                <tr>
                    <td>参与人数</td>
                    <td>{{salesData.joinNum}}人</td>
                </tr>
                <tr>
                    <td>购房人数</td>
                    <td>{{salesData.orderNum}}人</td>
                </tr>
                <tr>
                    <td>购房人数占比</td>
                    <td>{{salesData.percentage}}%</td>
                </tr>
                <tr>
                </tr>
            </table>
        </th-panel>
        <th-panel title="已售房源">
            <div class="echarts_pie" ref="soldRef"></div>
        </th-panel>
        <th-panel title="占比购房">
            <div class="echarts_pie" ref="orderRef"></div>
        </th-panel>
        <div class="exchange-open-type">
            <el-select v-model="openDataType" placeholder="请选择开盘数据类型" class="reset-select-lineheight">
              <el-option key="formal" value="y" label="正式开盘数据"></el-option>
              <el-option key="simulation" value="n" label="模拟开盘数据"></el-option>
            </el-select>
        </div>
    </div>
</template>

<script>
let echarts = require("echarts/lib/echarts");
// 引入柱状图组件
require("echarts/lib/chart/pie");
// 引入提示框和title组件
require("echarts/lib/component/tooltip");
require("echarts/lib/component/legend");

import { salesOverview } from "@/api/sale.js";
export default {
  data() {
    return {
      salesData: {},
      openDataType:'y'
    };
  },
  watch:{
    openDataType(){
      this.getSalesOverview()
    }
  },
  methods: {
    getSalesOverview() {
      if (this.$router.actId)
        salesOverview({ activityId: this.$router.actId, isRegular: this.openDataType }).then(res => {
          if (res.code == 200) {
            this.salesData = res.data;
            this.setEcharts(
              this.$refs.soldRef,
              "已售房源",
              "已售套数",
              "未售套数",
              res.data.soldNum,//res.data.soldHouseResources,
              res.data.notSaleNum,//res.data.totalHouseResources - res.data.soldHouseResources,
              '#97C7FF',
              '#E6E6E6'
            );
            this.setEcharts(
              this.$refs.orderRef,
              "占比购房",
              "参与人数",
              "购房人数",
              res.data.joinNum,
              res.data.orderNum,
              '#F4B63A',
              '#E6E6E6'
            );
          }
        });
    },
    setEcharts(r, t, t1, t2, d1, d2,c1,c2) {
      let myChart = echarts.init(r);
      myChart.setOption({
        tooltip: {
          trigger: "item",
          formatter: "{b}: {c} ({d}%)"
        },
        legend: {
          x: "center",
          y: "bottom",
          itemGap: 20,
          padding: 20,
          itemHeight:10,
          icon: "circle",
          data: [t1,t2]
        },
        series: [
          {
            name: t,
            type: "pie",
            radius: ["50%", "60%"],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: "center"
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: "30",
                  fontWeight: "bold"
                }
              }
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            data: [
              { value: d1, name: t1, itemStyle: { color: c1 } },
              { value: d2, name: t2, itemStyle: { color: c2 } }
            ]
          }
        ]
      });
    }
  },
  created() {
    this.getSalesOverview();
  }
};
</script>

<style lang="scss" scoped>
.th-home-page {
  font-size: 14px;
  display: flex;
  justify-content: space-between;

  .th-panel {
    flex: 1;
    height: 350px;
    min-width: 280px;
    line-height: 40px;
  }

  .th-panel:nth-child(2) {
    margin: 10px 10px 0 10px;
  }

  .sale_overs {
    width: 100%;
    text-align: right;
    margin-top: 20px;

    tr td {
      padding: 0 10px;
    }
    tr td:nth-child(2) {
      width: 65%;
      text-align: left;
    }
  }
  .echarts_pie {
    height: 300px;
    width: 100%;
  }
}
</style>
<style scoped>
.th-panel >>> .th-panel-content {
  padding: 5px 10px;
}
</style>

