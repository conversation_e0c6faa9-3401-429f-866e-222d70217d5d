<template>
    <div class="simulation">
        <th-search>
            <th-trem title="客户姓名：">
                <el-input v-model="searchModel.name" placeholder="搜索客户姓名"></el-input>
            </th-trem>
            <th-trem title="电话号码：">
                <el-input v-model="searchModel.tel" placeholder="搜索电话号码"></el-input>
            </th-trem>
            <th-trem title="置业顾问：">
                <el-input v-model="searchModel.saleName" placeholder="搜索置业顾问"></el-input>
            </th-trem>
            <th-trem title="楼栋：">
                <el-select v-model="searchModel.buildingName" filterable placeholder="请选择">
                    <el-option v-for="item in buildList" v-if="item" :key="item.buildingName" :label="item.buildingName" :value="item.buildingName">
                    </el-option>
                </el-select>
            </th-trem>
            <th-trem title="单元：">
                <el-select v-model="searchModel.unitName" :disabled="searchModel.buildingName?false:true" filterable placeholder="请选择">
                    <el-option v-for="item in floorList" v-if="item" :key="item.unitName" :label="item.unitName" :value="item.unitName">
                    </el-option>
                </el-select>
            </th-trem>
            <th-trem title="房间号：">
                <el-select v-model="searchModel.roomNum" :disabled="searchModel.unitName?false:true" filterable placeholder="请选择">
                    <el-option v-for="item in unitList" v-if="item" :key="item.roomNum" :label="item.roomNum" :value="item.roomNum">
                    </el-option>
                </el-select>
            </th-trem>
            <template v-slot:search-btn>
                <th-button v-has="'simulation_01'" @click="search">查询</th-button>
                <th-button v-has="'simulation_02'" @click="handleReset">重置</th-button>
            </template>
        </th-search>
        <th-panel title="订单列表">
            <template v-slot:th-panel-header>
                <th-button v-has="'simulation_03'" @click="handleDownload()">导出订单</th-button>
                <th-button v-has="'simulation_04'" @click="handleDel()">释放房源</th-button>
            </template>
            <el-table ref="orderListRef" :data="searchData.records" stripe v-loading="loading" tooltip-effect="dark">
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column label="序号" type="index" width="50" align="center" />
                <el-table-column label="客户姓名" prop="name" show-overflow-tooltip/>
                <el-table-column label="电话号码" prop="tel" show-overflow-tooltip/>
                <el-table-column label="身份证号" prop="idCard" show-overflow-tooltip/>
                <el-table-column label="楼栋号" prop="buildingName" show-overflow-tooltip/>
                <el-table-column label="单元号" prop="unitName" show-overflow-tooltip/>
                <el-table-column label="房间号" prop="roomNum" show-overflow-tooltip/>
                <el-table-column label="房源名称" prop="houseName" show-overflow-tooltip/>
                <el-table-column label="下单时间" prop="creationDate" show-overflow-tooltip/>
                <el-table-column label="客户/房源来源" prop="createdBy" show-overflow-tooltip/>
                <!-- <el-table-column label="预置订单" prop="isBeforehand" show-overflow-tooltip/> -->
                <el-table-column label="置业顾问" prop="saleName" show-overflow-tooltip/>
            </el-table>
            <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
            <!-- <wcg-page :total="searchData.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
        </th-panel>
    </div>
</template>

<script >
import { getBuilding, getFloor } from "@/api/house";
import {
  opOrderPageList,
  downLoadOrderExcel,
  deleteOrderN,
  roomNum
} from "@/api/sale";
export default {
    data() {
        return {
            searchModel: {},
            searchData: {},
            loading: false,
            buildList: [],
            floorList: [],
            unitList: [],
            page:{
                pageNum:1,
                pageSize:10,
                total:0
            }
        }
    },
    methods:{
        handleSearch(pageNum, pageSize) {
            this.loading = true;
            this.searchModel.pageNum = pageNum || 1;
            this.searchModel.pageSize = pageSize || 20;
            this.searchModel.activityIdNow = this.$router.actId;
            this.searchModel.isRegular = 'n';
            this.searchModel.isAdmin = this.$router.isAdmin;
            this.searchData = {};
            opOrderPageList(this.searchModel)
                .then(res => {
                    if (res.code == 200) {
                        this.searchData = res.data;
                        this.page.total = res.data.total;
                        this.loading = false;
                    }
                })
                    .catch(err => {
                    this.loading = false;
                });
        },
        changePageNum(val){
            this.page.pageNum = val;
            this.handleSearch(val,this.page.pageSize);
        },
        changePageSize(val){
            this.page.pageSize = val;
            this.handleSearch(1,val);
        },
        refresh(){
            this.handleSearch(this.page.pageNum,this.page.pageSize);
        },
        search(){
            this.handleSearch(1,this.page.pageSize);
        },
        handleDel() {
            let _l = this.$refs.orderListRef.store.states.selection;
            if (_l && _l.length > 0) {
                this.$confirm('确认释放选中房源?','温馨提示',{center:true}).then(() => {
                    let idList = [];
                    console.log(_l);
                    _l.forEach(e => {
                    idList.push(e.orderId);
                    });
                    deleteOrderN(idList).then(res => {
                        if (res.code == 200) {
                            this.handleSearch();
                            this.$message({ type: "success", message: "删除成功" });
                        } else {
                            this.$message({ type: "error", message: res.msg })
                        };
                    });
                }).catch(() => {
                    return;
                })
            }else{
                this.$alert('当前未选中任何房源，请先选择需要释放的房源!','问题提示',{center:true})
            }
        },
        handleReset() {
            this.searchModel = {};
            this.search();
        },
        handleDownload() {
            // let link = document.createElement("a");
            // link.style.display = "none";
            // let params = JSON.stringify(this.searchModel);
            // params = JSON.parse(params);
            // delete params.pageNum;
            // delete params.pageSize;
            // params.activityIdNow = this.$router.actId;
            // params.isRegular = 'n';
            // link.href = downLoadOrderExcel(params);
            // link.target = "_blank";
            // document.body.appendChild(link);
            // link.click();
            this.loading = true;
            downLoadOrderExcel(this.searchModel).then(res=>{
            let blob = new Blob([res.data]); // 设置文件类型excel
            let url = URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
            // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement("a");
            a.href = url;
            a.download = '订单列表导出信息.xlsx'
            a.click();
            // 释放这个临时的对象url
            URL.revokeObjectURL(url);
            this.loading = false;
            this.handleReset();
            }).catch(() => {
            this.loading = false;
            })
        },
    },
    created(){
        this.handleSearch(this.page.pageNum,this.page.pageSize);
        getBuilding({ activityIdNow: this.$router.actId }).then(res => {
            if (res.code == 200) this.buildList = res.data;
        });
    },
    watch: {
        "searchModel.buildingName"(n) {
        this.$set(this.searchModel, "unitName", "");
        this.$set(this.searchModel, "roomNum", "");
        getFloor({
            activityIdNow: this.$router.actId,
            buildingName: n
        }).then(res => {
            if (res.code == 200) this.floorList = res.data;
        });
        },
        "searchModel.unitName"(n) {
        if (n) {
            this.$set(this.searchModel, "roomNum", "");
            roomNum({
            activityIdNow: this.$router.actId,
            buildingName: this.searchModel.buildingName,
            unitName: n
            }).then(res => {
            if (res.code == 200) this.unitList = res.data;
            });
        }
        }
  },
};
</script>

<style lang="scss" scoped>
</style>