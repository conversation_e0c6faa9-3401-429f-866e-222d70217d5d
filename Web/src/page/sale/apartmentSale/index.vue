<template>
    <div>
        <th-panel title="户型列表">
            <template v-slot:th-panel-header>
                <th-button v-has="'apartmentSale_01'" @click="handleDownload()">导出数据</th-button>
            </template>
            <el-table ref="CustListRef" :data="searchData.records" stripe v-loading="loading">
                <el-table-column label="序号" type="index" width="50" align="center" />
                <el-table-column label="户型名称" prop="typeName" />
                <el-table-column label="成交销售额(元)" prop="salesVolume" />
                <el-table-column label="成交面积(㎡)" prop="salesArea" />
                <el-table-column label="成交房源数(套)" prop="salesNum" />
                <el-table-column label="去化率" prop="percentage" />
            </el-table>
            <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
            <!-- <wcg-page :total="searchData.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
        </th-panel>
        <div class="exchange-open-type">
            <el-select v-model="openDataType" placeholder="请选择开盘数据类型" class="reset-select-lineheight">
              <el-option key="formal" value="y" label="正式开盘数据"></el-option>
              <el-option key="simulation" value="n" label="模拟开盘数据"></el-option>
            </el-select>
        </div>
    </div>
</template>

<script >
import { salesHouseTypeList, downloadHouseTypeList } from "@/api/sale";
export default {
  data() {
    return {
      searchModel: {},
      searchData: {},
      loading: false,
      openDataType:'y',
      page:{
        pageNum:1,
        pageSize:10,
        total:0
      }
    };
  },
  watch:{
    openDataType(){
      this.handleSearch(1,this.page.pageSize);
    }
  },
  methods: {
    handleDownload() {
      // let link = document.createElement("a");
      // link.style.display = "none";
      // link.href = downloadHouseTypeList({ id: this.$router.actId, isRegular: this.openDataType });
      // link.target = "_blank";
      // document.body.appendChild(link);
      // link.click();
      this.loading = true;
      downloadHouseTypeList({ id: this.$router.actId, isRegular: this.openDataType }).then(res=>{
          let blob = new Blob([res.data]); // 设置文件类型excel
          let url = URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement("a");
          a.href = url;
          a.download = '户型列表导出信息.xlsx'
          a.click();
          // 释放这个临时的对象url
          URL.revokeObjectURL(url);
          this.loading = false;
        }).catch(() => {
          this.loading = false;
        })
    },
    handleSearch(pageNum, pageSize) {
      this.loading = true;
      this.searchModel.pageNum = pageNum || 1;
      this.searchModel.pageSize = pageSize || 20;
      this.searchModel.activityIdNow = this.$router.actId;
      this.searchModel.isRegular = this.openDataType;
      this.searchData = {};
      salesHouseTypeList(this.searchModel)
        .then(res => {
          if (res.code == 200) {
            this.searchData = res.data;
            this.page.total = res.data.total;
            this.loading = false;
          }
        })
        .catch(err => {
          this.loading = false;
        });
    },
    changePageNum(val){
      this.page.pageNum = val;
      this.handleSearch(val,this.page.pageSize);
    },
    changePageSize(val){
      this.page.pageSize = val;
      this.handleSearch(1,val);
    },
    refresh(){
      this.handleSearch(this.page.pageNum,this.page.pageSize);
    },
    search(){
      this.handleSearch(1,this.page.pageSize);
    }
  },
  created() {
    this.handleSearch(this.page.pageNum,this.page.pageSize);
  }
};
</script>

<style lang="scss" scoped>
</style>
