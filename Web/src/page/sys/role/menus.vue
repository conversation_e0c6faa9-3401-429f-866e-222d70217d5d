<template>

  <el-tree ref="menuRef" :data="treeData" show-checkbox node-key="id" :props="defaultProps" auto-expand-parent>
  </el-tree>
</template>

<script>
import { sysRoleGet, sysRoleSave } from "@/api/sys.js";
export default {
  props: {},
  data() {
    return {
      treeData: [],
      defaultProps: {
        label: "name",
        children: "children"
      },
      roleData: []
    };
  },
  watch: {},
  methods: {
    authoritySave() {
      let _a = this.$refs.menuRef
        .getCheckedKeys()
        .sort()
        .join(",");
      this.roleData.authority = _a;
      sysRoleSave(this.roleData).then(res => {
        if (res.code == 200) {
          this.$emit("update:isVisible", false);
          this.$message({
            type: "success",
            message: "授权成功"
          });
        } else {
          this.$message({
            type: "error",
            message: res.msg
          });
        }
      });
    },
    sysRoleGet(id) {
      if (id) {
        this.$refs.menuRef.setCheckedKeys([]);
        sysRoleGet({ roleId: id }).then(res => {
          if (res.code == 200) {
            this.roleData = res.data;
            this.$refs.menuRef.setCheckedKeys(
              this.roleData.authority ? this.roleData.authority.split(",") : []
            );
          }
        });
      }
    },
    // 初始化数据
    setInit() {
      this.treeData = [];
      this.menus.forEach((e, i) => {
        if (e.isAdmin) return;
        let c = {
          id: i + 100 + 1,
          name: e.coment
        };
        if (e.children) {
          let ec = [];
          e.children.forEach((ee, ei) => {
            let cc = {
              id: c.id * 100 + ei + 1,
              name: ee.coment
            };
            if (ee.buttons) {
              let eec = [];
              ee.buttons.forEach((eee, eei) => {
                let ccb = {
                  id: cc.id * 100 + eei + 1,
                  name: eee.name
                };
                eec.push(ccb);
              });
              cc.children = eec;
            }
            ec.push(cc);
          });
          c.children = ec;
        }
        if (e.buttons) {
          let eb = [];
          e.buttons.forEach((ee, ei) => {
            let cb = {
              id: c.id * 100 + ei + 1,
              name: ee.name
            };
            eb.push(cb);
          });
          c.children = eb;
        }
        this.treeData.push(c);
      });
    }
  },
  created() {
    this.setInit();
  }
};
</script>

<style lang="scss" scoped>
</style>