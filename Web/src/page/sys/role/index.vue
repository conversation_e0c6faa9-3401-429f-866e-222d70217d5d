<template>
  <div>
    <th-search>
      <th-trem title="角色名称：">
        <el-input v-model="searchModel.roleName"></el-input>
      </th-trem>
      <template v-slot:search-btn>
        <th-button @click="search">查询</th-button>
        <th-button @click="resetModel">重置</th-button>
      </template>
    </th-search>
    <th-panel title="角色列表">
      <template v-slot:th-panel-header>
        <th-button @click="handleRoleInfo()">新增角色</th-button>
        <th-button @click="handleRoleDel()">删除角色</th-button>
      </template>

      <el-table ref="RoleListRef" :data="roleList.records" stripe>
        <el-table-column type="selection" width="50" />
        <el-table-column label="角色名称" prop="roleName" />
        <el-table-column label="角色编码" prop="roleCode" />
        <el-table-column label="是否隐藏敏感项" prop="isHideSensitivity" :formatter="isOrNo" />
        <el-table-column label="启用状态" prop="useable" :formatter="isOrNo" />
        <el-table-column label="角色描述" prop="remarks" />
        <el-table-column label="操作" align="center" width="200">
          <template slot-scope="scope">
            <th-button @click="handleRoleInfo(scope.row.id)">修改</th-button>
            <th-button @click="handleAuthority(scope.row.id)">功能授权</th-button>
          </template>
        </el-table-column>
      </el-table>
      <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
      <!-- <wcg-page :total="roleList.total || 0" :floorList="roleList.records || []" @on-change="sysRoleList"></wcg-page> -->
    </th-panel>
    <th-panel title="功能授权列表" isDialog :visible.sync="isVisible">
      <menus ref="menusRef" :isVisible.sync="isVisible"></menus>
      <template v-slot:th-panel-footer>
        <th-button @click="sysRoleAuthoritySave()">保存</th-button>
        <th-button @click="isVisible = false">取消</th-button>
      </template>
    </th-panel>
  </div>
</template>

<script>
import { sysRoleList, sysRoleDel } from "@/api/sys.js";
import Menus from "./menus";
export default {
  components: {
    Menus
  },
  data() {
    return {
      dialogVisible: false,
      searchModel: {},
      page: {
        total: 0,
        pageNum:1,
        pageSize:20
      },
      roleList: {},
      isVisible: false
    };
  },
  methods: {
    isOrNo(row, column,cellValue) {
      return cellValue == '0' ? '否':"是";
    },
    resetModel() {
      this.searchModel = {};
      this.search();
    },
    handleRoleInfo(id) {
      this.$router.push({
        name: "sysRoleInfo",
        params: { id: id }
      });
    },
    handleRoleDel() {
      let _l = this.$refs.RoleListRef.store.states.selection;
      if (_l && _l.length > 0) {
        this.$confirm(`确认删除选中角色吗?`, '温馨提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          center:true,
        }).then(() => {
          _l.forEach(e => {
            e.useable = 0;
          });
          sysRoleDel(_l).then(res => {
            if (res.code == 200) {
              this.$message({type:'success',message:'删除角色成功!'});
              this.sysRoleList();
            }else{
              this.$message({type:'error',message:res.message});
            }
          });
        }).catch(() => {
          return;          
        });
      }else{
        this.$alert('当前未勾选任何角色,请勾选需要删除的角色!', '温馨提示', {
          center:true,
          confirmButtonText: '确定',
        });
      }
    },
    handleAuthority(id) {
      this.isVisible = true;
      this.$refs.menusRef.sysRoleGet(id);
    },
    sysRoleAuthoritySave() {
      this.$refs.menusRef.authoritySave();
    },
    sysRoleList(pageNum,pageSize) {
      this.roleList = {};
      this.searchModel.pageNum = pageNum || 1;
      this.searchModel.pageSize = pageSize || 20;

      sysRoleList(this.searchModel).then(res => {
        if (res.code == 200) {
          this.roleList = res.data;
          this.page.total = this.roleList.total;
        }
      });
    },
    changePageNum(val){
      this.page.pageNum = val;
      this.sysRoleList(val,this.page.pageSize);
    },
    changePageSize(val){
      this.page.pageSize = val;
      this.sysRoleList(this.pageNum,val);
    },
    refresh(){
      this.sysRoleList(this.page.pageNum,this.page.pageSize);
    },
    search(){
      this.sysRoleList(1,this.page.pageSize);
    }
  },
  created() {
    this.sysRoleList();
  }
};
</script>

<style lang="scss" scoped>
</style>