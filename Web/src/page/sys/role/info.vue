<template>
  <div>
    <th-panel title="角色详情">

      <template v-slot:th-panel-header>
        <th-button @click="sysRoleSave()">保存</th-button>
        <th-button @click="routerBack">返回</th-button>
      </template>
      <el-form :inline="true" :model="roleData" :rules="rules" ref="ruleForm" label-width="120px">
        <el-form-item label="角色编码" prop="roleCode">
          <el-input :disabled="roleData.id?true:false" v-model="roleData.roleCode" placeholder="角色编码"></el-input>
        </el-form-item>
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="roleData.roleName" placeholder="角色名称"></el-input>
        </el-form-item>
        <!-- <el-form-item label="是否隐藏敏感项">
          <el-select v-model="roleData.isHideSensitivity">
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="是否启用">
          <el-select v-model="roleData.useable">
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="角色描述">
          <el-input type="textarea" v-model="roleData.remarks">
          </el-input>
        </el-form-item>
      </el-form>
    </th-panel>
  </div>
</template>

<script>
import { sysRoleGet, sysRoleSave, judgeNameCodeRpete } from "@/api/sys.js";
export default {
  data() {
    return {
      roleData: {},
      rules: {
        roleCode: [
          { required: true, message: '请填写角色编码', trigger: 'blur' },
        ],
        roleName: [
          { required: true, message: '请填写角色名称', trigger: 'blur' },
        ]
      }
    };
  },
  methods: {
    sysRoleSave() {
      //judgeNameCodeRpete
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          return;
        } else {
          let params = {
            roleCode:this.roleData.roleCode,
            roleName:this.roleData.roleName,
            id:this.roleData.id?this.roleData.id:''
          }
          judgeNameCodeRpete(params).then((res) => {
            if(res.code == 200 && res.data == 0){
              sysRoleSave(this.roleData).then(res => {
                if (res.code == 200) {
                  this.routerBack();
                  this.$message({ type: "success", message: "保存成功" });
                } else {
                  this.$message({ type: "error", message: res.msg });
                }
              });
            }else{
              this.$message({type:'warning',message:'角色名称或编码重复，请修改'})
            }
          }) 
        }
      });
    },
    sysRoleGet(id) {
      sysRoleGet({ roleId: id }).then(res => {
        if (res.code == 200) {
          this.roleData = res.data;
        }
      });
    },
    routerBack: function() {
      this.$router.back(-1);
    }
  },
  created() {
    if (this.$route.params.id) {
      this.sysRoleGet(this.$route.params.id);
    }
  }
};
</script>

<style lang="scss" scoped>
</style>