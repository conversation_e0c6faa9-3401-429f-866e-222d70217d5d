<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <div>
    <th-search>
      <th-trem title="活动状态：">
        <el-select v-model="searchModel.activityStatus" filterable placeholder="请选择">
          <el-option v-for="item in activityStatus" v-if="item" :key="item.statusCode" :label="item.statusName" :value="item.statusCode">
          </el-option>
        </el-select>
      </th-trem>
      <template v-slot:search-btn>
        <th-button @click="search">查询</th-button>
        <th-button @click="handleReset">重置</th-button>
      </template>
    </th-search>
    <th-panel title="活动列表">

      <el-table ref="RoleListRef" :data="searchData.records" stripe tooltip-effect="dark">
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="活动名称" prop="activityName" show-overflow-tooltip/>
        <el-table-column label="项目地址" prop="projectAddress" show-overflow-tooltip/>
        <el-table-column label="活动状态" prop="statusName" width="90" show-overflow-tooltip/>
        <el-table-column label="备注" prop="caseName" show-overflow-tooltip/>
        <el-table-column label="操作时间" show-overflow-tooltip width="150">
          <template slot-scope="scope">
            {{scope.row.lastUpdateDate.split(':')[0]+':'+scope.row.lastUpdateDate.split(':')[1]}}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="280">
          <template slot-scope="scope">
            <th-button @click="handleActivityInfo(scope.row)">修改活动</th-button>
            <th-button v-if="scope.row.statusCode == 'draft'" @click="handleDel(scope.row)">删除活动</th-button>
            <th-button v-if="scope.row.statusCode == 'running' || scope.row.statusCode == 'close'" @click="endActivity(scope.row)">结束活动</th-button>
          </template>
        </el-table-column>
      </el-table>

      <template v-slot:th-panel-footer>
        <div></div>
      </template>
      <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum" @currentPageSize="changePageSize" @refreshEvent="refresh"/>
    </th-panel>
  </div>
</template>

<script>
  import { activityPageList,delActivity,openOrCloseActivity } from "@/api/sys.js";
  export default {
    data() {
      return {
        searchModel: {},
        searchData: [],
        project: {},
        page: {
          total: 0,
          pageNum:1,
          pageSize:20,
        },
        showCreat:false,
        activityStatus: [{
          statusCode: "draft",
          statusName: "未发布"
        },{
          statusCode: "running",
          statusName: "进行中"
        },{
        statusCode: "close",
          statusName: "已关闭"
        },{statusCode: "end",
        statusName: "已结束"}]
      };
    },
  methods: {
    search(){
      this.handleSearch(1,this.page.pageSize);
     },
    handleReset() {
      this.searchModel = {};
      this.search()
    },
    handleActivityInfo(row) {
      console.log(row);
      this.$router.push({
        name: "pActivityInfo",
        params: { id:row.id,status:row.statusCode, project: {projectId:row.projectId},from:'activityList' }
      });
    },
    handleDel(r) {
      this.$confirm(`确认删除活动 【${r.activityName}】 吗?`, '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center:true,
      }).then(() => {
        delActivity({ id: r.id }).then(res => {
          if (res.code == 200) {
            this.handleSearch(1,this.page.pageSize);
            this.$message({ type: "success", message: "删除成功" });
          } else {
            this.$message({ type: "error", message: res.msg });
          }
        });
      }).catch(() => {
        return;          
      });
    },
    endActivity(row){
      console.log(row);
      let that =this;
      this.$confirm('确定结束活动 【'+row.activityName+'】 吗?', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center:true
      }).then(() => {
        let params = {
          id:row.id,
          status:'end',
          statusName:'已结束'
        }
        openOrCloseActivity(params).then((res) => {
          if(res.code == 200){
            that.$message({
              type:'success',
              message:'活动结束成功!'
            })
            this.handleSearch(1,this.page.pageSize);
          }else{
            that.$message({
              type:'error',
              message:res.message
            })
          }
        })
      }).catch(() => {
                 
      });
    },
    changePageNum(val){
      this.page.pageNum = val;
      this.handleSearch(val,this.page.pageSize);
    },
    changePageSize(val){
      this.page.pageSize = val;
      this.handleSearch(this.page.pageNum,val);
    },
    refresh(){
      this.handleSearch(this.page.pageNum,this.page.pageSize);
    },
    handleSearch(pageNum,pageSize) {
      this.searchData = [];
      this.searchModel.pageNum = pageNum || 1;
      this.searchModel.pageSize = pageSize || 20;
      activityPageList(this.searchModel).then(res => {
        if (res.code == 200) {
          this.searchData = res.data;
          this.page.total = this.searchData.total;
        }
      });
    },
    routerBack: function() {
      this.$router.back(-1);
    },
    },
    created() {
        this.handleSearch();
    }
  };
</script>

<style lang="scss" scoped>
</style>
