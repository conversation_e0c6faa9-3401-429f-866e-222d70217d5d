<template>
  <div>
    <th-search>
      <th-trem title="区域：">
        <el-input v-model="project.areaName" disabled />
      </th-trem>
      <th-trem title="城市：">
        <el-input v-model="project.cityName" disabled />
      </th-trem>
      <th-trem title="项目：">
        <el-input v-model="project.projectName" disabled />
      </th-trem>
      <template v-slot:search-btn>
        <th-button @click="routerBack">返回</th-button>
      </template>
    </th-search>
    <th-panel title="活动列表">
      <template v-slot:th-panel-header>
        <th-button @click="handleActivityInfo()" v-if="!showCreat">新增活动</th-button>
        <!-- <th-button @click="syncProjectInfo()">同步项目信息</th-button> -->
      </template>

      <el-table ref="RoleListRef" :data="searchData" stripe tooltip-effect="dark">
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="活动名称" prop="activityName" show-overflow-tooltip />
        <el-table-column label="项目地址" prop="projectAddress" show-overflow-tooltip />
        <el-table-column label="活动状态" prop="statusName" show-overflow-tooltip />
        <el-table-column label="备注" prop="caseName" show-overflow-tooltip />
        <el-table-column label="操作时间" show-overflow-tooltip width="150">
          <template slot-scope="scope">
            {{ scope.row.lastUpdateDate.split(':')[0] + ':' + scope.row.lastUpdateDate.split(':')[1] }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="280">
          <template slot-scope="scope">
            <th-button @click="handleActivityInfo(scope.row.id, scope.row.statusCode)">修改活动</th-button>
            <th-button @click="handleDel(scope.row)">删除活动</th-button>
            <th-button v-if="scope.row.statusCode == 'running' || scope.row.statusCode == 'close'"
              @click="endActivity(scope.row)">结束活动</th-button>
          </template>
        </el-table-column>
      </el-table>

      <template v-slot:th-panel-footer>
        <div></div>
      </template>
    </th-panel>
  </div>
</template>

<script>
import { activityList, delActivity, openOrCloseActivity, syncProjectInfo } from "@/api/sys.js";
export default {
  data() {
    return {
      searchModel: {},
      searchData: [],
      project: {},
      showCreat: false,
      isDisabled: false,
    };
  },
  methods: {
    endActivity(row) {
      let that = this;
      this.$confirm('确定结束活动 【' + row.activityName + '】 吗?', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true
      }).then(() => {
        let params = {
          id: row.id,
          status: 'end',
          statusName: '已结束'
        }
        openOrCloseActivity(params).then((res) => {
          if (res.code == 200) {
            that.$message({
              type: 'success',
              message: '活动结束成功!'
            })
            that.handleSearch();
          } else {
            that.$message({
              type: 'error',
              message: res.message
            })
          }
        })
      }).catch(() => {

      });
    },
    syncProjectInfo() {
      if (this.isDisabled) {
        return;
      }
      this.isDisabled = true
      let params = {
        projectId: this.$route.params.projectId
      }
      syncProjectInfo(params).then((res) => {
        if (res.code == 200) {
          this.$message({ type: 'success', message: '同步项目简要信息成功!' });
          setTimeout(() => {
            this.isDisabled = false;
          }, 1000);
        } else {
          this.$message({ type: "error", message: res.message });
          setTimeout(() => {
            this.isDisabled = false;
          }, 1000);
        }
      }).catch(error => {
        setTimeout(() => {
          this.isDisabled = false;
        }, 1000);
      })
    },
    handleActivityInfo(id, statusCode) {
      this.$router.push({
        name: "pActivityInfo",
        params: { id: id, status: statusCode, project: this.project }
      });
    },
    handleDel(r) {
      this.$confirm(`确认删除活动 【${r.activityName}】 吗?`, '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true,
      }).then(() => {
        delActivity({ id: r.id }).then(res => {
          if (res.code == 200) {
            this.handleSearch();
            this.$message({ type: "success", message: "删除成功" });
          } else {
            this.$message({ type: "error", message: res.msg });
          }
        });
      }).catch(() => {
        return;
      });
    },
    handleSearch() {
      this.searchData = [];
      activityList(this.searchModel).then(res => {
        if (res.code == 200) {
          this.searchData = res.data;
          if (this.searchData.length == 0) {
            this.showCreat = false;
          } else {
            for (let i in this.searchData) {
              if (this.searchData[i].statusCode == 'running' || this.searchData[i].statusCode == 'close' || this.searchData[i].statusCode == 'draft') {
                this.showCreat = true;
                break;
              }
            }
          }

        }
      });
    },
    routerBack: function () {
      this.$router.back(-1);
    }
  },
  created() {
    if (this.$route.params.projectId) {
      this.project = this.$route.params;
      this.searchModel.projectId = this.project.projectId;
      this.handleSearch();
    } else {
      this.$router.push({ name: "project" });
    }
  }
};
</script>

<style lang="scss" scoped></style>
