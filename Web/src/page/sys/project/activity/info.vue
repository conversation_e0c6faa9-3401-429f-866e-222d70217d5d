<template>
  <div>
    <th-panel title="活动详情">
      <template v-slot:th-panel-header>
        <th-button @click="sysRoleSave()" v-if="activityData.id && $route.params.status!='draft'">结束活动</th-button>
        <th-button @click="handleSave()" :disabled="isSaveDisabled">保存</th-button>
        <th-button @click="routerBack">返回</th-button>
      </template>
      <el-form ref="elForm" :inline="true" :model="activityData" label-width="120px" :rules="rules">
        <el-form-item label="活动名称" prop="activityName">
          <el-input v-model="activityData.activityName" placeholder="活动名称"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="caseName">
          <el-input v-model="activityData.caseName" placeholder="案场名称"></el-input>
        </el-form-item>
        <el-form-item label="项目地址">
          <el-input style="width:400px" v-model="activityData.projectAddress" placeholder="项目地址"></el-input>
        </el-form-item>
      </el-form>
      <th-panel noTitle v-if="activityData.id">
        <template v-slot:th-panel-header>
          <th-button @click="handleAddUser">添加成员</th-button>
        </template>

        <el-table ref="RoleListRef" :data="searchData" stripe>
          <el-table-column label="序号" type="index" width="50" align="center" />
          <el-table-column label="姓名" prop="userName" />
          <el-table-column label="角色" prop="roleName" />
          <el-table-column label="操作" width="150" align="center">
            <template slot-scope="scope">
              <th-button @click="handleDelActivityRole(scope.row)">删除</th-button>
            </template>
          </el-table-column>
        </el-table>

        <template v-slot:th-panel-footer>
          <div></div>
        </template>
      </th-panel>
    </th-panel>

    <th-panel title="添加人员信息" isDialog :visible.sync="isVisible">
      <el-form ref="userElForm" :inline="true" :model="userData" label-width="120px" :rules="userRules">
        <el-form-item label="用户姓名" prop="userName">
          <el-input v-model="userData.userName" @click.native="handlePanelSwitch" readonly placeholder="用户姓名"></el-input>
        </el-form-item>
        <el-form-item label="角色名称" prop="roleName">
          <el-select v-model="selectRole" filterable placeholder="请选择" value-key="roleCode">
            <el-option v-for="item in sysRoles" v-if="item" :key="item.roleCode" :label="item.roleName" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template v-slot:th-panel-footer>
        <th-button @click="handleSaveUser()">保存</th-button>
        <th-button @click="isVisible = false">取消</th-button>
      </template>
    </th-panel>

    <th-panel title="选择授权人员" isDialog :visible.sync="isVisible2">
      <div class="select-user">
        <div class="select-user__panel">
          <el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
          <el-tree :data="orgTree" node-key="id" :default-expanded-keys="[3931]" :props="orgTreeProps" :filter-node-method="filterNode" ref="orgTreeRef" @node-click="handleNodeClick">
          </el-tree>
        </div>

        <div class="select-user__panel">
          <el-input placeholder="输入关键字进行过滤" v-model="filterName">
            <el-button slot="append" icon="el-icon-search" @click="handleSelectUserName"></el-button>
          </el-input>
          <el-table :data="userList" stripe @row-click="handleUserRowClick" :row-class-name="rowClassName">
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="用户名" prop="username" />
            <el-table-column label="姓名" prop="name" />
          </el-table>
        </div>
      </div>

      <template v-slot:th-panel-footer>
        <th-button @click="handleUserSelect()">确定</th-button>
        <th-button @click="handlePanelSwitch()">取消</th-button>
      </template>
    </th-panel>

  </div>
</template>

<script>
import {
  activityDetails,
  saveActivity,
  activityRoleList,
  getProjectInfo,
  saveActivityRole,
  activityRoleInit,
  findUserByOrgId,
  delActivityRole,
  findUserByName
} from "@/api/sys.js";
export default {
  data() {
    return {
      searchData: [],
      activityData: {},
      rules: {
        activityName: [
          { required: true, message: "请输入活动名称", trigger: "blur" }
        ]
      },
      userRules: {
        userName: [
          { required: true, message: "请选择用户信息", trigger: "change" }
        ],
        roleName: [
          { required: true, message: "请选择角色信息", trigger: "blur" }
        ]
      },
      isVisible: false,
      isVisible2: false, //选择人员弹窗
      userData: {},
      orgTree: [],
      sysRoles: [],
      selectRole: {},
      filterText: "",
      filterName: "",
      orgTreeProps: {
        children: "childs",
        label: "name"
      },
      userList: [],
      selectUser: {},
      isSaveDisabled:false,
    };
  },
  watch: {
    selectRole: {
      handler(v) {
        if (v && v.roleCode) {
          this.userData.roleCode = v.roleCode;
          this.userData.roleName = v.roleName;
        }
      },
      deep: true
    },
    filterText(val) {
      this.$refs.orgTreeRef.filter(val);
    }
  },
  methods: {
    getData() {
      if (this.$route.params.id) {
        this.activityData.id = this.$route.params.id;
        activityDetails({ id: this.activityData.id }).then(res => {
          if (res.code == 200 && res.data) {
            this.activityData = res.data;
            this.activityData.creationDate = null;
            this.activityData.createdBy = null;
            this.activityData.lastUpdateDate = null;
            this.activityData.lastUpdateBy = null;
            this.handleSearch();
          }
        });
      } else {
        if (this.project) {
          this.activityData.projectName = this.project.projectName;
          this.activityData.projectId = this.project.projectId;
          getProjectInfo({ projectId: this.project.projectId }).then(res => {
            if (res.code == 200 && res.data) {
              this.$set(this.activityData, "projectAddress", res.data.address);
            }
          });
        }
      }
    },
    routerBack: function() {
      if(!this.$route.params.from || this.$route.params.from != 'activityList'){
        this.$router.push({
          name: "pActivity",
          params: this.project
        });
      }else{
        window.history.go(-1);
      }
    },
    handleSave() {
      if(this.isSaveDisabled){
        return;
      }else{
        this.isSaveDisabled = true;
        let _f = true;
        this.$refs["elForm"].validate(valid => {
          if (!valid) {
            this._f = valid;
            return false;
          }
        });
        if (this._f) {
          saveActivity(this.activityData).then(res => {
            if (res.code == 200) {
              this.routerBack();
              this.$message({ type: "success", message: "保存成功" });
              this.isSaveDisabled = false;
            } else this.$message({ type: "error", message: res.msg });
          });
        }
      }
    },
    handleSearch() {
      this.searchData = [];
      activityRoleList({ activityId: this.activityData.id }).then(res => {
        if (res.code == 200) {
          this.searchData = res.data;
        }
      });
    },
    handleAddUser() {
      this.isVisible = true;
      this.selectRole = {};
      this.userData = {
        activityName: this.activityData.activityName,
        activityId: this.activityData.id,
        userCode: "1",
        roleCode: "1"
      };
      if (this.orgTree.length == 0 && this.sysRoles.length == 0) {
        activityRoleInit().then(res => {
          if (res.code == 200) {
            this.orgTree = res.data.orgTree;
            this.sysRoles = res.data.sysRoles;
          }
        });
      }
    },
    handleDelActivityRole(r) {
      delActivityRole(r).then(res => {
        if (res.code == 200) {
          this.handleSearch();
          this.$message({ type: "success", message: "删除用户角色成功" });
        } else this.$message({ type: "error", message: res.msg });
      });
    },
    handleSaveUser() {
      let _f = true;
      this.$refs["userElForm"].validate(valid => {
        if (!valid) {
          this._f = valid;
          this.$message({ type: "error", message: "验证失败，请检查数据！" });
          return false;
        }
      });
      if (this._f) {
        saveActivityRole(this.userData).then(res => {
          if (res.code == 200) {
            this.handleSearch();
            this.$message({ type: "success", message: "保存用户角色成功" });
            this.isVisible = false;
          } else this.$message({ type: "error", message: res.msg });
        });
      }
    },
    handlePanelSwitch() {
      this.isVisible = !this.isVisible;
      this.isVisible2 = !this.isVisible2;
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleSelectUserName() {
      findUserByName({ username: this.filterName }).then(res => {
        if (res.code == 200) {
          this.userList = res.data;
        }
      });
    },
    handleNodeClick(d) {
      findUserByOrgId({ orgId: d.sid || "3931" }).then(res => {
        if (res.code == 200) {
          this.userList = res.data;
        }
      });
    },
    rowClassName({ row, rowIndex }) {
      return this.selectUser.username == row.username ? "active" : "";
    },
    handleUserRowClick(d) {
      this.selectUser = d;
    },
    handleUserSelect() {
      this.$set(this.userData, "userName", this.selectUser.name);
      this.$set(this.userData, "userCode", this.selectUser.username);
      this.handlePanelSwitch();
    }
  },
  created() {
    console.log(this.$root);
    if (this.$route.params.project && this.$route.params.project.projectId) {
      this.project = this.$route.params.project;
    } else {
      this.$router.push({ name: "project" });
    }
    this.getData();
  }
};
</script>

<style lang="scss" scoped>
.select-user {
  display: flex;
  justify-content: space-between;

  .select-user__panel {
    flex: 1;
    padding: 5px;
  }
  .select-user__panel > div:nth-child(2) {
    height: calc(50vh - 45px);
    margin-top: 5px;
    overflow: auto;
  }
}
</style>