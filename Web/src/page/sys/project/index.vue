<template>
  <div>
    <th-search>
      <th-trem title="公司：">
        <el-select v-model="searchModel.areaId" filterable placeholder="请选择">
          <el-option v-for="item in areaList" v-if="item" :key="item.areaId" :label="item.areaName"
            :value="item.areaId">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="项目：">
        <el-select v-model="searchModel.cityId" :disabled="searchModel.areaId ? false : true" filterable
          placeholder="请选择">
          <el-option v-for="item in cityList" v-if="item" :key="item.cityId" :label="item.cityName"
            :value="item.cityId">
          </el-option>
        </el-select>
      </th-trem>
      <th-trem title="分期：">
        <el-select v-model="searchModel.projectId" :disabled="searchModel.cityId ? false : true" filterable
          placeholder="请选择">
          <el-option v-for="item in projectList" v-if="item" :key="item.projectId" :label="item.projectName"
            :value="item.projectId">
          </el-option>
        </el-select>
      </th-trem>
      <template v-slot:search-btn>
        <th-button @click="search">查询</th-button>
        <th-button @click="handleReset">重置</th-button>
      </template>
    </th-search>
    <th-panel title="项目列表">
      <el-table ref="RoleListRef" :data="searchData.records" stripe @row-click="handleRowClick">
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="公司" prop="areaName" />
        <el-table-column label="项目" prop="cityName" />
        <el-table-column label="分期名称" prop="projectName" />
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <th-button @click.stop="handleDelActivityRole(scope.row)">编辑</th-button>
          </template>
        </el-table-column>
        <!-- <el-table-column label="分期" prop="stagesName" /> -->
      </el-table>
      <th-page :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @currentPageNum="changePageNum"
        @currentPageSize="changePageSize" @refreshEvent="refresh" />
      <!-- <wcg-page :total="page.total" :floorList="searchData.records || []" @on-change="handleSearch"></wcg-page> -->
    </th-panel>
    <el-dialog :append-to-body="true" title="编辑" :visible.sync="dialogVisible" width="30%" :show-close="false"
      top="30vh" :before-close="closeDialog">
      <div>
        <el-form v-loading="dialogLoading" ref="detailFormRef" label-position="right" :rules="rules" :model="detailForm"
          label-width="80px">
          <el-form-item label="户型:" prop="houseType">
            <el-input v-model="detailForm.houseType" :maxlength="500">
            </el-input>
          </el-form-item>
          <el-form-item label="地址:" prop="address">
            <el-input v-model="detailForm.address" :maxlength="500">
            </el-input>
          </el-form-item>
          <el-form-item label="均价:" prop="priceAvg">
            <el-input v-model="detailForm.priceAvg" :maxlength="500">
            </el-input>
          </el-form-item>
          <el-form-item label="电话:" prop="tel">
            <el-input v-model="detailForm.tel" :maxlength="500">
            </el-input>
          </el-form-item>
          <el-form-item label="图片:" prop="img">
            <el-upload v-loading="uploadLoading" class="avatar-uploader" :multiple="false" :action="action"
              :headers="myHeaders" :show-file-list="false" :on-success="handleAvatarSuccess"
              :before-upload="beforeUpload">
              <img v-if="detailForm.img" :src="detailForm.img" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button :loading="dialogLoading" @click="closeDialog">取 消</el-button>
        <el-button :loading="dialogLoading" type="primary" @click="saveDetail">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from '@/axios'
import {
  projectArea,
  projectCity,
  projectConditionList,
  projectPageList,
  getProjectShareByProjectId,
  updateProjectShare
} from "@/api/sys.js";
export default {
  data() {
    return {
      searchModel: {},
      page: {
        total: 0,
        pageNum: 1,
        pageSize: 20,
      },
      areaList: [],
      cityList: [],
      projectList: [],
      searchData: {},
      dialogVisible: false,
      dialogLoading: false,
      detailForm: {
        id: null,
        projectId: "",
        houseType: '',
        address: '',
        priceAvg: '',
        tel: '',
        img: ''
      },
      rules: {
        houseType: [
          { required: true, message: '请输入户型', trigger: 'submit' },
        ],
        address: [
          { required: true, message: '请输入地址', trigger: 'submit' },
        ],
        priceAvg: [
          { required: true, message: '请输入均价', trigger: 'submit' },
        ],
        tel: [
          { required: true, message: '请输入电话', trigger: 'submit' },
        ],
        img: [
          { required: true, message: '请上传图片', trigger: 'submit' },
        ],
      },
      uploadLoading: false,
      action: axios.api + '/opHouseType/uploadFile',
      myHeaders: {},
    };
  },
  watch: {
    "searchModel.areaId"(n) {
      this.$set(this.searchModel, "cityId", "");
      this.$set(this.searchModel, "projectId", "");
      projectCity({ areaId: n }).then(res => {
        if (res.code == 200) {
          this.cityList = res.data;
        }
      });
    },
    "searchModel.cityId"(n) {
      if (n) {
        this.$set(this.searchModel, "projectId", "");
        projectConditionList({
          areaId: this.searchModel.areaId,
          cityId: n
        }).then(res => {
          if (res.code == 200) {
            this.projectList = res.data;
          }
        });
      }
    }
  },
  methods: {
    beforeUpload() {
      this.uploadLoading = true
      return true
    },
    handleAvatarSuccess(res, file) {
      this.detailForm.img = res.data
      this.uploadLoading = false
    },
    closeDialog() {
      this.dialogVisible = false
      this.detailForm = {
        id: null,
        projectId: "",
        houseType: '',
        address: '',
        priceAvg: '',
        tel: '',
        img: ''
      }
    },
    handleDelActivityRole(row) {
      this.dialogVisible = true
      this.dialogLoading = true
      getProjectShareByProjectId({ projectId: row.projectId }).then(res => {
        if (res.code == 200) {
          this.detailForm = res.data;
        }
        this.dialogLoading = false
      })
    },
    saveDetail() {
      this.$refs['detailFormRef'].validate((valid) => {
        if (valid) {
          this.dialogLoading = true
          updateProjectShare({ ...this.detailForm }).then(res => {
            if (res.code == 200) {
              this.$message({ type: "success", message: "操作成功" });
              this.closeDialog()
              this.dialogLoading = false
            }
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleRowClick(r, c, e) {
      this.$router.push({
        name: "pActivity",
        params: r
      });
    },
    handleReset() {
      this.searchModel = {};
      this.search()
    },
    getArea() {
      projectArea().then(res => {
        if (res.code == 200) {
          this.areaList = res.data;
        }
      });
    },
    changePageNum(val) {
      this.page.pageNum = val;
      this.handleSearch(val, this.page.pageSize);
    },
    changePageSize(val) {
      this.page.pageSize = val;
      this.handleSearch(this.page.pageNum, val);
    },
    search() {
      this.handleSearch(1, this.page.pageSize);
    },
    refresh() {
      this.handleSearch(this.page.pageNum, this.page.pageSize);
    },
    handleSearch(pageNum, pageSize) {
      this.searchData = {};
      this.searchModel.pageNum = pageNum || 1;
      this.searchModel.pageSize = pageSize || 20;
      projectPageList(this.searchModel).then(res => {
        if (res.code == 200) {
          this.searchData = res.data;
          this.page.total = this.searchData.total;
        }
      });
    }
  },
  created() {
    this.myHeaders['bus-user-token'] = sessionStorage.getItem('busUserToken');
    // debugger;
    this.handleSearch();
    this.getArea();
  }
};
</script>

<style lang="scss" scoped>
.avatar-uploader {
  width: 180px;
  height: 180px;
}

>>>.avatar-uploader .el-upload {
  border: 1px solid #DCDFE6;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
