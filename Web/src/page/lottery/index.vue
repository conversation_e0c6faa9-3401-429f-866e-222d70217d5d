<template>
  <div>
    <th-panel title="摇号信息" v-loading="loading">
      <template v-slot:th-panel-header>
        <th-button
          v-has="'lottery_01'"
          :loading="loading"
          @click="handleSave"
          :disabled="isDisabled"
          >保存</th-button
        >
        <th-button v-has="'lottery_02'" :loading="loading" @click="routerBack"
          >返回</th-button
        >
      </template>

      <el-form
        ref="activityForm"
        :model="activityData"
        label-width="210px"
        :rules="rules"
      >
        <el-form-item label="摇号显示标题:" prop="lotteryTitle">
          <el-input
            v-model="activityData.lotteryTitle"
            placeholder="请输入"
          ></el-input>
          <span class="form-remark"
            >{{
              activityData.lotteryTitle ? activityData.lotteryTitle.length : 0
            }}/20</span
          >
        </el-form-item>
        <el-form-item label="摇号时间:" prop="lotteryDate">
          <div style="height:40px;line-height:40px">
            <el-date-picker
              v-model="activityData.lotteryDate"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束日期"
              style="width: 100%;"
              value-format="yyyy-MM-dd HH:mm:00"
            >
            </el-date-picker>
          </div>
          <span class="form-remark"></span>
        </el-form-item>
        <el-form-item label="摇号类型:" prop="lotteryFlag">
          <el-radio-group v-model="activityData.lotteryFlag">
            <el-radio :label="1">个人摇号</el-radio>
            <el-radio :label="0">集体摇号</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="摇号规则说明:" prop="lotteryExplain">
          <el-input
            type="textarea"
            :autosize="{ minRows: 10, maxRows: 10 }"
            v-model="activityData.lotteryExplain"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="摇号结果短信通知:" prop="lotteryResultFlag">
          <el-radio-group v-model="activityData.lotteryResultFlag">
            <el-radio :label="1">发送</el-radio>
            <el-radio :label="0">不发送</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开始摇号短信通知:" prop="openLotteryFlag">
          <el-radio-group v-model="activityData.openLotteryFlag">
            <el-radio :label="1">发送</el-radio>
            <el-radio :label="0">不发送</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="开始摇号时间前多少分钟发送:"
          prop="openLotteryBeforeMinutes"
        >
          <el-input-number
            class="input-number"
            v-model="activityData.openLotteryBeforeMinutes"
            :min="0"
            :step="1"
            :precision="0"
          ></el-input-number
          ><span style="width: 80%;font-size: 14px;color: #606266;">分钟</span>
        </el-form-item>
        <el-form-item label="二维码:" style="margin-top: 20px;">
          <div
            ref="qrcode"
            id="myQrCode"
            class="qrImg"
            style="display: flex;align-items: center;"
          ></div>
        </el-form-item>
        <div style="height:50px;"></div>
      </el-form>
    </th-panel>
  </div>
</template>

<script>
import QRCode from "qrcodejs2";
import { activityDetails, saveActivityLottery } from "@/api/sys.js";
import baiduMap from "@/components/BMap";
export default {
  components: {
    baiduMap
  },
  data() {
    return {
      qrcode: "",
      loading: false,
      baseActivityData: {},
      activityData: {
        lotteryTitle: "",
        lotteryDate: null,
        lotteryFlag: null,
        lotteryExplain: null,
        lotteryResultFlag: 0,
        openLotteryFlag: 0,
        openLotteryBeforeMinutes: 0,
        lotteryQRcode: null
      },
      rules: {
        lotteryTitle: [
          { required: true, message: "摇号显示标题为必填项", trigger: "blur" },
          { max: 20, message: "摇号显示标题超出20个字符", trigger: "blur" }
        ],
        lotteryDate: [
          { required: true, message: "摇号时间为必选项", trigger: "blur" }
        ],
        lotteryFlag: [
          { required: true, message: "摇号类型为必选项", trigger: "blur" }
        ],
        lotteryExplain: [
          { required: true, message: "摇号规则说明为必填项", trigger: "blur" },
          { max: 2000, message: "摇号规则说明超出2000个字符", trigger: "blur" }
        ],
        lotteryResultFlag: [
          {
            required: true,
            message: "摇号结果短信通知为必选项",
            trigger: "blur"
          }
        ],
        openLotteryFlag: [
          {
            required: true,
            message: "开始摇号短信通知为必选项",
            trigger: "blur"
          }
        ]
      },
      isDisabled: false
    };
  },
  watch: {},
  methods: {
    handleSave() {
      if (this.isDisabled) {
        return;
      } else {
        this.$refs["activityForm"].validate(valid => {
          if (valid) {
            let url = process.env.qrCodeUrl;
            let params = {
              id: this.$router.actId,
              ...this.activityData,
              lotteryStartDate: this.activityData.lotteryDate[0],
              lotteryEndDate: this.activityData.lotteryDate[1],
              lotteryQRcode: `${url}/#/login?id=${this.$router.actId}`
            };
            delete params.lotteryDate;
            this.loading = true;
            saveActivityLottery(params)
              .then(res => {
                this.isDisabled = false;
                if (res.code == 200) {
                  // this.routerBack();
                  this.$message({ type: "success", message: "保存成功" });
                  // this.isDisabled = false;
                  // this.$nextTick(() => {
                  //   this.downLoadQrcode();
                  // });
                  // this.qrcode = new QRCode(this.$refs.qrcode, {
                  //   width: 100, // 二维码宽度 （不支持100%）
                  //   height: 100, // 二维码高度（不支持100%）
                  //   text: this.activityData.lotteryQRcode, // 二维码地址
                  //   render: "canvas" // 设置渲染方式（有两种方式 table和canvas，默认是canvas）
                  // });
                  this.getActData();
                } else this.$message({ type: "error", message: res.msg });
              })
              .finally(() => {
                this.loading = false;
              });
          }
        });
      }
    },
    // downLoadQrcode() {
    //   let img = document.getElementById("myQrCode").getElementsByTagName[
    //     "img"
    //   ][0]; // 获取二维码图片标签
    //   let canvas = document.createElement("canvas"); // 创建画布
    //   canvas.width = img.width;
    //   canvas.height = img.height;
    //   let ctx = canvas.getContext("2d");
    //   ctx.drawImage(img, 0, 0);
    //   let tempUrl = canvas.toDataURL("image/png"); // 画布生成base64格式图片
    //   // 创建a标签，模拟点击下载
    //   let a = document.createElement("a");
    //   a.setAttribute("download", "我的二维码.png");
    //   a.style.display = "none";
    //   a.href = tempUrl;
    //   document.body.appendChild(a);
    //   a.click();
    //   document.body.removeChild(a);
    // },
    routerBack() {
      this.$router.push({ name: "main" });
    },
    getActData() {
      if (this.$router.actId) this.loading = true;
      activityDetails({ id: this.$router.actId })
        .then(res => {
          if (res.code == 200 && res.data) {
            this.activityData = {
              lotteryTitle: res.data.lotteryTitle || "",
              lotteryDate:
                res.data.lotteryStartDate && res.data.lotteryEndDate
                  ? [res.data.lotteryStartDate, res.data.lotteryEndDate]
                  : null,
              lotteryFlag: res.data.lotteryFlag,
              lotteryExplain: res.data.lotteryExplain,
              lotteryResultFlag: res.data.lotteryResultFlag,
              openLotteryFlag: res.data.openLotteryFlag,
              openLotteryBeforeMinutes: res.data.openLotteryBeforeMinutes,
              lotteryQRcode: res.data.lotteryQRcode
            };
            this.$nextTick(() => {
              this.$refs["activityForm"].clearValidate();
              this.$refs.qrcode.innerHTML = "";
              let url = process.env.qrCodeUrl;
              this.qrcode = new QRCode(this.$refs.qrcode, {
                width: 100, // 二维码宽度 （不支持100%）
                height: 100, // 二维码高度（不支持100%）
                text: `${url}/#/login?id=${this.$router.actId}`, // 二维码地址
                render: "canvas" // 设置渲染方式（有两种方式 table和canvas，默认是canvas）
              });
            });
            this.baseActivityData = JSON.parse(JSON.stringify(res.data));
          }
        })
        .finally(() => {
          this.loading = false;
        });
    }
  },
  created() {
    this.getActData();
  },
  mounted() {}
};
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 0px;

  &.is-error {
    margin-bottom: 20px;
  }
}

.form-remark {
  flex: 2;
  color: #ccc;
}

.input-number {
  >>> span {
    display: none;
  }

  >>> .el-input__inner {
    text-align: left;
    padding-left: 15px;
    padding-right: 10px;
  }
}

.check-box {
  width: 180px;
}
</style>
<style>
.el-scrollbar:nth-of-type(3) {
  display: none !important;
}

.el-time-spinner {
  text-align: center;
}
</style>
<style scoped>
.el-form-item >>> .el-form-item__content {
  display: flex;
  flex-wrap: wrap;
}

.el-form-item >>> .el-form-item__content > div {
  width: auto;
  flex: 1;
  margin-right: 20px;
}

.el-form-item >>> .el-form-item__content > div.check-box-div {
  width: 180px;
  flex: 0;
}

.el-form-item >>> .el-form-item__content > div .el-radio {
  line-height: 40px;
  height: 40px;
}

.messageH >>> .el-input {
  width: 100px !important;
}

.messageH >>> .el-input__inner {
  width: 100px !important;
}

.map-container {
  margin-left: 150px;
  width: 740px;
  height: 500px;
}

.dateList >>> .el-form-item__content {
  flex-direction: column;
}
</style>
