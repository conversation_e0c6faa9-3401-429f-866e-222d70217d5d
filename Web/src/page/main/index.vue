<template>
  <div
    class="th-home-page"
    v-loading="globalLoading"
    :element-loading-text="globalMaskText"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.5)"
  >
    <th-panel class="opening-link" title="活动专属链接">
      <div>
        <span> <i></i>开盘公示投影</span>
        <div>
          <th-button v-has="'main_01'" @click="copyLink">复制链接</th-button>
          <th-button v-has="'main_02'" @click="openPage">打开页面</th-button>
          <input
            v-show="isShowCopy"
            type="text"
            :value="screenUrl"
            id="copyInput"
            style="opacity:0"
          />
        </div>
      </div>
      <div>
        <span> <i></i>摇号公示</span>
        <div>
          <th-button v-has="'main_07'" @click="openLotteryPage"
            >打开页面</th-button
          >
        </div>
      </div>
      <div>
        <span> <i></i>小程序活动码</span>
        <div class="opening-tCode">
          <div class="opening-tCode--img">
            <img :src="qrUrl" width="120px" />
            <th-button v-has="'main_03'" @click="downloadImg('qr')"
              >活动入口码下载</th-button
            >
          </div>
          <!--          <div class="opening-tCode&#45;&#45;img" v-if="activityStatus.isSignIn =='y'">-->
          <!--            <img :src="signInUrl" width="120px">-->
          <!--            <th-button v-has="'main_04'" @click="downloadImg('sign')">签到入口码下载</th-button>-->
          <!--          </div>-->
        </div>
      </div>
    </th-panel>
    <th-panel class="opening-project" title="分期简要信息">
      <span>分期名称：{{ projectInfo.projectName }}</span>
      <span>产品类型：{{ projectInfo.houseType }}</span>
      <span>产品均价：{{ projectInfo.priceAvg }}元/㎡</span>
      <span>分期地址：{{ projectInfo.address }}</span>
      <span>咨询热线：{{ projectInfo.tel }}</span>
    </th-panel>
    <th-panel class="opening-setting" title="活动设置">
      <template v-slot:th-panel-header>
        <div style="float:right;">
          活动状态：<span style="color:#E63F3C;font-weight:bold;">{{
            activityStatus.activityStatusName
          }}</span>
        </div>
      </template>
      <div class="pr50">
        <span>1.预设活动管理信息</span>
        <span @click="goPage(activityStatus.activityManageStatus, '/activity')">
          <i
            class="iconfont icon-queren icon-confirm"
            v-if="activityStatus.activityManageStatus == 'y'"
          ></i
          ><i v-else class="iconfont icon-jinggao- icon-jinggao"></i>
          {{
            activityStatus.activityManageStatus == "y" ? "已完成" : "去设置"
          }}</span
        >
      </div>
      <div class="pr50">
        <span>2.预设开盘户型信息</span>
        <span
          @click="goPage(activityStatus.houseTypeStatus, '/house/apartment')"
        >
          <i
            class="iconfont icon-queren icon-confirm"
            v-if="activityStatus.houseTypeStatus == 'y'"
          ></i
          ><i v-else class="iconfont icon-jinggao- icon-jinggao"></i>
          {{
            activityStatus.houseTypeStatus == "y" ? "已完成" : "去设置"
          }}</span
        >
      </div>
      <div class="pr50">
        <span>3.预设开盘房源信息</span>
        <span
          @click="
            goPage(activityStatus.housingResourcesStatus, '/house/source')
          "
        >
          <i
            class="iconfont icon-queren icon-confirm"
            v-if="activityStatus.housingResourcesStatus == 'y'"
          ></i
          ><i v-else class="iconfont icon-jinggao- icon-jinggao"></i>
          {{
            activityStatus.housingResourcesStatus == "y" ? "已完成" : "去设置"
          }}</span
        >
      </div>
      <div class="pr50">
        <span>4.预设客户管理信息</span>
        <span @click="goPage(activityStatus.customerStatus, '/cust/custList')">
          <i
            class="iconfont icon-queren icon-confirm"
            v-if="activityStatus.customerStatus == 'y'"
          ></i
          ><i v-else class="iconfont icon-jinggao- icon-jinggao"></i>
          {{ activityStatus.customerStatus == "y" ? "已完成" : "去设置" }}</span
        >
      </div>
      <div class="pr50">
        <span>5.预设置业顾问/营销经理信息</span>
        <span @click="goPage(activityStatus.saleStatus, '/cust/adviser')">
          <i
            class="iconfont icon-queren icon-confirm"
            v-if="activityStatus.saleStatus == 'y'"
          ></i
          ><i v-else class="iconfont icon-jinggao- icon-jinggao"></i>
          {{ activityStatus.saleStatus == "y" ? "已完成" : "去设置" }}</span
        >
      </div>
      <div>
        <span>6.设置完成发布活动</span>
        <span>
          <th-button v-has="'main_05'" style="width:120px" @click="goPublic">{{
            activityStatus.activityStatusName == "进行中"
              ? "关闭活动"
              : "发布活动"
          }}</th-button>
        </span>
      </div>
    </th-panel>
    <el-dialog
      title="选择下载尺寸"
      :visible.sync="dialogVisible"
      width="40%"
      :before-close="handleClose"
    >
      <el-table
        :data="qrImgData"
        stripe
        style="width:100%"
        v-loading="loading"
        element-loading-text="玩命下载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.5)"
      >
        <el-table-column prop="width" label="二维码边长(cm)"></el-table-column>
        <el-table-column prop="distance" label="扫描距离(m)"></el-table-column>
        <el-table-column label="下载链接" align="center" width="80">
          <template slot-scope="scope">
            <i
              class="iconfont icon-iconxz"
              @click="downloadItem(scope.row)"
            ></i>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { projectInfo, activityStatus } from "@/api/homePage.js";
import {
  openActivity,
  getOpenActivityDetail,
  buildQrCode,
  downloadQrCode
} from "@/api/sys.js";
import utils from "@/api/common";
export default {
  data() {
    return {
      projectInfo: {},
      activityStatus: {},
      qrUrl: null,
      signInUrl: null,
      isShowCopy: false,
      screenUrl: location.href,
      dialogVisible: false,
      downloadType: "",
      qrImgData: [
        { width: "8cm", distance: "0.5m", px: 430 },
        { width: "12cm", distance: "0.8m", px: 645 },
        { width: "15cm", distance: "1m", px: 817 },
        { width: "30cm", distance: "1.5m", px: 1634 },
        { width: "50cm", distance: "2.5m", px: 2709 }
      ],
      loading: false,
      globalLoading: false,
      globalMaskText: "活动发布中"
    };
  },
  methods: {
    init() {
      this.getProjectInfo();
      this.getActivityStatus();
    },
    handleClose() {
      this.dialogVisible = false;
    },
    copyLink() {
      //复制链接
      this.screenUrl =
        window.location.protocol +
        window.location.host +
        "/#/screen?id=" +
        this.$router.actId;
      this.isShowCopy = true;
      let that = this;
      setTimeout(() => {
        let v = document.getElementById("copyInput");
        v.select();
        document.execCommand("Copy");
        that.$message({ type: "success", message: "复制链接成功!" });
        setTimeout(() => {
          that.isShowCopy = false;
        }, 200);
      }, 200);
    },
    openPage() {
      //打开页面
      window.open(
        window.location.protocol + "/#/screen?id=" + this.$router.actId
      );
    },
    openLotteryPage() {
      //打开集体摇号页面
      window.open(
        `${process.env.lotteryUrl}?id=${this.$router.actId}`
      );
    },
    downloadImg(type) {
      //二维码下载
      this.downloadType = type;
      this.dialogVisible = true;
    },
    downloadItem(row) {
      this.loading = true;
      let params = {};
      if (this.downloadType == "qr") {
        params = {
          activityId: this.$router.actId,
          width: row.px
        };
      } else if (this.downloadType == "sign") {
        params = {
          activityId: this.$router.actId + "&sign=true",
          width: row.px
        };
      }
      buildQrCode(params).then(res => {
        if (res.code == 200) {
          let that = this;
          downloadQrCode(params);
          setTimeout(() => {
            that.loading = false;
          }, 500);
        }
      });
    },
    getProjectInfo() {
      if (this.$router.actId) {
        projectInfo({ activityId: this.$router.actId }).then(res => {
          if (res.code == 200) {
            this.projectInfo = res.data || {};
          }
        });
        getOpenActivityDetail({ id: this.$router.actId }).then(res => {
          if (res.code == 200) {
            console.log("活动详情", res);
            this.qrUrl = res.data.qrUrl;
            this.signInUrl = res.data.signInUrl;
          }
        });
      }
    },
    getActivityStatus() {
      if (this.$router.actId)
        activityStatus({ activityId: this.$router.actId }).then(res => {
          if (res.code == 200) {
            this.activityStatus = res.data;
            this.globalMaskText =
              this.activityStatus.activityStatusName == "进行中"
                ? "正在关闭活动"
                : "正在发布活动";
          }
        });
    },
    goPage(judge, path) {
      if (judge != "y") this.$router.push(path);
    },
    goPublic() {
      if (
        this.activityStatus.activityManageStatus == "y" &&
        this.activityStatus.houseTypeStatus == "y" &&
        this.activityStatus.housingResourcesStatus == "y" &&
        this.activityStatus.customerStatus == "y" &&
        this.activityStatus.saleStatus == "y"
      ) {
        this.globalLoading = true;
        if (
          this.activityStatus.activityStatusCode == "draft" ||
          this.activityStatus.activityStatusCode == "running" ||
          this.activityStatus.activityStatusCode == "close"
        ) {
          let a = "running",
            b = "进行中",
            c = "发布",
            nowTime = new Date();
          console.log(nowTime);
          if (this.activityStatus.activityStatusCode == "running") {
            a = "close";
            b = "已关闭";
            c = "关闭";
          }
          if (this.activityStatus.activityStatusCode == "close") {
            if (
              this.activityStatus.simulationStart != "" &&
              (new Date(this.activityStatus.simulationStart).getTime() -
                new Date().getTime()) /
                1000 /
                60 /
                60 >
                0.05
            ) {
            } else if (
              (new Date(this.activityStatus.formalStart).getTime() -
                new Date().getTime()) /
                1000 /
                60 /
                60 >
              0.05
            ) {
            } else {
              this.$message.warning(
                "当前时间距离开盘时间小于3分钟，不能发布活动！"
              );
              return false;
            }
          }
          openActivity({ id: this.$router.actId, status: a, statusName: b })
            .then(res => {
              console.log(res);
              if (res.code == 200) {
                this.$message.success(c + "成功！");
                this.init();
                this.globalLoading = false;
              } else {
                this.$message.error(c + "失败！");
                this.globalLoading = false;
              }
            })
            .catch(error => {
              this.$message.error(c + "失败！");
              this.globalLoading = false;
            });
        }
      } else {
        this.$message.warning("请确保活动设置项均已完成");
      }
    }
  },
  created() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.th-home-page {
  font-size: 14px;
  display: flex;
  justify-content: space-between;

  .th-panel {
    flex: 1;
    height: 430px;
    min-width: 280px;
    line-height: 40px;
  }

  .th-panel:nth-child(2) {
    margin: 10px 10px 0 10px;
  }

  .opening-link {
    .opening-tCode {
      display: flex;
      justify-content: space-between;

      .opening-tCode--img {
        flex: 1;
        display: flex;
        flex-direction: column;
        .th-button,
        img {
          margin: 5px auto;
          width: 120px;
          flex: none;
        }
      }
    }
  }

  .opening-project {
    span {
      display: block;
      width: auto;
    }
  }

  .opening-setting {
    div {
      display: flex;
      justify-content: space-between;
      span:last-child {
        cursor: pointer;
      }
    }
  }
}
</style>
<style scoped>
.th-panel >>> .th-panel-content {
  padding: 5px 10px;
}
.icon-confirm {
  font-size: 16px;
  vertical-align: middle;
  color: green;
}
.icon-jinggao {
  font-size: 16px;
  vertical-align: middle;
  color: red;
}
.icon-iconxz {
  color: #e63f3c;
  cursor: pointer;
}
.pr50 {
  padding-right: 50px;
}
</style>
