<template>
  <div class="login-main">
    <div class="main-left">
      <span class="left-title">诚东资产管理有限公司</span>
      <div class="left-bottom-title">
        <div class="right-top-line" />
        <div class="right-projrct-name"> 一体化办公平台 </div>
        <div class="right-projrct-english" />
      </div>
    </div>
    <div class="main-right">
      <div class="right-top-logo">
        <img src="../../assets/img/login_logo.jpg" style="width: 35%;">
      </div>
      <div class="right-title">登录 | LOGIN</div>
      <div class="right-login-form">
        <el-tabs v-model="activeName" class="login-type">
          <el-tab-pane class="tabpane" label="密码登录" name="first">
            <el-form ref="ruleForm" class="login-form" :model="ruleForm" :rules="rules" label-width="0px">
              <el-form-item label="" prop="userAccount">
                <el-input v-model="ruleForm.userAccount" placeholder="请输入用户名" />
              </el-form-item>
              <el-form-item label="" prop="userPassword">
                <el-input v-model="ruleForm.userPassword" type="password" show-password placeholder="请输入密码" />
              </el-form-item>
              <!-- <el-form-item class="btn-area"> -->
              <el-button type="primary" class="login-submit" :loading="loading" @click="submitForm('ruleForm')">登
                录</el-button>
              <!-- <el-button @click="resetForm('ruleForm')">重置</el-button> -->
              <!-- </el-form-item> -->
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import { login, authLogin } from '@/api/login'
export default {
  data() {
    return {
      loading: false,
      activeName: 'first',
      ruleForm: {
        userAccount: '',
        userPassword: ''
      },
      rules: {
        userAccount: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        userPassword: [{ required: true, message: '请输入密码', trigger: 'blur' }]
      }
    }
  },
  mounted() { },
  methods: {
    submitForm(formName) {
      const self = this
      this.$refs[formName].validate((valid) => {
        if (valid) {
          self.login()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    login() {
      this.loading = true
      const self = this
      login(this.ruleForm)
        .then((res) => {
          const { code, data, message } = res
          if (code === 200) {
            // self.setCookie('busUserToken', data.busUserToken)
            sessionStorage.setItem('userInfo', data)
            sessionStorage.setItem('busUserToken', data.busUserToken)
            self.$router.push('/main')
            // authLogin({ accessCode, clientId }).then((data) => {
            //   if (data.code === 200) {
            //     const { query, hash } = self.$route
            //     const path = query.RetutnUrl ? hash.replace('#', '') : '/index'
            //     self.$router.push({
            //       path: path
            //     })
            //   }
            // })
          } else {
            this.$message.warning(message || '请求失败')
          }
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.login-main {
  width: 100vw;
  height: 100vh;
  min-width: 1024px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.main-left {
  width: 70%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
  box-sizing: border-box;
  background-position-x: 50%;
  background-position-y: 50%;
  background-size: cover;
  background-image: url('../../assets/img/login_bg.jpg');
}

.main-left>.left-title {
  color: rgb(0, 94, 187);
  font-weight: 600;
  font-size: 60px;
  position: relative;
  line-height: 90px;
  top: 50px;
  left: 50px;
}

.main-right>.right-top-logo {
  font-size: 12px;
  width: 100%;
  height: 30%;
  line-height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 120px !important;
  }
}

.main-right>.right-title {
  color: rgb(38, 65, 112);
  text-align: center;
  font-size: 30px;
  font-weight: 600;
  height: 60px;
  margin-bottom: 30px;
  margin-top: 2px;
}

.main-right>.right-login-form {
  width: 90%;
  margin: 39px auto 0
}

.main-right>.right-login-form>.login-type .el-tabs__header {
  padding: 0;
  position: relative;
  margin: 0 0 15px;
}

.main-right {
  width: 30%;
  height: 100%;
  background-color: #fff;
}

.el-tabs__header {
  padding: 0;
  position: relative;
  margin: 0 0 15px;
}

>.login-form {
  margin: 30px 0;

}

>.login-form .el-form-item {
  width: 100%;
  margin-bottom: 24px;
}

.el-form .el-form-item {
  height: 50px !important;
  border-radius: 4px;
  display: block;
  padding-right: 0;
  line-height: 24px;
}

.el-form .el-form-item .el-input__inner {
  height: 50px !important;
}

.btn-area .el-form-item__content {
  height: auto;
  padding-top: 10px;
  display: flex;
  justify-content: space-between;
}

.login-submit {
  color: #fff;
  background: rgb(30, 88, 188);
  border-color: rgb(30, 88, 188);
  display: block;
  margin: 40px auto 0;
  width: 100%;
  text-align: center;
  height: 50px !important;
  line-height: 0;
  border: 0px solid;
  border-radius: 4px;

  &.el-button--primary:hover {
    background: #1e58bc;
  }

  >>>span {
    line-height: 12px;
    margin-top: 2px;
    display: block;
    padding-top: 0
  }
}

.login-type {
  >>>#tab-first {
    color: #333;
    line-height: 0;
    font-size: 14px;
    border-bottom-color: #409eff;
    border-bottom-width: 2px;
  }

  >>>.el-tabs__header {
    margin-bottom: 15px
  }

  >>>.el-input__inner {
    height: 50px;
    line-height: 54px;
    font-size: 12px
  }
}

.login-main .main-left .left-bottom-title {
  text-align: right;
  overflow: hidden;
  position: relative;
  right: 50px;
  bottom: 50px;
}

.login-main .main-left .left-bottom-title .right-top-line {
  margin-left: auto;
  margin-right: 0;
  width: 160px;
  height: 5px;
  background-color: #ebeff2;
}

.login-main .main-left .left-bottom-title .right-projrct-name {
  font-size: 45px;
  color: #ebeff2;
  font-weight: 600;
  line-height: 1.5;
}

>>>.el-tabs__item {
  height: 20px;
}

>>>.el-tabs__active-bar {
  background-color: #409eff;
}

>>>.el-tabs__nav-wrap::after {
  display: none;
}
</style>
