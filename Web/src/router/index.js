import Vue from 'vue'
import Router from 'vue-router'

import {
  isAdmin,
  myActivityList,
  myActivityRole
} from "@/api/homePage.js";

import page from '@/page'
import NotFound from '@/components/error404'
import NoJurisdiction from '@/components/noJurisdiction'
import login from '@/page/login/index.vue'

import Screen from '@/screen'

import ActivityIndex from '@/page/activity'
import lotteryIndex from '@/page/lottery'
import AnalysisIndex from '@/page/analysis'
import CustIndex from '@/page/cust'
import HouseIndex from '@/page/house'
import MainIndex from '@/page/main'
import SaleIndex from '@/page/sale'
import SysIndex from '@/page/sys'

import ApartmentHeatIndex from '@/page/analysis/apartmentHeat'
import ACustIndex from '@/page/analysis/cust'
import HouseHeatIndex from '@/page/analysis/houseHeat'
import HouseHeatDetail from '@/page/analysis/houseHeat/detail'
import OpeningIndex from '@/page/analysis/opening'
import SignIndex from '@/page/analysis/sign'

import AdviserIndex from '@/page/cust/adviser'
import AdviserInfo from '@/page/cust/adviser/info'
import ListIndex from '@/page/cust/list'
import CustInfo from '@/page/cust/list/info'

import ApartmentIndex from '@/page/house/apartment'
import ApartmentInfo from '@/page/house/apartment/info'
import SourceIndex from '@/page/house/source'
import SourceInfo from '@/page/house/source/info'
import sourceWhiteList from '@/page/house/whiteList'
import batchManagement from '@/page/house/batchManagement'
import salesControlList from '@/page/house/salesControlList'

import AdviserSaleIndex from '@/page/sale/adviserSale'
import ApartmentSaleIndex from '@/page/sale/apartmentSale'
import FormalIndex from '@/page/sale/formal'
import OverviewIndex from '@/page/sale/overview'
import SimulationIndex from '@/page/sale/simulation'

import SysRoleIndex from '@/page/sys/role'
import SysRoleInfo from '@/page/sys/role/info'
import projectIndex from '@/page/sys/project'
import projectActivityIndex from '@/page/sys/project/activity'
import projectActivityInfo from '@/page/sys/project/activity/info'
import allActivityIndex from '@/page/sys/project/activity/allList'

Vue.use(Router)
const menus = [{
  path: 'sys',
  name: 'sys',
  coment: '系统管理',
  icon: 'iconfont icon-xitongguanli',
  isAdmin: true,
  component: SysIndex,
  children: [{
    path: 'project',
    name: 'project',
    coment: '项目活动配置',
    component: projectIndex
  }, {
    path: 'allActivity',
    name: 'allActivity',
    coment: '活动列表',
    component: allActivityIndex
  }, {
    path: 'pActivity',
    name: 'pActivity',
    coment: '项目活动列表',
    component: projectActivityIndex,
    isNotMenus: true
  }, {
    path: 'pActivityInfo',
    name: 'pActivityInfo',
    coment: '项目活动详情',
    component: projectActivityInfo,
    isNotMenus: true
  }, {
    path: 'sysRole',
    name: 'sysRole',
    coment: '角色配置',
    component: SysRoleIndex
  }, {
    path: 'sysRoleInfo',
    name: 'sysRoleInfo',
    coment: '角色详情',
    component: SysRoleInfo,
    isNotMenus: true
  }]
}, {
  path: 'main',
  name: 'main',
  coment: '首页',
  icon: 'iconfont icon-home',
  component: MainIndex,
  buttons: [{
    name: '复制链接',
    has: 'main_01'
  }, {
    name: '打开页面',
    has: 'main_02'
  }, {
    name: '小程序入口码',
    has: 'main_03'
  }, {
    name: '现场签到码',
    has: 'main_04'
  }, {
    name: '发布活动',
    has: 'main_05'
  }, {
    name: '关闭活动',
    has: 'main_06'
  }, {
    name: '摇号公示',
    has: 'main_07'
  }]
}, {
  path: 'activity',
  name: 'activity',
  coment: '活动管理',
  icon: 'iconfont icon-huodongguanli',
  component: ActivityIndex,
  buttons: [{
    name: '保存',
    has: 'activity_01'
  }, {
    name: '返回',
    has: 'activity_02'
  }]
}, {
  path: 'lottery',
  name: 'lottery',
  coment: '摇号管理',
  icon: 'iconfont icon-huodongguanli',
  component: lotteryIndex,
  buttons: [{
    name: '保存',
    has: 'lottery_01'
  }, {
    name: '返回',
    has: 'lottery_02'
  }]
}, {
  path: 'house',
  name: 'house',
  coment: '房源管理',
  icon: 'iconfont icon-fangyuanguanli',
  component: HouseIndex,
  children: [{
    path: 'apartment',
    name: 'apartment',
    coment: '配置户型',
    component: ApartmentIndex,
    buttons: [{
      name: '查询',
      has: 'apartment_01'
    }, {
      name: '重置',
      has: 'apartment_02'
    }, {
      name: '新增',
      has: 'apartment_03'
    }, {
      name: '删除',
      has: 'apartment_04'
    }, {
      name: '修改',
      has: 'apartment_05'
    }]
  }, {
    path: 'source',
    name: 'source',
    coment: '配置房源',
    component: SourceIndex,
    buttons: [{
      name: '查询',
      has: 'source_01'
    }, {
      name: '重置',
      has: 'source_02'
    }, {
      name: '下载模板',
      has: 'source_03'
    }, {
      name: '批量导入',
      has: 'source_04'
    },
      , {
      name: '导出',
      has: 'source_09'
    }, {
      //   name: '同步房源',
      //   has: 'source_05'
      // }, {
      name: '新增房源',
      has: 'source_06'
    }, {
      name: '修改房源信息',
      has: 'source_07'
    }, {
      name: '删除房源',
      has: 'source_08'
    }]
  },
  {
    path: 'salesControl',
    name: 'salesControl',
    coment: '销控列表',
    component: salesControlList,
    buttons: [{
      name: '查询',
      has: 'salesControl_01'
    }, {
      name: '重置',
      has: 'salesControl_02'
    }, {
      name: '销控',
      has: 'salesControl_03'
    }, {
      name: '取消销控',
      has: 'salesControl_04'
    }]
  },
  {
    path: 'whiteList',
    name: 'whiteList',
    coment: '房源白名单',
    component: sourceWhiteList,
    buttons: [{
      name: '查询',
      has: 'whiteList_01'
    }, {
      name: '重置',
      has: 'whiteList_02'
    }, {
      name: '导出',
      has: 'whiteList_03'
    }, {
      name: '下载模板',
      has: 'whiteList_04'
    }, {
      name: '批量导入',
      has: 'whiteList_05'
    }]
  },
  {
    path: 'batchManagement',
    name: 'batchManagement',
    coment: '批次管理',
    component: batchManagement,
    buttons: [{
      name: '查询',
      has: 'batchManagement_01'
    }, {
      name: '重置',
      has: 'batchManagement_02'
    }, {
      name: '新增',
      has: 'batchManagement_03'
    }, {
      name: '批量删除',
      has: 'batchManagement_04'
    }]
  },
  {
    path: 'apartmentInfo',
    name: 'apartmentInfo',
    coment: '配置户型详情',
    component: ApartmentInfo,
    isNotMenus: true
  }, {
    path: 'sourceInfo',
    name: 'sourceInfo',
    coment: '房源详情',
    component: SourceInfo,
    isNotMenus: true
  }]
}, {
  path: 'cust',
  name: 'cust',
  coment: '客户管理',
  icon: 'iconfont icon-kehuguanli',
  component: CustIndex,
  children: [{
    path: 'custList',
    name: 'custList',
    coment: '客户列表',
    component: ListIndex,
    buttons: [{
      name: '查询',
      has: 'custList_01'
    }, {
      name: '重置',
      has: 'custList_02'
    }, {
      name: '下载模板',
      has: 'custList_03'
    }, {
      name: '批量导入',
      has: 'custList_04'
    }, {
      //   name: '同步客户',
      //   has: 'custList_05'
      // }, {
      name: '新增客户',
      has: 'custList_06'
    }, {
      name: '修改客户信息',
      has: 'custList_07'
    }, {
      name: '删除客户',
      has: 'custList_08'
    }, {
      name: '房源白名单',
      has: 'custList_09'
    }]
  }, {
    path: 'adviser',
    name: 'adviser',
    coment: '置业顾问/营销经理',
    component: AdviserIndex,
    buttons: [{
      name: '查询',
      has: 'adviser_01'
    }, {
      name: '重置',
      has: 'adviser_02'
    }, {
      name: '下载模板',
      has: 'adviser_03'
    }, {
      name: '批量导入',
      has: 'adviser_04'
    }, {
      //   name: '同步数据',
      //   has: 'adviser_05'
      // }, {
      name: '新增人员',
      has: 'adviser_06'
    }, {
      name: '修改信息',
      has: 'adviser_07'
    }, {
      name: '删除人员',
      has: 'adviser_08'
    }]
  }, {
    path: 'custInfo',
    name: 'custInfo',
    coment: '客户详情',
    component: CustInfo,
    isNotMenus: true
  }, {
    path: 'adviserInfo',
    name: 'adviserInfo',
    coment: '置业顾问/营销经理详情',
    component: AdviserInfo,
    isNotMenus: true
  }]
}, {
  path: 'analysis',
  name: 'analysis',
  coment: '活动分析',
  icon: 'iconfont icon-huodongfenxi',
  component: AnalysisIndex,
  children: [{
    path: 'opening',
    name: 'opening',
    coment: '开盘数据',
    component: OpeningIndex
  }, {
    path: 'houseHeat',
    name: 'houseHeat',
    coment: '房源热度分析',
    component: HouseHeatIndex,
    buttons: [{
      name: '查询',
      has: 'houseHeat_01'
    }, {
      name: '重置',
      has: 'houseHeat_02'
    }, {
      name: '导出数据',
      has: 'houseHeat_03'
    }]
  }, {
    path: 'HouseHeatDetail',
    name: 'HouseHeatDetail',
    coment: '房源热度分析详情',
    component: HouseHeatDetail,
    isNotMenus: true,
    buttons: [{
      name: '导出数据',
      has: 'houseHeat_04'
    }]
  }, {
    path: 'apartmentHeat',
    name: 'apartmentHeat',
    coment: '户型热度分析',
    component: ApartmentHeatIndex,
    buttons: [{
      name: '查询',
      has: 'apartmentHeat_01'
    }, {
      name: '重置',
      has: 'apartmentHeat_02'
    }, {
      name: '导出数据',
      has: 'apartmentHeat_03'
    }]
  }, {
    path: 'cust',
    name: 'cust',
    coment: '客户分析',
    component: ACustIndex,
    buttons: [{
      name: '查询',
      has: 'cust_01'
    }, {
      name: '重置',
      has: 'cust_02'
    }, {
      name: '导出数据',
      has: 'cust_03'
    }]
  }, {
    path: 'sign',
    name: 'sign',
    coment: '签到统计',
    component: SignIndex,
    buttons: [{
      name: '查询',
      has: 'sign_01'
    }, {
      name: '重置',
      has: 'sign_02'
    }, {
      name: '导出数据',
      has: 'sign_03'
    }]
  }]
}, {
  path: 'sale',
  name: 'sale',
  coment: '销售管理',
  icon: 'iconfont icon-xiaoshouguanli',
  component: SaleIndex,
  children: [{
    path: 'overview',
    name: 'overview',
    coment: '销售数据总览',
    component: OverviewIndex
  }, {
    path: 'apartmentSale',
    name: 'apartmentSale',
    coment: '户型销售数据',
    component: ApartmentSaleIndex,
    buttons: [{
      name: '导出数据',
      has: 'apartmentSale_01'
    }]
  }, {
    path: 'adviserSale',
    name: 'adviserSale',
    coment: '置业顾问销售数据',
    component: AdviserSaleIndex,
    buttons: [{
      name: '查询',
      has: 'adviserSale_01'
    }, {
      name: '重置',
      has: 'adviserSale_02'
    }, {
      name: '导出数据',
      has: 'adviserSale_03'
    }]
  }, {
    path: 'formal',
    name: 'formal',
    coment: '正式订单',
    component: FormalIndex,
    buttons: [{
      name: '查询',
      has: 'formal_01'
    }, {
      name: '重置',
      has: 'formal_02'
    }, {
      //   name: '下载模板',
      //   has: 'formal_03'
      // }, {
      name: '导入订单',
      has: 'formal_04'
    }, {
      name: '导出订单',
      has: 'formal_05'
    }, {
      name: '释放房源',
      has: 'formal_06'
    }]
  }, {
    path: 'simulation',
    name: 'simulation',
    coment: '模拟订单',
    component: SimulationIndex,
    buttons: [{
      name: '查询',
      has: 'simulation_01'
    }, {
      name: '重置',
      has: 'simulation_02'
    }, {
      name: '导出订单',
      has: 'simulation_03'
    }, {
      name: '释放房源',
      has: 'simulation_04'
    }]
  }]
}]

Vue.prototype.menus = menus;
const router = new Router({
  // mode: 'history',
  routes: [{
    path: '/',
    component: page,
    children: menus
  }, {
    path: '/screen',
    component: Screen
  }, {
    path: '/noJurisdiction',
    component: NoJurisdiction
  }, {
    path: '/login',
    component: login
  }, {
    path: '*',
    component: NotFound
  }],
  setRoles
})

export default router;

// 全局路由守卫
router.beforeEach((to, from, next) => {
  if (to.path.indexOf('noJurisdiction') >= 0 || to.path.indexOf('screen') >= 0 || to.path.indexOf('login') >= 0) {
    next();
    return;
  }
  if (!router.roleMenus || router.roleMenus.length == 0 || from.path == '/login') {
    getRoles(to, next);
  } else {
    toNext(to, next)
  }
});

function toNext(to, next) {
  //授权拦截
  if (to.path == '/' && router.roleMenus && router.roleMenus[0]) {

    // 跳转权限列表第一个菜单
    if (router.roleMenus[0].children && router.roleMenus[0].children.length > 0) {
      next({
        name: router.roleMenus[0].children[0].name
      });
      return;
    } else {
      next({
        name: router.roleMenus[0].name
      })
      return;
    }
  }
  // 权限过滤
  let _f = to.path.substring(1).split('/');
  if (router.roleMenus && _f.length > 0) {
    let _p = router.roleMenus.filter(e => e.name == _f[0])
    if (_p && _p.length == 1) {
      if (_f.length == 2) {
        let _pc = _p[0].children.filter(pe => pe.name == _f[1])
        if (_pc && _pc.length > 0) {
          next();
          return;
        }
      } else {
        next();
        return;
      }
    }
  }
  next('/noJurisdiction');
}

function getRoles(to, next) {
  router.actList = [];
  router.actId = "";
  myActivityList().then(res => {
    if (res.code == 200 && res.data && res.data.length > 0) {
      router.actList = res.data;
      router.actId = res.data[0].activityId;
    }
    isAdmin().then(res => {
      if (res.code == 200) {
        router.userName = res.data.userName;
        router.realName = res.data.realName;
        router.isAdmin = res.data.isAdmin == 1;
      }
      setRoles(to, next)
    })
  });
}

function setRoles(to, next) {
  let roleMenus = [];
  if (router.isAdmin)
    roleMenus = menus.filter(i => i.isAdmin);

  let _r = "";
  if (router.actId) {
    myActivityRole({
      activityId: router.actId
    }).then(res => {
      if (res.code == 200 && res.data && res.data.length > 0) {
        res.data.forEach(r => {
          // router.roleCode = r.roleCode;
          // router.roleName = r.roleName;
          if (r.authority)
            if (_r == "") {
              _r = r.authority;
            } else {
              let _s = r.authority.split(",");
              _s.forEach(s => {
                if (_r.indexOf(s) < 0) _r += "," + s;
              });
            }
        });
      }
      setInitRoles(roleMenus, _r, to, next);
    });
  } else {
    setInitRoles(roleMenus, _r, to, next);
  }
}

function setInitRoles(roleMenus, r, to, next) {
  r += ',' + r;
  let roleHas = "";
  menus.forEach((e, i) => {
    if (e.isAdmin) return; // 排除管理员
    if (r.indexOf(',' + (i + 101)) >= 0) {
      let c = JSON.parse(JSON.stringify(e));
      if (e.children) {
        c.children = []
        e.children.forEach((ec, ic) => {
          if (r.indexOf(',' + ((i + 101) * 100 + ic + 1)) >= 0) {
            let cc = JSON.parse(JSON.stringify(ec));
            if (ec.buttons) {
              ec.buttons.forEach((ecb, icb) => {
                let num = ((i + 101) * 100 + ic + 1) * 100 + icb + 1;
                if (r.indexOf(',' + num) >= 0) {
                  roleHas += ecb.has + ','
                }
              })
            }
            c.children.push(cc)
          }
        })
      }
      if (e.buttons) {
        e.buttons.forEach((eb, ib) => {
          if (r.indexOf(',' + ((i + 101) * 100 + ib + 1)) >= 0) {
            roleHas += eb.has + ','
          }
        })
      }
      roleMenus.push(c);
    }
  })
  router.roleHas = roleHas;
  router.roleMenus = roleMenus;
  if (to && next) {
    toNext(to, next);
  }
}

// 权限指令
Vue.directive('has', {
  inserted: function (el, binding) {
    if (!router.roleHas || router.roleHas.indexOf(binding.value) < 0) {
      el.parentNode.removeChild(el);
    }
  }
});
