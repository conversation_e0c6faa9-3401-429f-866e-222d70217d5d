<template>
  <div class="opening_screen" @dblclick="dialogVisible = true">
    <div class="select-info-title">
      <h2>{{baseData.opActivity.activityName}}在线选房公示</h2>
      <div class="page-control" v-if="!screenParams.isTiling && pageInfo.pageSize">
        <span>{{pageInfo.pageNum+'/'+pageInfo.pageSize}}页</span>
      </div>
      <div  hidden>
        可选<span style="border:solid 1px #DCDCDC;"></span>
        已选<span style="background-color:#E53636;"></span>
      </div>
    </div>
    <div class="activity-detail-container">
      <div class="screen_left" v-if="activityData.id">
        <screen :act="activityData" :params="screenParams" :pageSet="pageSet" :selected="baseData.houserIdsString||[]"
                @getPager="getPageInfo"></screen>
      </div>
      <div class="screen_right" v-if="activityData.id">
        <div class="right-info-container">
          <p class="title">{{countDownTitle}}</p>
          <count-down
            ref="countDown"
            v-if="countDownTitle != '活动已结束'"
            :endTime="endDate"
            :callback="callback"
            @getContent="getContent"
            class="count-down-container"></count-down>
          <p class="count-down-container" v-if="countDownTitle == '活动已结束'">
            活动已结束
          </p>
        </div>
        <div class="right-info-container"  hidden>
          <p class="title">当前成交金额</p>
          <p class="volume-container">
            <span style="font-size:26px;color:#E53636;font-weight:bold;">{{baseData.totalAcount}}</span>
            <span style="font-size:26px;color:#000000;font-weight:bold;">万元</span>
          </p>
        </div>
        <div class="right-info-container flex-row" style="height:70px;">
          <div class="flex-item">
            <div>可选套数</div>
            <div>{{baseData.optionalProperties}}</div>
          </div>
          <div class="flex-item">
            <div>参与人数</div>
            <div>{{baseData.todayLoginUserCount}}</div>
          </div>
        </div>
        <div class="right-info-container last-container">
          <p class="title">成交记录</p>
          <div class="sucess-info-container">
            <p class="success-info-item" v-for="(item,index) in baseData.oderList" :key="index">{{item}}</p>
          </div>
        </div>
      </div>
    </div>
    <div v-if="!activityData.id">活动信息查询失败，请检查！</div>
    <div class="activity-start-countdown-mask" v-if="isShowStartCountdown">
      <div class="mask-count-down-container">
        {{countContent.split(':')[2]}}
      </div>
    </div>
    <div class="activity-end-mask" v-if="isShowEndInfo"><!---->
      <div class="activity-end-content">
        <div class="success-end-tip">
          恭贺{{baseData.opActivity.activityName}}<br/>
          在线选房圆满成功
        </div>
        <div class="chengjiao-jine" hidden>
          <p>成交金额：{{baseData.totalAcount}}万元</p>
        </div>
        <div class="open-activity-info">
          <div>本次开盘时间：{{baseData.opActivity.formalStart | TimeFormat('yyyy-MM-dd hh:mm:ss')}}</div>
          <div>
            <span hidden>参与人数：{{baseData.todayLoginUserCount}}人</span>
            <span>成交套数：{{baseData.oderList?baseData.oderList.length:0}}套</span>
          </div>
        </div>
      </div>
    </div>
    <el-dialog :append-to-body="true" title="显示设置" :visible.sync="dialogVisible" width="30%" :show-close="false"
               top="30vh" :before-close="closeDialog">
      <div>
        <el-form label-position="right" label-width="125px">
          <el-form-item label="全屏切换:">
            <el-switch v-model="fullFlg" active-text="是" inactive-text="否" @change="handleFull()">
            </el-switch>
          </el-form-item>
          <el-form-item label="是否平铺:">
            <el-switch v-model="screenParams.isTiling" active-text="是" inactive-text="否" @change="handleFull()">
            </el-switch>
          </el-form-item>
          <el-form-item label="是否翻页:">
            <el-switch v-model="pageSet.isPage" active-text="是" inactive-text="否" @change="handleFull()">
            </el-switch>
          </el-form-item>
          <el-form-item label="显示行数:">
            <el-slider ref="crateControler" :min="10" :max="30" v-model="screenParams.rowNum" show-input></el-slider>
          </el-form-item>
          <el-form-item label="显示列数:">
            <el-slider ref="crateControler" :min="10" :max="screenParams.isTiling ? 20 : 50" v-model="screenParams.columnNum" show-input></el-slider>
          </el-form-item>
          <el-form-item label="滚动频率(秒):">
            <el-slider ref="drateControler" :min="1" :max="20" v-model="screenParams.timerRun" show-input></el-slider>
          </el-form-item>
          <el-form-item label="翻页频率(秒):">
            <el-slider ref="drateControler" :min="1" :max="60" v-model="pageSet.pageRun" show-input></el-slider>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
    import countDown from "../components/countDown";
    import Screen from "@/assets/js/comp/screen.js";
    import Msg from "./msg";

    import {activityDetails, getScreenBaseData, getDate, updateScreen} from "@/api/sys";

    export default {
        components: {
            Msg,
            Screen,
            countDown
        },
        filters: {
            TimeFormat: (dt,fmt,isBlank) => {
                if (dt) {
                    if (String(dt).indexOf('-') == 0) {
                        return '--'
                    }
                    let date = new Date(dt)
                    let o = {
                        "M+": date.getMonth() + 1, //月份
                        "d+": date.getDate(), //日
                        "h+": date.getHours(), //小时
                        "m+": date.getMinutes(), //分
                        "s+": date.getSeconds(), //秒
                        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
                        "S": date.getMilliseconds(), //毫秒
                    };
                    if (/(y+)/.test(fmt))
                        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
                    for (var k in o)
                        if (new RegExp("(" + k + ")").test(fmt))
                            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                    return fmt;
                } else {
                    if (isBlank) {
                        return '';
                    } else {
                        return '--';
                    }
                }
            }
        },
        data() {
            return {
                activityData: {},
                fullFlg: false,
                houseList: [],
                screenParams: {
                    rowNum: 20,
                    columnNum: 20,
                    timerRun: 3,
                    isTiling: false
                },
                pageSet: {
                    pageRun: 6,
                    isPage: true
                },
                baseData: {
                    opActivity: {
                        activityName: ""
                    }
                },
                countDownTitle: "",
                timmer: null,
                leftTime: 0, //剩余时间
                DD: "",
                HH: "00",
                mm: "00",
                ss: "00",
                selectedIds: "",
                isShowStartCountdown: false,
                flag:false,
                isShowEndInfo: false,
                dialogVisible: false,
                pageInfo: {
                    pageNum: null,
                    pageSize: null,
                },
                endDate: null,
                nowDate: null,
                timePoint: null,
                countContent: 0
            };
        },
        watch: {
            countDownTitle(val) {
                if (val && val == '距离正式选房开始') {
                    this.isShowEndInfo = false;
                }
            },
            countContent(val) {
                const arr = val.split(':')
                const _d = arr[arr.length - 4] * 1000 * 60 * 60 * 24 || 0
                const _h = arr[arr.length - 3] * 1000 * 60 * 60 || 0
                const _m = arr[arr.length - 2] * 1000 * 60 || 0
                const _s = arr[arr.length - 1] * 1000 || 0
                const time = _d + _h + _m + _s
                if (this.countDownTitle.indexOf('开始') != -1) {
                    console.log(this.countDownTitle, 'isShowStartCountdown')
                    if (val == '00:00:10' || time < 10000) {
                        this.isShowStartCountdown = true
                        if (time < 1000) {
                            this.isShowStartCountdown = false
                            this.countDownTitle == this.countDownTitle.replace('开始', '结束')
                            this.flag = true
                        }
                    }
                } else if ((time < 1000 && this.countDownTitle == '距离正式选房结束') || this.baseData.optionalProperties == 0) {
                    console.log('活动已结束countContent')
                    if(!this.flag){
                        this.countDownTitle = '活动已结束';
                        this.isShowEndInfo = true;
                    }

                }
            },
            // ss(val) {
            //     if (val == '00' && this.DD == '' && this.HH == '00' && this.mm == '00' && this.countDownTitle == '距离选房结束') {
            //         this.countDownTitle = '活动已结束';
            //         this.isShowEndInfo = true;
            //     }
            // }
        },
        methods: {
            callback() {
                if (this.countDownTitle == '活动已结束') {
                    this.isShowEndInfo = true;
                }
            },
            handleFull() {
                if (this.fullFlg) this.inFullCreeen();
                else this.outFullCreeen();

                if(this.screenParams.isTiling){
                  this.screenParams.columnNum = 20
                }
            },
            closeDialog() {
                this.dialogVisible = false
                const str = JSON.stringify(Object.assign(
                    {...this.pageSet},
                    {...this.screenParams},
                ))
                updateScreen({activityId:this.activityData.id,screenSetStr:str}).then(res=>{
                    console.log(res)
                })
            },
            getPageInfo(info) {
                this.pageInfo = info;
            },
            inFullCreeen() {
                let el = document.documentElement;
                let rfs =
                    el.requestFullScreen ||
                    el.webkitRequestFullScreen ||
                    el.mozRequestFullScreen ||
                    el.msRequestFullScreen;
                if (typeof rfs != "undefined" && rfs) {
                    rfs.call(el);
                } else if (typeof window.ActiveXObject != "undefined") {
                    let wscript = new ActiveXObject("WScript.Shell");
                    if (wscript != null) {
                        wscript.SendKeys("{F11}");
                    }
                }
            },
            outFullCreeen() {
                let el = document;
                let cfs =
                    el.cancelFullScreen ||
                    el.webkitCancelFullScreen ||
                    el.mozCancelFullScreen ||
                    el.exitFullScreen;
                if (typeof cfs != "undefined" && cfs) {
                    cfs.call(el);
                } else if (typeof window.ActiveXObject != "undefined") {
                    let wscript = new ActiveXObject("WScript.Shell");
                    if (wscript != null) {
                        wscript.SendKeys("{F11}");
                    }
                }
            },
            getContent(val) {
                // console.log(val,'watch')
                this.countContent = val
                if (!val || val=='00:00:00') {
                    this.getBaseData()
                }
            },
            getBaseData() {
                if (!this.baseData) return
                getDate().then(res => {
                    let oa = this.baseData.opActivity || {};
                    this.nowDate = Number(res.data.sysDate);
                    this.timePoint = JSON.parse(JSON.stringify(this.nowDate))
                    let ss = new Date(
                        oa.simulationStart || "1970-01-01 00:00:00"
                    ).getTime();
                    let se = new Date(oa.simulationEnd || "1970-01-01 00:00:00").getTime();
                    let fs = new Date(oa.formalStart || "1970-01-01 00:00:00").getTime();
                    let fe = new Date(oa.formalEnd || "1970-01-01 00:00:00").getTime();

                    console.log(fs,fe,'获取时间')
                    if (!oa.simulationStart && !oa.simulationEnd && !oa.formalStart && !oa.formalEnd) {
                      // 没有配置时不重新调用getDate，
                    } else if (oa.simulationStart || oa.simulationEnd || oa.formalStart || oa.formalEnd) {
                        if (this.nowDate + 1000 < ss) {
                            //模拟选房未开始
                            this.countDownTitle = "距离模拟选房开始";
                            // console.log(this.countDownTitle, 'countDownTitle')
                            this.endDate = String(ss/1000)
                        } else if (this.nowDate + 1000 < se) {
                            //模拟选房中
                            this.countDownTitle = "距离模拟选房结束";
                            this.isShowStartCountdown = false; //关闭倒计时蒙版
                            this.endDate = String(se/1000)
                        } else if (this.nowDate + 1000 < fs) {
                            //正式选房未开始
                            this.countDownTitle = "距离正式选房开始";
                            console.log(this.countDownTitle, 'countDownTitle')
                            this.endDate = String(fs/1000)
                        } else if (this.nowDate + 1000 < fe) {
                            //正式选房中
                            this.countDownTitle = "距离正式选房结束";
                            this.isShowStartCountdown = false; //关闭倒计时蒙版
                            this.endDate = String(fe/1000)
                        } else {
                            console.log('无选房活动')
                            //无选房活动
                            this.isShowEndInfo = true; //开启活动结束蒙版
                            this.countDownTitle = "活动已结束";
                            console.log(this.countDownTitle, 'countDownTitle')
                        }
                    } else {
                        this.getBaseData()
                    }
                });

                if (!this.baseJY) {
                    let _this = this;
                    this.baseJY = setTimeout(() => {
                        _this.getBaseData();
                    }, 180000);
                }
            },
            webSocket() {
                var _this = this;
                var socket;
                if (typeof WebSocket == "undefined") {
                    console.log("您的浏览器不支持WebSocket");
                } else {
                    console.log("您的浏览器支持WebSocket");
                    //实现化WebSocket对象，指定要连接的服务器地址与端口  建立连接
                    var url = "wss://open.sky-dome.com.cn/opening/websocket/" + this.activityData.id;
                    // var url = "wss://kp.tzdc.com/opening/websocket/" + this.activityData.id;
                    socket = new WebSocket(url);
                    //打开事件
                    socket.onopen = function () {
                        console.log("Socket 已打开");
                        heartCheck.reset().start();    // 如果获取到消息，说明连接是正常的，重置心跳检测
                    };
                    //获得消息事件
                    socket.onmessage = function (msg) {
                        console.log(msg);
                        heartCheck.reset().start();    // 如果获取到消息，说明连接是正常的，重置心跳检测
                        if (msg.data != "连接成功" && msg.data != "ping" ) {
                            let d = JSON.parse(msg.data);
                            _this.baseData = d.data;
                            console.log(d.data,'socket')
                            const screenSet = d.data.opActivity.screenSetStr && JSON.parse(d.data.opActivity.screenSetStr)
                            if (screenSet) {
                                _this.screenParams.rowNum = screenSet.rowNum
                                _this.screenParams.columnNum = screenSet.columnNum
                                _this.screenParams.timerRun = screenSet.timerRun
                                _this.screenParams.isTiling = screenSet.isTiling
                                _this.pageSet.pageRun = screenSet.pageRun
                                _this.pageSet.isPage = screenSet.isPage
                            }
                            // _this.baseData.opActivity.simulationStart = '2023-05-15 10:00:00';
                            // _this.baseData.opActivity.simulationEnd = '2023-05-15 10:01:00';
                            // _this.baseData.opActivity.formalStart = '2023-05-15 10:02:00';
                            // _this.baseData.opActivity.formalEnd = '2023-05-15 10:03:00';
                            // console.log(d.data.optionalProperties)
                            _this.baseData.houserIdsString = d.data.houserIdsString ? d.data.houserIdsString.split(',') : []
                            // if (d.data.optionalProperties == 0) {
                            //     _this.countDownTitle == '活动已结束';
                            //     _this.isShowEndInfo = true;
                            // }
                            console.log(_this.baseJY)
                            if (!_this.baseJY) _this.getBaseData();
                        }
                    };
                    //关闭事件
                    socket.onclose = function () {
                        console.log("Socket已关闭");
                    };
                    //发生了错误事件
                    socket.onerror = function () {
                        alert("Socket发生了错误");
                    };


                    // 心跳检测, 每隔一段时间检测连接状态，如果处于连接中，就向server端主动发送消息，来重置server端与客户端的最大连接时间，如果已经断开了，发起重连。
                    var heartCheck = {
                        timeout: 55000,        // 9分钟发一次心跳，比server端设置的连接时间稍微小一点，在接近断开的情况下以通信的方式去重置连接时间。
                        serverTimeoutObj: null,
                        reset: function() {
                            console.log("-------开始重置----------")
                            clearInterval(this.serverTimeoutObj);
                            return this;
                        },
                        start: function() {
                            console.log("-------开始发送----------")
                            var self = this;
                            this.serverTimeoutObj = setInterval(function() {
                                if(socket.readyState == 1){
                                    console.log("连接状态，发送消息保持连接");
                                    socket.send("ping");
                                    // heartCheck.reset().start();    // 如果获取到消息，说明连接是正常的，重置心跳检测
                                } else {
                                    console.log("断开状态，尝试重连");
                                    webSocket();
                                }
                            }, this.timeout)
                        }
                    }
                }
            }
        },

        created() {
            this.activityData.id = this.$route.query.id;
            if (this.activityData.id) {
                this.webSocket();
            }
        }
    };
</script>

<style lang="scss" scoped>
  .opening_screen {
    width: calc(100vw - 40px);
    height: 100vh;
    padding: 0 20px;
    overflow: hidden;
    background-color: #0428ba;
    //display: flex;
    // > div {
    //   padding: 20px;
    // }
    .activity-detail-container {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .screen_left {
        padding: 10px 10px;
        border: solid 1px rgba(189, 189, 245, 0.5);
        flex: 1;
        height: calc(100vh - 100px);
        overflow-y: auto;
      }
      .screen_left::-webkit-scrollbar{
        width:3px;
        height:8px;
        background-color: #0664f8;
      }
      .screen_left::-webkit-scrollbar-track{
        background: #6188eb;
      }
      .screen_left::-webkit-scrollbar-thumb{
        background: #0664f8;
        border-radius:100px;
      }
      .screen_left::-webkit-scrollbar-thumb:hover{
        background: #61B6EB;
      }
      .screen_left::-webkit-scrollbar-corner{
        background: #61B6EB;
      }
      .screen_right {
        margin-left: 10px;

        .flex-row {
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          align-items: center;

          .flex-item {
            flex: 1;
            p {
              text-align: center;
              font-size: 16px;
              color: #000000;
              font-weight: bold;
            }

            div {
              color: #fff;
              font-size: 22px;
              font-weight: bold;
              margin-top: 5px;
              text-align: center;
            }
          }
        }

        .right-info-container {
          width: 320px;
          height: 96px;
          border: solid 1px rgba(189,189,245,0.5);
          margin-bottom: 10px;

          .title {
            margin-top: 10px;
            text-indent: 20px;
            font-size: 24px;
            font-family: "MicrosoftYaHeiUI-Bold";
            color: #fff;
            font-weight: bold;
          }

          .count-down-container {
            font-size: 32px;
            color: #e53636;
            text-indent: 20px;
            font-weight: bold;
          }

          .volume-container {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            margin-top: 10px;
          }

          .sucess-info-container {
            padding: 0 20px;
            margin-top: 10px;

            p {
              font-size: 12px;
              color: #fff;
              margin-top: 10px;
            }
          }
        }

        .last-container {
          height: calc(100% - 190px) !important;
        }

        //background: bisque;
      }
    }
  }
</style>
<style lang="scss">
  @font-face {
    font-family: "MicrosoftYaHeiUI-Light";
    src: url(~@/assets/font-family/msyh.ttc); //你的资源目录
    font-weight: "Ultra-light";
    font-style: "Regular";
  }

  .select-info-title {
    background: url(~@/assets/img/title-bg.png) center no-repeat no-repeat;
    height: 60px;
    width: 100%;
    margin: 0 !important;
    padding: 0 !important;
    position: relative;

    .page-control {
      position: absolute;
      bottom: 10px;
      right: 535px;
      height: 30px;
      line-height: 30px;
      color: #000000;

      span {
        // width: 60px;
        height: 30px;
        text-align: center;
        display: inline-block;
        //border: 1px solid rgb(220, 220, 220);
        margin-right: 10px;
        // color:#ffffff;
        // background-color:#d0021b;
      }
    }

    .select-tip {
      position: absolute;
      bottom: 10px;
      right: 330px;
      width: 210px;
      height: 30px;
      line-height: 30px;
      //background-color:#ff6600;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      color: #000000;
      font-family: "MicrosoftYaHeiUI-Bold";

      span {
        display: inline-block;
        width: 66px;
        height: 30px;
      }
    }

    h2 {
      width: 100%;
      height: 40px;
      text-align: center;
      padding-top: 4px;
      font-size: 20px;
      color: #fff;
      font-family: "MicrosoftYaHeiUI-Bold";
    }
  }

  .th-screen {
    height: 100%;
    text-align: center;
    //background: #fff;
    border-radius: 5px;
    line-height: 30px;
    font-family: MicrosoftYaHei;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    letter-spacing: 0;
    display: flex;
    flex-direction: column;
    justify-content: left;

    .select-house-tbody {
      tr {
        //height:57px;
      }
    }

    .th-table__body {
      width: 100%;
      height: 100%;

      tr th,
      tr td {
        border: 1px solid #537df6;
        min-width: 30px;
        min-height: 30px;
      }

      .screen-rowhide {
        display: none;
      }

      .screen-selected {
        background: #d0021b;
        color: #fff;
      }

      .screen-disabled {
        //color: #ffffff;
        // background: rgba(0, 0, 0, 0.2);
        //font-size: 18px;
        border: 0px;
      }

      thead {
        background-color: #002edb;
        color: #ffffff;

        .building-name {
          font-size: 16px;
          font-family: "MicrosoftYaHeiUI-Bold";
        }

        .unit-name {
          font-size: 14px;
          font-family: "MicrosoftYaHeiUI-Light";
        }
      }
    }
  }

  .activity-end-mask {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .activity-end-content {
      padding: 20px;
      background: rgba(250,215,190,.8);
      border-radius: 20px;
      width: 40%;
      height: 40%;
      display: flex;
      justify-content: space-around;
      flex-direction: column;
      font-weight: bolder;
    }

    .success-end-tip {
      //width: 57.34vw;
      /*width: 1101px;*/
      /*height: 252px;*/
      //height: 13.13vw;
      color: #c00000;
      //font-size: 4.6875vw;
      font-size: 38px;
      line-height: 55px;
      text-align: center;
      margin-top: 40px;
    }

    .chengjiao-jine {
      //display: inline-block;
      //height: 3.542vw;
      /*height: 67px;*/
      //font-size: 2.5vw;
      font-size: 48px;
      /*background-color: #ffffff;*/
      border: 2px solid #c00000;
      color: #c00000;
      //border-radius: 2.89vw;
      border-radius: 55.5px;
      //padding: 0 1.77vw;
      padding: 0 35px;
      //line-height: 3.542vw;
      line-height: 74px;
      //margin-top: 3.125vw;
      margin-top: 20px;
      text-align: center;
      overflow: hidden;
      p{
        -webkit-box-reflect:below -35px -webkit-linear-gradient(transparent,transparent 20%,rgba(255,255,255,.3))
      }
    }

    .open-activity-info {
      //height: 5.73vw;
      /*height: 110px;*/
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      //margin-top: 8.4375vw;
      margin-top: 20px;

      div {
        //height: 2.6vw;
        height: 40px;
        //line-height: 2.6vw;
        line-height: 40px;
        color: #c00000;
        //font-size: 1.875vw;
        font-size: 24px;

        span:nth-child(1) {
          //margin-right: 2.7vw;
          margin-right: 52px;
        }
      }
    }
  }

  .activity-start-countdown-mask {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .mask-count-down-container {
      width: 100%;
      height: 11.77vw;
      font-size: 10.42vw;
      color: #e53636;
      text-align: center;
      line-height: 11.77vw;
      background-color: rgba(0, 0, 0, 0.5);
    }
  }

  .el-input__inner {
    height: 32px !important;
    line-height: 32px !important;
  }
</style>
