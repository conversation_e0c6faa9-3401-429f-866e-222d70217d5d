import Vue from 'vue'
import axios from 'axios'

const service = axios.create({
  timeout: 100000 // 请求超时时间
});

// 跨域拦截
service.defaults.withCredentials = true;
service.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';

// 请求拦截器
service.interceptors.request.use(function (config) {
  if (config.params) {
    config.params.IERandom = Date.parse(new Date()) / 1000;
  }
  // var busUserToken = document.cookie.replace(/(?:(?:^|.*;\s*)Cookie\s*\=\s*([^;]*).*$)|^.*$/, "$1");
  config.headers['bus-user-token'] = sessionStorage.getItem('busUserToken')

  return config;
}, function (error) {
  return Promise.reject(error);
});
// 响应拦截器
service.interceptors.response.use(
  response => {
    if (
      response.headers['content-type'] ===
      'application/vnd.ms-excel;charset=UTF-8'
    ) {
      // 是否为导出
      return Promise.resolve(response)
    }
    if (response.data.code && response.data.code != 200) {
      Vue.prototype.$message({
        type: "error",
        message: response.data.msg || response.data.message
      });
      return
    }
    return response.data;
  },
  err => {
    isSSO(err);
    if (err.response) {
      switch (err.response.status) {
        case 400:
          err.message = '请求错误'
          break
        case 401:
          err.message = '未授权，请登录'
          break
        case 403:
          err.message = '拒绝访问'
          break
        case 404:
          err.message = `请求地址出错: ${err.response.config.url}`
          break
        case 408:
          err.message = '请求超时'
          break
        case 500:
          err.message = '服务器内部错误'
          break
        case 50010:
          err.message = '非法参数，请检查参数信息'
          break
        case 501:
          err.message = '服务未实现'
          break
        case 502:
          err.message = '网关错误'
          break
        case 503:
          err.message = '服务不可用'
          break
        case 504:
          err.message = '网关超时'
          break
        case 505:
          err.message = 'HTTP版本不受支持'
          break
        default:
      }
    }

    Vue.prototype.$message({
      type: "error",
      message: err.message
    });
    return Promise.reject(err);
  }
);

const api = '/opening/webapi';
const appApi = "/opening/app";

export default {
  service,
  api,
  appApi
}

import Router from '@/router/index'
function isSSO(err) {
  if (err.response.status == 401) {
    Router.push({ path: "/login" });
  }
}