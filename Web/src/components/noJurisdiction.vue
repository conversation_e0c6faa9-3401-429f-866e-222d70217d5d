<template>
  <div class="no-jurisdiction">
    <span class="text1">您无权限访问此页面</span>
    <div class="text2">如有需要，请联系管理员</div>
    <span class="text3" @click="back">返回</span>
    <img class="pic2" :src="require('@/assets/img/quanxian.png')" alt="" />
  </div>
</template>

<script>
export default {
  methods: {
    back() {
      let link = window.location.origin + "/opening/#/login";
      if (location.hostname.indexOf("demo") > 0) {
        window.location.href = link;
        // 'http://ucsso.tzdc.com:9988/logout?sysId=WXKEFU&ReturnURL=' + location.href.split("#")[0];
      } else {
        window.location.href = link;
        // 'http://ucsso.tzdc.com:9988/logout?sysId=WXKEFU&ReturnURL=' + location.href.split("#")[0];
      }
    }
  }
};
</script>

<style scoped>
.no-jurisdiction {
  margin-left: 33%;
  margin-top: 10%;
  position: relative;
}
.pic2 {
  width: 322px;
  height: 263px;
  position: absolute;
  top: 90px;
  left: 106px;
}
.text1 {
  /* border: 1px solid #979797; */
  display: inline-block;
  font-family: MicrosoftYaHei-Bold;
  font-size: 18px;
  font-weight: bold;
  color: #e63f3c;
  letter-spacing: -0.87px;
  border-bottom: 3px solid #000;
  padding: 10px 0 10px 0;
}
.text2 {
  font-size: 14px;
  color: #434040;
  letter-spacing: -0.12px;
  margin-top: 10px;
}
.text3 {
  display: inline-block;
  width: 95px;
  height: 22px;
  border-radius: 100px;
  background-color: #e63f3c;
  color: #fff;
  text-align: center;
  line-height: 22px;
  margin-top: 10px;
  cursor: pointer;
}
</style>
