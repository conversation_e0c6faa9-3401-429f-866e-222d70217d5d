<template>
    <div class="map-main-container">
        <div class="BMap-container" id="BMapContainer">
            
        </div>
        <div class="search-container">
            <input v-model="typeinPosition" id="suggestId" size="20" type="text" class="typein-input">
            <span @click="search">搜索</span>
        </div>
    </div>
</template>
<script>
export default {
    name:'b-map',
    props:[
        'range',
        'displayPoint'
    ],
    watch:{
        range(val){
            this.signArea = val;
            if(this.point){
                this.changeRange(val);
            }
        },
        point(val){
            this.$emit('signRangeEvent',val);
        },
        displayPoint(val){
            this.lng = val.split(',')[0]
            this.lat = val.split(',')[1]
        }
    },
    data(){
        return{
            map:null,
            lng:Number,
            lat:Number,
            point:null,
            marker:null,
            ac:null,
            circle:null,
            typeinPosition:'',
            signArea:0,
        }
    },
    methods:{
        rendBMap(){
            let that = this;
            this.map = new BMap.Map("BMapContainer");              
            this.map.centerAndZoom("北京", 12);            
            this.map.enableScrollWheelZoom(true)
            this.ac = new BMap.Autocomplete({
                "input" : "suggestId",
                "location" : that.map
            })
            if(this.point){   
                this.map.centerAndZoom(that.point, 19);  
                this.marker = new BMap.Marker(that.point);
                this.circle = new BMap.Circle(that.point,that.signArea,{fillColor:"blue", strokeWeight: 1 ,fillOpacity: 0.3, strokeOpacity: 0.3})
                that.map.addOverlay(that.circle)         // 创建标注    
                that.map.addOverlay(that.marker);   
            }
            this.ac.addEventListener("onconfirm",function(e){
                console.log(e);
                if(that.marker){
                    that.marker.remove();
                }
                if(that.circle){
                    that.circle.remove()
                }
                let _value = e.item.value;
                let myVal = _value.province + _value.city + _value.district + _value.street + _value.business;
                that.setPlace(myVal);
            })
            this.map.addControl(new BMap.NavigationControl());   
            this.map.addEventListener("click", function(e){ 
                console.log(e);   
                //console.log(e.point.lng + ", " + e.point.lat);
                if(that.marker){
                    that.marker.remove();
                }
                if(that.circle){
                    that.circle.remove()
                }
                that.lng = e.point.lng;
                that.lat = e.point.lat; 
                that.point = new BMap.Point(that.lng, that.lat);    
                that.map.centerAndZoom(that.point, 24);  
                that.marker = new BMap.Marker(that.point);
                that.circle = new BMap.Circle(that.point,that.signArea,{fillColor:"blue", strokeWeight: 1 ,fillOpacity: 0.3, strokeOpacity: 0.3})
                that.map.addOverlay(that.circle)         // 创建标注    
                that.map.addOverlay(that.marker);   
            });
            this.map.addEventListener("zoomend", function(e){
                that.map.centerAndZoom(that.point,that.map.getZoom()); 
            });
        },
        setPlace(val){
            let that =this;
            this.map.clearOverlays();
            function myfn(){
                that.point = local.getResults().getPoi(0).point;
                console.log('point',that.point);
                that.map.centerAndZoom(that.point, 24);
                that.marker = new BMap.Marker(that.point);
                that.circle = new BMap.Circle(that.point,that.signArea,{fillColor:"blue", strokeWeight: 1 ,fillOpacity: 0.3, strokeOpacity: 0.3})
                that.map.addOverlay(that.circle) 
                that.map.addOverlay(that.marker); 
            }
            let local = new BMap.LocalSearch(that.map,{
                onSearchComplete:myfn
            });
            local.search(val);
        },
        changeRange(val){
            let that = this;
            if(that.marker){
                that.marker.remove();
            }
            if(that.circle){
                that.circle.remove();
            }
            that.circle = new BMap.Circle(that.point,val,{fillColor:"blue", strokeWeight: 1 ,fillOpacity: 0.3, strokeOpacity: 0.3});
            that.marker = new BMap.Marker(that.point);
            that.map.addOverlay(that.circle) 
            that.map.addOverlay(that.marker);
        },
        search(){
            console.log(this.typeinPosition);
        }
    },
    created(){
        this.signArea = this.range;
        if(this.displayPoint){
            this.lng = this.displayPoint.split(',')[0]
            this.lat = this.displayPoint.split(',')[1]
            this.point = new BMap.Point(this.lng, this.lat);
        }
        //console.log(this.signArea);
    },
    mounted(){ 
        this.rendBMap();
    }

}
</script>
<style lang="scss" scoped="scoped">
.BMap-container{
   width:740px;
   height:500px;
   //background-color:#ff6600; 
}
.map-main-container{
    position:relative;
    width:740px;
    height:500px;
}
.search-container{
    position: absolute;
    width:250px;
    height:30px;
    top:20px;
    right:20px;
    display:flex;
    flex-direction: row;
    justify-content:space-between;
    align-items:center;
    input{
        width:200px;
        height:28px;
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
        outline:none;
        border:solid 1px #dadada;
        text-indent:10px;
        border-right:none;
    }
    input:focus{
        border:solid 1px #e63f3c;
        border-right:none;
    }
    span{
        display:inline-block;
        width:50px;
        height:30px;
        text-align:center;
        line-height:30px;
        background-color:#e63f3c;
        color:#ffffff;
        font-size:14px;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
    }
}
</style>
