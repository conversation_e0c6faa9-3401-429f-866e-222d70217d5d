<template>
    <div class="pager-container">
        <div class="page-item">
            <div class="left-container">
                显示第{{start}}到第{{end}}条记录，总共{{totalNum}}条记录&nbsp;&nbsp;&nbsp;每页显示
                <el-select class="page-size-select" v-model="thisPageSize" @change="changePageSize">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                条记录&nbsp;&nbsp;&nbsp;<span class="refresh-btn" @click="refreshHandle">刷新</span>
            </div>
            <div class="right-container">
                <el-pagination small layout="prev, pager, next" :current-page="thisPageNum" :page-size="thisPageSize" :total="totalNum" @refresh="refreshHandle" @current-change="changePageNum">
                </el-pagination>    
            </div>  
        </div>
    </div>
</template>
<script>
export default {
    name:'th-page',
    props:[
        'total',
        'pageNum',
        'pageSize',
    ],
    watch:{
        total(val){
            this.totalNum = val;
            this.judgeDataStatus()
        },
        pageSize(val){
            this.thisPageSize = val || 20;
        },
        pageNum(val){
            this.thisPageNum = val || 1;
        },
        thisPageNum(val){
            this.judgeDataStatus()
        },
        thisPageSize(val){
            this.judgeDataStatus()
        }
    },
    data(){
        return{
            options:[
                {label:10,value:10},
                {label:20,value:20},
                {label:30,value:30},
                {label:50,value:50},
            ],
            thisPageNum:1,
            thisPageSize:20,
            totalNum:0,
            start:1,
            end:20,
        }
    },
    methods:{
        changePageSize(val){
            console.log(val);
            this.thisPageSize = val;
            this.$emit('currentPageSize',val);
        },
        judgeDataStatus(){
            this.start = (Number(this.thisPageNum)-1)*Number(this.thisPageSize)+1;
            if(Number((this.thisPageNum)*Number(this.thisPageSize) < this.totalNum)){
                this.end = Number(this.thisPageNum)*Number(this.thisPageSize);
            }else{
                this.end = this.totalNum;
            }
        },
        changePageNum(val){
            console.log(val);
            this.thisPageNum = val;
            this.$emit('currentPageNum',val);
        },
        refreshHandle(){
            this.$emit('refreshEvent');
        }
    },
    created(){
        this.totalNum = this.total;
        this.thisPageNum = this.pageNum || 1;
        this.thisPageSize = this.pageSize || 20; 
    }
}
</script>
<style lang="scss" scoped="scoped">
.pager-container{
    width:100%;
}
.page-item{
    margin-top:10px;
    padding:0 30px;
    height:40px;
    line-height:28px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}
.left-container{
    font-size:14px;
    color:rgb(74,74,74);
    .el-select__caret.el-input__icon{
        line-height:28px!important;
    }
}
.page-size-select{
    width:70px;
    display:inline-block;
}
.refresh-btn{
    cursor:pointer;
    color:rgb(77, 131, 255);
}
</style>
