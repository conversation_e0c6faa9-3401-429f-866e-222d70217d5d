<template>
  <ul :class="['sale-menus',isCollapse?'sale-menu--collapse':'']">
    <router-link class="menu-child" :to="'/'+m.path" tag="li" v-for="(m,i) in $router.roleMenus" :key="i" :event="m.children && m.children.length >0?'':'click'">
      <div class="sale-menu-item">
        <i :class="['sale-menu-icon',m.icon || 'tahoe-icon icon-tubiao-duoxuanmoren']"></i>
        <div class="sale-menu-title" v-if="!isCollapse">
          <span>{{m.coment}}</span>
          <span>
            <i :class="['sale-menu-child',m.children && m.children.length>0?'el-icon-arrow-right':'']"></i>
          </span>
        </div>
      </div>
      <ul class="sale-menu-child__ul" v-if="m.children && m.children.length>0">
        <router-link :to="'/'+m.path+'/'+mc.path" tag="li" v-for="(mc,j) in m.children" :key="j" v-if="!mc.isNotMenus">
          <span>{{mc.coment}}</span>
        </router-link>
      </ul>
    </router-link>
  </ul>
</template>

<script>
export default {
  props: {
    isCollapse: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      childId: -1
    };
  },
  created() {
    this.roleMenus = this.$router.roleMenus;
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
ul.sale-menus {
  width: 56px;
  color: #4a4a4a;
  cursor: pointer;

  li {
    border: none;
    padding-left: 20px;
    height: 50px;
    line-height: 50px;
    display: flex;

    span {
      font-size: 14px;
    }
    .sale-menu-item {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      .sale-menu-title {
        flex: 1;
        display: flex;
        justify-content: space-between;
      }

      .sale-menu-icon {
        margin-right: 20px;
        line-height: 50px;
      }
    }
    .sale-menu-child {
      padding-right: 20px;
    }
    .sale-menu-child__ul {
      display: none;
      position: absolute;
      left: 56px;
      z-index: 999;

      li {
        width: 150px;
        height: 40px;
        line-height: 40px;
        padding-left: 10px;
        background: #f5f5f5;
        color: #4a4a4a;
      }
      li:hover {
        background: #e6e6e6;
      }
      li.router-link-exact-active {
        color: red;
      }
    }
  }

  li.menu-child.router-link-active {
    .sale-menu-item {
      border-top: 2px solid rgb(240, 240, 240);
    }
    border-left: 3px solid red;
    color: red;
    background: #fff5f5;
  }

  li:hover {
    background: #f5f5f5;
    .sale-menu-child__ul {
      display: block;
    }
  }
}

ul.sale-menus:not(.sale-menu--collapse) {
  width: 240px;
  .sale-menu-child__ul {
    left: 240px;
  }
}
</style>
