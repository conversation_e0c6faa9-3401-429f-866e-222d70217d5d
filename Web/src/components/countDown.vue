<template>
  <span :endTime="endTime" :callback="callback" :endText="endText">
    <slot>
      <p>{{content}}</p>
    </slot>
  </span>
</template>
<script>
    import {getDate} from "@/api/sys";
    export default {
        data(){
            return {
                content: '',
                nowTime: null,
                timePoint: null,
                executeTime: 0,
                diff: 0
            }
        },
        props:{
            endTime:{
                type: String,
                default :''
            },
            endText:{
                type : String,
                default:''
            },
            callback : {
                type : Function,
                default :''
            }
        },
        watch: {
            endTime: {
              handler(val) {
                if (val) {
                    this.countdowm(this.endTime)
                }
              },
              immediate: true,
              deep:true
            },
            content: {
              handler(val){
                this.$emit('getContent', val)
              },
              deep:true,
              immediate: true
            }
        },
        mounted () {
            this.getDate()
        },
        methods: {
            getDate() {
              let _date = new Date().getTime()
              getDate().then(res => {
                this.nowTime = Number(res.data.sysDate)
                this.timePoint = JSON.stringify(this.nowTime);
                  // console.log(new Date(this.nowTime).getSeconds(), 'nowTime')
              })
            },
            countdowm(timestamp){
                console.log(timestamp,'tttt')
                let self = this;
                let timer = setInterval(function(){
                  // self.nowTime = new Date().getTime()
                    let endTime = new Date(timestamp * 1000);
                    self.diff = new Date().getTime() - self.executeTime
                    // console.log(self.executeTime,new Date().getTime(), 'ssss')
                    // console.log(self.diff,'diff')
                    self.nowTime = new Date(self.nowTime).getTime() + (self.executeTime ? self.diff : 1000)
                    let t = endTime.getTime() - self.nowTime;
                    if (self.nowTime-self.timePoint >= 10000) {
                      self.getDate()
                    }
                    if (t > 0) {
                        let day = Math.floor(t / 86400000);
                        let hour = Math.floor((t / 3600000) % 24);
                        let min = Math.floor((t / 60000) % 60);
                        let sec = Math.floor((t / 1000) % 60);
                        hour = hour < 10 ? "0" + hour : hour;
                        min = min < 10 ? "0" + min : min;
                        sec = sec < 10 ? "0" + sec : sec;
                        let format = '';
                        if (day > 0) {
                            format = `${day}:${hour}:${min}:${sec}`;
                        }
                        if (day <= 0) {
                            format = `${hour}:${min}:${sec}`;
                        }
                        // if(day <= 0 && hour <= 0){
                        //     format =`${min}:${sec}`;
                        // }
                        self.content = format;
                    } else {
                        clearInterval(timer);
                        self.content = self.endText;
                        self._callback();
                    }
                  self.executeTime = new Date().getTime()
                  // console.log(self.executeTime,'sss--')
                },1000);
            },
            _callback(){
                if(this.callback && this.callback instanceof Function){
                    this.callback(...this);
                }
            }
        }
    }
</script>
