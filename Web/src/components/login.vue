<template>
  <div class="login-page">
    <div class="login-box">
      <div class="login-form-box">
        <h3>登录系统</h3>
        <el-form>
          <el-form-item label="" prop="companyCode">
            <el-input placeholder="请输入公司主体" prefix-icon="el-icon-user-solid" v-model="companyCode" size="small"/>
          </el-form-item>
          <el-form-item label="" prop="userAccount">
            <el-input placeholder="请输入用户名" prefix-icon="el-icon-user-solid" v-model="userAccount" size="small"/>
          </el-form-item>
          <el-form-item label="" prop="userPassword">
            <el-input placeholder="请输入密码" prefix-icon="el-icon-lock" v-model="userPassword" type="password" size="small"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" style="width: 100%" @click="onSubmit" :loading="loading">登录</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
    // import {busLogin,getVerificationCode} from '@/api/approval'
    // import SIdentify from '@/components/identify'
    export default {
        components: {
            SIdentify
        },
        data(){
            return{
                loading: false,
                companyCode: '',
                userAccount: '',
                userPassword: '',
                verificationCode: '',
                identifyCode: ''
            }
        },
        methods:{
            refreshCode() {
                this.makeCode()
            },
            makeCode() {
                getVerificationCode().then(res=>{
                    this.identifyCode = res.data
                })
            },
            onSubmit() {
                if (!this.companyCode) return this.$message.warning('请输入公司主体')
                if (!this.userAccount) return this.$message.warning('请输入用户名')
                if (!this.userPassword) return this.$message.warning('请输入密码')
                if (!this.verificationCode) return this.$message.warning('请输入验证码')
                this.loading = true
                busLogin({
                    companyCode: this.companyCode,
                    userAccount: this.userAccount,
                    userPassword: this.userPassword,
                    verificationCode: this.verificationCode
                }).then(res => {
                    this.loading = false
                    if (res.code === 200) {
                        const _time = 2 * 60 * 60 * 1000
                        this.setCookie('busUserToken', res.data.busUserToken)
                        this.setCookie('userName', res.data.userName)
                        this.setCookie('companyId', res.data.companyId)
                        this.setCookie('userAccount', res.data.userAccount)
                        this.setCookie('allowOpenManageFlag', res.data.allowOpenManageFlag)
                        this.$router.push({
                            path: '/main/home'
                        })
                    } else {
                        this.makeCode()
                        this.$message.error(res.msg)
                    }
                }).catch(err => {
                    this.loading = false
                })
            }
        },
        created(){
            this.identifyCode = ''
            this.makeCode()
        }
    }
</script>

<style lang="scss" scoped>
  .login-page {
    width: 100%;
    height: 100%;
    .login-box {
      width: 100%;
      height: 100%;
      background-image: url('~@/assets/img/0001.jpg');
      background-size: cover;
      position: relative;
      overflow: hidden;
      .login-form-box {
        width: 340px;
        height: 450px;
        background: #ffffff;
        box-sizing: border-box;
        padding-left: 30px;
        padding-right: 30px;
        position: absolute;
        right: 100px;
        top: calc(50% - 250px);
        .el-form {
          .el-form-item {
            margin: 30px 0;
          }
        }
      }
    }
  }
</style>
