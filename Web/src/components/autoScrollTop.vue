<template>
  <div
    id="textScrollTop"
    class="text-scroll-top"
    @mousemove="inBox"
    @mouseleave="leaveBox"
  >
    <div
      v-for="i in 2"
      :id="'scrollItemTop' + i"
      :key="'scrollItem' + i"
      class="scroll-item"
      :style="{ display: i === 1 ? 'flex' : 'none' }"
    >
      <div style="width: 100%;">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    auto: {
      type: Boolean,
      default: true
    }
  },
  components: {},
  data() {
    return {
      startTimer: null,
      top: 0,
      textScrollDiv: null,
      timer: null,
      scrollItemHeight: 0,
      isScroll: false
    };
  },

  computed: {},
  destroyed() {
    clearInterval(this.timer);
    clearTimeout(this.startTimer);
    this.startTimer = null;
  },
  mounted() {
    this.startTimer = setTimeout(() => {
      if (this.auto) {
        const that = this;
        this.$nextTick(() => {
          that.textScrollDiv = document.querySelector("#textScrollTop");
          that.scrollItemHeight = document.querySelector(
            "#scrollItemTop1"
          ).clientHeight;
          const outerBoxHeight = that.textScrollDiv.clientHeight;
          if (outerBoxHeight < that.scrollItemHeight) {
            this.isScroll = true;
            that.textScrollDiv.style.height = `${that.scrollItemHeight * 2}px`;
            that.timer = setInterval(function() {
              that.moveTop();
            }, 50);
            document.querySelector("#scrollItemTop2").style.display = "flex";
          }
        });
      }
    }, 5000);
  },
  methods: {
    moveTop() {
      if (this.textScrollDiv) {
        this.top -= 1;
        if (Math.abs(this.top) > this.scrollItemHeight) {
          this.top = 0;
        }
        this.textScrollDiv.style.transform = `translate(0px,${this.top}px)`;
      }
    },
    // 鼠标划入区域
    inBox() {
      // if (this.isScroll) {
      //   clearInterval(this.timer);
      //   this.timer = null;
      // }
    },
    // 鼠标离开区域
    leaveBox() {
      // if (this.isScroll) {
      //   const that = this;
      //   this.timer = setInterval(function() {
      //     that.moveTop();
      //   }, 30);
      // }
    }
  }
};
</script>
<style lang="scss" scoped>
.text-scroll-top {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  transition: all 0ms ease-in 0s;

  .scroll-item {
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
  }
}
</style>
