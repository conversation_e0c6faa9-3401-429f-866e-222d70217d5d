<template>
  <div class="wcg_page">
    <span v-if="showSpan" class="demonstration" style="fontSize:14px;color:#4A4A4A">显示第 {{pageStart}} 到第 {{pageStop}} 条记录，总共 {{total}} 条记录
      <span v-if="recordControl" style="margin: 0 20px;">
        每页显示
        <el-select v-model="pageSize" :disabled="disabled" :class="[disabled?'disabled-button':'','wcg_page--select']">
          <el-option :value="10" />
          <el-option :value="20" />
          <el-option :value="30" />
          <el-option :value="50" />
        </el-select>条记录
      </span>
      <a v-if="isRefresh" href="#" :disabled="disabled" :class="{'disabled-button':disabled}" style="color:#4D83FF;fontSize:14px" @click="handlerRefresh()"> 刷新</a>
    </span>
    <el-pagination :disabled="disabled" @current-change="handlerCurrentChange" :pager-count="pagerCount" :page-size="pageSize" :current-page="pageNum" layout="prev, pager, next" :total="total">

    </el-pagination>
  </div>
</template>
<script>
export default {
  name: "wcg-page",
  props: {
    pagerCount: {
      type: Number,
      default: 7
    },
    total: {
      type: Number,
      default: 0
    },
    floorList: {
      type: Array,
      default: []
    },
    showSpan: {
      type: Boolean,
      default: true
    },
    recordControl: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isRefresh: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      pageSize: 20,
      pageNum: 1,
      pageStart: 0,
      pageStop: 0
    };
  },
  watch: {
    pageSize(v) {
      this.handlerRefresh(1, v);
    },
    floorList: {
      handler(v) {
        if (v.length > 0) {
          let s = (this.pageNum - 1) * this.pageSize;
          this.pageStart = s + 1;
          this.pageStop = s + v.length;
        } else {
          this.pageStart = 0;
          this.pageStop = 0;
        }
      }
    }
  },
  methods: {
    handlerCurrentChange(val) {
      this.handlerRefresh(val, this.pageSize);
    },
    handlerRefresh(pageNum, pageSize) {
      this.$emit("on-change", pageNum || this.pageNum, pageSize || this.pageSize);
    }
  }
};
</script>
<style>
.wcg_page {
  margin-top: 10px;
  padding: 0 30px;
  height: 40px;
  line-height: 28px;
  display: flex;
  justify-content: space-between;
}
.wcg_page .wcg_page--select {
  width: 70px;
  margin: 0px 5px;
}
.wcg_page .wcg_page--select .el-input__icon {
  line-height: 28px !important;
}
.demonstration .pagination-size {
  flex: 1;
}

/*不可用的确定按钮*/
.disabled-button,
.el-icon-more.disabled {
  cursor: not-allowed !important;
}
.min .el-pager li {
  min-width: 24px !important;
}
</style>

