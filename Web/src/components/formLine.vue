<template>
    <div class="formline-container">
        <div :class="isBorder?'form-line-item':'form-line-item border-none'">
            <span class="item-label">
                {{name}}
            </span>
            <span class="item-value">
                {{content}}{{uni}}
            </span>
        </div>
    </div>
</template>
<script>
export default {
    props:[
        'noborder',
        'label',
        'value',
        'unit'
    ],
    watch:{
        noborder(val){
            this.isBorder = !val;
        },
        label(val){
            this.name = val;
        },
        value(val){
            this.content = val;
        },
        unit(val){
            this.uni = val
        }
    },
    data(){
        return{
            isBorder:true,
            name:'',
            content:'',
            uni:''
        }
    },
    created(){
        this.isBorder = !this.noborder;
        this.name = this.label;
        this.content = this.value;
        this.uni = this.unit;
    }
}
</script>
<style lang="scss" scoped="scoped">
.formline-container{
    width:100%;
    .form-line-item{
        width:calc(100% - 25px);
        height:49px;
        border-top:solid 1px #E6E6E6;
        display:flex;
        flex-direction: row;
        justify-content:space-between;
        align-items:center;
        font-size:16px!important;
        margin-left:13px;
        .item-value{
            color:#9B9B9B;
            margin-right:15px;
        }
    }
    .border-none{
        border-top:none;
    }
}
</style>
