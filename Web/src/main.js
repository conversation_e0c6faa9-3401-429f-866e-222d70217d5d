// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import 'babel-polyfill'
import Vue from 'vue'
import App from './App'
import router from './router'
import ElementUI from 'element-ui'
import comp from '@/assets/js/comp.js'
import './assets/scss/base.scss';
import './assets/scss/reset.scss';
import './assets/main_font/iconfont.css'
import './assets/nav_font/iconfont.css'
import wcgPage from "@/components/wcgPage";
import pager from "@/components/pager"
import TreeTransfer from "ele-tree-transfer";
import '@/api/common.js'

// 暴力覆盖字体定义
const injectCDNFont = () => {
  const style = document.createElement('style')
  style.innerHTML = `
    @font-face {
      font-family: 'element-icons';
      src: url('https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/fonts/element-icons.woff') format('woff'),
           url('https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/fonts/element-icons.ttf') format('truetype');
    }
  `
  document.head.appendChild(style)
}

// 先注入字体再初始化 ElementUI
injectCDNFont()
Vue.use(ElementUI)
Vue.use(TreeTransfer);
comp.install(Vue);
Vue.config.productionTip = false
Vue.component(wcgPage.name, wcgPage);
Vue.component(pager.name, pager);

new Vue({
  el: '#app',
  router,
  components: {
    App
  },
  template: '<App/>'
})
