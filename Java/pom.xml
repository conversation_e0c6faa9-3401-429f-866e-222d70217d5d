<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.tahoecn</groupId>
	<artifactId>opening-service</artifactId>
	<version>1.0.1</version>
	<packaging>jar</packaging>

	<name>project</name>
	<description>project for Spring Boot</description>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.0.3.RELEASE</version>
	</parent>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
	</properties>

<!--	<repositories>-->
<!--		<repository>-->
<!--			<id>tahoe-cn</id>-->
<!--			<url>http://mvn.tahoecn.com/repository/maven-public</url>-->
<!--			<releases>-->
<!--				<enabled>true</enabled>-->
<!--			</releases>-->
<!--			<snapshots>-->
<!--				<enabled>true</enabled>-->
<!--			</snapshots>-->
<!--		</repository>-->
<!--	</repositories>-->

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<version>${parent.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<version>${parent.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.landray</groupId>
			<artifactId>EKP-SSO-client-java</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/src/main/resources/lib/EKP-SSO-client-java-1.0.jar</systemPath>
		</dependency>
		
		<dependency>
			<groupId>com.tahoecn</groupId>
			<artifactId>uc-sso</artifactId>
			<version>1.0.0-release</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/src/main/resources/lib/uc-sso-1.0.0-release.jar</systemPath>
		</dependency>
		
		<dependency>
		    <groupId>io.jsonwebtoken</groupId>
		    <artifactId>jjwt</artifactId>
		    <version>0.7.0</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-zipkin</artifactId>
			<version>2.0.1.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
			<version>${parent.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.session</groupId>
			<artifactId>spring-session-data-redis</artifactId>
			<version>${parent.version}</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.alibaba/druid-spring-boot-starter -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>1.1.10</version>
		</dependency>

		<!-- mybatis-plus -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>3.0.6</version>
		</dependency>

		<!-- mybatis-plus-datasource -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
			<version>2.5.0</version>
		</dependency>

		<!-- mybatis-plus-generator -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-generator</artifactId>
			<version>3.0.6</version>
		</dependency>

		<dependency>
			<groupId>org.freemarker</groupId>
			<artifactId>freemarker</artifactId>
			<version>2.3.28</version>
		</dependency>

		<!-- mybatis-plus-dependency-freemarker -->
		<!-- <dependency> <groupId>org.freemarker</groupId> <artifactId>freemarker</artifactId> 
			<version>2.3.28</version> </dependency> -->

		<!-- https://mvnrepository.com/artifact/mysql/mysql-connector-java -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.46</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/cn.afterturn/easypoi -->
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-base</artifactId>
			<version>3.2.0</version>
		</dependency>
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-annotation</artifactId>
			<version>3.2.0</version>
		</dependency>

		<!-- swagger2 -->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.8.0</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>2.8.0</version>
		</dependency>

		<!-- 泰禾公共服务包，包含com.fasterxml.jackson2.9.6;org.apache.httpcomponents4.5.6 -->
		<dependency>
			<groupId>com.tahoecn</groupId>
			<artifactId>pmo-tool-all</artifactId>
			<version>1.0.3</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/src/main/resources/lib/pmo-tool-all-1.0.3.jar</systemPath>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>19.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.6</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>2.0.9</version>
		</dependency>
		<!-- Hutool包引入，用于短信 -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>4.1.19</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-amqp</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>3.15</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>3.15</version>
		</dependency>
		<!--  类拷贝   -->
		<dependency>
			<groupId>net.rakugakibox.spring.boot</groupId>
			<artifactId>orika-spring-boot-starter</artifactId>
			<version>1.9.0</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-quartz</artifactId>
		</dependency>


		<!-- 阿里云短信服务核心 SDK -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>dysmsapi20170525</artifactId>
			<version>3.0.0</version>
		</dependency>

		<!-- 阿里云 OpenAPI 配置类依赖 -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>tea-openapi</artifactId>
			<version>0.3.6</version>
		</dependency>
	</dependencies>



	<profiles>
		<profile>
			<id>develop</id>
			<properties>
				<package.environment>develop</package.environment>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>released</id>
			<properties>
				<package.environment>released</package.environment>
			</properties>
		</profile>
		<profile>
			<id>production</id>
			<properties>
				<package.environment>production</package.environment>
			</properties>
		</profile>
	</profiles>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources/${package.environment}</directory>
				<filtering>true</filtering>
				<includes>
					<!--包含文件夹以及子文件夹下所有资源 -->
					<include>**/*.*</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources/templates</directory>
			</resource>
			<resource>
				<directory>src/main/java</directory>
				<filtering>false</filtering>
				<includes>
					<include>**/mapper/xml/*.xml</include>
				</includes>
			</resource>

		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<fork>true</fork>
					<addResources>true</addResources>
					<includeSystemScope>true</includeSystemScope>
				</configuration>
			</plugin>

			<!-- 跳过单元测试 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
		</plugins>
<!--		<defaultGoal>compile</defaultGoal>-->
	</build>

</project>
