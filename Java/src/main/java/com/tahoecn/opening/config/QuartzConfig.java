package com.tahoecn.opening.config;

import com.tahoecn.opening.quartz.QuartzJobListener;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.config
 * @Description:// TODO 定时任务监听器配置类
 * @Date: 2025/5/6 14:13
 * @Version: 1.0
 **/
@Configuration
public class QuartzConfig {

    @Autowired
    private Scheduler scheduler;

    @PostConstruct
    public void initListener() throws SchedulerException {
        // 全局监听器：仅在初始化时注册一次
        scheduler.getListenerManager().addJobListener(new QuartzJobListener());
    }
}
