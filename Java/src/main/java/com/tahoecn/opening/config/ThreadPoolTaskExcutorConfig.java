package com.tahoecn.opening.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: dzkp-java
 * @Package: com.tahoecn.opening.config
 * @ClassName: ThreadPoolTaskExcutorConfig
 * @Description:// TODO 多线程配置类
 * @Date: 2023/10/18 19:01
 * @Version: 1.0
 */
@Configuration
public class ThreadPoolTaskExcutorConfig {

    @Value("${spring.task.execution.pool.core-size}")
    private Integer corePoolSize;
    @Value("${spring.task.execution.pool.max-size}")
    private Integer maxSize;
    @Value("${spring.task.execution.pool.queue-capacity}")
    private Integer queue;
    @Value("${spring.task.execution.pool.keep-alive}")
    private Integer keepAlive;
    @Value("${spring.task.execution.thread-name-prefix}")
    private String threadNamePrefix;


    /**
     * <AUTHOR>  <EMAIL>
     * @date 19:06 2023/10/18
     * @return org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
     * @description // TODO 初始化线程池对象
     **/
    @Bean(value = "taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        int i = Runtime.getRuntime().availableProcessors();
        System.out.println("i = " + i);


        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        // 核心线程数
        taskExecutor.setCorePoolSize(corePoolSize);
        // 最大线程数
        taskExecutor.setMaxPoolSize(maxSize);
        // 队列容量大小
        taskExecutor.setQueueCapacity(queue);
        // 是否允许核心线程超时
        taskExecutor.setAllowCoreThreadTimeOut(false);
        // 线程保活时间
        taskExecutor.setKeepAliveSeconds(keepAlive);
        // 线程命名前缀规则
        taskExecutor.setThreadNamePrefix(threadNamePrefix);
        // 等待终止时间(秒)
        taskExecutor.setAwaitTerminationSeconds(10);
        /**
         * @see https://blog.csdn.net/pfnie/article/details/52755769
         * 线程池满了之后如何处理：默认是 new AbortPolicy();
         *
         * (1) ThreadPoolExecutor.AbortPolicy   处理程序遭到拒绝将抛出运行时RejectedExecutionException;
         * (2) ThreadPoolExecutor.CallerRunsPolicy 线程调用运行该任务的 execute 本身。此策略提供简单的反馈控制机制，能够减缓新任务的提交速度
         * (3) ThreadPoolExecutor.DiscardPolicy  不能执行的任务将被删除;
         * (4) ThreadPoolExecutor.DiscardOldestPolicy  如果执行程序尚未关闭，则位于工作队列头部的任务将被删除，然后重试执行程序（如果再次失败，则重复此过程）。
         */
        String rejectedExecutionHandler = "AbortPolicy";
        switch (rejectedExecutionHandler){
            case "AbortPolicy":
                taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
                break;
            case "CallerRunsPolicy":
                taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
                break;
            case "DiscardPolicy":
                taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
                break;
            case "DiscardOldestPolicy":
                taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
                break;
        }
        // 初始化线程池
        taskExecutor.initialize();
        return taskExecutor;
    }
}
