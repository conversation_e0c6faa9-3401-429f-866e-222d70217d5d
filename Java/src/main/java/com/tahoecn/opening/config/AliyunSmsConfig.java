package com.tahoecn.opening.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.config
 * @Description:// TODO 阿里云短信配置类
 * @Date: 2025/5/8 14:18
 * @Version: 1.0
 **/
@Configuration
@ConfigurationProperties(prefix = "aliyun")
public class AliyunSmsConfig {

    private String accessKeyId;

    private String accessKeySecret;

    private String signName;

    private Boolean openSms;

    private String specialId;

    private Map<String, String> templates;

    public static final String SIGN_NAME = "天宇正清";

    public static final String SYSTEM_NAME = "诚东选房系统";

    public static final String TEMP_CODE_ACTIVITY = "activity";

    public static final String TEMP_CODE_PERSON = "person";

    public static final String TEMP_CODE_OPEN_LOTTERY = "openLottery";

    public static final String TEMP_CODE_LOTTERY_RESULT = "lotteryResult";

    public static final String TEMP_CODE_VERIFY_CODE = "verifyCode";

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName;
    }

    public Boolean getOpenSms() {
        return openSms;
    }

    public void setOpenSms(Boolean openSms) {
        this.openSms = openSms;
    }

    public String getSpecialId() {
        return specialId;
    }

    public void setSpecialId(String specialId) {
        this.specialId = specialId;
    }

    public Map<String, String> getTemplates() {
        return templates;
    }

    public void setTemplates(Map<String, String> templates) {
        this.templates = templates;
    }
}
