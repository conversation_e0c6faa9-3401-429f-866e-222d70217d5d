package com.tahoecn.opening.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * @ProJectName: housemanage
 * @Author: zwc  <EMAIL>
 * @CreateTime: 2019-09-10 13:41
 * @Description: //TODO websocket配置类
 **/
@Configuration
public class WebSocketConfig {

    /*
     * <AUTHOR>   <EMAIL>
     * @Date 13:42 2019/9/10
     * @Param []
     * @return org.springframework.web.socket.server.standard.ServerEndpointExporter
     * @Version 1.0
     * @Description //TODO 注入ServerEndpointExporter，
     *                 这个bean会自动注册使用了@ServerEndpoint注解声明的Websocket endpoint
     **/
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }
}
