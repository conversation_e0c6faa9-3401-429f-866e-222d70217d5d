package com.tahoecn.opening.config;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.config
 * @Description:// TODO 阿里云客户端配置
 * @Date: 2025/5/8 14:19
 * @Version: 1.0
 **/
@Configuration
public class AliyunSmsClientConfig {

    @Autowired
    private AliyunSmsConfig smsConfig;

    @Bean
    public Client smsClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(smsConfig.getAccessKeyId())
                .setAccessKeySecret(smsConfig.getAccessKeySecret());
        return new Client(config);
    }
}
