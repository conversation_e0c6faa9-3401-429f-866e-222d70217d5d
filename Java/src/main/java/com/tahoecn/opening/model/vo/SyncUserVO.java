package com.tahoecn.opening.model.vo;

import java.io.Serializable;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: chengben
 * @Package: com.telehot.co.appframe.models.vo
 * @ClassName: SyncUserVO
 * @Description:// TODO 同步用户VO
 * @Date: 2023/11/20 16:40
 * @Version: 1.0
 */
public class SyncUserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  用户唯一标识
     */
    private String userUuid;

    /**
     *  用户账号
     */
    private String userCode;

    /**
     *  用户名称
     */
    private String userName;

    /**
     *  所属组织id
     */
    private String belongOrgCode;

    /**
     *  删除标志 0 = 不删除，默认 1 = 删除
     */
    private String deletedStatus;

    /**
     *  成本表id
     */
    private Long costId;

    public String getUserUuid() {
        return userUuid;
    }

    public void setUserUuid(String userUuid) {
        this.userUuid = userUuid;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getBelongOrgCode() {
        return belongOrgCode;
    }

    public void setBelongOrgCode(String belongOrgCode) {
        this.belongOrgCode = belongOrgCode;
    }

    public String getDeletedStatus() {
        return deletedStatus;
    }

    public void setDeletedStatus(String deletedStatus) {
        this.deletedStatus = deletedStatus;
    }

    public Long getCostId() {
        return costId;
    }

    public void setCostId(Long costId) {
        this.costId = costId;
    }
}
