package com.tahoecn.opening.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-23
 */
@ApiModel(value="OpUserSign对象", description="")
public class OpUserSign implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "活动id")
    private String activityId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "签到时间")
    private LocalDateTime signInDate;

    @ApiModelProperty(value = "坐标")
    private String address;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
    public LocalDateTime getSignInDate() {
        return signInDate;
    }

    public void setSignInDate(LocalDateTime signInDate) {
        this.signInDate = signInDate;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Override
    public String toString() {
        return "OpUserSign{" +
        "id=" + id +
        ", activityId=" + activityId +
        ", userId=" + userId +
        ", signInDate=" + signInDate +
        ", address=" + address +
        "}";
    }
}
