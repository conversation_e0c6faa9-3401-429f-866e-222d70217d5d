package com.tahoecn.opening.model.dto;


import java.io.Serializable;
import java.util.Date;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.model.dto
 * @Description:// TODO 用户dto
 * @Date: 2025/1/21 15:45
 * @Version: 1.0
 **/
public class UserDto implements Serializable {

    private static final long serialVersionUID = 9153195910153042271L;

    /**
     *  用户id
     */
    private Integer id;

    /**
     *  用户name
     */
    private String name;

    /**
     *  可选房数
     */
    private Integer selectCount;

    /**
     *  用户序号
     */
    private Integer userSort;

    /**
     *  开始时间
     */
    private Date beginDate;

    /**
     *  结束时间
     */
    private Date endDate;

    /**
     *  用户电话
     */
    private String userTel;

    public UserDto() {
    }

    public UserDto(String name, Integer selectCount, Integer userSort) {
        this.name = name;
        this.selectCount = selectCount;
        this.userSort = userSort;
    }

    public UserDto(Integer id, String name, Integer selectCount, Integer userSort, String userTel) {
        this.id = id;
        this.name = name;
        this.selectCount = selectCount;
        this.userSort = userSort;
        this.userTel = userTel;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSelectCount() {
        return selectCount;
    }

    public void setSelectCount(Integer selectCount) {
        this.selectCount = selectCount;
    }

    public Integer getUserSort() {
        return userSort;
    }

    public void setUserSort(Integer userSort) {
        this.userSort = userSort;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getUserTel() {
        return userTel;
    }

    public void setUserTel(String userTel) {
        this.userTel = userTel;
    }

    @Override
    public String toString() {
        return "UserDto{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", selectCount=" + selectCount +
                ", userSort=" + userSort +
                ", beginDate=" + beginDate +
                ", endDate=" + endDate +
                ", userTel='" + userTel + '\'' +
                '}';
    }
}
