package com.tahoecn.opening.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 活动
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@ApiModel(value="OpBatch对象", description="活动")
public class OpBatch implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "活动id")
    private String activityId;

    @ApiModelProperty(value = "优先级")
    private Integer priorityLevel;

    @ApiModelProperty(value = "批次名称")
    private String batchName;

    @ApiModelProperty(value = "同步下一批次标识：0--同步;1--不同步（默认为0）")
    private Integer syncFlag;

    @ApiModelProperty(value = "是否可用")
    private String yn;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime creationDate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "更新人")
    private String lastUpdateBy;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }
    public Integer getPriorityLevel() {
        return priorityLevel;
    }

    public void setPriorityLevel(Integer priorityLevel) {
        this.priorityLevel = priorityLevel;
    }
    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }
    public Integer getSyncFlag() {
        return syncFlag;
    }

    public void setSyncFlag(Integer syncFlag) {
        this.syncFlag = syncFlag;
    }
    public String getYn() {
        return yn;
    }

    public void setYn(String yn) {
        this.yn = yn;
    }
    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(LocalDateTime creationDate) {
        this.creationDate = creationDate;
    }
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    public LocalDateTime getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(LocalDateTime lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }
    public String getLastUpdateBy() {
        return lastUpdateBy;
    }

    public void setLastUpdateBy(String lastUpdateBy) {
        this.lastUpdateBy = lastUpdateBy;
    }

    @Override
    public String toString() {
        return "OpBatch{" +
        "id=" + id +
        ", activityId=" + activityId +
        ", priorityLevel=" + priorityLevel +
        ", batchName=" + batchName +
        ", syncFlag=" + syncFlag +
        ", yn=" + yn +
        ", creationDate=" + creationDate +
        ", createdBy=" + createdBy +
        ", lastUpdateDate=" + lastUpdateDate +
        ", lastUpdateBy=" + lastUpdateBy +
        "}";
    }
}
