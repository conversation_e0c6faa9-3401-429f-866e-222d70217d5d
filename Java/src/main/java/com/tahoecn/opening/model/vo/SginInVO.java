package com.tahoecn.opening.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

public class SginInVO implements Serializable {

    private static final long serialVersionUID = 7814742577187998441L;

    @Excel(name = "客户姓名")
    private String name;

    @Excel(name = "手机号码")
    private String tel;

    @Excel(name = "是否签到")
    private String sginyn;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Excel(name = "签到时间")
    private Date sginInDate;

    @Excel(name = "身份证号")
    private String idCard;

    @Excel(name = "置业顾问")
    private String saleName;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getSginyn() {
        return sginyn;
    }

    public void setSginyn(String sginyn) {
        this.sginyn = sginyn;
    }

    public Date getSginInDate() {
        return sginInDate;
    }

    public void setSginInDate(Date sginInDate) {
        this.sginInDate = sginInDate;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getSaleName() {
        return saleName;
    }

    public void setSaleName(String saleName) {
        this.saleName = saleName;
    }
}
