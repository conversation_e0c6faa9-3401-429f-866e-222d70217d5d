package com.tahoecn.opening.model.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.model.dto
 * @ClassName: UserHouseResourceDTO
 * @Description:// TODO 用户房源关联关系表DTO
 * @Date: 2022/7/19 13:34
 * @Version: 1.0
 */
public class UserHouseResourceDTO implements Serializable {

    private static final long serialVersionUID = 9153195910153042271L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "房源ID")
    private Long houseResourceId;

    @ApiModelProperty(value = "房源名称")
    private String hourseName;

    @ApiModelProperty(value = "用户同步id")
    private String userSyncId;

    @ApiModelProperty(value = "房源同步id")
    private String hourseSyncId;

    @ApiModelProperty(value = "用户名")
    private String userName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getHouseResourceId() {
        return houseResourceId;
    }

    public void setHouseResourceId(Long houseResourceId) {
        this.houseResourceId = houseResourceId;
    }

    public String getHourseName() {
        return hourseName;
    }

    public void setHourseName(String hourseName) {
        this.hourseName = hourseName;
    }

    public String getUserSyncId() {
        return userSyncId;
    }

    public void setUserSyncId(String userSyncId) {
        this.userSyncId = userSyncId;
    }

    public String getHourseSyncId() {
        return hourseSyncId;
    }

    public void setHourseSyncId(String hourseSyncId) {
        this.hourseSyncId = hourseSyncId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
