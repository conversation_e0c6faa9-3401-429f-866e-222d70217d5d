/**
 * Copyright 2019 bejson.com
 */
package com.tahoecn.opening.model.interfaceBean;


import com.alibaba.fastjson.annotation.JSONField;

/**
 * Auto-generated: 2019-04-11 11:15:20
 */
public class JsonRootBean {

    @JSONField(name = "_datatype")
    private String datatype="test";
    @JSONField(name = "_param")
    private Param param;

    public String getDatatype() {
        return datatype;
    }

    public void setDatatype(String datatype) {
        this.datatype = datatype;
    }

    public Param getParam() {
        return param;
    }

    public void setParam(Param param) {
        this.param = param;
    }
}