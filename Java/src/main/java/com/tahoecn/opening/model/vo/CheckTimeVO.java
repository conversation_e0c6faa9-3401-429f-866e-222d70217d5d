package com.tahoecn.opening.model.vo;

import java.io.Serializable;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.model.vo
 * @ClassName: CheckTimeVO
 * @Description:// TODO 选择时间对象VO
 * @Date: 2024/6/12 13:18
 * @Version: 1.0
 */
public class CheckTimeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 该数据唯一标识
     */
    private String dataId;

    /**
     * 开始时间：格式yyyy-MM-dd HH:mm:ss
     */
    private String beginDate;

    /**
     * 结束时间：格式yyyy-MM-dd HH:mm:ss
     */
    private String endDate;

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
}
