package com.tahoecn.opening.model;

import java.math.BigDecimal;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 楼栋房源
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-08
 */
@ApiModel(value="OpHousingResources对象", description="楼栋房源")
public class OpHousingResources implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @Excel(name = "房源同步ID(RoomGUID)")
    @ApiModelProperty(value = "房源同步ID")
    private String houseSyncId;

    @Excel(name = "房源名称(RoomCode)")
    @ApiModelProperty(value = "房源名称")
    private String houseName;

//    @Excel(name = "项目ID(ProjGUID)")
    @ApiModelProperty(value = "项目ID")
    private String projectId;

//    @Excel(name = "项目名称(ProjName)")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

//    @Excel(name = "分期ID(ProjChildGUID)")
    @ApiModelProperty(value = "分期ID")
    private String proChildId;

//    @Excel(name = "分期名称(ProjChildName)")
    @ApiModelProperty(value = "分期名称")
    private String proChildName;

    @Excel(name = "楼栋名称(BldName)")
    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;

    @Excel(name = "单元名称(Unit)")
    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "单元层数")
    private String unitFloorNumber;

    @Excel(name = "当前层数(Floor)")
    @ApiModelProperty(value = "当前层数")
    private String currentFloor;

    @Excel(name = "房间类型(BProductTypeName)")
    @ApiModelProperty(value = "房间类型")
    private String roomType;

    @Excel(name = "房间号(No)")
    @ApiModelProperty(value = "房间号")
    private String roomNum;

    @Excel(name = "总价(Total)")
    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;

    @Excel(name = "单价(Price)")
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @Excel(name = "优惠总价(DiscountTotal)")
    @ApiModelProperty(value = "优惠总价")
    private BigDecimal discountTotalPrice;

    @Excel(name = "优惠单价(DiscountPrice)")
    @ApiModelProperty(value = "优惠单价")
    private BigDecimal discountUnitPrice;

    @Excel(name = "建筑面积(BldArea)")
    @ApiModelProperty(value = "建筑面积")
    private BigDecimal houserArea;

    @Excel(name = "户型(HuXing)")
    @ApiModelProperty(value = "户型")
    private String hourseType;

    @Excel(name = "户型结构(RoomStru)")
    @ApiModelProperty(value = "户型结构")
    private String roomStru;

    @ApiModelProperty(value = "户型图片")
    private String houseImg;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "是否可用")
    private String yn;

    @ApiModelProperty(value = "是否销控")
    private String saleControlFlag;

    @ApiModelProperty(value = "销控人账号")
    private String saleControlUserAccount;

    @ApiModelProperty(value = "销控人名称")
    private String saleControlUserName;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "销控操作时间")
    private Date saleControlDate;

    @ApiModelProperty(value = "创建日期")
    private Date creationDate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @Excel(name = "更新日期(gxtime)")
    @ApiModelProperty(value = "更新日期")
    private Date lastUpdateDate;

    @ApiModelProperty(value = "更新人")
    private String lastUpdateBy;

    @Excel(name = "模拟批次名称（默认为：common）")
    @ApiModelProperty(value = "模拟批次名称（默认为：common）")
    private String batchNameSimulation;

    @Excel(name = "正式批次名称（默认为：common）")
    @ApiModelProperty(value = "正式批次名称（默认为：common）")
    private String batchNameFormal;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getHouseSyncId() {
        return houseSyncId;
    }

    public void setHouseSyncId(String houseSyncId) {
        this.houseSyncId = houseSyncId;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getProChildId() {
        return proChildId;
    }

    public void setProChildId(String proChildId) {
        this.proChildId = proChildId;
    }

    public String getProChildName() {
        return proChildName;
    }

    public void setProChildName(String proChildName) {
        this.proChildName = proChildName;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getUnitFloorNumber() {
        return unitFloorNumber;
    }

    public void setUnitFloorNumber(String unitFloorNumber) {
        this.unitFloorNumber = unitFloorNumber;
    }

    public String getCurrentFloor() {
        return currentFloor;
    }

    public void setCurrentFloor(String currentFloor) {
        this.currentFloor = currentFloor;
    }

    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public BigDecimal getHouserArea() {
        return houserArea;
    }

    public void setHouserArea(BigDecimal houserArea) {
        this.houserArea = houserArea;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getHourseType() {
        return hourseType;
    }

    public void setHourseType(String hourseType) {
        this.hourseType = hourseType;
    }

    public String getRoomStru() {
        return roomStru;
    }

    public void setRoomStru(String roomStru) {
        this.roomStru = roomStru;
    }

    public String getHouseImg() {
        return houseImg;
    }

    public void setHouseImg(String houseImg) {
        this.houseImg = houseImg;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getYn() {
        return yn;
    }

    public void setYn(String yn) {
        this.yn = yn;
    }

    public String getSaleControlFlag() {
        return saleControlFlag;
    }

    public void setSaleControlFlag(String saleControlFlag) {
        this.saleControlFlag = saleControlFlag;
    }

    public String getSaleControlUserAccount() {
        return saleControlUserAccount;
    }

    public void setSaleControlUserAccount(String saleControlUserAccount) {
        this.saleControlUserAccount = saleControlUserAccount;
    }

    public String getSaleControlUserName() {
        return saleControlUserName;
    }

    public void setSaleControlUserName(String saleControlUserName) {
        this.saleControlUserName = saleControlUserName;
    }

    public Date getSaleControlDate() {
        return saleControlDate;
    }

    public void setSaleControlDate(Date saleControlDate) {
        this.saleControlDate = saleControlDate;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getLastUpdateBy() {
        return lastUpdateBy;
    }

    public void setLastUpdateBy(String lastUpdateBy) {
        this.lastUpdateBy = lastUpdateBy;
    }

    public BigDecimal getDiscountTotalPrice() {
        return discountTotalPrice;
    }

    public void setDiscountTotalPrice(BigDecimal discountTotalPrice) {
        this.discountTotalPrice = discountTotalPrice;
    }

    public BigDecimal getDiscountUnitPrice() {
        return discountUnitPrice;
    }

    public void setDiscountUnitPrice(BigDecimal discountUnitPrice) {
        this.discountUnitPrice = discountUnitPrice;
    }

    public String getBatchNameSimulation() {
        return batchNameSimulation;
    }

    public void setBatchNameSimulation(String batchNameSimulation) {
        this.batchNameSimulation = batchNameSimulation;
    }

    public String getBatchNameFormal() {
        return batchNameFormal;
    }

    public void setBatchNameFormal(String batchNameFormal) {
        this.batchNameFormal = batchNameFormal;
    }

    @Override
    public String toString() {
        return "OpHousingResources{" +
                "id=" + id +
                ", activityId='" + activityId + '\'' +
                ", houseSyncId='" + houseSyncId + '\'' +
                ", houseName='" + houseName + '\'' +
                ", projectId='" + projectId + '\'' +
                ", projectName='" + projectName + '\'' +
                ", proChildId='" + proChildId + '\'' +
                ", proChildName='" + proChildName + '\'' +
                ", buildingName='" + buildingName + '\'' +
                ", unitName='" + unitName + '\'' +
                ", unitFloorNumber='" + unitFloorNumber + '\'' +
                ", currentFloor='" + currentFloor + '\'' +
                ", roomType='" + roomType + '\'' +
                ", roomNum='" + roomNum + '\'' +
                ", totalPrice=" + totalPrice +
                ", unitPrice=" + unitPrice +
                ", discountTotalPrice=" + discountTotalPrice +
                ", discountUnitPrice=" + discountUnitPrice +
                ", houserArea=" + houserArea +
                ", hourseType='" + hourseType + '\'' +
                ", roomStru='" + roomStru + '\'' +
                ", houseImg='" + houseImg + '\'' +
                ", remark='" + remark + '\'' +
                ", status='" + status + '\'' +
                ", yn='" + yn + '\'' +
                ", saleControlFlag='" + saleControlFlag + '\'' +
                ", saleControlUserAccount='" + saleControlUserAccount + '\'' +
                ", saleControlUserName='" + saleControlUserName + '\'' +
                ", saleControlDate='" + saleControlDate + '\'' +
                ", creationDate=" + creationDate +
                ", createdBy='" + createdBy + '\'' +
                ", lastUpdateDate=" + lastUpdateDate +
                ", lastUpdateBy='" + lastUpdateBy + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof OpHousingResources)) return false;
        OpHousingResources that = (OpHousingResources) o;
        return Objects.equals(houseSyncId, that.houseSyncId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(houseSyncId);
    }
}
