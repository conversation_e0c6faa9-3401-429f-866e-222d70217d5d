/**
 * Copyright 2019 bejson.com
 */
package com.tahoecn.opening.model.interfaceBean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.xml.crypto.Data;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * Auto-generated: 2019-04-11 11:15:20
 */
public class CustomerFollowData {

    private Date followUpDate;
    private String followUpUserID;
    private String followUpContent;
    private String customerID;
    private String projectID;

    public void setFollowUpDate(Date followUpDate) {
        this.followUpDate = followUpDate;
    }
    public Date getFollowUpDate() {
        return followUpDate;
    }

    public void setFollowUpUserID(String followUpUserID) {
        this.followUpUserID = followUpUserID;
    }
    public String getFollowUpUserID() {
        return followUpUserID;
    }

    public void setFollowUpContent(String followUpContent) {
        this.followUpContent = followUpContent;
    }
    public String getFollowUpContent() {
        return followUpContent;
    }

    public void setCustomerID(String customerID) {
        this.customerID = customerID;
    }
    public String getCustomerID() {
        return customerID;
    }

    public void setProjectID(String projectID) {
        this.projectID = projectID;
    }
    public String getProjectID() {
        return projectID;
    }

}