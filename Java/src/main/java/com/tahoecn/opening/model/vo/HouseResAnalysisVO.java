package com.tahoecn.opening.model.vo;


import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

public class HouseResAnalysisVO implements Serializable {


    private static final long serialVersionUID = 6681247458297820811L;

    private  String houseId;
    @Excel(name = "楼栋",height = 11, width = 15)
    private String buildingName;

    @Excel(name = "单元",height = 11, width = 15)
    private String unitName;

    @Excel(name = "楼层",height = 11, width = 15)
    private String currentFloor;

    @Excel(name = "房间号",height = 11, width = 15)
    private String roomNum;

    @Excel(name = "房源名称",height = 11, width = 15)
    private String houseName;

    @Excel(name = "房源收藏数",height = 11, width = 15)
    private String favoNum;

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getCurrentFloor() {
        return currentFloor;
    }

    public void setCurrentFloor(String currentFloor) {
        this.currentFloor = currentFloor;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getFavoNum() {
        return favoNum;
    }

    public void setFavoNum(String favoNum) {
        this.favoNum = favoNum;
    }

    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    @Override
    public String toString() {
        return "HouseResAnalysisVO{" +
                "houseId='" + houseId + '\'' +
                ", buildingName='" + buildingName + '\'' +
                ", unitName='" + unitName + '\'' +
                ", currentFloor='" + currentFloor + '\'' +
                ", roomNum='" + roomNum + '\'' +
                ", houseName='" + houseName + '\'' +
                ", favoNum='" + favoNum + '\'' +
                '}';
    }
}
