package com.tahoecn.opening.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 客户
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-09
 */
@ApiModel(value = "OpUser对象", description = "客户")
public class OpUser implements Serializable {


    private static final long serialVersionUID = 9153195910153042271L;
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

//    @Excel(name = "项目ID(ProjGUID)")
    @ApiModelProperty(value = "项目ID")
    private String projectId;

//    @Excel(name = "OppGUID")
    @ApiModelProperty(value = "OppGUID")
    private String oppguid;

    @Excel(name = "CstGUID(唯一)")
    @ApiModelProperty(value = "CstGUID")
    private String cstguid;

    @Excel(name = "名字")
    @ApiModelProperty(value = "名字")
    private String name;

    @Excel(name = "性别(男女)")
    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "登陆名")
    private String loginName;

    @Excel(name = "身份证号")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

//    @Excel(name = "置业顾问CstGUID(不填无法关联顾问)")
    @ApiModelProperty(value = "置业顾问ID")
    private String saleId;

    @Excel(name = "置业顾问(请勿改动格式：名称_CstGUID)")
    @ApiModelProperty(value = "置业顾问名字")
    private String saleName;

    @Excel(name = "电话(唯一)" )
    @ApiModelProperty(value = "电话")
    private String tel;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Excel(name = "创建日期:yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date creationDate;

    @ApiModelProperty(value = "可选房数")
    @Excel(name = "可选房数(数字)")
    private Integer selectCount;

    @ApiModelProperty(value = "微信标示")
    private String openId;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Excel(name = "更新日期:yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date lastUpdateDate;

    @Excel(name = "认筹次数(数字)")
    @ApiModelProperty(value = "认筹次数")
    private String bookingNum;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色代码")
    private String roleCode;

    @ApiModelProperty(value = "上级营销经理")
    private Integer parentId;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "最后登录时间")
    private LocalDateTime lastLoginDate;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "是否可用")
    private String yn;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String lastUpdateBy;

    @ApiModelProperty(value = "销售类型")
    private String accountType;

    @ApiModelProperty(value = "销售类型名")
    private String accountName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "首次登陆标记")
    private Integer firstLogin;

    @Excel(name = "用户选房排序号(数字)")
    @ApiModelProperty(value = "用户序号")
    private Integer userSort;

    @ApiModelProperty(value = "调用结果：1--操作成功;2--操作异常;3--电子签返回结果不是成功")
    private String syncType;

    @ApiModelProperty(value = "操作不成功的结果")
    private String syncResult;


    /**
     *  模拟开始时间
     */
    @ApiModelProperty(value = "模拟开始时间")
    private Date simulationBeginDate;

    /**
     *  模拟结束时间
     */
    @ApiModelProperty(value = "模拟结束时间")
    private Date simulationEndDate;

    /**
     *  正式开始时间
     */
    @ApiModelProperty(value = "正式开始时间")
    private Date formalBeginDate;

    /**
     *  正式结束时间
     */
    @ApiModelProperty(value = "正式结束时间")
    private Date formalEndDate;

    @ApiModelProperty(value = "摇号序号")
    private Integer lotterySort;

    @Excel(name = "可看房源批次(多个用英文逗分割，如不填则默认查common批次房源)")
    @ApiModelProperty(value = "摇号批次名称字符串，多个用英文逗号分割")
    private String batchNameStr;

    @Excel(name = "房源信息")
    @ApiModelProperty(value = "房源信息")
    private String houseStr;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getOppguid() {
        return oppguid;
    }

    public void setOppguid(String oppguid) {
        this.oppguid = oppguid;
    }

    public String getCstguid() {
        return cstguid;
    }

    public void setCstguid(String cstguid) {
        this.cstguid = cstguid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getSaleId() {
        return saleId;
    }

    public void setSaleId(String saleId) {
        this.saleId = saleId;
    }

    public String getSaleName() {
        return saleName;
    }

    public void setSaleName(String saleName) {
        this.saleName = saleName;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Integer getSelectCount() {
        return selectCount;
    }

    public void setSelectCount(Integer selectCount) {
        this.selectCount = selectCount;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getBookingNum() {
        return bookingNum;
    }

    public void setBookingNum(String bookingNum) {
        this.bookingNum = bookingNum;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public LocalDateTime getLastLoginDate() {
        return lastLoginDate;
    }

    public void setLastLoginDate(LocalDateTime lastLoginDate) {
        this.lastLoginDate = lastLoginDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getYn() {
        return yn;
    }

    public void setYn(String yn) {
        this.yn = yn;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getLastUpdateBy() {
        return lastUpdateBy;
    }

    public void setLastUpdateBy(String lastUpdateBy) {
        this.lastUpdateBy = lastUpdateBy;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getFirstLogin() {
        return firstLogin;
    }

    public void setFirstLogin(Integer firstLogin) {
        this.firstLogin = firstLogin;
    }

    public Integer getUserSort() {
        return userSort;
    }

    public void setUserSort(Integer userSort) {
        this.userSort = userSort;
    }

    public String getSyncType() {
        return syncType;
    }

    public void setSyncType(String syncType) {
        this.syncType = syncType;
    }

    public String getSyncResult() {
        return syncResult;
    }

    public void setSyncResult(String syncResult) {
        this.syncResult = syncResult;
    }

    public Date getSimulationBeginDate() {
        return simulationBeginDate;
    }

    public void setSimulationBeginDate(Date simulationBeginDate) {
        this.simulationBeginDate = simulationBeginDate;
    }

    public Date getSimulationEndDate() {
        return simulationEndDate;
    }

    public void setSimulationEndDate(Date simulationEndDate) {
        this.simulationEndDate = simulationEndDate;
    }

    public Date getFormalBeginDate() {
        return formalBeginDate;
    }

    public void setFormalBeginDate(Date formalBeginDate) {
        this.formalBeginDate = formalBeginDate;
    }

    public Date getFormalEndDate() {
        return formalEndDate;
    }

    public void setFormalEndDate(Date formalEndDate) {
        this.formalEndDate = formalEndDate;
    }

    public String getBatchNameStr() {
        return batchNameStr;
    }

    public void setBatchNameStr(String batchNameStr) {
        this.batchNameStr = batchNameStr;
    }

    public Integer getLotterySort() {
        return lotterySort;
    }

    public void setLotterySort(Integer lotterySort) {
        this.lotterySort = lotterySort;
    }

    public String getHouseStr() {
        return houseStr;
    }

    public void setHouseStr(String houseStr) {
        this.houseStr = houseStr;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof OpUser)) return false;
        OpUser opUser = (OpUser) o;
        return Objects.equals(tel, opUser.tel);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tel);
    }
}
