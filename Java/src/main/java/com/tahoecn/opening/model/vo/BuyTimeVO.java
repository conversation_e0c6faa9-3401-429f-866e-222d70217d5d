package com.tahoecn.opening.model.vo;

import java.io.Serializable;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.model.vo
 * @ClassName: BuyTimeVO
 * @Description:// TODO 选房时间对象
 * @Date: 2024/8/13 16:21
 * @Version: 1.0
 */
public class BuyTimeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 该数据唯一标识
     */
    private String uuid;

    /**
     * 开始时间：格式yyyy-MM-dd HH:mm:ss
     */
    private String beginDate;

    /**
     * 结束时间：格式yyyy-MM-dd HH:mm:ss
     */
    private String endDate;

    /**
     * 排序字段
     */
    private Integer sortNum;

    public BuyTimeVO() {
    }

    public BuyTimeVO(String beginDate, String endDate) {
        this.beginDate = beginDate;
        this.endDate = endDate;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }
}
