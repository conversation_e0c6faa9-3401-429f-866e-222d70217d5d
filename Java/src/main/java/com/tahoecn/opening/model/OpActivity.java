package com.tahoecn.opening.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 活动
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-12
 */
@ApiModel(value="OpActivity对象", description="活动")
public class OpActivity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "案场名称")
    private String caseName;

    @ApiModelProperty(value = "项目地址")
    private String projectAddress;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "下单客户是否可以退房")
    private String isRefunds;

    @ApiModelProperty(value = "参与人数基数")
    private Integer joinNumber;

    @ApiModelProperty(value = "活动类型（h：房，c：车）")
    private String activityType;

    @ApiModelProperty(value = "选房成功提示")
    private String successTips;

    @ApiModelProperty(value = "客户收藏限制")
    private Integer collectionLimit;

    @ApiModelProperty(value = "登陆模式")
    private String loginType;

    @ApiModelProperty(value = "售楼处电话")
    private String salesTel;

    @ApiModelProperty(value = "是否提醒短信")
    private String isMessageRemind;

    @ApiModelProperty(value = "短信发送小时")
    private Integer messageH;

    @ApiModelProperty(value = "短信发送分钟")
    private Integer messageM;

    @ApiModelProperty(value = "需要公示文字")
    private String publicity;

    @ApiModelProperty(value = "是否模拟")
    private String isSimulation;

    @ApiModelProperty(value = "模拟开始")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime simulationStart;

    @ApiModelProperty(value = "模拟结束")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime simulationEnd;

    @ApiModelProperty(value = "正式开始")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime formalStart;

    @ApiModelProperty(value = "正式结束")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime formalEnd;

    @ApiModelProperty(value = "开盘前总价显隐")
    private String openPriceHide;

    @ApiModelProperty(value = "单价显隐")
    private String unitPriceHide;

    @ApiModelProperty(value = "开盘期间总价显隐")
    private String afterOpenPriceHide;

    @ApiModelProperty(value = "开盘期间单价显隐")
    private String afterUnitPriceHide;

    @ApiModelProperty(value = "大屏设置参数字符串")
    private String screenSetStr;

    @ApiModelProperty(value = "展示换行数设置str")
    private String showSplitSetStr;

    @ApiModelProperty(value = "活动开始前几分钟显示价格分钟数")
    private Integer showPriceMinutes;

    @ApiModelProperty(value = "是否短信验证")
    private String isMessageVal;

    @ApiModelProperty(value = "是否验证身份证")
    private String isIdVal;

    @ApiModelProperty(value = "每人可购房套数")
    private Integer buyNumber;

    @ApiModelProperty(value = "自动房源已购")
    private String isAutoBuy;

    @ApiModelProperty(value = "是否现场签到")
    private String isSignIn;

    @ApiModelProperty(value = "签到半径")
    private Integer signRange;

    @ApiModelProperty(value = "中心点坐标")
    private String centerCoordinate;

    @ApiModelProperty(value = "状态代码")
    private String statusCode;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "是否可用")
    private String yn;

    @ApiModelProperty(value = "正式选房提醒")
    private String formalMessage;

    @ApiModelProperty(value = "模拟选房提醒")
    private String simulationMessage;

    @ApiModelProperty(value = "线上选房须知")
    private String shouldKnow;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime creationDate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "更新人")
    private String lastUpdateBy;
    
    @ApiModelProperty(value = "图片地址")
    private String imgUrl;
    @ApiModelProperty(value = "二维码")
    private String qrUrl;
    @ApiModelProperty(value = "签到二维码")
    private String signInUrl;

    @ApiModelProperty(value = "是否展示收款信息")
    private String showPriceMsg;


    @ApiModelProperty(value = "自动生成订单标识：0--否；1--是（默认为0）")
    private Integer autoOrderFlag;

    @ApiModelProperty(value = "订单类型：0--全部；1--正式；2--模拟（默认为0）")
    private Integer orderType;

    @ApiModelProperty(value = "自动生成订单结果标识：0--未生成；1--正式已生成；2--模拟已生成；3--全部生成（默认为0）")
    private Integer autoOrderResultFlag;

    @ApiModelProperty(value = "活动选房模式：0--抢购模式；1--选房模式（默认为0）")
    private Integer checkMode;

    @ApiModelProperty(value = "单用户选房分钟数")
    private Integer checkMinutes;

    @ApiModelProperty(value = "该活动的模拟可选房区间列表")
    private String checkTimeStr;

    @ApiModelProperty(value = "该活动的正式可选房区间列表")
    private String formalCheckTimeStr;

    @ApiModelProperty(value = "开启电子签标识：0--否;1--是(默认为0)")
    private Integer openElectronFlag;

    @ApiModelProperty(value = "电子签模板id")
    private String templateId;

    @ApiModelProperty(value = "按批次展示房源标识：0--否;1--是(默认为0)")
    private Integer batchFlag;

    @ApiModelProperty(value = "开启个人摇号标识：0--否;1--是(默认为0)")
    private Integer lotteryFlag;

    @ApiModelProperty("摇号显示标题")
    private String lotteryTitle;

    @ApiModelProperty("摇号开始日期")
    private LocalDateTime lotteryStartDate;

    @ApiModelProperty("摇号截止日期")
    private LocalDateTime lotteryEndDate;

    @ApiModelProperty("摇号规则说明")
    private String lotteryExplain;

    @ApiModelProperty("摇号生成二维码")
    @TableField("lottery_QRcode")
    private String lotteryQRcode;

    @ApiModelProperty(value = "开始选房短信通知标识：0--不发送;1--发送(默认为0)")
    private Integer openCheckFlag;

    @ApiModelProperty("每人选房开始时间前多少分钟发送")
    private Integer everyBodyBeforeMinutes;

    @ApiModelProperty(value = "摇号结果短信通知标识：0--不发送;1--发送(默认为0)")
    private Integer lotteryResultFlag;

    @ApiModelProperty(value = "开始摇号短信通知标识：0--不发送;1--发送(默认为0)")
    private Integer openLotteryFlag;

    @ApiModelProperty("开始摇号时间前多少分钟发送")
    private Integer openLotteryBeforeMinutes;

    public String getQrUrl() {
        return qrUrl;
    }

    public void setQrUrl(String qrUrl) {
        this.qrUrl = qrUrl;
    }

    public String getSignInUrl() {
        return signInUrl;
    }

    public void setSignInUrl(String signInUrl) {
        this.signInUrl = signInUrl;
    }

    public String getShowPriceMsg() {
        return showPriceMsg;
    }

    public void setShowPriceMsg(String showPriceMsg) {
        this.showPriceMsg = showPriceMsg;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }
    public String getCaseName() {
        return caseName;
    }

    public void setCaseName(String caseName) {
        this.caseName = caseName;
    }
    public String getProjectAddress() {
        return projectAddress;
    }

    public void setProjectAddress(String projectAddress) {
        this.projectAddress = projectAddress;
    }
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
    public String getIsRefunds() {
        return isRefunds;
    }

    public void setIsRefunds(String isRefunds) {
        this.isRefunds = isRefunds;
    }
    public Integer getJoinNumber() {
        return joinNumber;
    }

    public void setJoinNumber(Integer joinNumber) {
        this.joinNumber = joinNumber;
    }
    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }
    public String getSuccessTips() {
        return successTips;
    }

    public void setSuccessTips(String successTips) {
        this.successTips = successTips;
    }
    public Integer getCollectionLimit() {
        return collectionLimit;
    }

    public void setCollectionLimit(Integer collectionLimit) {
        this.collectionLimit = collectionLimit;
    }
    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }
    public String getSalesTel() {
        return salesTel;
    }

    public void setSalesTel(String salesTel) {
        this.salesTel = salesTel;
    }
    public String getIsMessageRemind() {
        return isMessageRemind;
    }

    public void setIsMessageRemind(String isMessageRemind) {
        this.isMessageRemind = isMessageRemind;
    }
    public Integer getMessageH() {
        return messageH;
    }

    public void setMessageH(Integer messageH) {
        this.messageH = messageH;
    }
    public Integer getMessageM() {
        return messageM;
    }

    public void setMessageM(Integer messageM) {
        this.messageM = messageM;
    }
    public String getPublicity() {
        return publicity;
    }

    public void setPublicity(String publicity) {
        this.publicity = publicity;
    }
    public String getIsSimulation() {
        return isSimulation;
    }

    public void setIsSimulation(String isSimulation) {
        this.isSimulation = isSimulation;
    }
    public LocalDateTime getSimulationStart() {
        return simulationStart;
    }

    public void setSimulationStart(LocalDateTime simulationStart) {
        this.simulationStart = simulationStart;
    }
    public LocalDateTime getSimulationEnd() {
        return simulationEnd;
    }

    public void setSimulationEnd(LocalDateTime simulationEnd) {
        this.simulationEnd = simulationEnd;
    }
    public LocalDateTime getFormalStart() {
        return formalStart;
    }

    public void setFormalStart(LocalDateTime formalStart) {
        this.formalStart = formalStart;
    }
    public LocalDateTime getFormalEnd() {
        return formalEnd;
    }

    public void setFormalEnd(LocalDateTime formalEnd) {
        this.formalEnd = formalEnd;
    }
    public String getOpenPriceHide() {
        return openPriceHide;
    }

    public void setOpenPriceHide(String openPriceHide) {
        this.openPriceHide = openPriceHide;
    }
    public String getUnitPriceHide() {
        return unitPriceHide;
    }

    public void setUnitPriceHide(String unitPriceHide) {
        this.unitPriceHide = unitPriceHide;
    }
    public String getIsMessageVal() {
        return isMessageVal;
    }

    public String getAfterOpenPriceHide() {
        return afterOpenPriceHide;
    }

    public void setAfterOpenPriceHide(String afterOpenPriceHide) {
        this.afterOpenPriceHide = afterOpenPriceHide;
    }

    public String getAfterUnitPriceHide() {
        return afterUnitPriceHide;
    }

    public void setAfterUnitPriceHide(String afterUnitPriceHide) {
        this.afterUnitPriceHide = afterUnitPriceHide;
    }

    public String getScreenSetStr() {
        return screenSetStr;
    }

    public void setScreenSetStr(String screenSetStr) {
        this.screenSetStr = screenSetStr;
    }

    public String getShowSplitSetStr() {
        return showSplitSetStr;
    }

    public void setShowSplitSetStr(String showSplitSetStr) {
        this.showSplitSetStr = showSplitSetStr;
    }

    public Integer getShowPriceMinutes() {
        return showPriceMinutes;
    }

    public void setShowPriceMinutes(Integer showPriceMinutes) {
        this.showPriceMinutes = showPriceMinutes;
    }

    public void setIsMessageVal(String isMessageVal) {
        this.isMessageVal = isMessageVal;
    }
    public String getIsIdVal() {
        return isIdVal;
    }

    public void setIsIdVal(String isIdVal) {
        this.isIdVal = isIdVal;
    }
    public Integer getBuyNumber() {
        return buyNumber;
    }

    public void setBuyNumber(Integer buyNumber) {
        this.buyNumber = buyNumber;
    }
    public String getIsAutoBuy() {
        return isAutoBuy;
    }

    public void setIsAutoBuy(String isAutoBuy) {
        this.isAutoBuy = isAutoBuy;
    }
    public String getIsSignIn() {
        return isSignIn;
    }

    public void setIsSignIn(String isSignIn) {
        this.isSignIn = isSignIn;
    }
    public Integer getSignRange() {
        return signRange;
    }

    public void setSignRange(Integer signRange) {
        this.signRange = signRange;
    }
    public String getCenterCoordinate() {
        return centerCoordinate;
    }

    public void setCenterCoordinate(String centerCoordinate) {
        this.centerCoordinate = centerCoordinate;
    }
    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }
    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }
    public String getYn() {
        return yn;
    }

    public void setYn(String yn) {
        this.yn = yn;
    }
    public String getFormalMessage() {
        return formalMessage;
    }

    public void setFormalMessage(String formalMessage) {
        this.formalMessage = formalMessage;
    }
    public String getSimulationMessage() {
        return simulationMessage;
    }

    public void setSimulationMessage(String simulationMessage) {
        this.simulationMessage = simulationMessage;
    }
    public String getShouldKnow() {
        return shouldKnow;
    }

    public void setShouldKnow(String shouldKnow) {
        this.shouldKnow = shouldKnow;
    }
    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(LocalDateTime creationDate) {
        this.creationDate = creationDate;
    }
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    public LocalDateTime getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(LocalDateTime lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }
    public String getLastUpdateBy() {
        return lastUpdateBy;
    }

    public void setLastUpdateBy(String lastUpdateBy) {
        this.lastUpdateBy = lastUpdateBy;
    }

    public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

    public Integer getAutoOrderFlag() {
        return autoOrderFlag;
    }

    public void setAutoOrderFlag(Integer autoOrderFlag) {
        this.autoOrderFlag = autoOrderFlag;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getAutoOrderResultFlag() {
        return autoOrderResultFlag;
    }

    public void setAutoOrderResultFlag(Integer autoOrderResultFlag) {
        this.autoOrderResultFlag = autoOrderResultFlag;
    }

    public Integer getCheckMode() {
        return checkMode;
    }

    public void setCheckMode(Integer checkMode) {
        this.checkMode = checkMode;
    }

    public Integer getCheckMinutes() {
        return checkMinutes;
    }

    public void setCheckMinutes(Integer checkMinutes) {
        this.checkMinutes = checkMinutes;
    }

    public String getCheckTimeStr() {
        return checkTimeStr;
    }

    public void setCheckTimeStr(String checkTimeStr) {
        this.checkTimeStr = checkTimeStr;
    }

    public String getFormalCheckTimeStr() {
        return formalCheckTimeStr;
    }

    public void setFormalCheckTimeStr(String formalCheckTimeStr) {
        this.formalCheckTimeStr = formalCheckTimeStr;
    }

    public Integer getOpenElectronFlag() {
        return openElectronFlag;
    }

    public void setOpenElectronFlag(Integer openElectronFlag) {
        this.openElectronFlag = openElectronFlag;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public Integer getBatchFlag() {
        return batchFlag;
    }

    public void setBatchFlag(Integer batchFlag) {
        this.batchFlag = batchFlag;
    }

    public Integer getLotteryFlag() {
        return lotteryFlag;
    }

    public void setLotteryFlag(Integer lotteryFlag) {
        this.lotteryFlag = lotteryFlag;
    }

    public String getLotteryTitle() {
        return lotteryTitle;
    }

    public void setLotteryTitle(String lotteryTitle) {
        this.lotteryTitle = lotteryTitle;
    }

    public LocalDateTime getLotteryStartDate() {
        return lotteryStartDate;
    }

    public void setLotteryStartDate(LocalDateTime lotteryStartDate) {
        this.lotteryStartDate = lotteryStartDate;
    }

    public LocalDateTime getLotteryEndDate() {
        return lotteryEndDate;
    }

    public void setLotteryEndDate(LocalDateTime lotteryEndDate) {
        this.lotteryEndDate = lotteryEndDate;
    }

    public String getLotteryExplain() {
        return lotteryExplain;
    }

    public void setLotteryExplain(String lotteryExplain) {
        this.lotteryExplain = lotteryExplain;
    }

    public String getLotteryQRcode() {
        return lotteryQRcode;
    }

    public void setLotteryQRcode(String lotteryQRcode) {
        this.lotteryQRcode = lotteryQRcode;
    }

    public Integer getOpenCheckFlag() {
        return openCheckFlag;
    }

    public void setOpenCheckFlag(Integer openCheckFlag) {
        this.openCheckFlag = openCheckFlag;
    }

    public Integer getEveryBodyBeforeMinutes() {
        return everyBodyBeforeMinutes;
    }

    public void setEveryBodyBeforeMinutes(Integer everyBodyBeforeMinutes) {
        this.everyBodyBeforeMinutes = everyBodyBeforeMinutes;
    }

    public Integer getLotteryResultFlag() {
        return lotteryResultFlag;
    }

    public void setLotteryResultFlag(Integer lotteryResultFlag) {
        this.lotteryResultFlag = lotteryResultFlag;
    }

    public Integer getOpenLotteryFlag() {
        return openLotteryFlag;
    }

    public void setOpenLotteryFlag(Integer openLotteryFlag) {
        this.openLotteryFlag = openLotteryFlag;
    }

    public Integer getOpenLotteryBeforeMinutes() {
        return openLotteryBeforeMinutes;
    }

    public void setOpenLotteryBeforeMinutes(Integer openLotteryBeforeMinutes) {
        this.openLotteryBeforeMinutes = openLotteryBeforeMinutes;
    }

    @Override
    public String toString() {
        return "OpActivity{" +
                "id=" + id +
                ", activityName='" + activityName + '\'' +
                ", caseName='" + caseName + '\'' +
                ", projectAddress='" + projectAddress + '\'' +
                ", projectName='" + projectName + '\'' +
                ", projectId='" + projectId + '\'' +
                ", isRefunds='" + isRefunds + '\'' +
                ", joinNumber=" + joinNumber +
                ", activityType='" + activityType + '\'' +
                ", successTips='" + successTips + '\'' +
                ", collectionLimit=" + collectionLimit +
                ", loginType='" + loginType + '\'' +
                ", salesTel='" + salesTel + '\'' +
                ", isMessageRemind='" + isMessageRemind + '\'' +
                ", messageH=" + messageH +
                ", messageM=" + messageM +
                ", publicity='" + publicity + '\'' +
                ", isSimulation='" + isSimulation + '\'' +
                ", simulationStart=" + simulationStart +
                ", simulationEnd=" + simulationEnd +
                ", formalStart=" + formalStart +
                ", formalEnd=" + formalEnd +
                ", openPriceHide='" + openPriceHide + '\'' +
                ", unitPriceHide='" + unitPriceHide + '\'' +
                ", afterOpenPriceHide='" + afterOpenPriceHide + '\'' +
                ", afterUnitPriceHide='" + afterUnitPriceHide + '\'' +
                ", screenSetStr='" + screenSetStr + '\'' +
                ", showSplitSetStr='" + showSplitSetStr + '\'' +
                ", showPriceMinutes=" + showPriceMinutes +
                ", isMessageVal='" + isMessageVal + '\'' +
                ", isIdVal='" + isIdVal + '\'' +
                ", buyNumber=" + buyNumber +
                ", isAutoBuy='" + isAutoBuy + '\'' +
                ", isSignIn='" + isSignIn + '\'' +
                ", signRange=" + signRange +
                ", centerCoordinate='" + centerCoordinate + '\'' +
                ", statusCode='" + statusCode + '\'' +
                ", statusName='" + statusName + '\'' +
                ", yn='" + yn + '\'' +
                ", formalMessage='" + formalMessage + '\'' +
                ", simulationMessage='" + simulationMessage + '\'' +
                ", shouldKnow='" + shouldKnow + '\'' +
                ", creationDate=" + creationDate +
                ", createdBy='" + createdBy + '\'' +
                ", lastUpdateDate=" + lastUpdateDate +
                ", lastUpdateBy='" + lastUpdateBy + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", qrUrl='" + qrUrl + '\'' +
                ", signInUrl='" + signInUrl + '\'' +
                ", showPriceMsg='" + showPriceMsg + '\'' +
                ", autoOrderFlag=" + autoOrderFlag +
                ", orderType=" + orderType +
                ", autoOrderResultFlag=" + autoOrderResultFlag +
                ", checkMode=" + checkMode +
                ", checkMinutes=" + checkMinutes +
                ", checkTimeStr='" + checkTimeStr + '\'' +
                ", formalCheckTimeStr='" + formalCheckTimeStr + '\'' +
                ", openElectronFlag=" + openElectronFlag +
                ", templateId='" + templateId + '\'' +
                ", batchFlag=" + batchFlag +
                ", lotteryFlag=" + lotteryFlag +
                ", lotteryTitle='" + lotteryTitle + '\'' +
                ", lotteryStartDate=" + lotteryStartDate +
                ", lotteryEndDate=" + lotteryEndDate +
                ", lotteryExplain='" + lotteryExplain + '\'' +
                ", lotteryQRcode='" + lotteryQRcode + '\'' +
                ", openCheckFlag=" + openCheckFlag +
                ", everyBodyBeforeMinutes=" + everyBodyBeforeMinutes +
                ", lotteryResultFlag=" + lotteryResultFlag +
                ", openLotteryFlag=" + openLotteryFlag +
                ", openLotteryBeforeMinutes=" + openLotteryBeforeMinutes +
                '}';
    }
}
