package com.tahoecn.opening.model.vo;


import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 销售数据总览VO类
 */
public class SalesOverview implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer totalNum;

    private Integer soldNum;

    private Integer totalHouseResources;

    private Integer soldHouseResources;

    private Integer joinNum;

    private Integer orderNum;

    private Integer percentage;

    /**
     * 未售房源数量
     */
    private Integer notSaleNum;

    public Integer getNotSaleNum() {
        return notSaleNum;
    }

    public void setNotSaleNum(Integer notSaleNum) {
        this.notSaleNum = notSaleNum;
    }

    public Integer getTotalHouseResources() {
        return totalHouseResources;
    }

    public void setTotalHouseResources(Integer totalHouseResources) {
        this.totalHouseResources = totalHouseResources;
    }

    public Integer getSoldHouseResources() {
        return soldHouseResources;
    }

    public void setSoldHouseResources(Integer soldHouseResources) {
        this.soldHouseResources = soldHouseResources;
    }

    public Integer getJoinNum() {
        return joinNum;
    }

    public void setJoinNum(Integer joinNum) {
        this.joinNum = joinNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getPercentage() {
        return percentage;
    }

    public void setPercentage(Integer percentage) {
        this.percentage = percentage;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public Integer getSoldNum() {
        return soldNum;
    }

    public void setSoldNum(Integer soldNum) {
        this.soldNum = soldNum;
    }
}
