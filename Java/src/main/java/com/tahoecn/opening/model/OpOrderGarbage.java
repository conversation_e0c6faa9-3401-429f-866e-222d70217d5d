package com.tahoecn.opening.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 删除的订单记录
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-23
 */
@ApiModel(value="OpOrderGarbage对象", description="订单")
public class OpOrderGarbage implements Serializable {


    private static final long serialVersionUID = -1696765210703428526L;
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "用户名字")
    private String userName;

    @ApiModelProperty(value = "订单房源ID")
    private String houseId;

    @ApiModelProperty(value = "订单房源名称")
    private String houseName;

    @ApiModelProperty(value = "是否正式")
    private String isRegular;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "是否可用")
    private String yn;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime creationDate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "更新人")
    private String lastUpdateBy;

    @ApiModelProperty(value = "是否预制订单")
    private String isBeforehand;

    private String userSyncId;

    private String houseSyncId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }
    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }
    public String getIsRegular() {
        return isRegular;
    }

    public void setIsRegular(String isRegular) {
        this.isRegular = isRegular;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getYn() {
        return yn;
    }

    public void setYn(String yn) {
        this.yn = yn;
    }
    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(LocalDateTime creationDate) {
        this.creationDate = creationDate;
    }
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    public LocalDateTime getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(LocalDateTime lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }
    public String getLastUpdateBy() {
        return lastUpdateBy;
    }

    public void setLastUpdateBy(String lastUpdateBy) {
        this.lastUpdateBy = lastUpdateBy;
    }
    public String getIsBeforehand() {
        return isBeforehand;
    }

    public void setIsBeforehand(String isBeforehand) {
        this.isBeforehand = isBeforehand;
    }
    public String getUserSyncId() {
        return userSyncId;
    }

    public void setUserSyncId(String userSyncId) {
        this.userSyncId = userSyncId;
    }
    public String getHouseSyncId() {
        return houseSyncId;
    }

    public void setHouseSyncId(String houseSyncId) {
        this.houseSyncId = houseSyncId;
    }

    @Override
    public String toString() {
        return "OpOrderGarbage{" +
        "id=" + id +
        ", projectId=" + projectId +
        ", activityId=" + activityId +
        ", userId=" + userId +
        ", userName=" + userName +
        ", houseId=" + houseId +
        ", houseName=" + houseName +
        ", isRegular=" + isRegular +
        ", status=" + status +
        ", yn=" + yn +
        ", creationDate=" + creationDate +
        ", createdBy=" + createdBy +
        ", lastUpdateDate=" + lastUpdateDate +
        ", lastUpdateBy=" + lastUpdateBy +
        ", isBeforehand=" + isBeforehand +
        ", userSyncId=" + userSyncId +
        ", houseSyncId=" + houseSyncId +
        "}";
    }
}
