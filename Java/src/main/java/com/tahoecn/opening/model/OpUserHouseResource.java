package com.tahoecn.opening.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 客户房源关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-19
 */
@ApiModel(value="OpUserHouseResource对象", description="客户房源关联表")
public class OpUserHouseResource implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "客户id")
    private Long userId;

    @ApiModelProperty(value = "房源id")
    private Long houseResourceId;

    @ApiModelProperty(value = "创建人账号")
    private String createId;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    public OpUserHouseResource() {
    }

    public OpUserHouseResource(Long activityId, Long userId, Long houseResourceId, String createId, String createName, LocalDateTime createTime) {
        this.activityId = activityId;
        this.userId = userId;
        this.houseResourceId = houseResourceId;
        this.createId = createId;
        this.createName = createName;
        this.createTime = createTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    public Long getHouseResourceId() {
        return houseResourceId;
    }

    public void setHouseResourceId(Long houseResourceId) {
        this.houseResourceId = houseResourceId;
    }
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "OpUserHouseResource{" +
        "id=" + id +
        ", activityId=" + activityId +
        ", userId=" + userId +
        ", houseResourceId=" + houseResourceId +
        ", createId=" + createId +
        ", createName=" + createName +
        ", createTime=" + createTime +
        "}";
    }
}
