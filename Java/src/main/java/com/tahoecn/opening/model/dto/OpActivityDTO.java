package com.tahoecn.opening.model.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.model.dto
 * @ClassName: OpActivityDTO
 * @Description:// TODO 活动DTO
 * @Date: 2022/8/11 10:17
 * @Version: 1.0
 */
public class OpActivityDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty(value = "自动生成订单标识：0--否；1--是（默认为0）")
    private Integer autoOrderFlag;

    @ApiModelProperty(value = "订单类型：0--全部；1--正式；2--模拟（默认为0）")
    private Integer orderType;

    @ApiModelProperty(value = "自动生成订单结果标识：0--未生成；1--正式已生成；2--模拟已生成；3--全部生成（默认为0）")
    private Integer autoOrderResultFlag;

    @ApiModelProperty(value = "是否模拟")
    private String isSimulation;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAutoOrderFlag() {
        return autoOrderFlag;
    }

    public void setAutoOrderFlag(Integer autoOrderFlag) {
        this.autoOrderFlag = autoOrderFlag;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getAutoOrderResultFlag() {
        return autoOrderResultFlag;
    }

    public void setAutoOrderResultFlag(Integer autoOrderResultFlag) {
        this.autoOrderResultFlag = autoOrderResultFlag;
    }

    public String getIsSimulation() {
        return isSimulation;
    }

    public void setIsSimulation(String isSimulation) {
        this.isSimulation = isSimulation;
    }
}
