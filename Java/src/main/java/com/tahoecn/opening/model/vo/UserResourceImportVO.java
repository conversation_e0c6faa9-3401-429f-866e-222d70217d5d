package com.tahoecn.opening.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: dzkp-java
 * @Package: com.tahoecn.opening.model.vo
 * @ClassName: UserResourceImportVO
 * @Description:// TODO 客户白名单导入对象VO
 * @Date: 2023/10/26 15:53
 * @Version: 1.0
 */
public class UserResourceImportVO implements Serializable {

    private static final long serialVersionUID = 9119566312747323029L;

    @Excel(name = "客户电话(与导入客户的电话强一致)")
    private String tel;

    @Excel(name = "房源同步ID(与导入房源的RoomGUID强一致)")
    private String houseSyncId;

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getHouseSyncId() {
        return houseSyncId;
    }

    public void setHouseSyncId(String houseSyncId) {
        this.houseSyncId = houseSyncId;
    }
}
