package com.tahoecn.opening.model.vo;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.model.vo
 * @Description:// TODO 阿里云变量Vo
 * @Date: 2025/5/12 15:45
 * @Version: 1.0
 **/
public class AliyunVariableVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 开始时间
     */
    private String openStr;

    /**
     * 手机号
     */
    private String userMobile;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户序号
     */
    private String userSort;

    /**
     * 验证码
     */
    private String code;

    public AliyunVariableVo() {
    }

    public AliyunVariableVo(String code) {
        this.code = code;
    }

    public AliyunVariableVo(String systemName, String activityName) {
        this.systemName = systemName;
        this.activityName = activityName;
    }

    public AliyunVariableVo(String systemName, String activityName, String openStr) {
        this.systemName = systemName;
        this.activityName = activityName;
        this.openStr = openStr;
    }

    public AliyunVariableVo(String systemName, String activityName, String openStr, String userMobile) {
        this.systemName = systemName;
        this.activityName = activityName;
        this.openStr = openStr;
        this.userMobile = userMobile;
    }

    public AliyunVariableVo(String systemName, String activityName, String openStr, String userName, String userSort) {
        this.systemName = systemName;
        this.activityName = activityName;
        this.openStr = openStr;
        this.userName = userName;
        this.userSort = userSort;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getOpenStr() {
        return openStr;
    }

    public void setOpenStr(String openStr) {
        this.openStr = openStr;
    }

    public String getUserMobile() {
        return userMobile;
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserSort() {
        return userSort;
    }

    public void setUserSort(String userSort) {
        this.userSort = userSort;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
