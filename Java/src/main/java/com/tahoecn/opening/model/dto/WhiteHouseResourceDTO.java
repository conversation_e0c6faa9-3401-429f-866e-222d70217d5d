package com.tahoecn.opening.model.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.model.dto
 * @ClassName: WhiteHouseResourceDTO
 * @Description:// TODO 房源白名单DTO
 * @Date: 2023/3/13 14:36
 * @Version: 1.0
 */
public class WhiteHouseResourceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "房源同步ID")
    private String houseSyncId;

    @Excel(name = "房源名称", width = 25)
    @ApiModelProperty(value = "房源名称")
    private String houseName;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "分期ID")
    private String proChildId;

    @ApiModelProperty(value = "分期名称")
    private String proChildName;

    @Excel(name = "楼栋", width = 25)
    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;

    @Excel(name = "单元", width = 25)
    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "单元层数")
    private String unitFloorNumber;

    @Excel(name = "楼层", width = 25)
    @ApiModelProperty(value = "当前层数")
    private String currentFloor;

    @Excel(name = "产品类型", width = 25)
    @ApiModelProperty(value = "房间类型")
    private String roomType;

    @Excel(name = "房间号", width = 25)
    @ApiModelProperty(value = "房间号")
    private String roomNum;

    @Excel(name = "房源总价(元)", width = 25)
    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;

    @Excel(name = "房源单价(元)", width = 25)
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @Excel(name = "优惠总价(元)", width = 25)
    @ApiModelProperty(value = "优惠总价")
    private BigDecimal discountTotalPrice;

    @Excel(name = "优惠单价(元)", width = 25)
    @ApiModelProperty(value = "优惠单价")
    private BigDecimal discountUnitPrice;

    @Excel(name = "房源面积(m2)", width = 25)
    @ApiModelProperty(value = "建筑面积")
    private BigDecimal houserArea;

    @Excel(name = "户型名称", width = 25)
    @ApiModelProperty(value = "户型")
    private String hourseType;

    @Excel(name = "户型结构", width = 25)
    @ApiModelProperty(value = "户型结构")
    private String roomStru;

    @ApiModelProperty(value = "户型图片")
    private String houseImg;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "是否可用")
    private String yn;

    @Excel(name = "用户名称", width = 25)
    @ApiModelProperty(value = "用户名称")
    private String userName;

    @Excel(name = "身份证号", width = 25)
    @ApiModelProperty(value = "身份证号")
    private String userIdCard;

    @Excel(name = "用户手机号", width = 25)
    @ApiModelProperty(value = "用户手机号")
    private String userMobile;

    @Excel(name = "操作人账号", width = 25)
    @ApiModelProperty(value = "创建人账号")
    private String createId;

    @Excel(name = "操作人", width = 25)
    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @Excel(name = "操作时间", width = 25)
    @ApiModelProperty(value = "创建时间str")
    private String createTimeStr;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getHouseSyncId() {
        return houseSyncId;
    }

    public void setHouseSyncId(String houseSyncId) {
        this.houseSyncId = houseSyncId;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProChildId() {
        return proChildId;
    }

    public void setProChildId(String proChildId) {
        this.proChildId = proChildId;
    }

    public String getProChildName() {
        return proChildName;
    }

    public void setProChildName(String proChildName) {
        this.proChildName = proChildName;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getUnitFloorNumber() {
        return unitFloorNumber;
    }

    public void setUnitFloorNumber(String unitFloorNumber) {
        this.unitFloorNumber = unitFloorNumber;
    }

    public String getCurrentFloor() {
        return currentFloor;
    }

    public void setCurrentFloor(String currentFloor) {
        this.currentFloor = currentFloor;
    }

    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getDiscountTotalPrice() {
        return discountTotalPrice;
    }

    public void setDiscountTotalPrice(BigDecimal discountTotalPrice) {
        this.discountTotalPrice = discountTotalPrice;
    }

    public BigDecimal getDiscountUnitPrice() {
        return discountUnitPrice;
    }

    public void setDiscountUnitPrice(BigDecimal discountUnitPrice) {
        this.discountUnitPrice = discountUnitPrice;
    }

    public BigDecimal getHouserArea() {
        return houserArea;
    }

    public void setHouserArea(BigDecimal houserArea) {
        this.houserArea = houserArea;
    }

    public String getHourseType() {
        return hourseType;
    }

    public void setHourseType(String hourseType) {
        this.hourseType = hourseType;
    }

    public String getRoomStru() {
        return roomStru;
    }

    public void setRoomStru(String roomStru) {
        this.roomStru = roomStru;
    }

    public String getHouseImg() {
        return houseImg;
    }

    public void setHouseImg(String houseImg) {
        this.houseImg = houseImg;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getYn() {
        return yn;
    }

    public void setYn(String yn) {
        this.yn = yn;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserIdCard() {
        return userIdCard;
    }

    public void setUserIdCard(String userIdCard) {
        this.userIdCard = userIdCard;
    }

    public String getUserMobile() {
        return userMobile;
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }
}
