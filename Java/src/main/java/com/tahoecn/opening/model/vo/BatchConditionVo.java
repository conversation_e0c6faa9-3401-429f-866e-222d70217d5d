package com.tahoecn.opening.model.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.model.vo
 * @Description:// TODO 批次条件vo
 * @Date: 2025/4/27 15:05
 * @Version: 1.0
 **/
public class BatchConditionVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "优先级")
    private Integer priorityLevel;

    @ApiModelProperty(value = "批次名称")
    private String batchName;

    @ApiModelProperty(value = "同步下一批次标识：0--同步;1--不同步（默认为0）")
    private Integer syncFlag;

    /**
     *  该批次第一个用户的模拟开始时间
     */
    @ApiModelProperty(value = "模拟开始时间")
    private Date simulationBeginDate;

    /**
     *  该批次第一个用户的模拟结束时间
     */
    @ApiModelProperty(value = "模拟结束时间")
    private Date simulationEndDate;

    /**
     *  该批次第一个用户的正式开始时间
     */
    @ApiModelProperty(value = "正式开始时间")
    private Date formalBeginDate;

    /**
     *  该批次第一个用户的正式结束时间
     */
    @ApiModelProperty(value = "正式结束时间")
    private Date formalEndDate;

    @ApiModelProperty(value = "模拟是否已经同步")
    private boolean simulationFlag = false;

    @ApiModelProperty(value = "正式是否已经同步")
    private boolean formalFlag = false;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    public BatchConditionVo() {
    }

    public BatchConditionVo(Integer id, Integer priorityLevel, String batchName, Integer syncFlag, Integer userId) {
        this.id = id;
        this.priorityLevel = priorityLevel;
        this.batchName = batchName;
        this.syncFlag = syncFlag;
        this.userId = userId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPriorityLevel() {
        return priorityLevel;
    }

    public void setPriorityLevel(Integer priorityLevel) {
        this.priorityLevel = priorityLevel;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public Integer getSyncFlag() {
        return syncFlag;
    }

    public void setSyncFlag(Integer syncFlag) {
        this.syncFlag = syncFlag;
    }

    public Date getSimulationBeginDate() {
        return simulationBeginDate;
    }

    public void setSimulationBeginDate(Date simulationBeginDate) {
        this.simulationBeginDate = simulationBeginDate;
    }

    public Date getFormalBeginDate() {
        return formalBeginDate;
    }

    public void setFormalBeginDate(Date formalBeginDate) {
        this.formalBeginDate = formalBeginDate;
    }

    public boolean getSimulationFlag() {
        return simulationFlag;
    }

    public void setSimulationFlag(boolean simulationFlag) {
        this.simulationFlag = simulationFlag;
    }

    public boolean getFormalFlag() {
        return formalFlag;
    }

    public void setFormalFlag(boolean formalFlag) {
        this.formalFlag = formalFlag;
    }

    public Date getSimulationEndDate() {
        return simulationEndDate;
    }

    public void setSimulationEndDate(Date simulationEndDate) {
        this.simulationEndDate = simulationEndDate;
    }

    public Date getFormalEndDate() {
        return formalEndDate;
    }

    public void setFormalEndDate(Date formalEndDate) {
        this.formalEndDate = formalEndDate;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }
}
