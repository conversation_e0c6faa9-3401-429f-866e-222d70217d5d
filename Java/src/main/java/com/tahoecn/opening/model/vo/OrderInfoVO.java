package com.tahoecn.opening.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

public class OrderInfoVO  implements Serializable{


    private static final long serialVersionUID = -823763392057490300L;

    private  String orderId;

    private String activityIdNow;

    private String projectId;

    private String saleId;

    @Excel(name = "客户姓名",height = 11, width = 15)
    private String name;

    @Excel(name = "电话号码",height = 11, width = 15)
    private String tel;

    @Excel(name = "身份证号",height = 11, width = 15)
    private String idCard;

    @Excel(name = "楼栋号",height = 11, width = 15)
    private String buildingName;

    @Excel(name = "单元号",height = 11, width = 15)
    private String unitName;

    @Excel(name = "房间号",height = 11, width = 15)
    private String roomNum;

    @Excel(name = "房源名称",height = 11, width = 15)
    private String houseName;

    @Excel(name = "下单时间",height = 11, width = 15)
    private LocalDateTime creationDate;

    @Excel(name = "客户/房源来源",height = 11, width = 15)
    private String createdBy;

    @Excel(name = "预置订单",height = 11, width = 15)
    private String isBeforehand;

    @Excel(name = "置业顾问",height = 11, width = 15)
    private String saleName;

    @Excel(name = "客户同步ID(CstGUID)",height = 11, width = 15)
    private String cstguid;

    @Excel(name = "认筹次数(BookingNum)",height = 11, width = 15)
    private String bookingNum;

    @Excel(name = "房源同步ID(RoomGUID)",height = 11, width = 15)
    private String houseSyncId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "电子签编号")
    private String electronCode;

    @ApiModelProperty(value = "电子签名称")
    private String electronName;

    @ApiModelProperty(value = "订单类型：0--无需电子签;1--调用电子签成功;2--操作异常;3--电子签返回结果不是成功")
    private Integer orderType;

    @ApiModelProperty(value = "电子签id")
    private String electronId;

    @ApiModelProperty(value = "电子签url")
    private String electronUrl;

    @ApiModelProperty(value = "电子签异常结果")
    private String electronResult;

    @ApiModelProperty(value = "订单状态：0--待签约;1--签约中;2--签约完成;3--签约失败;(默认为0)")
    private Integer orderStatus;

    @ApiModelProperty(value = "电子签附件url")
    private String electronFileUrl;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getActivityIdNow() {
        return activityIdNow;
    }

    public void setActivityIdNow(String activityIdNow) {
        this.activityIdNow = activityIdNow;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getSaleId() {
        return saleId;
    }

    public void setSaleId(String saleId) {
        this.saleId = saleId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(LocalDateTime creationDate) {
        this.creationDate = creationDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getIsBeforehand() {
        return isBeforehand;
    }

    public void setIsBeforehand(String isBeforehand) {
        this.isBeforehand = isBeforehand;
    }

    public String getSaleName() {
        return saleName;
    }

    public void setSaleName(String saleName) {
        this.saleName = saleName;
    }

    public String getCstguid() {
        return cstguid;
    }

    public void setCstguid(String cstguid) {
        this.cstguid = cstguid;
    }

    public String getBookingNum() {
        return bookingNum;
    }

    public void setBookingNum(String bookingNum) {
        this.bookingNum = bookingNum;
    }

    public String getHouseSyncId() {
        return houseSyncId;
    }

    public void setHouseSyncId(String houseSyncId) {
        this.houseSyncId = houseSyncId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getElectronCode() {
        return electronCode;
    }

    public void setElectronCode(String electronCode) {
        this.electronCode = electronCode;
    }

    public String getElectronName() {
        return electronName;
    }

    public void setElectronName(String electronName) {
        this.electronName = electronName;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getElectronId() {
        return electronId;
    }

    public void setElectronId(String electronId) {
        this.electronId = electronId;
    }

    public String getElectronUrl() {
        return electronUrl;
    }

    public void setElectronUrl(String electronUrl) {
        this.electronUrl = electronUrl;
    }

    public String getElectronResult() {
        return electronResult;
    }

    public void setElectronResult(String electronResult) {
        this.electronResult = electronResult;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getElectronFileUrl() {
        return electronFileUrl;
    }

    public void setElectronFileUrl(String electronFileUrl) {
        this.electronFileUrl = electronFileUrl;
    }
}
