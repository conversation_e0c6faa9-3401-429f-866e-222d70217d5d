package com.tahoecn.opening.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.data.annotation.Transient;

/**
 * <p>
 * 订单
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@ApiModel(value="OpOrder对象", description="订单")
public class OpOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Excel(name = "项目ID",height = 11, width = 15)
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @Excel(name = "活动ID",height = 11, width = 15)
    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @Excel(name = "用户ID",height = 11, width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;

    @Excel(name = "用户名字",height = 11, width = 15)
    @ApiModelProperty(value = "用户名字")
    private String userName;

    @Excel(name = "订单房源ID",height = 11, width = 15)
    @ApiModelProperty(value = "订单房源ID")
    private String houseId;

    @Excel(name = "订单房源名称",height = 11, width = 15)
    @ApiModelProperty(value = "订单房源名称")
    private String houseName;

    @Excel(name = "是否正式",height = 11, width = 15)
    @ApiModelProperty(value = "是否正式")
    private String isRegular;

    @Excel(name = "状态",height = 11, width = 15)
    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "是否可用")
    private String yn;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime creationDate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新日期")
    private LocalDateTime lastUpdateDate;

    @ApiModelProperty(value = "更新人")
    private String lastUpdateBy;

    @Excel(name = "是否预制订单",height = 11, width = 15)
    @ApiModelProperty(value = "是否预制订单")
    private String isBeforehand;

    @ApiModelProperty(value = "用户同步id")
    private String userSyncId;

    @ApiModelProperty(value = "房源同步id")
    private String houseSyncId;

    @ApiModelProperty(value = "是否回传")
    private String isReturn;

    @ApiModelProperty(value = "是否有效标识：0--有效;1--无效;(默认为0)")
    private Integer effectiveFlag;

    @ApiModelProperty(value = "电子签编号")
    private String electronCode;

    @ApiModelProperty(value = "电子签名称")
    private String electronName;

    @ApiModelProperty(value = "订单类型：0--无需电子签;1--调用电子签成功;2--操作异常;3--电子签返回结果不是成功")
    private Integer orderType;

    @ApiModelProperty(value = "电子签id")
    private String electronId;

    @ApiModelProperty(value = "电子签url")
    private String electronUrl;

    @ApiModelProperty(value = "电子签异常结果")
    private String electronResult;

    @ApiModelProperty(value = "订单状态：0--待签约;1--签约中;2--签约完成;3--签约失败;(默认为0)")
    private Integer orderStatus;

    @ApiModelProperty(value = "电子签附件url")
    private String electronFileUrl;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    private Date saleControlDate;

    public OpOrder() {
    }

    public OpOrder(String houseId, String houseName, Date saleControlDate) {
        this.houseId = houseId;
        this.houseName = houseName;
        this.saleControlDate = saleControlDate;
    }

    public String getIsReturn() {
        return isReturn;
    }

    public void setIsReturn(String isReturn) {
        this.isReturn = isReturn;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getIsRegular() {
        return isRegular;
    }

    public void setIsRegular(String isRegular) {
        this.isRegular = isRegular;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getYn() {
        return yn;
    }

    public void setYn(String yn) {
        this.yn = yn;
    }

    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(LocalDateTime creationDate) {
        this.creationDate = creationDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(LocalDateTime lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getLastUpdateBy() {
        return lastUpdateBy;
    }

    public void setLastUpdateBy(String lastUpdateBy) {
        this.lastUpdateBy = lastUpdateBy;
    }

    public String getIsBeforehand() {
        return isBeforehand;
    }

    public void setIsBeforehand(String isBeforehand) {
        this.isBeforehand = isBeforehand;
    }

    public String getUserSyncId() {
        return userSyncId;
    }

    public void setUserSyncId(String userSyncId) {
        this.userSyncId = userSyncId;
    }

    public String getHouseSyncId() {
        return houseSyncId;
    }

    public void setHouseSyncId(String houseSyncId) {
        this.houseSyncId = houseSyncId;
    }

    public Integer getEffectiveFlag() {
        return effectiveFlag;
    }

    public void setEffectiveFlag(Integer effectiveFlag) {
        this.effectiveFlag = effectiveFlag;
    }

    public Date getSaleControlDate() {
        return saleControlDate;
    }

    public void setSaleControlDate(Date saleControlDate) {
        this.saleControlDate = saleControlDate;
    }

    public String getElectronCode() {
        return electronCode;
    }

    public void setElectronCode(String electronCode) {
        this.electronCode = electronCode;
    }

    public String getElectronName() {
        return electronName;
    }

    public void setElectronName(String electronName) {
        this.electronName = electronName;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getElectronId() {
        return electronId;
    }

    public void setElectronId(String electronId) {
        this.electronId = electronId;
    }

    public String getElectronUrl() {
        return electronUrl;
    }

    public void setElectronUrl(String electronUrl) {
        this.electronUrl = electronUrl;
    }

    public String getElectronResult() {
        return electronResult;
    }

    public void setElectronResult(String electronResult) {
        this.electronResult = electronResult;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getElectronFileUrl() {
        return electronFileUrl;
    }

    public void setElectronFileUrl(String electronFileUrl) {
        this.electronFileUrl = electronFileUrl;
    }

    @Override
    public String toString() {
        return "OpOrder{" +
                "id=" + id +
                ", projectId='" + projectId + '\'' +
                ", activityId='" + activityId + '\'' +
                ", userId='" + userId + '\'' +
                ", userName='" + userName + '\'' +
                ", houseId='" + houseId + '\'' +
                ", houseName='" + houseName + '\'' +
                ", isRegular='" + isRegular + '\'' +
                ", status='" + status + '\'' +
                ", yn='" + yn + '\'' +
                ", creationDate=" + creationDate +
                ", createdBy='" + createdBy + '\'' +
                ", lastUpdateDate=" + lastUpdateDate +
                ", lastUpdateBy='" + lastUpdateBy + '\'' +
                ", isBeforehand='" + isBeforehand + '\'' +
                ", userSyncId='" + userSyncId + '\'' +
                ", houseSyncId='" + houseSyncId + '\'' +
                ", isReturn='" + isReturn + '\'' +
                ", effectiveFlag=" + effectiveFlag +
                ", electronCode='" + electronCode + '\'' +
                ", electronName='" + electronName + '\'' +
                ", orderType=" + orderType +
                ", electronId='" + electronId + '\'' +
                ", electronUrl='" + electronUrl + '\'' +
                ", electronResult='" + electronResult + '\'' +
                ", orderStatus=" + orderStatus +
                ", electronFileUrl='" + electronFileUrl + '\'' +
                ", saleControlDate=" + saleControlDate +
                '}';
    }
}
