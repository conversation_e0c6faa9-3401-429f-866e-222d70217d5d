/**
 * Copyright 2019 bejson.com
 */
package com.tahoecn.opening.model.interfaceBean;



import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;

/**
 * Auto-generated: 2019-04-11 11:15:20
 */
public class Param {

    @JSONField(name = "List")
    private List<CustomerFollowData> customerFollowData;

    public List<CustomerFollowData> getCustomerFollowData() {
        return customerFollowData;
    }

    public void setCustomerFollowData(List<CustomerFollowData> customerFollowData) {
        this.customerFollowData = customerFollowData;
    }
}