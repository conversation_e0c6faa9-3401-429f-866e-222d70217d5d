package com.tahoecn.opening.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 销售订单导出
 */
public class SalesOrderVO implements Serializable{


    private static final long serialVersionUID = -2220912114033836051L;


    @Excel(name = "销售机会ID",height = 11, width = 15)
    @ApiModelProperty(value = "OppGUID")
    private String oppguid;

    @Excel(name = "房屋ID",height = 11, width = 15)
    @ApiModelProperty(value = "房源同步ID")
    private String houseSyncId;
    @ApiModelProperty(value = "房源ID")
    private Integer houseId;
    @Excel(name = "房屋名称",height = 11, width = 15)
    @ApiModelProperty(value = "房源名称")
    private String houseName;

    @Excel(name = "购房人ID",height = 11, width = 15)
    @ApiModelProperty(value = "CstGUID")
    private String cstguid;

    @Excel(name = "购房人姓名",height = 11, width = 15)
    @ApiModelProperty(value = "名字")
    private String name;

    @Excel(name = "购房人手机号",height = 11, width = 15)
    @ApiModelProperty(value = "电话")
    private String tel;

    @Excel(name = "购房人身份证",height = 11, width = 15)
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @Excel(name = "购房价格",height = 11, width = 15)
    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Excel(name = "购房时间",height = 11, width = 15)
    @ApiModelProperty(value = "购房时间")
    private Date creationDate;

    public String getOppguid() {
        return oppguid;
    }

    public void setOppguid(String oppguid) {
        this.oppguid = oppguid;
    }

    public String getHouseSyncId() {
        return houseSyncId;
    }

    public void setHouseSyncId(String houseSyncId) {
        this.houseSyncId = houseSyncId;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getCstguid() {
        return cstguid;
    }

    public void setCstguid(String cstguid) {
        this.cstguid = cstguid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Integer getHouseId() {
        return houseId;
    }

    public void setHouseId(Integer houseId) {
        this.houseId = houseId;
    }

    @Override
    public String toString() {
        return "SalesOrderVO{" +
                "oppguid='" + oppguid + '\'' +
                ", houseSyncId='" + houseSyncId + '\'' +
                ", houseName='" + houseName + '\'' +
                ", cstguid='" + cstguid + '\'' +
                ", name='" + name + '\'' +
                ", tel='" + tel + '\'' +
                ", idCard='" + idCard + '\'' +
                ", totalPrice=" + totalPrice +
                ", creationDate=" + creationDate +
                '}';
    }
}
