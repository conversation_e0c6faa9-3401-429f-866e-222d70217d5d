package com.tahoecn.opening.model.electronVo;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.model.electronVo
 * @Description:// TODO 合同状态变更回调入参Vo
 * @Date: 2025/1/17 10:06
 * @Version: 1.0
 */
public class ContractStatusParamVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  合同ID
     */
    private String id;

    /**
     *  电子合同平台合同编号
     */
    private String code;

    /**
     *  业务系统合同编号
     */
    private String ctCode;

    /**
     *  合同状态：0-待审核，1-待签章，2-签章中，3-已签章，4-已归档，5-已撤销，6-作废中，7-已作废，8-审核不通过，9-审核中,10-待设置
     */
    private String status;

    /**
     *  类别：1：商务合同，2：人事合同，3：电子文件
     */
    private String type;

    /**
     *  拒签原因
     */
    private String refuseReason;

    /**
     *  撤销原因类型：1：合同内容问题;2：业务系统问题；3：转线下合同;0：其它（默认）
     */
    private String reasonType;

    /**
     *  撤销原因
     */
    private String reason;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRefuseReason() {
        return refuseReason;
    }

    public void setRefuseReason(String refuseReason) {
        this.refuseReason = refuseReason;
    }

    public String getReasonType() {
        return reasonType;
    }

    public void setReasonType(String reasonType) {
        this.reasonType = reasonType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
