package com.tahoecn.opening.model.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.model.vo
 * @ClassName: WhiteHouseResourceParamVO
 * @Description:// TODO 房源白名单条查参数VO
 * @Date: 2023/3/13 14:23
 * @Version: 1.0
 */
public class WhiteHouseResourceParamVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  开始页
     */
    private int pageNum = 1;

    /**
     *  每页条数
     */
    private int pageSize = 10;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "房源名称")
    private String houseName;

    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;

    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "当前层数")
    private String currentFloor;

    @ApiModelProperty(value = "房间类型")
    private String roomType;

    @ApiModelProperty(value = "房间号")
    private String roomNum;

    @ApiModelProperty(value = "户型")
    private String hourseType;

    @ApiModelProperty(value = "户型结构")
    private String roomStru;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "用户手机号")
    private String userMobile;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getCurrentFloor() {
        return currentFloor;
    }

    public void setCurrentFloor(String currentFloor) {
        this.currentFloor = currentFloor;
    }

    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public String getHourseType() {
        return hourseType;
    }

    public void setHourseType(String hourseType) {
        this.hourseType = hourseType;
    }

    public String getRoomStru() {
        return roomStru;
    }

    public void setRoomStru(String roomStru) {
        this.roomStru = roomStru;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserMobile() {
        return userMobile;
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }
}
