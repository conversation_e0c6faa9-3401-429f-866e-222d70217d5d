package com.tahoecn.opening.model.vo;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.model.vo
 * @ClassName: UserHouseResourceParamVO
 * @Description:// TODO 新增修改用户房源关系参数VO
 * @Date: 2022/7/19 13:56
 * @Version: 1.0
 */
public class UserHouseResourceParamVO implements Serializable {

    private static final long serialVersionUID = 9153195910153042271L;

    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "用户ID必传")
    private Long userId;

    @ApiModelProperty(value = "活动id")
    @NotNull(message = "活动ID必传")
    private Long activityId;

    @ApiModelProperty(value = "房源id集合")
    private List<Long> houseResourceIdList;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public List<Long> getHouseResourceIdList() {
        return houseResourceIdList;
    }

    public void setHouseResourceIdList(List<Long> houseResourceIdList) {
        this.houseResourceIdList = houseResourceIdList;
    }
}
