package com.tahoecn.opening.model.vo;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: chengben
 * @Package: com.telehot.co.appframe.models.vo
 * @ClassName: SyncOrgVO
 * @Description:// TODO 同步组织机构VO
 * @Date: 2023/11/20 15:45
 * @Version: 1.0
 */
public class SyncOrgVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  唯一标识
     */
    private String id;

    /**
     *  名称
     */
    private String name;

    /**
     *  父级唯一标识
     */
    private String parentId;

    /**
     *  删除标志 0 = 不删除，默认 1 = 删除
     */
    private String deletedStatus;

    /**
     *  类型 1:集团  2:公司 3:部门 4:项目 5:分期 6:板块（板块是成本系统来定义）
     */
    private String type;

    /**
     *  子集
     */
    private List<SyncOrgVO> childVOList;

    /**
     *  成本表id
     */
    private Long costId;

    /**
     *  成本表父级id
     */
    private Long costParentId;

    /**
     *  partyEntity表id
     */
    private Long entityId;

    /**
     *  partyEntity表父级id
     */
    private Long entityParentId;

    /**
     *  全路径
     */
    private String prefix;

    /**
     *  所属公司id
     */
    private Long belongCompanyId;

    /**
     *  所属项目id
     */
    private Long belongProjectId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getDeletedStatus() {
        return deletedStatus;
    }

    public void setDeletedStatus(String deletedStatus) {
        this.deletedStatus = deletedStatus;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<SyncOrgVO> getChildVOList() {
        return childVOList;
    }

    public void setChildVOList(List<SyncOrgVO> childVOList) {
        this.childVOList = childVOList;
    }

    public Long getCostId() {
        return costId;
    }

    public void setCostId(Long costId) {
        this.costId = costId;
    }

    public Long getCostParentId() {
        return costParentId;
    }

    public void setCostParentId(Long costParentId) {
        this.costParentId = costParentId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public Long getEntityParentId() {
        return entityParentId;
    }

    public void setEntityParentId(Long entityParentId) {
        this.entityParentId = entityParentId;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public Long getBelongCompanyId() {
        return belongCompanyId;
    }

    public void setBelongCompanyId(Long belongCompanyId) {
        this.belongCompanyId = belongCompanyId;
    }

    public Long getBelongProjectId() {
        return belongProjectId;
    }

    public void setBelongProjectId(Long belongProjectId) {
        this.belongProjectId = belongProjectId;
    }
}
