package com.tahoecn.opening.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 客户
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-09
 */
@ApiModel(value = "OpUser对象", description = "客户")
public class ConsultantVO implements Serializable {


    private static final long serialVersionUID = 9142432524659457479L;
//    @ApiModelProperty(value = "活动ID")
//    private String activityId;
//
//    @Excel(name = "项目ID")
//    @ApiModelProperty(value = "项目ID")
//    private String projectId;

//    @Excel(name = "OppGUID")
//    @ApiModelProperty(value = "OppGUID")
//    private String oppguid;

    @Excel(name = "CstGUID(唯一)")
    @ApiModelProperty(value = "CstGUID")
    private String cstguid;

    @Excel(name = "名字")
    @ApiModelProperty(value = "名字")
    private String name;

    @Excel(name = "性别(男女)")
    @ApiModelProperty(value = "性别(男女)")
    private String gender;

//    @Excel(name = "登陆名")
//    @ApiModelProperty(value = "登陆名")
//    private String loginName;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Excel(name = "创建日期:yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期：yyyy-MM-dd HH:mm:ss")
    private Date creationDate;

    @Excel(name = "电话(唯一)" )
    @ApiModelProperty(value = "电话")
    private String tel;

    @Excel(name = "角色(置业顾问/营销经理)" )
    @ApiModelProperty(value = "角色名称")
    private String roleName;

//    @Excel(name = "角色代码" )
//    @ApiModelProperty(value = "角色代码")
//    private String roleCode;

//    @Excel(name = "销售类型" )
//    @ApiModelProperty(value = "销售类型")
//    private String accountType;
//
//    @Excel(name = "销售类型名" )
//    @ApiModelProperty(value = "销售类型名")
//    private String accountName;


    public String getCstguid() {
        return cstguid;
    }

    public void setCstguid(String cstguid) {
        this.cstguid = cstguid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }
}
