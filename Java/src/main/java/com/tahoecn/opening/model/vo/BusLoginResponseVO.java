package com.tahoecn.opening.model.vo;

import java.io.Serializable;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: bpm
 * @Package: com.tyzq.bpm.model.vo.business.response
 * @ClassName: BusLoginResponseVO
 * @Description:// TODO 业务用户登录响应结果VO
 * @Date: 2022/10/19 13:28
 * @Version: 1.0
 */
public class BusLoginResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * fid
     */
    private String fid;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 用户手机号
     */
    private String userMobile;
    /**
     * 用户邮箱
     */
    private String userEmail;
    /**
     * 组织id
     */
    private String orgId;
    /**
     *  组织名称
     */
    private String orgName;
    /**
     * 业务用户登录token
     */
    private String busUserToken;
    /**
     *  是超级管理员
     */
    private Boolean adminFlag;

    public BusLoginResponseVO() {
    }

    public BusLoginResponseVO(Long id, String fid, String userAccount, String userName, String userMobile, String userEmail, String orgId, String orgName, String busUserToken) {
        this.id = id;
        this.fid = fid;
        this.userAccount = userAccount;
        this.userName = userName;
        this.userMobile = userMobile;
        this.userEmail = userEmail;
        this.orgId = orgId;
        this.orgName = orgName;
        this.busUserToken = busUserToken;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFid() {
        return fid;
    }

    public void setFid(String fid) {
        this.fid = fid;
    }

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserMobile() {
        return userMobile;
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getBusUserToken() {
        return busUserToken;
    }

    public void setBusUserToken(String busUserToken) {
        this.busUserToken = busUserToken;
    }

    public Boolean getAdminFlag() {
        return adminFlag;
    }

    public void setAdminFlag(Boolean adminFlag) {
        this.adminFlag = adminFlag;
    }
}
