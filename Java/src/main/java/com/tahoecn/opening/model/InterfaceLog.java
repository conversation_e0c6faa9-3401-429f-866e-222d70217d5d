package com.tahoecn.opening.model;

import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-06
 */
@ApiModel(value="InterfaceLog对象", description="")
public class InterfaceLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "请求")
    private String request;

    @ApiModelProperty(value = "返回状态码")
    private String errcode;

    @ApiModelProperty(value = "返回信息")
    private String errmsg;

    @ApiModelProperty(value = "调用时间")
    private LocalDateTime date;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }
    public String getErrcode() {
        return errcode;
    }

    public void setErrcode(String errcode) {
        this.errcode = errcode;
    }
    public String getErrmsg() {
        return errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }
    public LocalDateTime getDate() {
        return date;
    }

    public void setDate(LocalDateTime date) {
        this.date = date;
    }

    @Override
    public String toString() {
        return "InterfaceLog{" +
        "id=" + id +
        ", request=" + request +
        ", errcode=" + errcode +
        ", errmsg=" + errmsg +
        ", date=" + date +
        "}";
    }
}
