package com.tahoecn.opening.model.electronVo;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.model.electronVo
 * @Description:// TODO 合同文件数据回调入参Vo
 * @Date: 2025/1/17 10:19
 * @Version: 1.0
 */
public class ContractFileParamVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  合同ID
     */
    private String id;

    /**
     *  电子合同平台合同编号
     */
    private String code;

    /**
     *  业务系统合同编号
     */
    private String ctCode;

    /**
     *  合同PDF文件base64数据
     */
    private String fileBase64Str;

    /**
     *  合同状态：3-已签章
     */
    private String status;

    /**
     *  类别：1：商务合同，2：人事合同，3：电子文件
     */
    private String type;

    /**
     *  合同业务数据项列表
     */
    private List<BusinessItemVo> businessItemList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode;
    }

    public String getFileBase64Str() {
        return fileBase64Str;
    }

    public void setFileBase64Str(String fileBase64Str) {
        this.fileBase64Str = fileBase64Str;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<BusinessItemVo> getBusinessItemList() {
        return businessItemList;
    }

    public void setBusinessItemList(List<BusinessItemVo> businessItemList) {
        this.businessItemList = businessItemList;
    }

    /**
     *  合同业务数据项Vo对象
     */
    class BusinessItemVo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         *  关键字
         */
        private String keyword;

        /**
         *  输入值
         */
        private String Value;

        public String getKeyword() {
            return keyword;
        }

        public void setKeyword(String keyword) {
            this.keyword = keyword;
        }

        public String getValue() {
            return Value;
        }

        public void setValue(String value) {
            Value = value;
        }
    }
}
