package com.tahoecn.opening.model.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.model.dto
 * @ClassName: HouseResourceDTO
 * @Description:// TODO 房源DTO
 * @Date: 2022/7/19 16:34
 * @Version: 1.0
 */
public class HouseResourceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long houseResourceId;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "房源同步ID")
    private String houseSyncId;

    @ApiModelProperty(value = "房源名称")
    private String houseName;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;

    @ApiModelProperty(value = "单元名称")
    private String unitName;

    @ApiModelProperty(value = "当前层数")
    private String currentFloor;

    @ApiModelProperty(value = "房间号")
    private String roomNum;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "户型")
    private String hourseType;

    @ApiModelProperty(value = "户型名称")
    private String hourseTypeName;

    @ApiModelProperty(value = "户型描述")
    private String houseDescribe;

    @ApiModelProperty(value = "建筑面积")
    private BigDecimal houserArea;

    @ApiModelProperty(value = "户型图片")
    private String houseImg;

    /**
     * 前端需要字段，楼栋、单元给true，房间给false
     */
    @ApiModelProperty(value = "前端需要字段，楼栋、单元给true，房间给false")
    private boolean disable = false;

    private List<HouseResourceDTO> childList;

    /**
     * 自定义排序字段
     */
    private Long sortNum;

    public Long getHouseResourceId() {
        return houseResourceId;
    }

    public void setHouseResourceId(Long houseResourceId) {
        this.houseResourceId = houseResourceId;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getHouseSyncId() {
        return houseSyncId;
    }

    public void setHouseSyncId(String houseSyncId) {
        this.houseSyncId = houseSyncId;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getCurrentFloor() {
        return currentFloor;
    }

    public void setCurrentFloor(String currentFloor) {
        this.currentFloor = currentFloor;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getHourseType() {
        return hourseType;
    }

    public void setHourseType(String hourseType) {
        this.hourseType = hourseType;
    }

    public String getHourseTypeName() {
        return hourseTypeName;
    }

    public void setHourseTypeName(String hourseTypeName) {
        this.hourseTypeName = hourseTypeName;
    }

    public String getHouseDescribe() {
        return houseDescribe;
    }

    public void setHouseDescribe(String houseDescribe) {
        this.houseDescribe = houseDescribe;
    }

    public BigDecimal getHouserArea() {
        return houserArea;
    }

    public void setHouserArea(BigDecimal houserArea) {
        this.houserArea = houserArea;
    }

    public String getHouseImg() {
        return houseImg;
    }

    public void setHouseImg(String houseImg) {
        this.houseImg = houseImg;
    }

    public boolean getDisable() {
        return disable;
    }

    public void setDisable(boolean disable) {
        this.disable = disable;
    }

    public List<HouseResourceDTO> getChildList() {
        return childList;
    }

    public void setChildList(List<HouseResourceDTO> childList) {
        this.childList = childList;
    }

    public Long getSortNum() {
        return sortNum;
    }

    public void setSortNum(Long sortNum) {
        this.sortNum = sortNum;
    }
}
