package com.tahoecn.opening.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

public class CustomerAnalysisVO implements Serializable {
    private static final long serialVersionUID = 603423926222606519L;


    @Excel(name = "客户姓名")
    private  String name;

    @Excel(name = "手机号码")
    private  String tel;

    @Excel(name = "身份证号")
    private  String idCard;

    @Excel(name = "登录时间")
    private String lastLoginDate;

    @Excel(name = "有无订单")
    private  String orderYN;

    @Excel(name = "置业顾问")
    private  String saleName;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getLastLoginDate() {
        return lastLoginDate;
    }

    public void setLastLoginDate(String lastLoginDate) {
        this.lastLoginDate = lastLoginDate;
    }

    public String getSaleName() {
        return saleName;
    }

    public void setSaleName(String saleName) {
        this.saleName = saleName;
    }

    public String getOrderYN() {
        return orderYN;
    }

    public void setOrderYN(String orderYN) {
        this.orderYN = orderYN;
    }
}
