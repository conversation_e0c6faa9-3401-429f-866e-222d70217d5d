package com.tahoecn.opening.model.vo;

import java.io.Serializable;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: dzkp-java
 * @Package: com.tahoecn.opening.model.vo
 * @ClassName: ShowSplitVO
 * @Description:// TODO 展示换行对象VO
 * @Date: 2023/10/27 13:31
 * @Version: 1.0
 */
public class ShowSplitVO implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final int DEFAULT_NUM = 99999;

    /**
     * 换行数量
     */
    private Integer splitNum;

    /**
     * 产品类型
     */
    private String roomType;

    /**
     * 是否被选中
     */
    private boolean checkFlag = false;

    public ShowSplitVO() {
    }

    public ShowSplitVO(Integer splitNum, String roomType, boolean checkFlag) {
        this.splitNum = splitNum;
        this.roomType = roomType;
        this.checkFlag = checkFlag;
    }

    public Integer getSplitNum() {
        return splitNum;
    }

    public void setSplitNum(Integer splitNum) {
        this.splitNum = splitNum;
    }

    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    public boolean isCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(boolean checkFlag) {
        this.checkFlag = checkFlag;
    }
}
