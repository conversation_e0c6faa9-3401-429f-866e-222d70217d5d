package com.tahoecn.opening.model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

/**
 * <p>
 * 短息发送日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-18
 */
public class CsSendSmsLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 手机号
     */
    private String sendMobiles;
    /**
     * 发送内容
     */
    private String contentDesc;
    /**
     * 返回信息
     */
    private String sendResult;
    /**
     * 发送时间
     */
    private Date createDate;

    /**
     * 活动id
     */
    private String activityId;

    /**
     * 任务id
     */
    private String jobId;

    /**
     * 任务类型
     */
    private String jobType;

    public CsSendSmsLog() {
    }

    public CsSendSmsLog(String userName, String sendMobiles, String contentDesc, String sendResult, Date createDate, String activityId, String jobType) {
        this.userName = userName;
        this.sendMobiles = sendMobiles;
        this.contentDesc = contentDesc;
        this.sendResult = sendResult;
        this.createDate = createDate;
        this.activityId = activityId;
        this.jobType = jobType;
    }

    public CsSendSmsLog(String userName, String sendMobiles, String contentDesc, String sendResult, Date createDate, String activityId, String jobType, String jobId) {
        this.userName = userName;
        this.sendMobiles = sendMobiles;
        this.contentDesc = contentDesc;
        this.sendResult = sendResult;
        this.createDate = createDate;
        this.activityId = activityId;
        this.jobType = jobType;
        this.jobId = jobId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getSendMobiles() {
        return sendMobiles;
    }

    public void setSendMobiles(String sendMobiles) {
        this.sendMobiles = sendMobiles;
    }

    public String getContentDesc() {
        return contentDesc;
    }

    public void setContentDesc(String contentDesc) {
        this.contentDesc = contentDesc;
    }

    public String getSendResult() {
        return sendResult;
    }

    public void setSendResult(String sendResult) {
        this.sendResult = sendResult;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }
}
