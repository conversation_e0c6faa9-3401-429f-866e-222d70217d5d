package com.tahoecn.opening.model.vo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: bpm
 * @Package: com.tyzq.bpm.model.vo.business.param
 * @ClassName: BusLoginParamVO
 * @Description:// TODO 业务用户登录参数VO
 * @Date: 2022/10/19 10:03
 * @Version: 1.0
 */
public class BusLoginParamVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务用户账号
     */
    @NotNull(message = "业务用户账号不能为空！")
    private String userAccount;

    /**
     * 业务用户密码
     */
    @NotNull(message = "业务用户密码不能为空！")
    private String userPassword;

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    public String getUserPassword() {
        return userPassword;
    }

    public void setUserPassword(String userPassword) {
        this.userPassword = userPassword;
    }
}
