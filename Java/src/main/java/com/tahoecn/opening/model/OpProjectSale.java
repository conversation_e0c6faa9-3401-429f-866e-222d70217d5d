package com.tahoecn.opening.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.xml.crypto.Data;

/**
 * <p>
 * 销售系统项目
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@ApiModel(value="OpProjectSale对象", description="销售系统项目")
public class OpProjectSale implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Excel(name = "项目ID(ProjGUID)")
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @Excel(name = "项目名称(ProjName)")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @Excel(name = "项目地址(ProjAddress)")
    @ApiModelProperty(value = "项目地址")
    private String projectAddress;

    @Excel(name = "分期id(ProjChildGUID)")
    @ApiModelProperty(value = "分期id")
    private String stagesId;

    @Excel(name = "分期名称(ProjChildName)")
    @ApiModelProperty(value = "分期名称")
    private String stagesName;

    @Excel(name = "区域id(BUGUID)")
    @ApiModelProperty(value = "区域id")
    private String areaId;

    @Excel(name = "区域名称(BUName)")
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @Excel(name = "城市id(BUChildGUID)")
    @ApiModelProperty(value = "城市id")
    private String cityId;

    @Excel(name = "城市名称(BUChildName)")
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "业态")
    private String businessType;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "是否可用")
    private String yn;

    @ApiModelProperty(value = "创建日期")
    private Date creationDate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @Excel(name = "更新日期(ModifiedOn)")
    @ApiModelProperty(value = "更新日期")
    private Date lastUpdateDate;

    @ApiModelProperty(value = "更新人")
    private String lastUpdateBy;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectAddress() {
        return projectAddress;
    }

    public void setProjectAddress(String projectAddress) {
        this.projectAddress = projectAddress;
    }

    public String getStagesId() {
        return stagesId;
    }

    public void setStagesId(String stagesId) {
        this.stagesId = stagesId;
    }

    public String getStagesName() {
        return stagesName;
    }

    public void setStagesName(String stagesName) {
        this.stagesName = stagesName;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getYn() {
        return yn;
    }

    public void setYn(String yn) {
        this.yn = yn;
    }



    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getLastUpdateBy() {
        return lastUpdateBy;
    }

    public void setLastUpdateBy(String lastUpdateBy) {
        this.lastUpdateBy = lastUpdateBy;
    }

    @Override
    public String toString() {
        return "OpProjectSale{" +
                "id=" + id +
                ", projectId='" + projectId + '\'' +
                ", projectName='" + projectName + '\'' +
                ", projectAddress='" + projectAddress + '\'' +
                ", stagesId='" + stagesId + '\'' +
                ", stagesName='" + stagesName + '\'' +
                ", areaId='" + areaId + '\'' +
                ", areaName='" + areaName + '\'' +
                ", cityId='" + cityId + '\'' +
                ", cityName='" + cityName + '\'' +
                ", businessType='" + businessType + '\'' +
                ", status='" + status + '\'' +
                ", yn='" + yn + '\'' +
                ", creationDate=" + creationDate +
                ", createdBy='" + createdBy + '\'' +
                ", lastUpdateDate=" + lastUpdateDate +
                ", lastUpdateBy='" + lastUpdateBy + '\'' +
                '}';
    }
}
