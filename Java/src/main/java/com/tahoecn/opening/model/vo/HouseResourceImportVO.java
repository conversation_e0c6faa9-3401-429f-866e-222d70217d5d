package com.tahoecn.opening.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: dzkp-java
 * @Package: com.tahoecn.opening.model.vo
 * @ClassName: HouseResourceImportVO
 * @Description:// TODO 导出房源对象VO
 * @Date: 2023/10/27 10:59
 * @Version: 1.0
 */
public class HouseResourceImportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Excel(name = "楼栋")
    private String buildingName;

    @Excel(name = "单元")
    private String unitName;

    @Excel(name = "楼层")
    private String currentFloor;

    @Excel(name = "房间号")
    private String roomNum;

    @Excel(name = "房源名称")
    private String houseName;

    @Excel(name = "房源面积(㎡)")
    private BigDecimal houserArea;

    @Excel(name = "房源总价(元)")
    private BigDecimal totalPrice;

    @Excel(name = "房源单价(元)")
    private BigDecimal unitPrice;

    @Excel(name = "优惠单价(元)")
    private BigDecimal discountUnitPrice;

    @Excel(name = "优惠总价(元)")
    private BigDecimal discountTotalPrice;

    @Excel(name = "产品类型")
    private String roomType;

    @Excel(name = "户型名称")
    private String hourseType;

    @Excel(name = "户型结构")
    private String roomStru;

    @Excel(name = "模拟批次名称（默认为：common）")
    private String batchNameSimulation;

    @Excel(name = "正式批次名称（默认为：common）")
    private String batchNameFormal;

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getCurrentFloor() {
        return currentFloor;
    }

    public void setCurrentFloor(String currentFloor) {
        this.currentFloor = currentFloor;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public BigDecimal getHouserArea() {
        return houserArea;
    }

    public void setHouserArea(BigDecimal houserArea) {
        this.houserArea = houserArea;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getDiscountUnitPrice() {
        return discountUnitPrice;
    }

    public void setDiscountUnitPrice(BigDecimal discountUnitPrice) {
        this.discountUnitPrice = discountUnitPrice;
    }

    public BigDecimal getDiscountTotalPrice() {
        return discountTotalPrice;
    }

    public void setDiscountTotalPrice(BigDecimal discountTotalPrice) {
        this.discountTotalPrice = discountTotalPrice;
    }

    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    public String getHourseType() {
        return hourseType;
    }

    public void setHourseType(String hourseType) {
        this.hourseType = hourseType;
    }

    public String getRoomStru() {
        return roomStru;
    }

    public void setRoomStru(String roomStru) {
        this.roomStru = roomStru;
    }

    public String getBatchNameSimulation() {
        return batchNameSimulation;
    }

    public void setBatchNameSimulation(String batchNameSimulation) {
        this.batchNameSimulation = batchNameSimulation;
    }

    public String getBatchNameFormal() {
        return batchNameFormal;
    }

    public void setBatchNameFormal(String batchNameFormal) {
        this.batchNameFormal = batchNameFormal;
    }
}
