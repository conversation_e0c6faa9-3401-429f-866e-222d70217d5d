/**
 * Copyright 2019 bejson.com
 */
package com.tahoecn.opening.model.interfaceBean;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Auto-generated: 2019-04-11 11:15:20
 */
public class HouseTypeData {


    @JsonProperty("ID")
    private String id;
    @JsonProperty("HouseCode")
    private String houseCode;
    @JsonProperty("Name")
    private String name;
    @JsonProperty("BuildArea")
    private String buildArea;
    @JsonProperty("ImgDesc")
    private String imgDesc;
    @JsonProperty("ImgUrl")
    private String imgUrl;
    @JsonProperty("TypeName")
    private String typeName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getHouseCode() {
        return houseCode;
    }

    public void setHouseCode(String houseCode) {
        this.houseCode = houseCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBuildArea() {
        return buildArea;
    }

    public void setBuildArea(String buildArea) {
        this.buildArea = buildArea;
    }

    public String getImgDesc() {
        return imgDesc;
    }

    public void setImgDesc(String imgDesc) {
        this.imgDesc = imgDesc;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    @Override
    public String toString() {
        return "HouseTypeData{" +
                "id='" + id + '\'' +
                ", houseCode='" + houseCode + '\'' +
                ", name='" + name + '\'' +
                ", buildArea='" + buildArea + '\'' +
                ", imgDesc='" + imgDesc + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", typeName='" + typeName + '\'' +
                '}';
    }
}