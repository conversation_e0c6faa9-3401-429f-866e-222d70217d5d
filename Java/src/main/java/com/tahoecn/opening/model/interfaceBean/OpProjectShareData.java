package com.tahoecn.opening.model.interfaceBean;


import com.fasterxml.jackson.annotation.JsonProperty;


public class OpProjectShareData {

    @JsonProperty("ProjectID")
    private String ProjectID;
    @JsonProperty("Name")
    private String Name;
    @JsonProperty("ProductType")
    private String ProductType;
    @JsonProperty("ProductPriceTotal")
    private String ProductPriceTotal;
    @JsonProperty("ProductPriceAvg")
    private String ProductPriceAvg;
    @JsonProperty("ImgSrc")
    private String ImgSrc;
    @JsonProperty("Address")
    private String Address;
    @JsonProperty("Tel")
    private String Tel;

    public void setProjectID(String ProjectID) {
        this.ProjectID = ProjectID;
    }

    public String getProjectID() {
        return ProjectID;
    }

    public void setName(String Name) {
        this.Name = Name;
    }

    public String getName() {
        return Name;
    }

    public void setProductType(String ProductType) {
        this.ProductType = ProductType;
    }

    public String getProductType() {
        return ProductType;
    }

    public void setProductPriceTotal(String ProductPriceTotal) {
        this.ProductPriceTotal = ProductPriceTotal;
    }

    public String getProductPriceTotal() {
        return ProductPriceTotal;
    }

    public void setProductPriceAvg(String ProductPriceAvg) {
        this.ProductPriceAvg = ProductPriceAvg;
    }

    public String getProductPriceAvg() {
        return ProductPriceAvg;
    }

    public void setImgSrc(String ImgSrc) {
        this.ImgSrc = ImgSrc;
    }

    public String getImgSrc() {
        return ImgSrc;
    }

    public void setAddress(String Address) {
        this.Address = Address;
    }

    public String getAddress() {
        return Address;
    }

    public void setTel(String Tel) {
        this.Tel = Tel;
    }

    public String getTel() {
        return Tel;
    }

    @Override
    public String toString() {
        return "OpProjectShareData{" +
                "ProjectID='" + ProjectID + '\'' +
                ", Name='" + Name + '\'' +
                ", ProductType='" + ProductType + '\'' +
                ", ProductPriceTotal='" + ProductPriceTotal + '\'' +
                ", ProductPriceAvg='" + ProductPriceAvg + '\'' +
                ", ImgSrc='" + ImgSrc + '\'' +
                ", Address='" + Address + '\'' +
                ", Tel='" + Tel + '\'' +
                '}';
    }
}

