package com.tahoecn.opening.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class FavoriteDetil implements Serializable {
    private static final long serialVersionUID = -4351437379789653838L;

    @Excel(name = "客户姓名")
    @ApiModelProperty(value = "名字")
    private String name;

    @Excel(name = "电话号码")
    @ApiModelProperty(value = "电话")
    private String tel;

    @Excel(name = "身份证号")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @Excel(name = "置业顾问")
    @ApiModelProperty(value = "置业顾问名字")
    private String saleName;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getSaleName() {
        return saleName;
    }

    public void setSaleName(String saleName) {
        this.saleName = saleName;
    }
}
