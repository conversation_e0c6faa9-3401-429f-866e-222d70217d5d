package com.tahoecn.opening.model.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.model.vo
 * @Description:// TODO 批次条查vo
 * @Date: 2025/4/25 15:27
 * @Version: 1.0
 **/
public class BatchParamVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  开始页
     */
    private int pageNum = 1;

    /**
     *  每页条数
     */
    private int pageSize = 10;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "批次名称")
    private String batchName;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }
}
