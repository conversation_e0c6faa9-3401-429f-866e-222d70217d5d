package com.tahoecn.opening.model.electronVo;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.model.electronVo
 * @Description:// TODO 合同入参Vo
 * @Date: 2025/1/23 14:16
 * @Version: 1.0
 **/
public class ContractParamVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 属性英文名
     */
    private String keyword;

    /**
     * 属性中文名
     */
    private String name;

    /**
     * 属性值
     */
    private String value;

    public ContractParamVo() {
    }

    public ContractParamVo(String keyword, String name, String value) {
        this.keyword = keyword;
        this.name = name;
        this.value = value;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
