package com.tahoecn.opening.model.vo;


import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * 销售数据总览VO类
 */
public class SaleDataVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @Excel(name = "置业顾问",height = 11, width = 15)
    private String saleName;

    @Excel(name = "成交销售额(元)",height = 11, width = 15)
    private String salesVolume;

    @Excel(name = "成交客户数(人)",height = 11, width = 15)
    private String salesUserNum;

    @Excel(name = "成交房源数(套)",height = 11, width = 15)
    private Integer salesHouseNum;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSaleName() {
        return saleName;
    }

    public void setSaleName(String saleName) {
        this.saleName = saleName;
    }

    public String getSalesVolume() {
        return salesVolume;
    }

    public void setSalesVolume(String salesVolume) {
        this.salesVolume = salesVolume;
    }

    public String getSalesUserNum() {
        return salesUserNum;
    }

    public void setSalesUserNum(String salesUserNum) {
        this.salesUserNum = salesUserNum;
    }

    public Integer getSalesHouseNum() {
        return salesHouseNum;
    }

    public void setSalesHouseNum(Integer salesHouseNum) {
        this.salesHouseNum = salesHouseNum;
    }
}
