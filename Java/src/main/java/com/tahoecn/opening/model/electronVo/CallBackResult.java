package com.tahoecn.opening.model.electronVo;

import java.io.Serializable;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.tahoecn.opening.service.impl.ElectronServiceImpl;

/**
 * @Author: Zhengwenchao  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.model.electronVo
 * @Description:// TODO 一签通专用回调方法对象
 * @Date: 2025/1/17 9:29
 * @Version: 1.0
 */
public class CallBackResult implements Serializable {

	private static final Logger log= LoggerFactory.getLogger(CallBackResult.class);

    private static final long serialVersionUID = 1L;

    private static final String SUCCESS_CODE = "0";

    private static final String ERROR_CODE = "500";

    /**
     *  输出编码：0--成功;其他响应码均为失败;
     */
    private String code;

    /**
     *  输出信息
     */
    private String message;

    public CallBackResult() {
    }

    public CallBackResult(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 9:36
     * @Param        message    响应内容
     * @Return       com.tahoecn.opening.model.electronVo.CallBackResult
     * @Description  TODO   回调成功
     **/
    public static CallBackResult success(String message) {
    	log.info("回调成功:"+SUCCESS_CODE+"--"+message);
        return new CallBackResult(SUCCESS_CODE, message);
    }

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 9:36
     * @Param        message    响应内容
     * @Return       com.tahoecn.opening.model.electronVo.CallBackResult
     * @Description  TODO   回调失败
     **/
    public static CallBackResult error(String message) {
        return new CallBackResult(ERROR_CODE, message);
    }
}
