package com.tahoecn.opening.model.interfaceBean;


import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Auto-generated: 2019-04-11 20:23:35
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class UserData {

    @JsonProperty("ID")
    private String id;
    @JsonProperty("OpenID")
    private String openID;
    @JsonProperty("EmployeeName")
    private String employeeName;
    @JsonProperty("UserName")
    private String userName;
    @JsonProperty("Gender")
    private String gender;
    @JsonProperty("Mobile")
    private String mobile;
    @JsonProperty("RoleID")
    private String roleID;
    @JsonProperty("RoleName")
    private String roleName;
    @JsonProperty("AccountType")
    private String accountType;
    @JsonProperty("AccountTypeName")
    private String accountTypeName;
    @JsonProperty("ProjectID")
    private String projectID;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOpenID() {
        return openID;
    }

    public void setOpenID(String openID) {
        this.openID = openID;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getRoleID() {
        return roleID;
    }

    public void setRoleID(String roleID) {
        this.roleID = roleID;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountTypeName() {
        return accountTypeName;
    }

    public void setAccountTypeName(String accountTypeName) {
        this.accountTypeName = accountTypeName;
    }

    public String getProjectID() {
        return projectID;
    }

    public void setProjectID(String projectID) {
        this.projectID = projectID;
    }

    @Override
    public String toString() {
        return "UserData{" +
                "id='" + id + '\'' +
                ", openID='" + openID + '\'' +
                ", employeeName='" + employeeName + '\'' +
                ", userName='" + userName + '\'' +
                ", gender='" + gender + '\'' +
                ", mobile='" + mobile + '\'' +
                ", roleID='" + roleID + '\'' +
                ", roleName='" + roleName + '\'' +
                ", accountType='" + accountType + '\'' +
                ", accountTypeName='" + accountTypeName + '\'' +
                ", projectID='" + projectID + '\'' +
                '}';
    }
}