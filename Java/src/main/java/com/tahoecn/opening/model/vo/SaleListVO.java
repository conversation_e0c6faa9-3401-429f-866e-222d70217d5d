package com.tahoecn.opening.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;

public class SaleListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private  String id;

	@Excel(name = "姓名",height = 11, width = 15)
	private String saleName;

	@Excel(name = "手机号码",height = 11, width = 15)
	@ApiModelProperty(value = "电话")
	private String tel;

	@Excel(name = "添加时间",height = 11, width = 15)
	private LocalDateTime creationDate;

	@Excel(name = "关联客户数",height = 11, width = 15)
	private Integer holdUserNum;

	@Excel(name = "角色",height = 11, width = 15)
	@ApiModelProperty(value = "角色名称")
	private String roleName;

	public String getSaleName() {
		return saleName;
	}

	public void setSaleName(String saleName) {
		this.saleName = saleName;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public LocalDateTime getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(LocalDateTime creationDate) {
		this.creationDate = creationDate;
	}

	public Integer getHoldUserNum() {
		return holdUserNum;
	}

	public void setHoldUserNum(Integer holdUserNum) {
		this.holdUserNum = holdUserNum;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	@Override
	public String toString() {
		return "SaleListVO{" +
				"id='" + id + '\'' +
				", saleName='" + saleName + '\'' +
				", tel='" + tel + '\'' +
				", creationDate=" + creationDate +
				", holdUserNum=" + holdUserNum +
				", roleName='" + roleName + '\'' +
				'}';
	}
}
