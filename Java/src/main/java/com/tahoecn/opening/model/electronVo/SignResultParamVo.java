package com.tahoecn.opening.model.electronVo;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.model.electronVo
 * @Description:// TODO 签约人签章结果回调入参Vo
 * @Date: 2025/1/17 9:38
 * @Version: 1.0
 */
public class SignResultParamVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  合同ID
     */
    private String id;

    /**
     *  签约人ID
     */
    private String subId;

    /**
     *  签约人名称
     */
    private String subName;

    /**
     *  电子合同平台合同编号
     */
    private String code;

    /**
     *  业务系统合同编号
     */
    private String ctCode;

    /**
     *  状态：0: 未签约;1: 待签约;2: 已签约
     */
    private String status;

    /**
     *  类别：1：商务合同，2：人事合同，3：电子文件
     */
    private String type;

    /**
     *  签章时间(yyyy-MM-dd HH:mm:sss)
     */
    private String signTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSignTime() {
        return signTime;
    }

    public void setSignTime(String signTime) {
        this.signTime = signTime;
    }
}
