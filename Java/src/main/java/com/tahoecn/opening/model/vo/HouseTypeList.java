package com.tahoecn.opening.model.vo;


import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * 销售数据总览VO类
 */
public class HouseTypeList implements Serializable {

    private static final long serialVersionUID = -7125468152664085741L;

    @Excel(name = "户型名称",height = 11, width = 15)
    private String typeName;

    @Excel(name = "成交销售额(元)",height = 11, width = 15)
    private String salesVolume;

    @Excel(name = "成交面积(㎡)",height = 11, width = 15)
    private String salesArea;

    @Excel(name = "成交房源数(套)",height = 11, width = 15)
    private String salesNum;

    @Excel(name = "去化率",height = 11, width = 15)
    private Double percentage;

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getSalesVolume() {
        return salesVolume;
    }

    public void setSalesVolume(String salesVolume) {
        this.salesVolume = salesVolume;
    }

    public String getSalesArea() {
        return salesArea;
    }

    public void setSalesArea(String salesArea) {
        this.salesArea = salesArea;
    }

    public String getSalesNum() {
        return salesNum;
    }

    public void setSalesNum(String salesNum) {
        this.salesNum = salesNum;
    }

    public Double getPercentage() {
        return percentage;
    }

    public void setPercentage(Double percentage) {
        this.percentage = percentage;
    }
}
