package com.tahoecn.opening.model;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 小程序请求
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-16
 */
@ApiModel(value = "小程序请求对象", description = "参数Dto")
public class AppRequstDto implements Serializable {

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "项目ID")
    private String projectId;
    
    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;

    @ApiModelProperty(value = "单元名称")
    private String unitName;
    
    @ApiModelProperty(value = "用户登录名")
    private String userName;
    
    @ApiModelProperty(value = "用户ID")
    private String userId;
    
    @ApiModelProperty(value = "是否正式开盘")
    private String isRegular;
    
    @ApiModelProperty(value = "房屋ID")
    private String houseId;
    
    @ApiModelProperty(value = "房屋名称")
    private String houseName;
    
    @ApiModelProperty(value = "收藏排序ids")
    private String myFavoriteIds;
    
    @ApiModelProperty(value = "订单id")
    private String orderId;
    
    @ApiModelProperty(value = "电话")
    private String tel;
    
    @ApiModelProperty(value = "微信openId")
    private String openId;
    
    @ApiModelProperty(value = "身份证后六位")
    private String idCard;
    
    @ApiModelProperty(value = "验证码")
    private String verificationCode;
    
    @ApiModelProperty(value = "用户中文名称")
    private String userFullName;
    
    @ApiModelProperty(value = "用户物理位置")
    private String address;

	public String getActivityId() {
		return activityId;
	}

	public void setActivityId(String activityId) {
		this.activityId = activityId;
	}

	public String getProjectId() {
		return projectId;
	}

	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}

	public String getBuildingName() {
		return buildingName;
	}

	public void setBuildingName(String buildingName) {
		this.buildingName = buildingName;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getIsRegular() {
		return isRegular;
	}

	public void setIsRegular(String isRegular) {
		this.isRegular = isRegular;
	}

	public String getHouseId() {
		return houseId;
	}

	public void setHouseId(String houseId) {
		this.houseId = houseId;
	}

	public String getHouseName() {
		return houseName;
	}

	public void setHouseName(String houseName) {
		this.houseName = houseName;
	}

	public String getMyFavoriteIds() {
		return myFavoriteIds;
	}

	public void setMyFavoriteIds(String myFavoriteIds) {
		this.myFavoriteIds = myFavoriteIds;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getIdCard() {
		return idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	public String getVerificationCode() {
		return verificationCode;
	}

	public void setVerificationCode(String verificationCode) {
		this.verificationCode = verificationCode;
	}

	public String getUserFullName() {
		return userFullName;
	}

	public void setUserFullName(String userFullName) {
		this.userFullName = userFullName;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}


}
