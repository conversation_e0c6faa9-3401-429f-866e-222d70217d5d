package com.tahoecn.opening.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

public class OrderExcelImportVO implements Serializable {


    private static final long serialVersionUID = 9119566312747323029L;
    @Excel(name = "用户CstGUID")
    private String userId;

    @Excel(name = "订单房源HouseSyncId")
    private String houseId;

    @Excel(name = "创建日期")
    private Date creationDate;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }


    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof OrderExcelImportVO)) return false;
        OrderExcelImportVO that = (OrderExcelImportVO) o;
        return Objects.equals(userId, that.userId) &&
                Objects.equals(houseId, that.houseId) &&
                Objects.equals(creationDate, that.creationDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, houseId, creationDate);
    }

    @Override
    public String toString() {
        return "OrderExcelImportVO{" +
                "userId='" + userId + '\'' +
                ", houseId='" + houseId + '\'' +
                ", creationDate=" + creationDate +
                '}';
    }
}
