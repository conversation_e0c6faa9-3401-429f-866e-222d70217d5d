package com.tahoecn.opening.interceptor;

import java.util.Arrays;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import com.landray.sso.client.EKPSSOClient;

/**
 * Created by zhanghw on 2018/10/11.
 */
/**
 * @ClassName WebSSOConfig
 * <AUTHOR>
 * @date 2019年4月15日
 */
@SuppressWarnings("deprecation")
@Configuration
public class WebSSOConfig extends WebMvcConfigurerAdapter {
	
	@Bean
	SsoInterceptor ssoInterceptor(){
		return new SsoInterceptor();
	}

	@Bean
	ApplInterceptor applInterceptor(){
		return new ApplInterceptor();
	}


//	/**
//	 * sso过滤器
//	 */
//	@SuppressWarnings({ "rawtypes", "unchecked" })
//	@Bean
//	public FilterRegistrationBean ssoFilterRegistration() {
//		FilterRegistrationBean registration = new FilterRegistrationBean();
//		registration.setFilter(new EKPSSOClient());
//		registration.setUrlPatterns(Arrays.asList("/webapi/*"));
//		registration.setName("EKPSSOClient");
//		registration.addInitParameter("filterConfigFile", "/sso-config.properties");
//		registration.setOrder(-1);// 过滤器顺序
//		return registration;
//	}
	
	/**
	 * 拦截器配置
	 */
	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		// 多个拦截器组成一个拦截器链
		// addPathPatterns 用于添加拦截规则
		// excludePathPatterns 用户排除拦截
		registry.addInterceptor(ssoInterceptor()).addPathPatterns("/webapi/**", "/login/**", "/swagger-ui.html")
				.excludePathPatterns("/webapi/opHousingResources/pullOrg", "/webapi/opHousingResources/pullUser")
				.excludePathPatterns("/webapi/opProjectShare/updataProjectAUTO")
				.excludePathPatterns("/webapi/opHouseType/updataAll")
				.excludePathPatterns("/app/common/downloadRqImg")
				.excludePathPatterns("/webapi/opActivity/getActivityByActivityId")
				.excludePathPatterns("/webapi/opActivity/getLotteryActivityConfigById")
				.excludePathPatterns("/webapi/opActivity/autoLottery")
				.excludePathPatterns("/webapi/opUser/opUserActivityList")
//				.excludePathPatterns("/app/house/initHouseResourceWhiteOrder")
//				.excludePathPatterns("/webapi/opUser/getUserHouseResourceListByUserId")
//				.excludePathPatterns("/webapi/opUser/saveUserHouseResource")
				.excludePathPatterns("/webapi/opHousingResources/pushWebsocket")
				.excludePathPatterns("/login/getTicket")
                .excludePathPatterns("/login/jumpByTicket")
                .excludePathPatterns("/login/busLogin")
//                .excludePathPatterns("/login/busLogout")
                .excludePathPatterns("/electron/**")
                .excludePathPatterns("/sync/addQuartz")
                .excludePathPatterns("/sync/deleteQuartz")
                .excludePathPatterns("/sync/updateQuartz")
				.excludePathPatterns("/webapi/opHouseType/socket/push");
		registry.addInterceptor(applInterceptor()).addPathPatterns("/app/house/**","/app/auth/**","/app/activity/**");

	}

	/**
	 * 跨域拦截
	 */
	@Override
	public void addCorsMappings(CorsRegistry registry) {
		registry.addMapping("/app/**").allowedOrigins("*");
	}
}
