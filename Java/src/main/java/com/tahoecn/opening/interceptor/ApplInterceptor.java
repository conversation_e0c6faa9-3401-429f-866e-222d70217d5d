package com.tahoecn.opening.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import com.tahoecn.opening.common.constants.AesConstants;
import com.tahoecn.opening.common.utils.AesUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.concurrent.TimeUnit;


/**
 * 全局的拦截器
 */
@SuppressWarnings({ "unchecked", "rawtypes" })
@Component
public class ApplInterceptor implements HandlerInterceptor {

	private static final Log log = LogFactory.get();

	@Autowired
	RedisTemplate redisTemplate;

	/**
	 * 在处理请求之前要做的动作
	 *
	 * @param request
	 * @param response
	 * @param handler
	 * @return
	 * @throws Exception
	 */
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
		log.info("=================进入拦截器=================");
		String requestUri = request.getRequestURI();
		String contextPath = request.getContextPath();
		String url = requestUri.substring(contextPath.length());

		log.info("requestUri:===========" + requestUri);
		log.info("contextPath:==========" + contextPath);
		log.info("url:==================" + url);

		// 过滤掉vue中的OPTIONS请求
		if (StringUtils.equals(request.getMethod(), "OPTIONS")) {
			return true;
		}

		if (!url.startsWith("/app")
				|| url.equalsIgnoreCase("/app/auth/login")
				|| url.equalsIgnoreCase("/app/auth/lotteryLogin")
				|| url.equalsIgnoreCase("/app/auth/getTimeByUser")
				|| url.equalsIgnoreCase("/app/auth/loginAndGetUser")
				|| url.equalsIgnoreCase("/app/auth/checkPublicLogin")
				|| url.equalsIgnoreCase("/app/house/selectHouseResourceListByActivityId")
				|| url.equalsIgnoreCase("/app/common/twoCode")
				|| url.equalsIgnoreCase("/app/auth/getVerificationCode")
				|| url.equalsIgnoreCase("/app/common/downloadRqImg")) {
			return true;
		}
		//简单的设置编码的方法
		response.setContentType("text/html;charset=utf-8");
		PrintWriter pw;
		String token = request.getHeader("token");
		try {
			if (StringUtils.isBlank(token)) {
				pw = response.getWriter();
				pw.write("{\"code\":\"4030\",\"message\":\"请登录！\"}");
				pw.flush();
				pw.close();
				return false;
			}
			JSONObject jsonObject = JSONObject.parseObject(token);
			String loginToken = jsonObject.getString("loginToken");
			if (StringUtils.isBlank(loginToken)) {
				pw = response.getWriter();
				pw.write("{\"code\":\"4030\",\"message\":\"请登录！\"}");
				pw.flush();
				pw.close();
				return false;
			} else if ("M6g532ppgMBSIPHI7tsE2PwOV7JosLYO".equals(loginToken)) {
				// 当loginToken为该值的时候，直接过滤
				return true;
			}
			// 获取登录token
			Object o = redisTemplate.opsForValue().get("LOGIN_TOKEN_" + loginToken);
			if (null == o) {
				pw = response.getWriter();
				pw.write("{\"code\":\"4030\",\"message\":\"您的账号已在其他设备登录，请重新登录！\"}");
				pw.flush();
				pw.close();
				return false;
			}
			request.getSession().setAttribute("token", loginToken);
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		}







		/*String headToken = request.getHeader("token");


		JSONObject token = JSON.parseObject(headToken);
		PrintWriter pw;
		try {

			if (headToken == null){
				pw = response.getWriter();
				pw.write("{\"code\":\"403\"}");
				pw.flush();
				pw.close();
				return false;
			}
			String checkStr = AesUtils.encrypt(token.get("openId").toString()+","+token.get("timeStr").toString(), AesConstants.AES_KEY);
			if (token.get("checkStr").toString().equals(checkStr)){
				boolean lock;
				lock = redisTemplate.opsForValue().setIfAbsent(token.get("checkStr").toString(), "1");
				redisTemplate.expire(token.get("checkStr").toString(), 1, TimeUnit.HOURS);
				if (lock){
					return true;
				}else{
					pw = response.getWriter();
					pw.write("{\"code\":\"403\"}");
					pw.flush();
					pw.close();
					return false;
				}
			}else{
				pw = response.getWriter();
				pw.write("{\"code\":\"403\"}");
				pw.flush();
				pw.close();
				return false;
			}

		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}*/
		return true;
	}

	/**
	 * 完成请求处理后要做的动作
	 *
	 * @param request
	 * @param response
	 * @param handler
	 * @param modelAndView
	 * @throws Exception
	 */
	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) {
		// log.info("【2】完成请求处理后要做的动作");
	}

	/**
	 * 请求结束后要做的动作
	 *
	 * @param request
	 * @param response
	 * @param handler
	 * @param ex
	 * @throws Exception
	 */
	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
			Exception ex) {
		// log.info("【3】请求结束后要做的动作");
	}

}
