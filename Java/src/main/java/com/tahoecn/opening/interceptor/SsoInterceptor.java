package com.tahoecn.opening.interceptor;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.model.vo.BusLoginResponseVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.handler.HandlerMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;

import com.tahoecn.opening.common.utils.ThreadLocalUtils;
import com.tahoecn.opening.model.CsUcUser;
import com.tahoecn.opening.service.CsUcUserService;
import com.tahoecn.uc.sso.SSOConfig;
import com.tahoecn.uc.sso.SSOHelper;
import com.tahoecn.uc.sso.annotation.Action;
import com.tahoecn.uc.sso.annotation.Login;
import com.tahoecn.uc.sso.common.CookieHelper;
import com.tahoecn.uc.sso.security.token.SSOToken;
import com.tahoecn.uc.sso.utils.LtpaToken;
import com.tahoecn.uc.sso.web.interceptor.SSOSpringInterceptor;

/**
 * 全局的拦截器
 */
@SuppressWarnings({ "unchecked", "rawtypes" })
@Component
public class SsoInterceptor extends SSOSpringInterceptor {

	private static final Log log = LogFactory.get();

	@Autowired
	RedisTemplate redisTemplate;

	/**
	 * 在处理请求之前要做的动作
	 *
	 * @param request
	 * @param response
	 * @param handler
	 * @return
	 * @throws Exception
	 */
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
		String token = request.getHeader("bus-user-token");
        if (StringUtils.isBlank(token)) {
			log.info("===用户token为空===");
			// 未登录
			sendError(response);
			return false;
		}
        // 获取
		Object obj = redisTemplate.opsForValue().get(GlobalConstants.REDIS_USER_TOKEN_KEY + token);
        if (null == obj) {
			log.info("===用户token失效===");
			// 未登录
			sendError(response);
			return false;
		}
        // 更新登录时间
		BusLoginResponseVO vo = JSONObject.parseObject(obj.toString(), BusLoginResponseVO.class);
		//获取账号
		String userAccount = vo.getUserAccount();
		//判断是admin
		if (!userAccount.equals("admin")) {
			//获取当前接口
			String requestUri = request.getRequestURI();
			String contextPath = request.getContextPath();
			String url = requestUri.substring(contextPath.length());
			//判断路径等于系统管理路径则没权限
			if (url.equals("/webapi/opProjectSale/projectPageList")
					||url.equals("/webapi/opProjectShare/getProjectShareByProjectId")
					||url.equals("/webapi/opProjectShare/updateProjectShare")
					||url.equals("/webapi/opActivity/activityPage")
//					||url.equals("/webapi/opActivity/activityDetails")
					||url.equals("/webapi/opActivity/activityRoleList")
					||url.equals("/webapi/opActivity/delActivityRole")
					||url.equals("/webapi/opActivity/delActivity")
					||url.equals("/webapi/sysRole/list")
					||url.equals("/webapi/sysRole/saveRole")
					||url.equals("/webapi/sysRole/get")
					||url.equals("/webapi/sysRole/removeRoles")
					) {
				log.info("===用户无权限===");
				// 未登录
				sendError(response);
				return false;
			}
			System.out.println(url);
		}
		redisTemplate.opsForValue().set(GlobalConstants.REDIS_USER_TOKEN_KEY + token, JSONObject.toJSON(vo).toString(), 6, TimeUnit.HOURS);
		ThreadLocalUtils.setUser(vo);
		return true;
	}

//
//	/**
//	 * 在处理请求之前要做的动作
//	 *
//	 * @param request
//	 * @param response
//	 * @param handler
//	 * @return
//	 * @throws Exception
//	 */
//	@Override
//	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
//
//		// SSO 魔改
//		if ((handler instanceof HandlerMethod)) {
//			HandlerMethod handlerMethod = (HandlerMethod) handler;
//			Method method = handlerMethod.getMethod();
//			Login login = (Login) method.getAnnotation(Login.class);
//			if ((login != null) && (login.action() == Action.Skip)) {
//				return true;
//			}
//		}
//		SSOToken ssoToken = SSOHelper.getSSOToken(request);
//		if (ssoToken == null) {
//			try {
//				if (request.getRequestURI().indexOf("/webapi") < 0) {
//					if (getHandlerInterceptor().preTokenIsNull(request, response)) {
//						SSOHelper.clearRedirectLogin(request, response);
//					}
//					return false;
//				}
//				sendError(response);
//			} catch (IOException e) {
//				e.printStackTrace();
//			}
//			return false;
//		}
//
//		String LtpaTokenCookie = CookieHelper.getCookie(request, SSOConfig.getInstance().getOaCookieName());
//		if ((StringUtils.isEmpty(LtpaTokenCookie)) || (LtpaTokenCookie.length() < 10)) {
//			String tokenValue = LtpaToken.generateTokenByUserName(ssoToken.getIssuer(),
//					String.valueOf(SSOConfig.getInstance().getExpireT()), SSOConfig.getInstance().getOatokenKey());
//			String domain = SSOConfig.getInstance().getCookieDomain();
//
//			response.addHeader("Set-Cookie", SSOConfig.getInstance().getOaCookieName() + "=" + tokenValue + ";Domain="
//					+ domain + "; Path=" + SSOConfig.getInstance().getCookiePath());
//		}
//		request.setAttribute("ucssoTokenAttr", ssoToken);
//
//		// 单点登陆用户过滤
//		Optional<SSOToken> sso = Optional.ofNullable(SSOHelper.attrToken(request));
//		String loginName = sso.map(SSOToken::getIssuer).orElse(null);
//
//		if (StringUtils.isNotBlank(loginName)) {
//			BusLoginResponseVO vo = (BusLoginResponseVO) redisTemplate.opsForValue().get(loginName);
//			// 声明对象
//			if (vo == null) {
//				CsUcUser user = csUcUserService.selectByUsername(loginName);
//				if (user == null) {
//					sendError(response);
//					return false;
//				}
//				vo = new BusLoginResponseVO(user.getId(), user.getFdSid(), user.getFdUsername(), user.getFdName(), user.getFdWorkPhone(), user.getFdEmail(), user.getFdOrgId(), user.getFdOrgName(), "");
//				// 校验是否为超级管理员
//				if (null != user.getFdLock() && 1 == user.getFdLock()) {
//					vo.setAdminFlag(true);
//				}
//				redisTemplate.opsForValue().set(loginName, vo, 6, TimeUnit.HOURS);
//			}
//			ThreadLocalUtils.setUser(vo);
//			return true;
//		}
//		sendError(response);
//		return false;
//	}

	private void sendError(HttpServletResponse response) {
		try {
			response.sendError(401, "获取授权失败");
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 完成请求处理后要做的动作
	 * 
	 * @param request
	 * @param response
	 * @param handler
	 * @param modelAndView
	 * @throws Exception
	 */
	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) {
	}

	/**
	 * 请求结束后要做的动作
	 * 
	 * @param request
	 * @param response
	 * @param handler
	 * @param ex
	 * @throws Exception
	 */
	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
			Exception ex) {
		ThreadLocalUtils.remove();
	}

}
