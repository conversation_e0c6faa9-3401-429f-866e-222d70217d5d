package com.tahoecn.opening.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.constants.MQConstant;
import com.tahoecn.opening.common.utils.DateUtils;
import com.tahoecn.opening.common.utils.LocalDateTimeUtils;
import com.tahoecn.opening.common.utils.ThreadLocalUtils;
import com.tahoecn.opening.mapper.*;
import com.tahoecn.opening.model.*;
import com.tahoecn.opening.model.dto.HouseResourceDTO;
import com.tahoecn.opening.model.dto.WhiteHouseResourceDTO;
import com.tahoecn.opening.model.vo.CustomerAnalysisVO;
import com.tahoecn.opening.model.vo.UserResourceImportVO;
import com.tahoecn.opening.model.vo.WhiteHouseResourceParamVO;
import com.tahoecn.opening.server.WebSocketServer;
import com.tahoecn.opening.service.IOpBatchService;
import com.tahoecn.opening.service.IOpFavoriteService;
import com.tahoecn.opening.service.IOpHousingResourcesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tahoecn.opening.service.IOpOrderService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.AbstractJavaTypeMapper;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 楼栋房源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-08
 */
@Service
public class OpHousingResourcesServiceImpl extends ServiceImpl<OpHousingResourcesMapper, OpHousingResources> implements IOpHousingResourcesService {

    @Autowired
    private OpOrderMapper opOrderMapper;
    @Autowired
	private  OpHousingResourcesMapper opHousingResourcesMapper;
    @Autowired
	private OpActivityMapper opActivityMapper;

    @Autowired
	private IOpOrderService opOrderService;

    @Autowired
    private  IOpFavoriteService favoriteService;

	@Resource
	private OpUserHouseResourceMapper userHouseResourceMapper;

	@Autowired
	OpUserMapper opUserMapper;

	@Autowired
	private RabbitTemplate rabbitTemplate;

	@Resource
	private IOpBatchService batchService;

	@Value("${server.port}")
	private Integer serverPort;

	private static final Logger log = LoggerFactory.getLogger(OpHousingResourcesServiceImpl.class);

	/**
	 * 销控随机姓氏
	 */
	private static List<String> nameList = Arrays.asList("赵", "王", "张", "李", "杨", "马", "关", "刘", "郑", "田");

//	public void testInsert(OpHousingResources opHousingResources){
//		opHousingResourcesMapper.testInsert(opHousingResources);
//	}


	@Override
	public List<HashMap<String,Object>> getBuildingListByActivityId(String activityId, Integer batchFlag, Integer formalFlag, List<String> batchNameList) {
		return baseMapper.getBuildingListByActivityId(activityId, batchFlag, formalFlag, batchNameList);
	}

	@Override
	public List<HashMap<String, Object>> getUnitListByActivityIdAndBuildingName(String activityId, String buildingName, Integer batchFlag, Integer formalFlag, List<String> batchNameList) {
		return baseMapper.getUnitListByActivityIdAndBuildingName(activityId,buildingName, batchFlag, formalFlag, batchNameList);
	}

	@Override
	public List<HashMap<String, Object>> getRoomMsgByActivityIdAndBuildingAndUnit(String activityId,
			String buildingName, String unitName,String currentFloor,String isRegular, String userId) {
		return baseMapper.getRoomMsgByActivityIdAndBuildingAndUnit(activityId,buildingName,unitName,currentFloor,isRegular,userId);
	}
	
	@Override
	public List<HashMap<String, Object>> getRoomMsgByActivityIdAndBuildingAndUnitMg(String activityId, String buildingName, String unitName,String currentFloor,String isRegular, String userId,
																					Integer batchFlag, Integer formalFlag, List<String> batchNameList) {
		return baseMapper.getRoomMsgByActivityIdAndBuildingAndUnitMg(activityId,buildingName,unitName,currentFloor,isRegular,userId, batchFlag, formalFlag, batchNameList);
	}


	@Override
	public List<HashMap<String, Object>> getFloorListByActivityIdAndBuildingAndUnit(String activityId,
			String buildingName, String unitName) {
		return baseMapper.getFloorListByActivityIdAndBuildingAndUnit(activityId,buildingName,unitName);
	}

	@Override
	public String getHouseCountByActivityId(Integer activityId) {
		return baseMapper.getHouseCountByActivityId(activityId);
	}

    @Override
    public boolean canBeDelete(List<String> idList) {
        QueryWrapper<OpOrder> wrapper = new QueryWrapper<>();
        wrapper.eq("yn", "y");
        //获取所有订单 如houseid与idlist中的有相等的 不可删除 返回false
        List<OpOrder> opOrders = opOrderMapper.selectList(wrapper);
        for (String id : idList) {
            for (OpOrder opOrder : opOrders) {
                if (id.equals(opOrder.getHouseId())){
                    return false;
                }
            }
        }
        return true;
    }

	@Override
	public HashMap<String, Object> getActivityAnalysis(String activityId) {
		HashMap<String,Object> resultMap = new HashMap<String,Object>();
		//获取开盘数据
		HashMap<String,Object> openDataMap = baseMapper.getOpenDataByActivityId(activityId);
		resultMap.put("openData", openDataMap);
		//获取登录数据-人员总数
		int totalUserCount = baseMapper.getTotalUserByActivityId(activityId);
		//获取登录数据-历史登录人数
		int hisLoginUserCount = baseMapper.getHisLoginUserCountByActivityId(activityId);
		//获取登录数据-今日登录人数
		int todayLoginUserCount = baseMapper.getTodayLoginUserCountByActivityId(activityId);
		HashMap<String,Object> loginDataMap = new HashMap<String,Object>();
		loginDataMap.put("totalUserCount", totalUserCount);
		loginDataMap.put("hisLoginUserCount", hisLoginUserCount);
		loginDataMap.put("hisNoLoginUserCount", totalUserCount-hisLoginUserCount);
		loginDataMap.put("todayLoginUserCount", todayLoginUserCount);
		loginDataMap.put("todayNoLoginUserCount", totalUserCount-todayLoginUserCount);
		resultMap.put("loginData", loginDataMap);
		//房源热度数据-获取房间最大收藏数
		Integer favoriteMaxCount = baseMapper.getFavoriteMaxCountByActivityId(activityId);
		if (favoriteMaxCount == null) {
			favoriteMaxCount = 0;
		}
		//int roomCount = baseMapper.getRoomCountByActivityId(activityId);
		int minTotal = 0;
		int maxtotal = 0;
		List<HashMap<String,Object>> favoriteList = new ArrayList<HashMap<String,Object>>();
		if(favoriteMaxCount < 5){
			if(favoriteMaxCount <= 1){
				HashMap<String,Object> roomFavoriteCountMap = new HashMap<String,Object>();
				minTotal = 0;
				roomFavoriteCountMap.put("name", "收藏0次");
				roomFavoriteCountMap.put("count", baseMapper.getRoomFavoriteCountByActivityId(activityId,minTotal,maxtotal));
				favoriteList.add(roomFavoriteCountMap);
				if(1 == favoriteMaxCount){
					HashMap<String,Object> roomFavoriteCountMap1 = new HashMap<String,Object>();
					minTotal = 1;
					maxtotal = 1;
					roomFavoriteCountMap1.put("name", "收藏1次");
					roomFavoriteCountMap1.put("count", baseMapper.getRoomFavoriteCountByActivityId(activityId,minTotal,maxtotal));
					favoriteList.add(roomFavoriteCountMap1);
				}
				
			}else{
				HashMap<String,Object> roomFavoriteCountMap0 = new HashMap<String,Object>();
				minTotal = 0;
				roomFavoriteCountMap0.put("name", "收藏0次");
				roomFavoriteCountMap0.put("count", baseMapper.getRoomFavoriteCountByActivityId(activityId,minTotal,maxtotal));
				favoriteList.add(roomFavoriteCountMap0);
				for(int i =1 ; i<= favoriteMaxCount; i++){
					HashMap<String,Object> roomFavoriteCountMap = new HashMap<String,Object>();
					minTotal = i;
					maxtotal = i;
					roomFavoriteCountMap.put("name", "收藏"+minTotal+"次");
					if(i==favoriteMaxCount){
						maxtotal=maxtotal+1;
					}
					roomFavoriteCountMap.put("count", baseMapper.getRoomFavoriteCountByActivityId(activityId,minTotal,maxtotal));
					favoriteList.add(roomFavoriteCountMap);
				}
			}
			for(int i=1; i<=5-favoriteMaxCount;i++){
				HashMap<String,Object> roomFavoriteCountMap = new HashMap<String,Object>();
				roomFavoriteCountMap.put("name", "(无)");
				roomFavoriteCountMap.put("count", 0);
				favoriteList.add(roomFavoriteCountMap);
			}
		}else{
			HashMap<String,Object> roomFavoriteCountMap = new HashMap<String,Object>();
			minTotal = 0;
			roomFavoriteCountMap.put("name", "收藏0次");
			roomFavoriteCountMap.put("count", baseMapper.getRoomFavoriteCountByActivityId(activityId,minTotal,maxtotal));
			favoriteList.add(roomFavoriteCountMap);
			minTotal = 1;
			maxtotal = (int) Math.round(favoriteMaxCount*0.2);
			roomFavoriteCountMap.put("name", "收藏"+minTotal+"~"+maxtotal+"次");
			roomFavoriteCountMap.put("count", baseMapper.getRoomFavoriteCountByActivityId(activityId,minTotal,maxtotal));
			favoriteList.add(roomFavoriteCountMap);
			minTotal=maxtotal;
			maxtotal = (int) Math.round(favoriteMaxCount*0.4);
			roomFavoriteCountMap.put("name", "收藏"+minTotal+"~"+maxtotal+"次");
			roomFavoriteCountMap.put("count", baseMapper.getRoomFavoriteCountByActivityId(activityId,minTotal,maxtotal));
			favoriteList.add(roomFavoriteCountMap);
			minTotal=maxtotal;
			maxtotal = (int) Math.round(favoriteMaxCount*0.6);
			roomFavoriteCountMap.put("name", "收藏"+minTotal+"~"+maxtotal+"次");
			roomFavoriteCountMap.put("count", baseMapper.getRoomFavoriteCountByActivityId(activityId,minTotal,maxtotal));
			favoriteList.add(roomFavoriteCountMap);
			minTotal=maxtotal;
			maxtotal = (int) Math.round(favoriteMaxCount*0.8);
			roomFavoriteCountMap.put("name", "收藏"+minTotal+"~"+maxtotal+"次");
			roomFavoriteCountMap.put("count", baseMapper.getRoomFavoriteCountByActivityId(activityId,minTotal,maxtotal));
			favoriteList.add(roomFavoriteCountMap);
			minTotal=maxtotal;
			maxtotal = (int) Math.round(favoriteMaxCount);
			roomFavoriteCountMap.put("name", "收藏"+minTotal+"~"+maxtotal+"次");
			roomFavoriteCountMap.put("count", baseMapper.getRoomFavoriteCountByActivityId(activityId,minTotal,maxtotal+1));
			favoriteList.add(roomFavoriteCountMap);
			
		}
		resultMap.put("roomFavoriteData", favoriteList);
		return resultMap;
	}

	/**
	 * Excel导入
	 * @param opHousingResourcesList
	 * @param activityIdNow
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public JSONResult saveImportData(List<OpHousingResources> opHousingResourcesList, String activityIdNow) {
		JSONResult<Object> jsonResult = new JSONResult<>();
    	//存储重复数据list
		List<OpHousingResources> repeatDataList= new ArrayList<>();

		Set<OpHousingResources> repeatDataSet=new HashSet<>();
		QueryWrapper<OpOrder> orderWrapper=new QueryWrapper<>();

        orderWrapper.eq("activity_id",activityIdNow);
         List<OpOrder> opOrders = opOrderMapper.selectList(orderWrapper);
         if (opOrders.size()!=0){
             jsonResult.setCode(GlobalConstants.E_CODE);
             jsonResult.setMsg("活动下存在订单,不可导入房源");
             jsonResult.setData(repeatDataList);
             return jsonResult;
         }
		//验证opHousingResourcesList中房源长ID不可重复
		if (opHousingResourcesList.size()==2){
			if(opHousingResourcesList.get(0).getHouseSyncId().equals(opHousingResourcesList.get(1).getHouseSyncId())){
				repeatDataSet.add(opHousingResourcesList.get(0));
				repeatDataSet.add(opHousingResourcesList.get(1));
			}
		}else {
			for (int i = 0; i < opHousingResourcesList.size(); i++) {
				for (int  j  =  opHousingResourcesList.size()  -   1 ; j  >  i; j --) {
					if (opHousingResourcesList.get(i).getHouseSyncId().equals(opHousingResourcesList.get(j).getHouseSyncId())) {
						repeatDataSet.add(opHousingResourcesList.get(i));
					}
				}
			}
		}
		//验证项目id是否是当前活动
        OpActivity activity = opActivityMapper.selectById(activityIdNow);

		// 赋值为空的批次名称
		opHousingResourcesList.forEach(s -> {
			if (StringUtils.isBlank(s.getBatchNameSimulation())) {
				s.setBatchNameSimulation(GlobalConstants.COMMON);
			}
			if (StringUtils.isBlank(s.getBatchNameFormal())) {
				s.setBatchNameFormal(GlobalConstants.COMMON);
			}
		});

		// 选房模式 && 开启按批次处理
		if (null != activity.getCheckMode() && 1 == activity.getCheckMode().intValue() && null != activity.getBatchFlag() && 1 == activity.getBatchFlag().intValue()) {
			// 校验批次
			// 获取该活动所有批次
			List<OpBatch> batchList = batchService.selectAllBatchListByActivityId(activityIdNow);
			List<String> collect = batchList.stream().map(OpBatch::getBatchName).collect(Collectors.toList());
			collect.add(GlobalConstants.COMMON);
			for (OpHousingResources house : opHousingResourcesList) {
				if (!collect.contains(house.getBatchNameSimulation())) {
					jsonResult.setCode(GlobalConstants.E_CODE);
					jsonResult.setMsg("您导入房源中【模拟批次名称】列中批次必须在以下批次中：" + collect.stream().collect(Collectors.joining(",")));
					return jsonResult;
				}
				if (!collect.contains(house.getBatchNameFormal())) {
					jsonResult.setCode(GlobalConstants.E_CODE);
					jsonResult.setMsg("您导入房源中【正式批次名称】列中批次必须在以下批次中：" + collect.stream().collect(Collectors.joining(",")));
					return jsonResult;
				}
			}
		}

        //数据是重复一次的  需去重再删除  0 1 2 3 4 5
		//去掉重复数据
//		System.out.println(opHousingResourcesList.size());
//		//查询出当前running状态的活动下 所有房源
//		QueryWrapper<OpActivity> wrapper=new QueryWrapper();
//		wrapper.eq("status_code","running");
//		//排除同活动下房源
//		wrapper.ne("id",activityIdNow);
//		List<OpActivity> opActivityList = opActivityMapper.selectList(wrapper);
//		if (opActivityList.size()!=0) {
//            QueryWrapper<OpHousingResources> queryWrapper = new QueryWrapper<>();
//
//            List<Integer> list=new ArrayList<>();
//            for (OpActivity opActivity : opActivityList) {
//                list.add(opActivity.getId());
//            }
//            queryWrapper.in("activity_id",list);
//            List<OpHousingResources> opHousingResourcesListRunning = opHousingResourcesMapper.selectList(queryWrapper);
//
//            List<String> syncIdList = new ArrayList<>();
//            //将syncId存入List
//            for (OpHousingResources opHousingResources : opHousingResourcesListRunning) {
//                syncIdList.add(opHousingResources.getHouseSyncId());
//            }
//            //判断syncIdList中是否有和本次导入的相同的syncid
//            for (OpHousingResources opHousingResources : opHousingResourcesList) {
//                if (syncIdList.contains(opHousingResources.getHouseSyncId())) {
//                    repeatDataSet.add(opHousingResources);
//                }
//            }
//            //数据是重复一次的  需去重再删除  0 1 2 3 4 5
//            //去掉重复数据
//
//            repeatDataList.addAll(repeatDataSet);
//            //如果重复 直接返回 不可导入数据
//        }
        if (repeatDataSet.size() != 0) {
			StringBuffer sbf = new StringBuffer();
			for (OpHousingResources opHousingResources : repeatDataSet) {
                System.out.println("+++++++++++++++"+opHousingResources.getHouseSyncId());
                sbf.append(opHousingResources.getHouseSyncId()).append(";");
            }
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("您导入的房源同步ID重复的数据为：" + sbf);
            return jsonResult;
        }
		//跳出 不删除重复数据了
//		opHousingResourcesList.removeAll(repeatDataSet);

//		开始写入数据
		//重复写入了 有时间改改
		List<OpHousingResources> opHousingResourcesListImport = new ArrayList<>();
		for (OpHousingResources opHousingResources : opHousingResourcesList) {
			OpHousingResources opHousingResourcesImport = new OpHousingResources();
			opHousingResourcesImport.setActivityId(activityIdNow);
			opHousingResourcesImport.setProjectId(activity.getProjectId());
			opHousingResourcesImport.setProjectName(activity.getProjectName());
			//导入的房源Id 存入本地房源同步id
			opHousingResourcesImport.setHouseSyncId(opHousingResources.getHouseSyncId());
			opHousingResourcesImport.setHouseName(opHousingResources.getHouseName());
			opHousingResourcesImport.setProChildId("");
			opHousingResourcesImport.setProChildName("");
			opHousingResourcesImport.setBuildingName(opHousingResources.getBuildingName());
			opHousingResourcesImport.setUnitName(opHousingResources.getUnitName());
			opHousingResourcesImport.setCurrentFloor(opHousingResources.getCurrentFloor());
			opHousingResourcesImport.setRoomType(opHousingResources.getRoomType());
			opHousingResourcesImport.setRoomNum(opHousingResources.getRoomNum());
			opHousingResourcesImport.setHouserArea(opHousingResources.getHouserArea());
			opHousingResourcesImport.setTotalPrice(opHousingResources.getTotalPrice());
			opHousingResourcesImport.setUnitPrice(opHousingResources.getUnitPrice());
			opHousingResourcesImport.setDiscountTotalPrice(opHousingResources.getDiscountTotalPrice());
			opHousingResourcesImport.setDiscountUnitPrice(opHousingResources.getDiscountUnitPrice());
			opHousingResourcesImport.setHourseType(opHousingResources.getHourseType());
			opHousingResourcesImport.setRoomStru(opHousingResources.getRoomStru());
			opHousingResourcesImport.setLastUpdateDate(opHousingResources.getLastUpdateDate());
			opHousingResourcesImport.setYn(GlobalConstants.Y);
			//导入对象
			opHousingResourcesImport.setCreatedBy("ExcelImport");
			opHousingResourcesImport.setCreationDate(LocalDateTimeUtils.localDateTimeToDate(LocalDateTime.now()));
			opHousingResourcesImport.setBatchNameSimulation(opHousingResources.getBatchNameSimulation());
			opHousingResourcesImport.setBatchNameFormal(opHousingResources.getBatchNameFormal());
			opHousingResourcesListImport.add(opHousingResourcesImport);
		}
		try {
			opHousingResourcesMapper.delete(new QueryWrapper<OpHousingResources>().eq("activity_id",activityIdNow));

			// 删除 数据库中已存在房源白名单的客户信息
			QueryWrapper<OpUserHouseResource> userHouseResourceQueryWrapper = new QueryWrapper<>();
			userHouseResourceQueryWrapper.eq("activity_id", activityIdNow);
			userHouseResourceMapper.delete(userHouseResourceQueryWrapper);

			for (OpHousingResources opHousingResources : opHousingResourcesListImport) {
				opHousingResourcesMapper.insert(opHousingResources);
			}
		}catch (Exception e){
		    e.printStackTrace();
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("Excel数据插入数据库异常");
			return jsonResult;
		}
		//导入成功后 删除收藏下本活动所有房源
        QueryWrapper<OpFavorite> favoritequeryWrapper=new QueryWrapper<>();
		favoritequeryWrapper.eq("activity_id",activityIdNow);
        favoriteService.remove(favoritequeryWrapper);

		opOrderService.initCanOrderBySql(activityIdNow);
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("导入成功!");
		return jsonResult;
	}

	@Override
	public List<OpHousingResources> getOpHousingResourcesList(String activityId) {
		return baseMapper.getOpHousingResourcesList(activityId);
	}

	@Override
	public HashMap<String, Object> getOpHousingBaseData(String activityId, String regular) {
		HashMap<String,Object> resultMap = new HashMap<String,Object>();
		//获取房源套数
		int openHouseCount = Integer.parseInt(baseMapper.getHouseCountByActivityId(Integer.parseInt(activityId)));
		//获取订单数据
		List<OpHousingResources> openData = baseMapper.getSalesDataByActivityId(activityId, regular);
		//可选套数
		//resultMap.put("optionalProperties", openHouseCount-openData.size());
		//已售金额
		BigDecimal totalAcount = new BigDecimal("0.00");
		//订单滚动提示
		List<String> oderList = new ArrayList<>();
		//成交房间Id
		String houserIdsString = "";
        BigDecimal partten = new BigDecimal("10000");
        // 模拟生成销控房源记录数据
        for (OpHousingResources resources : openData) {
            resources.setRemark(StringUtils.isBlank(resources.getRemark()) ? "" : resources.getRemark().substring(0, 1));
            houserIdsString = houserIdsString +  resources.getId().toString() + ",";
            totalAcount = totalAcount.add(resources.getTotalPrice());
            oderList.add(//"恭喜" + resources.getRemark() + "**，成功选定-" +
                    resources.getBuildingName() + resources.getUnitName() + resources.getRoomNum()+ " 已被成功选定");
        }
        resultMap.put("orderDataList", openData);
		resultMap.put("houserIdsString", StringUtils.isBlank(houserIdsString) ? "" : houserIdsString.substring(0, houserIdsString.length() - 1));
        totalAcount = totalAcount.divide(partten, 2, BigDecimal.ROUND_HALF_UP);
		resultMap.put("totalAcount", totalAcount);
		resultMap.put("oderList", oderList);

		OpActivity opActivity = opActivityMapper.selectById(activityId);
		// 销控逻辑
		// 定义是否需要校验 销控逻辑标识
		LocalDateTime nowDate = LocalDateTime.now();
		DateTimeFormatter pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		String endDate = nowDate.format(pattern);
		// 校验 当前时间 在 模拟开盘的开始和结束时间区间内
		if (null != opActivity.getSimulationStart() && null != opActivity.getSimulationEnd() && nowDate.isBefore(opActivity.getSimulationEnd())) {
			endDate = opActivity.getSimulationEnd().format(pattern);
		}
		// 校验 当前时间 在 正式开盘的开始和结束时间区间内
		if (null != opActivity.getFormalStart() && null != opActivity.getFormalEnd()) {
			// 校验
			if (null != opActivity.getSimulationEnd()) {
				if (nowDate.isAfter(opActivity.getSimulationEnd())) {
					endDate = opActivity.getFormalEnd().format(pattern);
				} else {
					endDate = opActivity.getSimulationEnd().format(pattern);
				}
			} else {
				endDate = opActivity.getFormalEnd().format(pattern);
			}
		}
		//获取登录数据-今日登录人数
		int todayLoginUserCount = baseMapper.getTodayLoginUserCountByActivityIdTwo(activityId, "", endDate);
//		int todayLoginUserCount = baseMapper.getTodayLoginUserCountByActivityId(activityId);
		// 获取 当前活动白名单人数
		Integer count = baseMapper.countOpUserHouseResourceByActivityId(activityId);
		if (null != count) {
			todayLoginUserCount = todayLoginUserCount + count.intValue();
		}
		resultMap.put("optionalProperties", opHousingResourcesMapper.getOptionalProperties(activityId, regular));
		resultMap.put("todayLoginUserCount", todayLoginUserCount);
		return resultMap;
	}
	@Override
	public Integer getOptionalProperties(String activityId,String regular)
	{
		return opHousingResourcesMapper.getOptionalProperties(activityId, regular);
	}
	@Override
	public int getTodayLoginUserCountByActivityIdTwo(String activityId, String beginDate, String endDate) {
		return baseMapper.getTodayLoginUserCountByActivityIdTwo(activityId, beginDate, endDate);
	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:10 2023/10/23
	 * @param activity 		活动对象
	 * @return java.util.Map<java.lang.String,java.lang.Object>
	 * @description // TODO 获取推送大屏的map
	 **/
	@Override
	public Map<String, Object> getSocketMap(OpActivity activity) {
		// 定义时间、返回结果
		LocalDateTime nowDate = LocalDateTime.now();
		DateTimeFormatter pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		String endDate = nowDate.format(pattern);
		Map<String, Object> resultMap = Maps.newHashMapWithExpectedSize(5);
		// 定义当前时间所在时间段
		//————1————模拟开始————2————模拟结束————3————正式开始————4————正式结束————5————
		int stageNum = 1;
		// 模拟 开始结束时间不为空
		if (null != activity.getSimulationStart() && null != activity.getSimulationEnd()) {
			if (nowDate.isBefore(activity.getSimulationStart())) {
				stageNum = 1;
			} else if (nowDate.isAfter(activity.getSimulationStart()) && nowDate.isBefore(activity.getSimulationEnd())) {
				stageNum = 2;
			} else {
				stageNum = 3;
			}
			// 处理登录时间
			if (nowDate.isBefore(activity.getSimulationEnd())) {
				endDate = activity.getSimulationEnd().format(pattern);
			}
		}
		// 正式 开始结束时间不为空
		if (null != activity.getFormalStart() && null != activity.getFormalEnd()) {
			if (nowDate.isAfter(activity.getFormalStart()) && nowDate.isBefore(activity.getFormalEnd())) {
				stageNum = 4;
			} else if (nowDate.isAfter(activity.getFormalEnd())) {
				stageNum = 5;
			}
			// 处理登录时间
			if (null != activity.getSimulationEnd()) {
				if (nowDate.isAfter(activity.getSimulationEnd())) {
					endDate = activity.getFormalEnd().format(pattern);
				} else {
					endDate = activity.getSimulationEnd().format(pattern);
				}
			} else {
				endDate = activity.getFormalEnd().format(pattern);
			}
		}

		/**
		 * 	大屏参与人数 = 参与人数基数 + 截止当前登录人数 + 白名单人数 + 销控房间数
		 *
		 * 	参与人数基数：活动管理页面设置的参与人数基数
		 * 	截止当前登录人数：用户表last_login_date字段不为空且时间早于 "时间参数"
		 * 					（"时间参数" = 如果当前时间在模拟结束时间之前则取模拟结束时间；
		 * 								   如果当前时间在模拟结束时间以后，且在正式结束时间以前则取正式结束时间；
		 * 								   否则取当前时间）
		 * 					其中last_login_date只有当小程序用户退出登录才会清空，否则小程序用户登陆过以后该时间就不再变化了。
		 *	白名单人数：白名单所涉及到的用户人数
		 *	销控房间数：当前时间在模拟开始时间以后且在模拟结束时间以前、或者当前时间在正式开始时间以后且在正式结束时间以前，
		 *				这两种时间区间内，销控房间数为数据库销控的房间数量，其他时间均为0
		 */

		// 获取今日登录人数
		int todayLoginUserCount = baseMapper.getTodayLoginUserCountByActivityIdTwo(activity.getId().toString(), "", endDate);
		// 获取 当前活动白名单人数
		Integer count = baseMapper.countOpUserHouseResourceByActivityId(activity.getId().toString());
		if (null != count) {
			todayLoginUserCount = todayLoginUserCount + count.intValue();
		}
		// 大屏虚拟参与人数
        if (null != activity.getJoinNumber()) {
            todayLoginUserCount = todayLoginUserCount + activity.getJoinNumber().intValue();
        }
		// 赋值 已售房源字符串
		resultMap.put("houserIdsString", "");
		// 赋值 订单滚动集合
		resultMap.put("oderList", Lists.newArrayListWithCapacity(1));
		// 赋值 可售套数
		resultMap.put("optionalProperties", 0);
		// 赋值 参与人数
		resultMap.put("todayLoginUserCount", todayLoginUserCount);
		// 赋值 活动
		resultMap.put("opActivity", activity);
		// 获取所有房源数量
		List<OpHousingResources> opHousingResourcesList = baseMapper.getOpHousingResourcesList(activity.getId().toString());
		// 校验 房源
		if (CollectionUtils.isNotEmpty(opHousingResourcesList)) {
			// 校验 当前时间段
			if (stageNum == 1) {
				// 模拟开始前
				// 赋值 可售套数
				resultMap.put("optionalProperties", opHousingResourcesList.size());
			} else if (stageNum == 2) {
				// 模拟开始后，模拟结束前
				convertData(resultMap, activity, "n", opHousingResourcesList, todayLoginUserCount);
			} else if (stageNum == 3) {
				// 模拟结束后，正式开始前
				// 赋值 可售套数
				resultMap.put("optionalProperties", opHousingResourcesList.size());
			} else if (stageNum == 4) {
				// 正式开始后，正式结束前
				convertData(resultMap, activity, "y", opHousingResourcesList, todayLoginUserCount);
			} else {
				// 正式结束后
				convertData(resultMap, activity, "y", opHousingResourcesList, todayLoginUserCount);
			}
		}
		return resultMap;
	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:23 2023/10/26
	 * @param userResourceImportVOList	数据集合
	 * @param activityIdNow 			活动id
	 * @return com.tahoecn.core.json.JSONResult<java.lang.Object>
	 * @description // TODO 导入客户白名单
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
    public JSONResult<Object> saveUserResourceImportData(List<UserResourceImportVO> userResourceImportVOList, String activityIdNow) {
		JSONResult<Object> jsonResult = new JSONResult<>();
		// 获取 该活动下所有房源数据
		QueryWrapper<OpHousingResources> resourcesQueryWrapper = new QueryWrapper<>();
		resourcesQueryWrapper.eq("activity_id", activityIdNow);
		resourcesQueryWrapper.eq("yn", "y");
		List<OpHousingResources> opHousingResourcesList = opHousingResourcesMapper.selectList(resourcesQueryWrapper);
		if (CollectionUtils.isEmpty(opHousingResourcesList)) {
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("该活动下房源数据为空！");
			return jsonResult;
		}
		// 获取 该活动下所有用户数据
		QueryWrapper<OpUser> userQueryWrapper = new QueryWrapper<>();
		userQueryWrapper.eq("activity_id", activityIdNow);
		userQueryWrapper.eq("yn", "y");
		List<OpUser> opUserList = opUserMapper.selectList(userQueryWrapper);
		if (CollectionUtils.isEmpty(opUserList)) {
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("该活动下客户数据为空！");
			return jsonResult;
		}
		LocalDateTime now = LocalDateTime.now();
		// 定义 待入库对象集合
		List<OpUserHouseResource> insertList = Lists.newArrayListWithCapacity(userResourceImportVOList.size());
		// 定义 房源同步id集合
		List<String> roomIdList = Lists.newArrayListWithCapacity(userResourceImportVOList.size());
		// 定义 房源同步ID重复的字符串
		StringBuffer repeatRoomIdSbf = new StringBuffer();
		// 定义 不存在的房源同步ID字符串
		StringBuffer notExistRoomIdSbf = new StringBuffer();
		// 定义 已被销控的房源同步ID字符串
		StringBuffer saleControlRoomIdSbf = new StringBuffer();
		// 定义 不存在的手机号字符串
		StringBuffer notExistTelSbf = new StringBuffer();
		// 定义 超过此人可购房源数量的手机号字符串
		StringBuffer moreThanTelSbf = new StringBuffer();

		// 处理数据
		for (UserResourceImportVO importVO : userResourceImportVOList) {
			// 校验房源同步ID重复
			if (roomIdList.contains(importVO.getHouseSyncId())) {
				repeatRoomIdSbf.append(importVO.getHouseSyncId()).append(";");
				continue;
			}
			roomIdList.add(importVO.getHouseSyncId());
			// 定义 房源对象
			OpHousingResources thisResource = null;
			// 遍历房源
			for (OpHousingResources resources : opHousingResourcesList) {
				if (importVO.getHouseSyncId().equals(resources.getHouseSyncId())) {
					thisResource = resources;
					break;
				}
			}
			// 校验
			if (null == thisResource) {
				// 不存在的房源同步ID
				notExistRoomIdSbf.append(importVO.getHouseSyncId()).append(";");
				continue;
			} else if (null != thisResource.getSaleControlFlag() && "y".equals(thisResource.getSaleControlFlag())) {
				// 已被销控的房源同步ID
				saleControlRoomIdSbf.append(importVO.getHouseSyncId()).append(";");
				continue;
			}
			// 定义 客户对象
			OpUser thisUser = null;
			// 遍历客户
			for (OpUser user : opUserList) {
				if (importVO.getTel().equals(user.getTel())) {
					thisUser = user;
					// 用户可选房数--
					user.setSelectCount(null != user.getSelectCount() ? user.getSelectCount().intValue() - 1 : 0);
					break;
				}
			}
			// 校验
			if (null == thisUser) {
				// 不存在的手机号
				notExistTelSbf.append(importVO.getTel()).append(";");
				continue;
			} else if (0 == thisUser.getSelectCount().intValue()) {
				// 超过此人可购房源数量的手机号
				moreThanTelSbf.append(importVO.getTel()).append(";");
				continue;
			}
			// 定义待入库对象
			insertList.add(new OpUserHouseResource(Long.valueOf(activityIdNow), thisUser.getId().longValue(), thisResource.getId().longValue(), "ExcelImport", "ExcelImport", now));
		}

		// 校验 导入数据是否合规
		// 存在不合规数据，返回
		Map<String, Object> errorMap = Maps.newHashMap();
		if (repeatRoomIdSbf.toString().contains(";")) {
			errorMap.put("重复的房源同步ID的数据为", repeatRoomIdSbf.toString());
		}
		if (notExistRoomIdSbf.toString().contains(";")) {
			errorMap.put("不存在的房源同步ID的数据为", notExistRoomIdSbf.toString());
		}
		if (saleControlRoomIdSbf.toString().contains(";")) {
			errorMap.put("已被销控的房源同步ID的数据为", saleControlRoomIdSbf.toString());
		}
		if (notExistTelSbf.toString().contains(";")) {
			errorMap.put("不存在的手机号的数据为", notExistTelSbf.toString());
		}
		if (moreThanTelSbf.toString().contains(";")) {
			errorMap.put("超过此人可购房源数量手机号的数据为", moreThanTelSbf.toString());
		}
		// 校验
		if (errorMap.size() > 0) {
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg(JSONObject.toJSON(errorMap).toString());
			return jsonResult;
		}

		// 删除 数据库中已存在房源白名单的客户信息
		QueryWrapper<OpUserHouseResource> userHouseResourceQueryWrapper = new QueryWrapper<>();
		userHouseResourceQueryWrapper.eq("activity_id", activityIdNow);
		userHouseResourceMapper.delete(userHouseResourceQueryWrapper);
		// 批量入库
		userHouseResourceMapper.insertList(insertList);
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("导入成功!");
		return jsonResult;
    }

    /**
	 * <AUTHOR>  <EMAIL>
	 * @date 11:27 2023/10/24
	 * @param resultMap					结果map
	 * @param activity					活动对象
	 * @param regular					是否正式标识："y"--正式;"n"--模拟
	 * @param opHousingResourcesList	房源集合
	 * @param userCount					今日在线人数
	 * @description // TODO	处理开盘时间区间内数据
	 **/
	public void convertData(Map<String, Object> resultMap, OpActivity activity, String regular,
							List<OpHousingResources> opHousingResourcesList, int userCount) {
		int todayLoginUserCount = userCount;
		// 获取 所有订单数据
		QueryWrapper<OpOrder> orderQueryWrapper = new QueryWrapper<>();
		orderQueryWrapper.select("house_id AS houseId, house_name AS houseName, creation_date AS saleControlDate")
				.eq("activity_id", activity.getId().toString()).eq("is_regular", regular)
				.eq("yn", "y").eq("effective_flag", 0);
		List<OpOrder> opOrderList = opOrderMapper.selectList(orderQueryWrapper);
		// 校验 订单集合是否存在
		if (CollectionUtils.isEmpty(opOrderList)) {
			opOrderList = new ArrayList<>();
		}
		// 遍历数据
		for (OpHousingResources resources : opHousingResourcesList) {
			// 销控数据
			if (null != resources.getSaleControlFlag() && "y".equals(resources.getSaleControlFlag())) {
				// 参数人数++
				todayLoginUserCount++;
				// 放入订单集合
				opOrderList.add(new OpOrder(resources.getId().toString(), resources.getBuildingName() + resources.getUnitName() + resources.getRoomNum(), resources.getSaleControlDate()));
			}
		}
		// 校验
		if (opOrderList.size() >= 1) {
			// 按照销控时间倒叙排序
			// 按照 销控时间/订单创建时间，倒序排序
			opOrderList = opOrderList.stream().sorted(Comparator.comparing(OpOrder::getSaleControlDate).reversed()).collect(Collectors.toList());
			// 声明 房源id字符串
			StringBuffer sbf = new StringBuffer();
			// 声明订单提示集合
			List<String> orderStrList = Lists.newArrayListWithCapacity(opOrderList.size());
			// 处理订单数据
			for (OpOrder order : opOrderList) {
				// 拼接字符串
				sbf.append(order.getHouseId()).append(",");
				// 赋值订单
				orderStrList.add(order.getHouseName() + "已被成功选定");
			}
			// 赋值 已售房源字符串
			resultMap.put("houserIdsString", sbf.deleteCharAt(sbf.length() - 1).toString());
			// 赋值 订单滚动集合
			resultMap.put("oderList", orderStrList);
			// 赋值 可售套数
			resultMap.put("optionalProperties", opHousingResourcesList.size() - opOrderList.size());
		} else {
			// 赋值 可售套数
			resultMap.put("optionalProperties", opHousingResourcesList.size());
		}
		// 赋值 参与人数
		resultMap.put("todayLoginUserCount", todayLoginUserCount);
	}

	@Override
	public Integer saveOne(OpHousingResources opHousingResources) {
		return opHousingResourcesMapper.saveOne(opHousingResources);
	}

	@Override
	public List<Map<String, Object>> getScreenList(String activityId) {
		return baseMapper.getScreenList(activityId);
	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:35 2022/7/19
	 * @param activityId 活动id  必传
	 * @return java.util.List<com.tahoecn.opening.model.dto.HouseResourceDTO>
	 * @description // TODO 递归查询房源信息根据活动ID
	 **/
	@Override
	public List<HouseResourceDTO> selectHouseResourceListByActivityId(Long activityId) {
		return baseMapper.selectHouseResourceListByActivityId(activityId);
	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 14:35 2023/3/13
	 * @param paramVO 条查参数VO
	 * @return com.tahoecn.core.json.JSONResult
	 * @description // TODO 条查房源白名单列表
	 **/
    @Override
    public IPage<WhiteHouseResourceDTO> selectWhiteHouseResourceListByCondition(WhiteHouseResourceParamVO paramVO) {
		Page page = new Page(paramVO.getPageNum(), paramVO.getPageSize());
    	// 查询
		IPage<WhiteHouseResourceDTO> iPage = opHousingResourcesMapper.selectWhiteHouseResourceListByCondition(page, paramVO);
		List<WhiteHouseResourceDTO> dataList = iPage.getRecords();
		// 校验处理
		if (CollectionUtils.isNotEmpty(dataList)) {
			// 不足两位小数补0
			DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			DecimalFormat format = new DecimalFormat("0.00#");
			for (WhiteHouseResourceDTO vo : dataList) {
				vo.setTotalPrice(null != vo.getTotalPrice() ? vo.getTotalPrice().setScale(0, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
				vo.setUnitPrice(null != vo.getUnitPrice() ? new BigDecimal(format.format(vo.getUnitPrice().setScale(2, BigDecimal.ROUND_HALF_UP))) : new BigDecimal("0.00"));
				vo.setDiscountTotalPrice(null != vo.getDiscountTotalPrice() ? vo.getDiscountTotalPrice().setScale(0, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
				vo.setDiscountUnitPrice(null != vo.getDiscountUnitPrice() ? new BigDecimal(format.format(vo.getDiscountUnitPrice().setScale(2, BigDecimal.ROUND_HALF_UP))) : new BigDecimal("0.00"));
				vo.setCreateTimeStr(dtf.format(vo.getCreateTime()));
			}
			iPage.setRecords(dataList);
		}
        return iPage;
    }

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:53 2023/3/13
	 * @param activityId		活动ID
	 * @param thisSaleControlFlag 	是否销控标识：y--是;n--否
	 * @param ids				房源ids，多个用英文逗号分隔
	 * @return com.tahoecn.core.json.JSONResult
	 * @description // TODO 更改房源销控状态
	 **/
	@Transactional(rollbackFor = Exception.class)
    @Override
    public JSONResult updateSaleControlFlag(String activityId, String thisSaleControlFlag, String ids) {
        JSONResult<Object> jsonResult = new JSONResult<>();
		List<String> idList = Arrays.asList(ids.split(","));
		// 获取房源数据
		QueryWrapper<OpHousingResources> wrapper = new QueryWrapper<>();
		wrapper.eq("activity_id", activityId).in("id", idList);
		List<OpHousingResources> resourcesList = opHousingResourcesMapper.selectList(wrapper);
		if (CollectionUtils.isNotEmpty(resourcesList)) {
		    // 获取所有白名单房源列表
            // 查询当前活动下 所有的房源白名单配置房源信息
            QueryWrapper<OpUserHouseResource> resourceQueryWrapper = new QueryWrapper<>();
            resourceQueryWrapper.eq("activity_id", activityId);
            List<OpUserHouseResource> objectList = userHouseResourceMapper.selectList(resourceQueryWrapper);
            if (CollectionUtils.isNotEmpty(objectList)) {
                List<Long> haveIdList = objectList.stream().map(OpUserHouseResource::getHouseResourceId).collect(Collectors.toList());
                // 定义 不允许操作的房源字符串
                StringBuffer sbf = new StringBuffer();
                sbf.append("白名单房源不允许操作！房源名依次为：");
                // 匹配数据
                for (OpHousingResources house : resourcesList) {
                    if (haveIdList.contains(house.getId().longValue())) {
                        sbf.append(house.getHouseName()).append(";");
                    }
                }
                // 校验
                if (sbf.toString().contains(";")) {
                    jsonResult.setCode(GlobalConstants.E_CODE);
                    jsonResult.setMsg(sbf.deleteCharAt(sbf.length() - 1).toString());
                    return jsonResult;
                }
            }

            // 查询当前活动下 获取所有正式订单房源列表
            QueryWrapper<OpOrder> orderQueryWrapper = new QueryWrapper<>();
            orderQueryWrapper.eq("activity_id", activityId).eq("is_regular", "y").eq("yn", "y");
            List<OpOrder> orderList = opOrderMapper.selectList(orderQueryWrapper);
            if (CollectionUtils.isNotEmpty(orderList)) {
                List<String> haveIdList = orderList.stream().map(OpOrder::getHouseId).collect(Collectors.toList());
                // 定义 不允许操作的房源字符串
                StringBuffer sbf = new StringBuffer();
                sbf.append("已存在正式订单房源不允许操作！房源名依次为：");
                // 匹配数据
                for (OpHousingResources house : resourcesList) {
                    if (haveIdList.contains(house.getId().toString())) {
                        sbf.append(house.getHouseName()).append(";");
                    }
                }
                // 校验
                if (sbf.toString().contains(";")) {
                    jsonResult.setCode(GlobalConstants.E_CODE);
                    jsonResult.setMsg(sbf.deleteCharAt(sbf.length() - 1).toString());
                    return jsonResult;
                }
            }

            // 校验
			if ("y".equalsIgnoreCase(thisSaleControlFlag)) {
				String userAccount = ThreadLocalUtils.getUserName();
				String userName = ThreadLocalUtils.getRealName();
				Date date = new Date();
				Random random = new Random();
				for (OpHousingResources resources : resourcesList) {
					// 销控状态
					resources.setSaleControlFlag(thisSaleControlFlag);
					// 销控
					resources.setRemark(StringUtils.isNotBlank(resources.getRemark()) ? resources.getRemark() : nameList.get(random.nextInt(10)));
					resources.setSaleControlUserAccount(userAccount);
					resources.setSaleControlUserName(userName);
					resources.setSaleControlDate(date);
					opHousingResourcesMapper.updateById(resources);
				}
			} else {
				// 批量更改
				List<Integer> deleteIdList = resourcesList.stream().map(OpHousingResources::getId).collect(Collectors.toList());
				opHousingResourcesMapper.updateSaleControlFlagByIdList(deleteIdList);
			}

			String userAccount = ThreadLocalUtils.getUserName();
			String userName = ThreadLocalUtils.getRealName();
			Date date = new Date();
			Random random = new Random();
			for (OpHousingResources resources : resourcesList) {
				// 销控状态
				resources.setSaleControlFlag(thisSaleControlFlag);
                // 生成记录姓氏
				if ("y".equalsIgnoreCase(thisSaleControlFlag)) {
					// 销控
					resources.setRemark(StringUtils.isNotBlank(resources.getRemark()) ? resources.getRemark() : nameList.get(random.nextInt(10)));
					resources.setSaleControlUserAccount(userAccount);
					resources.setSaleControlUserName(userName);
					resources.setSaleControlDate(date);
				} else {
					resources.setRemark("");
					resources.setSaleControlUserAccount("");
					resources.setSaleControlUserName("");
					resources.setSaleControlDate(null);
				}
                opHousingResourcesMapper.updateById(resources);
			}

			sendWebSocket(activityId);
		}


        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 11:10 2023/9/15
     * @param activityId 
     * @description // TODO 写入大屏数据
     **/
	@Override
    public void sendWebSocket(String activityId) {
    	pushWebSocketMQ(activityId);
	}

    /**
     * <AUTHOR>  <EMAIL>
     * @date 11:10 2023/9/15
     * @param activityId
     * @description // TODO 写入大屏数据
     **/
	@Override
    public void pushWebsocketData(String activityId) {
		// 重新写入大屏
        OpActivity opActivity =  opActivityMapper.selectById(activityId);
        Map<String, Object> socketMap = getSocketMap(opActivity);
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(socketMap);
        try {
            WebSocketServer.sendInfo(JSON.toJSON(jsonResult).toString(), activityId);
        } catch (Exception e) {
            e.printStackTrace();
        }
	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 11:22 2023/11/7
	 * @param activityId 	活动id
	 * @description // TODO 推送websocket消息到MQ
	 **/
	@Override
	public void pushWebSocketMQ(String activityId) {
		try {
			rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
			rabbitTemplate.setExchange(MQConstant.WEBSOCKET_EXCHANGE);
			Message message= MessageBuilder.withBody(getBytesFromObject(activityId)).setDeliveryMode(MessageDeliveryMode.PERSISTENT).build();
			message.getMessageProperties().setHeader(AbstractJavaTypeMapper.DEFAULT_CONTENT_CLASSID_FIELD_NAME, MessageProperties.CONTENT_TYPE_JSON);
			rabbitTemplate.convertAndSend(message);
			log.info("端口号为：{}, 活动id为：{}, 消息成功写入到mq", serverPort, activityId);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("端口号为：{}, 活动id为：{}, 消息写入到mq异常！异常为：{}", serverPort, activityId, e);
		}
	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 11:41 2023/11/7
	 * @param bytes 			消息字节数组
	 * @description // TODO 消费websocketMQ消息
	 **/
	@RabbitListener(bindings = @QueueBinding(
			value = @Queue(),
			exchange = @Exchange(value = MQConstant.WEBSOCKET_EXCHANGE, type = ExchangeTypes.FANOUT)
		)
	)
	public void consumerWebSocketMQ(byte[] bytes) {
		try {
			String activityId = (String) getObjectFromBytes(bytes);
			log.info("端口号为：{}, 接收到消息内容为：{}", serverPort, activityId);
			// 校验本机是否存在websocket长连接
			if (WebSocketServer.checkWebsocket(activityId, serverPort)) {
				pushWebsocketData(activityId);
				log.info("端口号为：{}, 成功消费MQ！消费消息内容为：{}", serverPort, activityId);
			} else {
				log.info("端口号为：{}, 不存在本活动连接，故本次不消费！本次不消费的消息内容为：{}", serverPort, activityId);
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("端口号为：{}, 消费字节内容为：{}, 消费消息异常！异常为：{}", serverPort, bytes, e);
		}
	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 11:33 2023/11/7
	 * @param obj 			待转换object
	 * @return byte[]		转换后字节数组
	 * @description // TODO object转换字节数组
	 **/
	public byte[] getBytesFromObject(Serializable obj) {
		if (obj == null) {
			return null;
		}
		try {
			ByteArrayOutputStream bo = new ByteArrayOutputStream();
			ObjectOutputStream oo = new ObjectOutputStream(bo);
			oo.writeObject(obj);
			return bo.toByteArray();
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 11:36 2023/11/7
	 * @param objBytes 			字节数组
	 * @return java.lang.Object	转换后对象
	 * @description // TODO 	字节数组转换为对象
	 **/
	public Object getObjectFromBytes(byte[] objBytes) {
		if (objBytes == null || objBytes.length == 0) {
			return null;
		}
		try {
			ByteArrayInputStream bi = new ByteArrayInputStream(objBytes);
			ObjectInputStream oi = new ObjectInputStream(bi);
			return oi.readObject();
		} catch (Exception e) {
			return null;
		}
	}


//
//    /**
//     * <AUTHOR>  <EMAIL>
//     * @date 11:10 2023/9/15
//     * @param activityId
//     * @description // TODO 写入大屏数据
//     **/
//    public void sendWebSocket(String activityId) {
//		// 重新写入大屏
//		// websocket推送前端数据 -关闭 改为通过循环方式调用
//		// 获取到活动详情
//		LocalDateTime nowDate = LocalDateTime.now();
//		OpActivity opActivity =  opActivityMapper.selectById(activityId);
//		// 获取 当前时间属于该活动的哪一个时间段，模拟选房时间，还是 正式选房时间
//		HashMap<String, Object> resultMap = getOpHousingBaseData(activityId, DateUtils.regular(opActivity, nowDate));
//		resultMap.put("opActivity", opActivity);
//		int todayLoginUserCount = (int)(resultMap.get("todayLoginUserCount") == null ? 0: resultMap.get("todayLoginUserCount"))+(opActivity.getJoinNumber() == null ? 0 : opActivity.getJoinNumber());
//		resultMap.put("todayLoginUserCount", todayLoginUserCount);
//		resultMap.put("sysDate", new Date().getTime());
//
//		// 销控逻辑
//		// 定义是否需要校验 销控逻辑标识
//		boolean saleControlFlag = false;
//		// 校验 当前时间 在 模拟开盘的开始和结束时间区间内
//		if (null != opActivity.getSimulationStart() && null != opActivity.getSimulationEnd()) {
//			// 在 模拟开始结束时间区间内
//			if (nowDate.isAfter(opActivity.getSimulationStart()) && nowDate.isBefore(opActivity.getSimulationEnd())) {
//				saleControlFlag = true;
//			}
//		}
//		// 校验 当前时间 在 正式开盘的开始和结束时间区间内
//		if (null != opActivity.getFormalStart() && null != opActivity.getFormalEnd()) {
//			// 在 正式开始时间以后
//			if (nowDate.isAfter(opActivity.getFormalStart())) {
//				saleControlFlag = true;
//			}
//		}
//		if (saleControlFlag) {
//			// 获取该活动 对应销控的房源数据信息
//			QueryWrapper<OpHousingResources> resourceQueryWrapper2 = new QueryWrapper<>();
//			resourceQueryWrapper2.eq("activity_id", opActivity.getId()).eq("sale_control_flag", "y").eq("yn", "y").orderByAsc("sale_control_date");
//			List<OpHousingResources> saleControlResourceList = opHousingResourcesMapper.selectList(resourceQueryWrapper2);
//			// 校验
//			if (CollectionUtils.isNotEmpty(saleControlResourceList)) {
//				// 获取 已存在的订单数据
//				//可选套数
//				Integer optionalProperties = Integer.valueOf(resultMap.get("optionalProperties") + "");
//				//resultMap.put("optionalProperties", optionalProperties.intValue() - saleControlResourceList.size());
//				// 当前活动销控人数
//				resultMap.put("todayLoginUserCount", todayLoginUserCount + saleControlResourceList.size());
//				//订单滚动提示
//				List<OpHousingResources> orderDataList = (List<OpHousingResources>) resultMap.get("orderDataList");
//				// 定义总集合
//				// 校验赋值
//				if (CollectionUtils.isNotEmpty(orderDataList)) {
//					saleControlResourceList.addAll(orderDataList);
//				}
//				// 按照 销控时间/订单创建时间，倒序排序
//				saleControlResourceList = saleControlResourceList.stream().sorted(Comparator.comparing(OpHousingResources::getSaleControlDate).reversed()).collect(Collectors.toList());
//
//				//已售金额
//				BigDecimal totalAcount = new BigDecimal("0.00");
//				//订单滚动提示
//				List<String> oderList = new ArrayList<>();
//				//成交房间Id
//				String houserIdsString = "";
//				BigDecimal partten = new BigDecimal("10000");
//				// 模拟生成销控房源记录数据
//				for (OpHousingResources resources : saleControlResourceList) {
//					houserIdsString = houserIdsString +  resources.getId().toString() + ",";
//					totalAcount = totalAcount.add(resources.getTotalPrice());
//					oderList.add(//"恭喜" + resources.getRemark() + "**，成功选定-" +
//							resources.getBuildingName() + resources.getUnitName() + resources.getRoomNum()+ " 已被成功选定");
//				}
//				resultMap.put("houserIdsString", StringUtils.isBlank(houserIdsString) ? "" : houserIdsString.substring(0, houserIdsString.length() - 1));
//				totalAcount = totalAcount.divide(partten, 2, BigDecimal.ROUND_HALF_UP);
//				resultMap.put("totalAcount", totalAcount);
//				resultMap.put("oderList", oderList);
//			}
//		}
//		resultMap.put("optionalProperties",opHousingResourcesMapper.getOptionalProperties(activityId, DateUtils.regular(opActivity, nowDate)));
//		JSONResult<Object> jsonResult2 = new JSONResult<>();
//		jsonResult2.setCode(GlobalConstants.S_CODE);
//		jsonResult2.setMsg("SUCCESS");
//		jsonResult2.setData(resultMap);
//		try {
//			WebSocketServer.sendInfo(JSON.toJSONString(jsonResult2), activityId);
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//	}

	/**
	 *
	 * <AUTHOR> <EMAIL>
	 * @Date         2025/4/27 18:03
	 * @Param        activityId 活动id
	 * @Param        dataList   批次名称集合
	 * @Return       void
	 * @Description  TODO 修改活动模拟批次为公共
	 **/
	@Transactional(rollbackFor = Exception.class)
	@Override
	public void updateBatchNameSimulation(String activityId, List<String> dataList) {
		opActivityMapper.updateBatchNameSimulation(activityId, dataList);
	}

	/**
	 *
	 * <AUTHOR> <EMAIL>
	 * @Date         2025/4/27 18:03
	 * @Param        activityId 活动id
	 * @Param        dataList   批次名称集合
	 * @Return       void
	 * @Description  TODO 修改活动正式批次为公共
	 **/
	@Transactional(rollbackFor = Exception.class)
	@Override
	public void updateBatchNameFormal(String activityId, List<String> dataList) {
		opActivityMapper.updateBatchNameFormal(activityId, dataList);
	}
}
