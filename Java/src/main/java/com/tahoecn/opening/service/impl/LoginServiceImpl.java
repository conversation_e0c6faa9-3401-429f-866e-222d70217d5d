package com.tahoecn.opening.service.impl;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.ApiResult;
import com.tahoecn.opening.common.utils.HttpClientUtil;
import com.tahoecn.opening.common.utils.JwtTokenUtil;
import com.tahoecn.opening.common.utils.RSAUtil;
import com.tahoecn.opening.common.utils.UUIDUtil;
import com.tahoecn.opening.mapper.CsUcUserMapper;
import com.tahoecn.opening.model.CsUcUser;
import com.tahoecn.opening.model.vo.BusLoginParamVO;
import com.tahoecn.opening.model.vo.BusLoginResponseVO;
import com.tahoecn.opening.service.LoginService;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.service.impl
 * @ClassName: LoginServiceImpl
 * @Description:// TODO 登录接口实现类
 * @Date: 2024/8/8 13:37
 * @Version: 1.0
 */
@Transactional
@Service
public class LoginServiceImpl implements LoginService {

    public static final Log log = LogFactory.get();

    @Resource
    CsUcUserMapper csUcUserMapper;

    @Autowired
    RedisTemplate redisTemplate;

    @Value("${chengdong.url}")
    private String rootUrl;

    @Value("${chengdong.accessCode}")
    private String accessCode;

    @Value("${chengdong.login}")
    private String login;

    @Value("${chengdong.clientId}")
    private String clientId;

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:47 2024/8/8
     * @param paramVO
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO 业务用户登录
     **/
    @Override
    public ApiResult busLogin(BusLoginParamVO paramVO, String type) {
        // 获取用户对象
        QueryWrapper<CsUcUser> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(CsUcUser::getFdUsername, paramVO.getUserAccount()).eq(CsUcUser::getFdIsdelete, "-1");
        List<CsUcUser> userList = csUcUserMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(userList)) {
            return new ApiResult(ApiResult.ERROR, "该用户不存在！");
        }
        CsUcUser user = userList.get(0);
        // 校验用户是否可用
        if (null == user.getFdAvailable() || 1 != user.getFdAvailable().intValue()) {
            return new ApiResult(ApiResult.ERROR, "该用户被禁用！");
        }
        // 校验是否需要密码验证
        if (StringUtils.isBlank(type)) {
            // 校验密码
            String sign = DigestUtils.md5Hex(paramVO.getUserAccount() + "_" + paramVO.getUserPassword() + "_" + paramVO.getUserAccount()).toLowerCase();
            if (!sign.equals(user.getFdPassword() + "")) {
            	System.out.println("qazwsx！！！"+sign);
            	//修改user
            	if (user.getErrorsNum()==4) {
            		user.setErrorsNum(user.getErrorsNum()+1);
            		user.setFdIsLocked(1);
            		// 获取 Calendar 实例
                    Calendar calendar = Calendar.getInstance();
                    // 加上 10 分钟
                    calendar.add(Calendar.MINUTE, 10);
                    // 转换为 Date 类型
                    Date date = calendar.getTime();
            		user.setLockedDate(date);
				}else {
					user.setErrorsNum(user.getErrorsNum()+1);
				}
            	csUcUserMapper.updateById(user);
                return new ApiResult(ApiResult.ERROR, "密码错误！");
            }
        }
        String token = UUIDUtil.getUUID32();
        // 声明对象
        BusLoginResponseVO vo = new BusLoginResponseVO(user.getId(), user.getFdSid(), user.getFdUsername(), user.getFdName(), user.getFdWorkPhone(), user.getFdEmail(), user.getFdOrgId(), user.getFdOrgName(), token);
        // 校验是否为超级管理员
        if (null != user.getFdLock() && 1 == user.getFdLock()) {
            vo.setAdminFlag(true);
        } else {
            vo.setAdminFlag(false);
        }
        // 放入缓存
        redisTemplate.opsForValue().set(GlobalConstants.REDIS_USER_TOKEN_KEY + token, JSONObject.toJSON(vo).toString(), 6, TimeUnit.HOURS);
        // 放入cookie
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        response.addHeader("Cookie", token);
        Cookie cookie = new Cookie("Cookie", token);
        response.addCookie(cookie);
        return ApiResult.getSuccessApiResponse(vo);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:26 2024/8/19
     * @param paramVO
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     获取ticket
     **/
    @Override
    public ApiResult getTicket(BusLoginParamVO paramVO) {
        try {
        	// 判断账号是否锁定
        	// 1.查询用户对象数据
        	QueryWrapper<CsUcUser> wrapper = new QueryWrapper<>();
        	String userAccount = paramVO.getUserAccount();
        	if (userAccount.endsWith("-PG12138-")) {
        		userAccount = paramVO.getUserAccount().replace("-PG12138-", "");
        	}
        	wrapper.lambda().eq(CsUcUser::getFdUsername, userAccount).eq(CsUcUser::getFdIsdelete, "-1");
        	List<CsUcUser> userList = csUcUserMapper.selectList(wrapper);
        	if (CollectionUtils.isEmpty(userList)) {
        		return new ApiResult(ApiResult.ERROR, "账号或密码不正确！");
        	}
        	//用户对象数据
        	CsUcUser csUcUser = userList.get(0);
        	if (csUcUser.getErrorsNum()==null) {
        		csUcUser.setErrorsNum(0);
			}
        	//获取十分钟之前的日期
            // 转换为 Date 类型（需指定时区）
            Date newDate = new Date();
            //获取锁定时间
            Date lockedDate = csUcUser.getLockedDate();
        	//判断是否锁定
        	if (csUcUser.getFdIsLocked()==1) {
        		if (lockedDate!=null&&lockedDate.getTime()<=newDate.getTime()) {
					//锁定时间已经超过
        			csUcUser.setFdIsLocked(2);
        			csUcUser.setLockedDate(null);
        			csUcUser.setErrorsNum(0);
        			csUcUserMapper.updateById(csUcUser);
				}else {
					//还在锁定期内
					return new ApiResult(ApiResult.ERROR, "账号已锁定请等待十分钟再试！");
				}
			}
            // 校验账号
            if (paramVO.getUserAccount().endsWith("-PG12138-")) {
                paramVO.setUserAccount(paramVO.getUserAccount().replace("-PG12138-", ""));
                return busLogin(paramVO, null);
            }
//            // 获取用户对象
//            QueryWrapper<CsUcUser> wrapper = new QueryWrapper<>();
//            wrapper.lambda().eq(CsUcUser::getFdUsername, paramVO.getUserAccount()).eq(CsUcUser::getFdIsdelete, "-1").eq(CsUcUser::getFdIfWechatShow, "1");
//            List<CsUcUser> userList = csUcUserMapper.selectList(wrapper);
//            if (CollectionUtils.isNotEmpty(userList)) {
//                return busLogin(paramVO, null);
//            }
            
            //解密
            JSONObject paramObj = new JSONObject();
            paramObj.put("account", paramVO.getUserAccount());
            byte[] bytes = RSAUtil.encryptByPublicKey(RSAUtil.PASSWORD_PUBLIC_KEY, paramVO.getUserPassword());
            String encryptBASE64 = RSAUtil.encryptBASE64(bytes);
            paramObj.put("password", encryptBASE64);
            log.info("请求统一登陆平台授权码入参为：" + paramObj.toJSONString());
            String accessStr = HttpClientUtil.HttpPostJson(rootUrl + accessCode, paramObj);
            log.info("请求统一登陆平台授权码返参为：" + accessStr);
            // 核验登录信息
            JSONObject accessObj = JSONObject.parseObject(accessStr);
            if ("1".equals(accessObj.getString("code"))) {
                // 获取访问code
                JSONObject dataObj = JSONObject.parseObject(accessObj.get("data").toString());
                // 拼接路劲
                String url = rootUrl + login;
                url = url + "?clientId=" + clientId + "&accessCode=" + dataObj.getString("accessCode");
                log.info("统一平台核验入参为：" + url);
                String loginStr = HttpClientUtil.HttpGet(url);
                log.info("统一平台核验返参为：" + loginStr);
                JSONObject loginObj = JSONObject.parseObject(loginStr);
                if (ApiResult.SUCCESS.intValue() == loginObj.getIntValue("code")) {
                    // 核验成功
                    // 登录逻辑
                    log.info("登录逻辑开始=======================：");
                    return busLogin(paramVO, "cccc");
                } else {
                	//修改user
                	if (csUcUser.getErrorsNum()==4) {
                		csUcUser.setErrorsNum(csUcUser.getErrorsNum()+1);
                		csUcUser.setFdIsLocked(1);
                		// 获取 Calendar 实例
                        Calendar calendar = Calendar.getInstance();
                        // 加上 10 分钟
                        calendar.add(Calendar.MINUTE, 10);
                        // 转换为 Date 类型
                        Date date = calendar.getTime();
                        csUcUser.setLockedDate(date);
    				}else {
    					csUcUser.setErrorsNum(csUcUser.getErrorsNum()+1);
    				}
                	csUcUserMapper.updateById(csUcUser);
                    // 核验失败
                    return ApiResult.getFailedApiResponse(loginObj.getString("message"));
                }
            } else {
            	//修改user
            	if (csUcUser.getErrorsNum()==4) {
            		csUcUser.setErrorsNum(csUcUser.getErrorsNum()+1);
            		csUcUser.setFdIsLocked(1);
            		// 获取 Calendar 实例
                    Calendar calendar = Calendar.getInstance();
                    // 加上 10 分钟
                    calendar.add(Calendar.MINUTE, 10);
                    // 转换为 Date 类型
                    Date date = calendar.getTime();
                    csUcUser.setLockedDate(date);
				}else {
					csUcUser.setErrorsNum(csUcUser.getErrorsNum()+1);
				}
            	csUcUserMapper.updateById(csUcUser);
                return ApiResult.getFailedApiResponse(accessObj.getString("message"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ApiResult.getFailedApiResponse("获取Ticket异常！");
        }
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:45 2024/8/19
     * @param token 
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     跳转登录
     **/
    @Override
    public ApiResult jumpByTicket(String token) {
        try {
            log.info("核验ticket的入参token为：" + token);
            String userName = JwtTokenUtil.getSubjectFromToken(token, JwtTokenUtil.jwtSecret);
            log.info("核验ticket的用户名为：" + userName);
            // 校验用户
            if (StringUtils.isBlank(userName)) {
                return ApiResult.getFailedApiResponse("解析用户名为空！");
            }
            // 登录逻辑
            log.info("登录逻辑开始=======================：");
            BusLoginParamVO paramVO = new BusLoginParamVO();
            paramVO.setUserAccount(userName);
            return busLogin(paramVO, "cccc");
        } catch (Exception e) {
            e.printStackTrace();
            return ApiResult.getFailedApiResponse("");
        }
    }
}
