package com.tahoecn.opening.service.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.LocalDateTimeUtils;
import com.tahoecn.opening.common.utils.ThreadLocalUtils;
import com.tahoecn.opening.mapper.*;
import com.tahoecn.opening.model.*;
import com.tahoecn.opening.model.dto.UserHouseResourceDTO;
import com.tahoecn.opening.model.vo.ConsultantVO;
import com.tahoecn.opening.model.vo.SaleListVO;
import com.tahoecn.opening.model.vo.UserHouseResourceParamVO;
import com.tahoecn.opening.service.IOpBatchService;
import com.tahoecn.opening.service.IOpFavoriteService;
import com.tahoecn.opening.service.IOpOrderService;
import com.tahoecn.opening.service.IOpUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 客户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@Service
public class OpUserServiceImpl extends ServiceImpl<OpUserMapper, OpUser> implements IOpUserService {

    @Autowired
    OpUserMapper opUserMapper;
    @Autowired
    OpOrderMapper opOrderMapper;
    @Autowired
    OpActivityMapper opActivityMapper;
    @Autowired
    IOpOrderService iOpOrderService;

    @Autowired
    IOpFavoriteService favoriteService;

    @Resource
    private OpUserHouseResourceMapper userHouseResourceMapper;

    @Resource
    private OpHousingResourcesMapper housingResourcesMapper;

    @Resource
    private IOpBatchService batchService;

    @Override
    public List<OpUser> queryByTelNumOrOpenId(String telNum, String openId) {
        QueryWrapper<OpUser> userWrapper = new QueryWrapper<>();
        userWrapper.eq("tel", telNum).or().eq("open_id", openId);
        List<OpUser> userList = list(userWrapper);
        if (userList == null || userList.size() == 0) {
            throw new RuntimeException("电话号码" + telNum + "无权访问,请填写正确的预留手机号码！");
        }
        return userList;
    }

    @Override
    public OpUser getOneByTelNumOrOpenId(String telNum, String openId) {
        QueryWrapper<OpUser> userWrapper = new QueryWrapper<>();
        userWrapper.eq("tel", telNum).or().eq("open_id", openId);
        OpUser one = getOne(userWrapper);
        return one;
    }


    @Override
    public IPage<SaleListVO> getUserSale(Page page, String activityIdNow,  OpUser opUser) {
        opUser.setActivityId(activityIdNow);
        return opUserMapper.getUserSale(page, opUser);
    }


    @Override
    public List<String> getRoleName() {
        List<String> roleNames = opUserMapper.getRoleName();
        //去掉返回列表中的客户选项
        roleNames.remove("customer");
        return roleNames;
    }

    @Override
    public boolean canBeDelete(List<Integer> idList) {
        //如果当前活动下 订单数据庞大 效率问题
        QueryWrapper<OpOrder> wrapper = new QueryWrapper<>();
        wrapper.eq("yn","y");
        List<OpOrder> list = opOrderMapper.selectList(wrapper);
        for (Integer id : idList) {
            for (OpOrder opOrder : list) {
                if (id==Integer.parseInt(opOrder.getUserId())) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Excel导入客户
     * @param opUserList
     * @param opActivity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONResult saveImportData(List<OpUser> opUserList, OpActivity opActivity) {
        System.out.println(opUserList);
        JSONResult<Object> jsonResult = new JSONResult<>();
        // 校验 是否存在订单数据
        QueryWrapper<OpOrder> orderWrapper=new QueryWrapper<>();
        orderWrapper.eq("activity_id", opActivity.getId().toString());
        List<OpOrder> opOrders = opOrderMapper.selectList(orderWrapper);
        if (opOrders.size()!=0){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("活动下存在订单,不可导入客户");
            return jsonResult;
        }

        // 查询该活动下所有的置业顾问
        QueryWrapper<OpUser> saleWrapper=new QueryWrapper<>();
        saleWrapper.eq("activity_id", opActivity.getId().toString());
        saleWrapper.eq("role_name", "置业顾问").eq("yn", "y");
        List<OpUser> saleUserList = opUserMapper.selectList(saleWrapper);
        if (CollectionUtils.isEmpty(saleUserList)) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("活动下暂无置业顾问,不可导入客户");
            return jsonResult;
        }

        List<String> saleList = Lists.newArrayListWithCapacity(saleUserList.size());
        for (OpUser sale : saleUserList) {
            saleList.add(sale.getName() + "_" + sale.getCstguid());
        }

        // 定义 手机号、cstguid、身份证号 集合
        List<String> telList = Lists.newArrayListWithCapacity(opUserList.size());
        List<String> cstguidList = Lists.newArrayListWithCapacity(opUserList.size());
        List<String> cardList = Lists.newArrayListWithCapacity(opUserList.size());
        // 定义 手机号重复、cstguid重复、身份证号重复  的字符串
        StringBuffer repeatTelSbf = new StringBuffer();
        StringBuffer repeatCstGuidSbf = new StringBuffer();
        StringBuffer repeatCardSbf = new StringBuffer();
        // 定义  手机号有误、身份证号有误、置业顾问有误、置业顾问不存在  的字符串
        StringBuffer errorTelSbf = new StringBuffer();
        StringBuffer errorCardSbf = new StringBuffer();
        StringBuffer errorSaleNameSbf = new StringBuffer();
        StringBuffer noneSaleNameSbf = new StringBuffer();
        // 定义 手机号为空、姓名为空、身份证号为空、cstguid为空  的条数
        int noneTelNum = 0;
        int noneNameNum = 0;
        int noneCardNum = 0;
        int noneCstguidNum = 0;

        // 数据合规性校验
        for (OpUser vo : opUserList) {
            // 校验 手机号为空
            if (null == vo.getTel()) {
                noneTelNum++;
            } else {
                // 校验 是否合规
                if (11 != vo.getTel().length()) {
                    errorTelSbf.append(vo.getTel()).append(";");
                } else {
                    // 校验 是否重复
                    if (telList.contains(vo.getTel())) {
                        repeatTelSbf.append(vo.getTel()).append(";");
                    } else {
                        telList.add(vo.getTel());
                    }
                }
            }
            // 校验 cstguid为空
            if (null == vo.getCstguid()) {
                noneCstguidNum++;
            } else {
                // 校验 是否重复
                if (cstguidList.contains(vo.getCstguid())) {
                    repeatCstGuidSbf.append(vo.getCstguid()).append(";");
                } else {
                    cstguidList.add(vo.getCstguid());
                }
            }
            // 校验 姓名为空
            if (null == vo.getName()) {
                noneNameNum++;
            }
            // 校验 身份证号为空
            if (null == vo.getIdCard()) {
                noneCardNum++;
            } else {
                // 校验 是否合规
                if (18 != vo.getIdCard().length()) {
                    errorCardSbf.append(vo.getIdCard()).append(";");
                } else {
                    // 校验 是否重复
                    if (cardList.contains(vo.getIdCard())) {
                        repeatCardSbf.append(vo.getIdCard()).append(";");
                    } else {
                        cardList.add(vo.getIdCard());
                    }
                }
            }
            // 校验 置业顾问是否合规
            if (StringUtils.isNotBlank(vo.getSaleName())) {
                if (vo.getSaleName().contains("_")) {
                    // 校验 所传置业顾问是否存在
                    if (saleList.contains(vo.getSaleName())) {
                        // 按照 _ 分割
                        String[] split = vo.getSaleName().split("_");
                        vo.setSaleId(split [1]);
                        vo.setSaleName(split [0]);
                    } else {
                        noneSaleNameSbf.append(vo.getSaleName()).append(";");
                    }
                } else {
                    errorSaleNameSbf.append(vo.getSaleName()).append(";");
                }
            }
        }

        // 校验 导入数据是否合规
        // 存在不合规数据，返回
        Map<String, Object> errorMap = Maps.newHashMap();
        if (noneTelNum > 0) {
            errorMap.put("手机号为空的条数为：", noneTelNum);
        }
        if (noneNameNum > 0) {
            errorMap.put("姓名为空的条数为：", noneNameNum);
        }
        if (noneCardNum > 0) {
            errorMap.put("身份证号为空的条数为：", noneCardNum);
        }
        if (noneCstguidNum > 0) {
            errorMap.put("CstGUID为空的条数为：", noneCstguidNum);
        }
        if (repeatTelSbf.toString().contains(";")) {
            errorMap.put("手机号重复的数据为", repeatTelSbf.toString());
        }
        if (repeatCstGuidSbf.toString().contains(";")) {
            errorMap.put("CstGUID重复的数据为", repeatCstGuidSbf.toString());
        }
//        if (repeatCardSbf.toString().contains(";")) {
//            errorMap.put("身份证号重复的数据为", repeatCardSbf.toString());
//        }
        if (errorTelSbf.toString().contains(";")) {
            errorMap.put("手机号不合规的数据为", errorTelSbf.toString());
        }
        if (errorCardSbf.toString().contains(";")) {
            errorMap.put("身份证号不合规的数据为", errorCardSbf.toString());
        }
        if (errorSaleNameSbf.toString().contains(";")) {
            errorMap.put("置业顾问不合规的数据为", errorSaleNameSbf.toString());
        }
        if (noneSaleNameSbf.toString().contains(";")) {
            errorMap.put("置业顾问不存在的数据为", noneSaleNameSbf.toString());
        }
        // 校验
        if (errorMap.size() > 0) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg(JSONObject.toJSON(errorMap).toString());
            return jsonResult;
        }

        // 选房模式 && 开启按批次处理
        if (null != opActivity.getCheckMode() && 1 == opActivity.getCheckMode().intValue() && null != opActivity.getBatchFlag() && 1 == opActivity.getBatchFlag().intValue()) {
            // 校验批次
            // 获取该活动所有批次
            List<OpBatch> batchList = batchService.selectAllBatchListByActivityId(opActivity.getId().toString());
            List<String> collect = batchList.stream().map(OpBatch::getBatchName).collect(Collectors.toList());
            collect.add(GlobalConstants.COMMON);
            // 校验批次
            for (OpUser opUser : opUserList) {
                // 校验批次
                if (StringUtils.isNotBlank(opUser.getBatchNameStr())) {
                    List<String> list = Arrays.asList(opUser.getBatchNameStr().split(","));
                    // 遍历
                    for (String s : list) {
                        if (!collect.contains(s)) {
                            jsonResult.setCode(GlobalConstants.E_CODE);
                            jsonResult.setMsg("您导入用户中【可看房源批次】列中批次必须在以下批次中：" + collect.stream().collect(Collectors.joining(",")));
                            return jsonResult;
                        }
                    }
                }
            }
        }

       try {
           // 删除 数据库中已存在的客户信息
           QueryWrapper<OpUser> wrapper=new QueryWrapper<>();
           wrapper.eq("activity_id", opActivity.getId().toString());
           wrapper.eq("role_name", "customer");
           opUserMapper.delete(wrapper);

           // 删除 数据库中已存在房源白名单的客户信息
           QueryWrapper<OpUserHouseResource> userHouseResourceQueryWrapper = new QueryWrapper<>();
           userHouseResourceQueryWrapper.eq("activity_id", opActivity.getId().toString());
           userHouseResourceMapper.delete(userHouseResourceQueryWrapper);

           // 遍历 赋值入库
           for (OpUser opUserImport : opUserList) {
               opUserImport.setActivityId(opActivity.getId().toString());
               opUserImport.setProjectId(opActivity.getProjectId());
               // 校验
               if (null == opUserImport.getSelectCount()) {
                   // 为空时候取活动的配置
                   opUserImport.setSelectCount(opActivity.getBuyNumber());
               }
               opUserImport.setCreationDate(LocalDateTimeUtils.localDateTimeToDate(LocalDateTime.now()));
               opUserImport.setCreatedBy("ExcelImport");
               opUserImport.setLastUpdateBy("ExcelImport");
               opUserImport.setRoleName("customer");
               opUserImport.setYn(GlobalConstants.Y);

               opUserMapper.insert(opUserImport);
           }
       } catch (Exception e) {
           e.printStackTrace();
           //手动回滚
           TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
           jsonResult.setCode(GlobalConstants.E_CODE);
           jsonResult.setMsg("Excel数据插入数据库异常");
           return jsonResult;
       }

        //导入成功后 删除收藏下本活动所有数据
        QueryWrapper<OpFavorite> favoritequeryWrapper=new QueryWrapper<>();
        favoritequeryWrapper.eq("activity_id", opActivity.getId().toString());
        favoriteService.remove(favoritequeryWrapper);

        iOpOrderService.initCanOrderBySql(opActivity.getId().toString());
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("导入成功!");
        return jsonResult;
    }


    /**
     * Excel导入置业顾问
     * @param consultantVOSList
     * @param activity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONResult saveImportDataConsultant(List<ConsultantVO> consultantVOSList, OpActivity activity) {
        JSONResult<Object> jsonResult = new JSONResult<>();

        // 定义 手机号、cstguid 集合
        List<String> telList = Lists.newArrayListWithCapacity(consultantVOSList.size());
        List<String> cstguidList = Lists.newArrayListWithCapacity(consultantVOSList.size());
        // 定义 手机号重复、cstguid重复  的字符串
        StringBuffer repeatTelSbf = new StringBuffer();
        StringBuffer repeatCstGuidSbf = new StringBuffer();
        // 定义  手机号有误、角色有误  的字符串
        StringBuffer errorTelSbf = new StringBuffer();
        StringBuffer errorRoleSbf = new StringBuffer();
        // 定义 手机号为空、姓名为空、角色为空、cstguid为空  的条数
        int noneTelNum = 0;
        int noneNameNum = 0;
        int noneRoleNum = 0;
        int noneCstguidNum = 0;

        // 数据合规性校验
        for (ConsultantVO vo : consultantVOSList) {
            // 校验 手机号为空
            if (null == vo.getTel()) {
                noneTelNum++;
            } else {
                // 校验 是否合规
                if (11 != vo.getTel().length()) {
                    errorTelSbf.append(vo.getTel()).append(";");
                } else {
                    // 校验 是否重复
                    if (telList.contains(vo.getTel())) {
                        repeatTelSbf.append(vo.getTel()).append(";");
                    } else {
                        telList.add(vo.getTel());
                    }
                }
            }
            // 校验 cstguid为空
            if (null == vo.getCstguid()) {
                noneCstguidNum++;
            } else {
                // 校验 是否重复
                if (cstguidList.contains(vo.getCstguid())) {
                    repeatCstGuidSbf.append(vo.getCstguid()).append(";");
                } else {
                    cstguidList.add(vo.getCstguid());
                }
            }
            // 校验 姓名为空
            if (null == vo.getName()) {
                noneNameNum++;
            }
            // 校验 角色为空
            if (null == vo.getRoleName()) {
                noneRoleNum++;
            } else {
                // 校验 是否合规
                if (!"置业顾问".equals(vo.getRoleName()) && !"营销经理".equals(vo.getRoleName())) {
                    errorRoleSbf.append(vo.getRoleName()).append(";");
                }
            }
        }

        // 校验 导入数据是否合规
        // 存在不合规数据，返回
        Map<String, Object> errorMap = Maps.newHashMap();
        if (noneTelNum > 0) {
            errorMap.put("手机号为空的条数为：", noneTelNum);
        }
        if (noneNameNum > 0) {
            errorMap.put("姓名为空的条数为：", noneNameNum);
        }
        if (noneRoleNum > 0) {
            errorMap.put("角色为空的条数为：", noneRoleNum);
        }
        if (noneCstguidNum > 0) {
            errorMap.put("CstGUID为空的条数为：", noneCstguidNum);
        }
        if (repeatTelSbf.toString().contains(";")) {
            errorMap.put("手机号重复的数据为", repeatTelSbf.toString());
        }
        if (repeatCstGuidSbf.toString().contains(";")) {
            errorMap.put("CstGUID重复的数据为", repeatCstGuidSbf.toString());
        }
        if (errorTelSbf.toString().contains(";")) {
            errorMap.put("手机号不合规的数据为", errorTelSbf.toString());
        }
        if (errorRoleSbf.toString().contains(";")) {
            errorMap.put("角色不合规的数据为", errorRoleSbf.toString());
        }
        // 校验
        if (errorMap.size() > 0) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg(JSONObject.toJSON(errorMap).toString());
            return jsonResult;
        }

//        // 先 查询 以上手机号和cstguid是否存在于数据库中
//        QueryWrapper<OpUser> wrapper = new QueryWrapper<>();
//        wrapper.select("cstguid");
//        wrapper.eq("activity_id", activity);
//        wrapper.in("cstguid", cstguidList);
//        wrapper.ne("role_name","customer");
//        List<Object> existCstGuidList = opUserMapper.selectObjs(wrapper);
//        wrapper = new QueryWrapper<>();
//        wrapper.select("tel");
//        wrapper.eq("activity_id", activity);
//        wrapper.in("tel", telList);
//        wrapper.ne("role_name","customer");
//        List<Object> existTelList = opUserMapper.selectObjs(wrapper);
//        if (CollectionUtils.isNotEmpty(existCstGuidList)) {
//            errorMap.put("CstGUID数据库已存在的数据为：", existCstGuidList);
//        }
//        if (CollectionUtils.isNotEmpty(existTelList)) {
//            errorMap.put("手机号数据库已存在的数据为：", existTelList);
//        }
//        // 校验
//        if (errorMap.size() > 0) {
//            jsonResult.setCode(GlobalConstants.E_CODE);
//            jsonResult.setMsg(JSONObject.toJSON(errorMap).toString());
//            return jsonResult;
//        }

        try {
            // 物理删除数据库数据
            QueryWrapper<OpUser> wrapper = new QueryWrapper<>();
            wrapper.eq("activity_id", activity.getId().toString());
            wrapper.ne("role_name","customer");
            opUserMapper.delete(wrapper);

            // 入库
            OpUser opUserImport = null;
            for (ConsultantVO opUser : consultantVOSList) {
                opUserImport = new OpUser();
                opUserImport.setActivityId(activity.getId().toString());
                opUserImport.setProjectId(activity.getProjectId());
                opUserImport.setCstguid(opUser.getCstguid());
                opUserImport.setName(opUser.getName());
                opUserImport.setTel(opUser.getTel());
                opUserImport.setGender(opUser.getGender());
                opUserImport.setLastUpdateDate(LocalDateTimeUtils.localDateTimeToDate(LocalDateTime.now()));
                opUserImport.setCreationDate(opUser.getCreationDate());
                opUserImport.setCreatedBy("ExcelImport");
                opUserImport.setLastUpdateBy("ExcelImport");
                opUserImport.setRoleName(opUser.getRoleName());
//            opUserImport.setRoleCode(opUser.getRoleCode());
//            opUserImport.setAccountName(opUser.getAccountName());
//            opUserImport.setAccountType(opUser.getAccountType());
                opUserImport.setYn(GlobalConstants.Y);

                opUserMapper.insert(opUserImport);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //手动回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("Excel数据插入数据库异常");
            return jsonResult;
        }

        iOpOrderService.initCanOrderBySql(activity.getId().toString());
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("导入成功!");
        return jsonResult;
    }
	@Override
	public List<HashMap<String, String>> getOpUserByProjectIdAndTelOrOpenId(String projectId, String tel, String openId) {
		return baseMapper.getOpUserByProjectIdAndTelOrOpenId(projectId,tel,openId);
	}

	@Override
	public List<OpUser> getOpUserListByProjectIdAndTel(String tel, String projectId) {
		return baseMapper.getOpUserListByProjectIdAndTel(tel,projectId);
	}

	@Override
	public List<HashMap<String, String>> getOpUserByActivityIds(List activityIds) {
		return baseMapper.getOpUserByActivityIds(activityIds);
	}

	@Override
	public List<OpUser> getOpUserListByActivityIdAndOpenId(String activityId, String openId, String tel) {
		return baseMapper.getOpUserListByActivityIdAndOpenId(activityId,openId,tel);
	}

	@Override
	public List<HashMap<String, String>> getActivityRunOrCloseList(String projectId) {
		return baseMapper.getActivityRunOrCloseList(projectId);
	}

    @Override
    public IPage<OpUser> getUserList(Page page, OpUser opUser) {
        return opUserMapper.getUserList(page,opUser);
    }

    @Override
    public List<String> getBookingNum(String activityId) {
        return opUserMapper.getBookingNum(activityId);
    }

    @Override
    public List<String> getSelecCount(String activityId) {
        return opUserMapper.getselecCount(activityId);
    }

    @Override
    public List<OpUser> getSaleName(String activityId) {
        return opUserMapper.getSaleName(activityId);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:12 2022/7/4
     * @param activityId
     * @param tel 
     * @description // TODO 退出登录
     **/
    @Transactional
    @Override
    public void logout(String activityId, String tel) {
        OpUser opUser = new OpUser();
        opUser.setOpenId("OPENID");
        opUser.setLastLoginDate(null);
        opUser.setFirstLogin(0);
        opUser.setActivityId(activityId);
        opUser.setTel(tel);
        // 更改退出登录后的最后登录时间
        opUserMapper.updateUserLoginDate(opUser);
    }
    public void updateUserLoginDateByTel(String activityId, String tel,LocalDateTime lastLoginDate)
    {
        OpUser opUser=new OpUser();
        opUser.setLastLoginDate(lastLoginDate);
        opUser.setActivityId(activityId);
        opUser.setTel(tel);
        opUserMapper.updateUserLoginDateByTel(opUser);
    }
    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:35 2022/7/19
     * @param userId    用户id 必传
     * @return java.util.List<com.tahoecn.opening.model.dto.UserHouseResourceDTO>
     * @description // TODO 根据用户id查询用户白名单房源列表
     **/
    @Override
    public List<UserHouseResourceDTO> getUserHouseResourceListByUserId(Long userId) {
        return opUserMapper.getUserHouseResourceListByUserId(userId);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:03 2022/7/19
     * @param paramVO   参数VO
     * @return com.tahoecn.core.json.JSONResult
     * @description // TODO 新增修改用户房源白名单数据
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public JSONResult saveUserHouseResource(UserHouseResourceParamVO paramVO) {
        QueryWrapper<OpUserHouseResource> wrapper = null;
        // 校验 房源列表
        if (CollectionUtils.isNotEmpty(paramVO.getHouseResourceIdList())) {
            List<Long> collect = paramVO.getHouseResourceIdList().stream().distinct().collect(Collectors.toList());
            paramVO.setHouseResourceIdList(collect);
            // 先查询 选中的房源中，是否已经存在被挂关系的数据
            wrapper = new QueryWrapper<>();
            wrapper.eq("activity_id", paramVO.getActivityId());
            wrapper.ne("user_id", paramVO.getUserId());
            wrapper.in("house_resource_id", paramVO.getHouseResourceIdList());
            // 查询
            List<OpUserHouseResource> existsUserHouseResourceList = userHouseResourceMapper.selectList(wrapper);
            // 校验
            if (CollectionUtils.isNotEmpty(existsUserHouseResourceList)) {
                // 取出所有 房源id集合
                List<Long> existsIdList = existsUserHouseResourceList.stream().map(OpUserHouseResource::getHouseResourceId).collect(Collectors.toList());
                // 查询 所有房源名称
                List<OpHousingResources> resourcesList = housingResourcesMapper.selectBatchIds(existsIdList);
                // 取出 所有的房源名称并拼接
                String houseNameStr = resourcesList.stream().map(OpHousingResources::getHouseName).collect(Collectors.joining("/"));
                return new JSONResult(GlobalConstants.E_CODE, "房源：" + houseNameStr + "，已被其他客户关联，请重新选择！");
            }
        }
        // 删除 数据库中 该活动下，该用户的所有白名单数据
        wrapper = new QueryWrapper<>();
        wrapper.eq("activity_id", paramVO.getActivityId());
        wrapper.eq("user_id", paramVO.getUserId());
        // 直接物理删除
        if (null != paramVO.getUserId()) {
            userHouseResourceMapper.delete(wrapper);
        }
        // 校验 是否新增入库
        if (CollectionUtils.isNotEmpty(paramVO.getHouseResourceIdList())) {
            // 新增 入库，这里断定数据不会超过20条，所以我们这里直接 循环写入数据库了，如果数据很多，要自定义批量入库sql
            OpUserHouseResource resource = new OpUserHouseResource();
            resource.setUserId(paramVO.getUserId());
            resource.setActivityId(paramVO.getActivityId());
            resource.setCreateId(ThreadLocalUtils.getUserName());
            resource.setCreateName(ThreadLocalUtils.getRealName());
            resource.setCreateTime(LocalDateTime.now());
            // 遍历入库
            for (Long houseResourceId : paramVO.getHouseResourceIdList()) {
                resource.setId(null);
                resource.setHouseResourceId(houseResourceId);
                userHouseResourceMapper.insert(resource);
            }
        }
        return new JSONResult(GlobalConstants.S_CODE, "操作成功！");
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 11:01 2022/8/12
     * @param cstguid
     * @param name
     * @description // TODO 将 数据库中 所有的sale_id是当前用户的saleName改值
     **/
    @Override
    public void updateSaleNameById(String cstguid, String name) {
        opUserMapper.updateSaleNameById(cstguid, name);
    }

	@Override
	public List<OpUser> getList(QueryWrapper<OpUser> wrapper) {
		return opUserMapper.selectList(wrapper);
	}
}
