package com.tahoecn.opening.service;

import com.tahoecn.opening.common.utils.ApiResult;
import com.tahoecn.opening.model.vo.BusLoginParamVO;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.service
 * @ClassName: LoginService
 * @Description:// TODO 登录接口
 * @Date: 2024/8/8 13:37
 * @Version: 1.0
 */
public interface LoginService {

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:47 2024/8/8
     * @param paramVO
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO 业务用户登录
     **/
    ApiResult busLogin(BusLoginParamVO paramVO, String type);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:26 2024/8/19
     * @param paramVO
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     获取ticket
     **/
    ApiResult getTicket(BusLoginParamVO paramVO);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:45 2024/8/19
     * @param token
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO 跳转登录
     **/
    ApiResult jumpByTicket(String token);
}
