package com.tahoecn.opening.service.impl;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.tahoecn.opening.config.AliyunSmsConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @Author: Zhengwenchao  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.service.impl
 * @Description:// TODO 阿里云服务类
 * @Date: 2025/5/9 16:44
 * @Version: 1.0
 **/
@Service
public class AliyunServiceImpl {

    private static final Logger log = LoggerFactory.getLogger(AliyunServiceImpl.class);

    @Autowired
    private Client smsClient;

    @Autowired
    private AliyunSmsConfig smsConfig;

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/12 14:45
     * @Param        phone          手机号
     * @Param        templateKey    模板key
     * @Param        paramJsonStr   入参对象json字符串
     * @Return       boolean
     * @Description  TODO 发送短信
     **/
    public boolean sendSms(String phone, String templateKey, String paramJsonStr) {
        try {
            String templateCode = smsConfig.getTemplates().get(templateKey);
            SendSmsRequest request = new SendSmsRequest()
                    .setPhoneNumbers(phone)
                    .setSignName(smsConfig.SIGN_NAME)
                    .setTemplateCode(templateCode)
                    .setTemplateParam(paramJsonStr);
            SendSmsResponse response = smsClient.sendSms(request);
            if (!"OK".equalsIgnoreCase(response.getBody().getCode())) {
                log.error("短信发送失败: {}", response.getBody().getMessage());
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("短信服务异常", e);
            return false;
        }
    }
}
