package com.tahoecn.opening.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.tahoecn.core.util.NetUtil;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.LocalDateTimeUtils;
import com.tahoecn.opening.common.utils.ThreadLocalUtils;
import com.tahoecn.opening.config.AliyunSmsConfig;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpMassageJob;
import com.tahoecn.opening.mapper.OpMassageJobMapper;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.model.vo.AliyunVariableVo;
import com.tahoecn.opening.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 定时短信表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-25
 */
@Service
public class OpMassageJobServiceImpl extends ServiceImpl<OpMassageJobMapper, OpMassageJob> implements IOpMassageJobService {


    @Autowired
    private CsSendSmsLogService csSendSmsLogService;

    @Autowired
    private IOpActivityService activityService;

    @Autowired
    private IOpUserService iOpUserService;

    @Value("${sms_environment}")
    private String smsEnvironment;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IOpOrderService opOrderService;

    @Autowired
    private AliyunSmsConfig smsConfig;

    @Value("${bind_host}")
    private String bindHost;

    @Override
    public void saveMessageRemindJob(OpActivity activity, String jobType){
        QueryWrapper<OpMassageJob> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpMassageJob::getActivityId, activity.getId());
        wrapper.lambda().eq(OpMassageJob::getJobType, jobType);
        wrapper.lambda().eq(OpMassageJob::getIsSent, GlobalConstants.N);
        this.remove(wrapper);//重新设置 全删
        if (GlobalConstants.Y.equals(activity.getIsMessageRemind())){
            OpMassageJob opMassageJob = new OpMassageJob();
            //Date sendDate = LocalDateTimeUtils.addDate(LocalDateTimeUtils.localDateTimeToDate(activity.getFormalStart()),0,activity.getMessageH(),activity.getMessageM());
            if(activity.getFormalStart() != null){
            	LocalDateTime sendDate = activity.getFormalStart().minusHours(activity.getMessageH()).minusMinutes(activity.getMessageM());
            	opMassageJob.setActivityId(activity.getId()+"");
            	opMassageJob.setSendTime(sendDate);
            	opMassageJob.setIsSent(GlobalConstants.N);
            	opMassageJob.setIsFormal(GlobalConstants.Y);
            	opMassageJob.setMsgContent(getMsgContent(activity));
            	opMassageJob.setJobType(jobType);
            	opMassageJob.setCreateTime(LocalDateTime.now());


                QueryWrapper<OpMassageJob> wrapperTemp = new QueryWrapper<>();
                wrapperTemp.lambda().eq(OpMassageJob::getActivityId, activity.getId());
                wrapperTemp.lambda().eq(OpMassageJob::getJobType, jobType);
                wrapperTemp.lambda().eq(OpMassageJob::getSendTime, sendDate);
                if (this.list(wrapperTemp).size() == 0)
            	    this.save(opMassageJob);
            }

//            if (GlobalConstants.Y.equals(activity.getIsSimulation())){
//                OpMassageJob opMassageJobSimulation = new OpMassageJob();
//                //Date sendDate = LocalDateTimeUtils.addDate(LocalDateTimeUtils.localDateTimeToDate(activity.getFormalStart()),0,activity.getMessageH(),activity.getMessageM());
//                if(activity.getSimulationStart() != null){
//                	LocalDateTime sendDateSimulation = activity.getSimulationStart().minusHours(activity.getMessageH()).minusMinutes(activity.getMessageM());
//                	opMassageJobSimulation.setActivityId(activity.getId()+"");
//                	opMassageJobSimulation.setSendTime(sendDateSimulation);
//                	opMassageJobSimulation.setIsSent(GlobalConstants.N);
//                	opMassageJobSimulation.setIsFormal(GlobalConstants.N);
//                	opMassageJobSimulation.setMsgContent(activity.getSimulationMessage());
//                	opMassageJobSimulation.setJobType("message");
//                    opMassageJobSimulation.setCreateTime(LocalDateTime.now());
//
//                    QueryWrapper<OpMassageJob> wrapperTemp = new QueryWrapper<>();
//                    wrapperTemp.lambda().eq(OpMassageJob::getActivityId, activity.getId());
//                    wrapperTemp.lambda().eq(OpMassageJob::getJobType, "message");
//                    wrapperTemp.lambda().eq(OpMassageJob::getSendTime, sendDateSimulation);
//                    if (this.list(wrapperTemp).size() == 0)
//                	    this.save(opMassageJobSimulation);
//                }
//            }
        }
//        this.saveBeforehandOrderJob(activity);
    }

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/12 15:50
     * @Param        activity               活动对象
     * @Return       java.lang.String
     * @Description  TODO 获取短信文本json
     **/
    public String getMsgContent(OpActivity activity) {
        String content = "";
        if (null != activity) {
            AliyunVariableVo vo = new AliyunVariableVo(smsConfig.SYSTEM_NAME, activity.getActivityName(), LocalDateTimeUtils.formatDateTime(activity.getFormalStart()));
            content = JSON.toJSONString(vo);
        }
        return content;
    }

    @Override
    public void removeMessageRemindJob(OpActivity activity){
        QueryWrapper<OpMassageJob> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpMassageJob::getActivityId, activity.getId());
        //wrapper.lambda().eq(OpMassageJob::getJobType, "message"); //活动关闭预制订单、开盘短信 统统删掉
        wrapper.lambda().eq(OpMassageJob::getIsSent, GlobalConstants.N);
        this.remove(wrapper);//重新设置 全删
    }

    @Override
    public void saveBeforehandOrderJob(OpActivity activity){
        QueryWrapper<OpMassageJob> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpMassageJob::getActivityId, activity.getId());
        wrapper.lambda().eq(OpMassageJob::getJobType, "beforehand");
        wrapper.lambda().eq(OpMassageJob::getIsSent, GlobalConstants.N);
        this.remove(wrapper);//重新设置 全删
        OpMassageJob opMassageJob = new OpMassageJob();
        if(activity.getFormalStart() == null || "".equals(activity.getFormalStart())){
        	return;
        }
        LocalDateTime sendDate = activity.getFormalStart().plusMinutes(5);//开始时间+5分钟
        opMassageJob.setActivityId(activity.getId()+"");
        opMassageJob.setSendTime(sendDate);
        opMassageJob.setIsSent(GlobalConstants.N);
        opMassageJob.setIsFormal(GlobalConstants.Y);
        opMassageJob.setJobType("beforehand");
        opMassageJob.setCreateTime(LocalDateTime.now());
        this.save(opMassageJob);
    }

//    @Scheduled(cron = "0 0/10 * * * ?") //十分钟执行一次
    public void sendMessageRemind() {

        if (org.apache.commons.lang3.StringUtils.isNotBlank(bindHost)) {
            Boolean flg = false;
            for (String ip : NetUtil.localIpv4s()) {
                flg = flg ? true : bindHost.equals(ip);

            }
            if (!flg) {
                return;
            }
        }

        if ("prod".equals(smsEnvironment)) {     //生产环境发短信   开盘提醒
            boolean lock = false;
            try {
                lock = redisTemplate.opsForValue().setIfAbsent("sendMessageRemindKey", "sendMessageRemindLock");
                if (lock) {
                    QueryWrapper<OpMassageJob> wrapper = new QueryWrapper<>();
                    wrapper.lambda().eq(OpMassageJob::getIsSent, GlobalConstants.N);    //没发送的
                    //wrapper.lambda().between(OpMassageJob::getSendTime, LocalDateTime.now(), LocalDateTime.now().plusMinutes(10));
                    wrapper.lambda().lt(OpMassageJob::getSendTime, LocalDateTime.now());
                    wrapper.lambda().eq(OpMassageJob::getJobType, "message");   //短信
                    List<OpMassageJob> jobs = this.list(wrapper);
                    for (OpMassageJob job : jobs) {
                        QueryWrapper<OpUser> userQueryWrapper = new QueryWrapper<>();
                        userQueryWrapper.lambda().eq(OpUser::getActivityId, job.getActivityId());
                        userQueryWrapper.lambda().eq(OpUser::getYn, GlobalConstants.Y);
                        userQueryWrapper.lambda().eq(OpUser::getRoleName, "customer");
                        List<OpUser> users = iOpUserService.list(userQueryWrapper);
                        if (users.size() == 0)
                            continue;
                        StringBuffer tels = new StringBuffer();
                        StringBuffer names = new StringBuffer();
                        for (OpUser user : users) {
                            tels.append(user.getTel()).append(",");
                            names.append(user.getName()).append(",");
                        }
                        tels = tels.deleteCharAt(tels.length() - 1);
                        names = names.deleteCharAt(names.length() - 1);
                        if (StringUtils.isNotEmpty(job.getMsgContent()))
                            csSendSmsLogService.sendSms(tels.toString(), job.getMsgContent(), names.toString());

                        job.setIsSent(GlobalConstants.Y);
                        job.setUpdateTime(LocalDateTime.now());
                        this.saveOrUpdate(job);
                    }
                }
            } finally {
                redisTemplate.delete("sendMessageRemindKey");
            }
        }

    }

    /**
     * 定时3分钟执行预制订单显示
     */
//    @Scheduled(cron = "0 0/1 * * * ?") //三分钟分钟执行一次
    public void setBeforeHandOrderShow() {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(bindHost)) {
            Boolean flg = false;
            for (String ip : NetUtil.localIpv4s()) {
                flg = flg ? true : bindHost.equals(ip);

            }
            if (!flg) {
                return;
            }
        }
        boolean lock = false;
        try {
            lock = redisTemplate.opsForValue().setIfAbsent("setBeforeHandOrderShowKey", "setBeforeHandOrderShowKey");
            if (lock) {
                System.out.println("setBeforeHandOrderShow lock = " + lock);
                QueryWrapper<OpMassageJob> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(OpMassageJob::getIsSent, GlobalConstants.N);    //没发送的
                wrapper.lambda().lt(OpMassageJob::getSendTime, LocalDateTime.now());
                wrapper.lambda().eq(OpMassageJob::getJobType, "beforehand");   //预制订单
                List<OpMassageJob> jobs = this.list(wrapper);
                for (OpMassageJob job : jobs) {
                    opOrderService.setBeforeOrderShow(job.getActivityId());

                    job.setIsSent(GlobalConstants.Y);
                    job.setUpdateTime(LocalDateTime.now());
                    this.saveOrUpdate(job);
                }
            }
        } finally {
            redisTemplate.delete("setBeforeHandOrderShowKey");
        }
    }

//    @Scheduled(cron = "0 0/3 * * * ?") //三分钟分钟执行一次
    public void closeActivityJob() {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(bindHost)) {
            Boolean flg = false;
            for (String ip : NetUtil.localIpv4s()) {
                flg = flg ? true : bindHost.equals(ip);

            }
            if (!flg) {
                return;
            }
        }
        boolean lock = false;
        try {
            lock = redisTemplate.opsForValue().setIfAbsent("closeActivityJobKey", "closeActivityJobKey");
            if (lock) {
                System.out.println("closeActivityJob lock = " + lock);
                QueryWrapper<OpActivity> wrapper = new QueryWrapper<>();
                wrapper.lambda().lt(OpActivity::getFormalEnd, LocalDateTime.now());
                wrapper.lambda().eq(OpActivity::getStatusCode, GlobalConstants.STATUS_R);
                List<OpActivity> jobs = activityService.list(wrapper);
                for (OpActivity job : jobs) {
//                    this.removeMessageRemindJob(job);
                    opOrderService.removeCanOrderRedis(job.getId().toString());
                    job.setStatusCode(GlobalConstants.STATUS_CLOSE);
                    job.setStatusName(GlobalConstants.STATUS_CLOSE_NAME);
                    job.setLastUpdateBy("autoClose");
                    job.setLastUpdateDate(LocalDateTime.now());
                    activityService.saveOrUpdate(job);
                }
            }
        } finally {
            redisTemplate.delete("closeActivityJobKey");
        }
    }
}
