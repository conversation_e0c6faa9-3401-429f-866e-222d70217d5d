package com.tahoecn.opening.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.model.dto.UserHouseResourceDTO;
import com.tahoecn.opening.model.vo.ConsultantVO;
import com.tahoecn.opening.model.vo.SaleListVO;
import com.tahoecn.opening.model.vo.UserHouseResourceParamVO;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 客户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
public interface IOpUserService extends IService<OpUser> {

    /**
     * 根据授权手机号或openId查看客户池是否存在该用户
     *
     * @param telNum 授权手机号
     * @param openId 微信标识ID
     * @return {@link List<OpUser>}
     */
    List<OpUser> queryByTelNumOrOpenId(String telNum, String openId);

    /**
     * 根据授权手机号或openId查看客户池是否存在该用户
     *
     * @param telNum 授权手机号
     * @param openId 微信标识ID
     * @return {@link OpUser}
     */
    OpUser getOneByTelNumOrOpenId(String telNum, String openId);

    IPage<SaleListVO> getUserSale(Page page, String activityIdNow, OpUser opUser);

    List<String> getRoleName();

    boolean canBeDelete(List<Integer> idList);

    JSONResult saveImportData(List<OpUser> opUserList, OpActivity activity);

    JSONResult saveImportDataConsultant(List<ConsultantVO> consultantVOSList, OpActivity activity);

	List<HashMap<String, String>> getOpUserByProjectIdAndTelOrOpenId(String projectId, String tel, String openId);

	List<OpUser> getOpUserListByProjectIdAndTel(String tel, String projectId);

	List<HashMap<String, String>> getOpUserByActivityIds(List activityIds);

	List<OpUser> getOpUserListByActivityIdAndOpenId(String activityId, String openId, String tel);

	List<HashMap<String, String>> getActivityRunOrCloseList(String projectId);

    IPage<OpUser> getUserList(Page page, OpUser opUser);

    List<String> getBookingNum(String activityId);

    List<String> getSelecCount(String activityId);

    List<OpUser> getSaleName(String activityId);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:11 2022/7/4
     * @param activityId
     * @param tel 
     * @description // TODO 退出登录
     **/
    void logout(String activityId, String tel);
    void updateUserLoginDateByTel(String activityId, String tel, LocalDateTime lastLoginDate);
    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:35 2022/7/19
     * @param userId    用户id 必传
     * @return java.util.List<com.tahoecn.opening.model.dto.UserHouseResourceDTO>
     * @description // TODO 根据用户id查询用户白名单房源列表
     **/
    List<UserHouseResourceDTO> getUserHouseResourceListByUserId(Long userId);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:03 2022/7/19
     * @param paramVO   参数VO
     * @return com.tahoecn.core.json.JSONResult
     * @description // TODO 新增修改用户房源白名单数据
     **/
    JSONResult saveUserHouseResource(UserHouseResourceParamVO paramVO);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 11:01 2022/8/12
     * @param cstguid
     * @param name
     * @description // TODO 将 数据库中 所有的sale_id是当前用户的saleName改值
     **/
    void updateSaleNameById(String cstguid, String name);
    
    List<OpUser> getList(QueryWrapper<OpUser> wrapper);
}
