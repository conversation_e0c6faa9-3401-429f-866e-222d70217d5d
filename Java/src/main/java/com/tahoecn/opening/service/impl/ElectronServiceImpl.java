package com.tahoecn.opening.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tahoecn.opening.common.utils.DateUtils;
import com.tahoecn.opening.common.utils.HttpClientUtil;
import com.tahoecn.opening.mapper.OpOrderMapper;
import com.tahoecn.opening.model.OpHousingResources;
import com.tahoecn.opening.model.OpOrder;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.model.electronVo.*;
import com.tahoecn.opening.service.ElectronService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: Zhengwenchao  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.service.impl
 * @Description:// TODO 电子签接口实现类
 * @Date: 2025/1/8 10:26
 * @Version: 1.0
 */
@Service
public class ElectronServiceImpl implements ElectronService {

    private static final Logger log= LoggerFactory.getLogger(ElectronServiceImpl.class);

    @Value("${electron.sign.basePath}")
    private String basePath;

    @Value("${electron.sign.appId}")
    private String appId;

    @Value("${electron.sign.appSecret}")
    private String appSecret;

    @Value("${electron.sign.orgId}")
    private String orgId;

    @Value("${electron.sign.password}")
    private String password;

    @Value("${electron.sign.version}")
    private String version;

    @Value("${electron.sign.createLoginName}")
    private String createLoginName;

    @Value("${electron.sign.notifyUrl}")
    private String notifyUrl;

    @Value("${electron.sign.statusNotifyUrl}")
    private String statusNotifyUrl;

    @Value("${electron.sign.contractDataNotifyUrl}")
    private String contractDataNotifyUrl;

    @Value("${electron.sign.apiOauthToken}")
    private String apiOauthToken;

    @Value("${electron.sign.queryOrgOrUserInfo}")
    private String queryOrgOrUserInfo;

    @Value("${electron.sign.syncInnerUser}")
    private String syncInnerUser;

    @Value("${electron.sign.getContractVariableInfoList}")
    private String getContractVariableInfoList;

    @Value("${electron.sign.createContract}")
    private String createContract;

    @Value("${electron.sign.getContracteBase64}")
    private String getContracteBase64;

    @Resource
    private OpOrderMapper opOrderMapper;


    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/8 10:32
     * @Return       java.lang.String
     * @Description  TODO   获取电子签token
     **/
    @Override
    public String getElectronToken() {
        try {
            // 声明入参
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(3);
            // 认证类型、授权账号、授权密码
            paramMap.put("grant_type", "1");
            paramMap.put("account", appId);
            paramMap.put("password", appSecret);
            log.info("电子签获取token入参为：{}", JSONObject.toJSONString(paramMap));
            String resultStr = HttpClientUtil.HttpPostJson(basePath + apiOauthToken, paramMap);
            log.info("电子签获取token结果为：{}", resultStr);
            if (StringUtils.isNotBlank(resultStr)) {
                JSONObject resultObj = JSONObject.parseObject(resultStr);
                if (ObjectUtil.isNotNull(resultObj.get("code")) && "0".equals(resultObj.getString("code"))) {
                    // 获取data
                    if (ObjectUtil.isNotNull(resultObj.get("data"))) {
                        JSONObject dataObj = JSONObject.parseObject(resultObj.getString("data"));
                        return dataObj.getString("access_token");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("电子签获取token异常！异常信息为：{}", e);
        }
        return "";
    }

    @Override
    public String queryOrgOrUserInfo() {
        try {
            // 声明入参
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(3);
            // 认证类型、授权账号、授权密码
            paramMap.put("version", version);
//            paramMap.put("userType", "1");
//            paramMap.put("orgNumber", "91110115MA7NFP7D15");
            paramMap.put("userType", "2");
            paramMap.put("idNumber", "412722200001010001");
            log.info("电子签获取用户信息入参为：{}", JSONObject.toJSONString(paramMap));
            String resultStr = HttpClientUtil.HttpPostJson(basePath + queryOrgOrUserInfo, paramMap);
            log.info("电子签获取用户信息结果为：{}", resultStr);
//            if (StringUtils.isNotBlank(resultStr)) {
//                JSONObject resultObj = JSONObject.parseObject(resultStr);
//                if (ObjectUtil.isNotNull(resultObj.get("code")) && "0".equals(resultObj.getString("code"))) {
//                    // 获取data
//                    if (ObjectUtil.isNotNull(resultObj.get("data"))) {
//                        JSONObject dataObj = JSONObject.parseObject(resultObj.getString("data"));
//                        return dataObj.getString("access_token");
//                    }
//                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("电子签获取用户信息异常！异常信息为：{}", e);
        }
        return "";
    }

    @Override
    public String syncInnerUser() {
        try {
            // 声明入参
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(10);
            // 认证类型、授权账号、授权密码
            paramMap.put("version", version);
            paramMap.put("userId", "412722200001010003");
            paramMap.put("name", "武传芬");
            paramMap.put("username", "412722200001010003");
            paramMap.put("password", password);
            paramMap.put("orgId", orgId);
            paramMap.put("initType", "4");
            paramMap.put("cellphone", "18501252001");
            paramMap.put("idnumber", "412722200001010003");
            paramMap.put("authentication", "1");
//            paramMap.put("version", version);
//            paramMap.put("userId", "412722200001010001");
//            paramMap.put("name", "张博");
//            paramMap.put("username", "412722200001010001");
//            paramMap.put("password", password);
//            paramMap.put("orgId", orgId);
//            paramMap.put("initType", "4");
//            paramMap.put("cellphone", "18739602964");
//            paramMap.put("idnumber", "412722200001010001");
//            paramMap.put("authentication", "0");
            log.info("电子签内部用户同步入参为：{}", JSONObject.toJSONString(paramMap));
            String resultStr = HttpClientUtil.HttpPostJson(basePath + syncInnerUser, paramMap);
            log.info("电子签内部用户同步结果为：{}", resultStr);
//            if (StringUtils.isNotBlank(resultStr)) {
//                JSONObject resultObj = JSONObject.parseObject(resultStr);
//                if (ObjectUtil.isNotNull(resultObj.get("code")) && "0".equals(resultObj.getString("code"))) {
//                    // 获取data
//                    if (ObjectUtil.isNotNull(resultObj.get("data"))) {
//                        JSONObject dataObj = JSONObject.parseObject(resultObj.getString("data"));
//                        return dataObj.getString("access_token");
//                    }
//                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("电子签内部用户同步异常！异常信息为：{}", e);
        }
        return "";
    }

    @Override
    public String getContractVariableInfoList() {
        try {
            // 声明入参
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(3);
            // 认证类型、授权账号、授权密码
            paramMap.put("version", version);
            paramMap.put("loginName", "18238967745");
            paramMap.put("templateId", "461851204292608b6b364a75e0e1a803");
            log.info("电子签查合同模板输入项入参为：{}", JSONObject.toJSONString(paramMap));
            String resultStr = HttpClientUtil.HttpPostJson(basePath + getContractVariableInfoList, paramMap);
            log.info("电子签查合同模板输入项结果为：{}", resultStr);
//            if (StringUtils.isNotBlank(resultStr)) {
//                JSONObject resultObj = JSONObject.parseObject(resultStr);
//                if (ObjectUtil.isNotNull(resultObj.get("code")) && "0".equals(resultObj.getString("code"))) {
//                    // 获取data
//                    if (ObjectUtil.isNotNull(resultObj.get("data"))) {
//                        JSONObject dataObj = JSONObject.parseObject(resultObj.getString("data"));
//                        return dataObj.getString("access_token");
//                    }
//                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("电子签查合同模板输入项异常！异常信息为：{}", e);
        }
        return "";
    }

    @Override
    public String createContract() {
        try {
            // 声明入参
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(20);
            // 认证类型、授权账号、授权密码
            paramMap.put("version", version);
            paramMap.put("serviceId", "1");
            paramMap.put("formSource", "3");
            paramMap.put("loginName", "18238967745");
            paramMap.put("code", "testPushContract008");
            paramMap.put("ctCode", "testPushContract008");
            paramMap.put("ctType", "测试合同编辑");
            paramMap.put("deadline", "2025-01-20 00:00:00");
            paramMap.put("name", "测推送合同008");
            paramMap.put("contractName", "测推送合同008");
            paramMap.put("templateId", "7e8a1e5cebe43ebf77457630a8f7269e");
            paramMap.put("waitSite", "0");
            // 合同签约人列表
            List<Map<String, String>> subscriberList = Lists.newArrayListWithCapacity(2);
            Map<String, String> firstSubscriberMap = Maps.newHashMapWithExpectedSize(5);
            firstSubscriberMap.put("keyword", "单位签章|0|10|20");
            firstSubscriberMap.put("orgNumber", "91110115MA7NFP7D15");
            firstSubscriberMap.put("subType", "1");
            firstSubscriberMap.put("autoSign", "0");
            firstSubscriberMap.put("signsort", "2");
            subscriberList.add(firstSubscriberMap);
            Map<String, String> secondSubscriberMap = Maps.newHashMapWithExpectedSize(5);
            secondSubscriberMap.put("keyword", "乙方签章|0|0|0");
            secondSubscriberMap.put("idNumber", "412722200001010003");
            secondSubscriberMap.put("subType", "2");
            secondSubscriberMap.put("signsort", "1");
            subscriberList.add(secondSubscriberMap);
            paramMap.put("subscriberList", subscriberList);
            // 合同签约人列表
            List<Map<String, String>> contractVariableList = Lists.newArrayListWithCapacity(2);
            Map<String, String> firstContractVariableListMap = Maps.newHashMapWithExpectedSize(3);
//            firstContractVariableListMap.put("keyword", "htwjmc");
//            firstContractVariableListMap.put("name", "合同文件名称");
            firstContractVariableListMap.put("id", "79f71ab8eb7b8220ce064159ab0e454d");
            firstContractVariableListMap.put("value", "测推送合同008合同名称");
            contractVariableList.add(firstContractVariableListMap);
            Map<String, String> secondContractVariableListMap = Maps.newHashMapWithExpectedSize(3);
//            firstContractVariableListMap.put("keyword", "htwjmc");
//            firstContractVariableListMap.put("name", "合同文件名称");
            secondContractVariableListMap.put("id", "b4cf11013815af0a419c235a34a36804");
            secondContractVariableListMap.put("value", "测推送合同008合同详情");
            contractVariableList.add(secondContractVariableListMap);
            paramMap.put("contractVariableList", contractVariableList);
            log.info("电子签创建合同入参为：{}", JSONObject.toJSONString(paramMap));
            String resultStr = HttpClientUtil.HttpPostJson(basePath + createContract, paramMap);
            log.info("电子签创建合同结果为：{}", resultStr);
//            if (StringUtils.isNotBlank(resultStr)) {
//                JSONObject resultObj = JSONObject.parseObject(resultStr);
//                if (ObjectUtil.isNotNull(resultObj.get("code")) && "0".equals(resultObj.getString("code"))) {
//                    // 获取data
//                    if (ObjectUtil.isNotNull(resultObj.get("data"))) {
//                        JSONObject dataObj = JSONObject.parseObject(resultObj.getString("data"));
//                        return dataObj.getString("access_token");
//                    }
//                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("电子签创建合同异常！异常信息为：{}", e);
        }
        return "";
    }

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/14 16:09
     * @Param        userName       用户名
     * @Param        idCard         身份证号
     * @Param        mobile         手机号
     * @Return       java.util.Map<java.lang.String,java.lang.String>
     * @Description  TODO   电子签同步用户
     **/
    @Override
    public Map<String, String> syncInnerUser(String userName, String idCard, String mobile) {
        // 定义返回结果：syncType调用结果：1--操作成功;2--操作异常;3--电子签返回结果不是成功
        Map<String, String> resultMap = Maps.newHashMapWithExpectedSize(2);
        resultMap.put("syncType", "2");
        resultMap.put("syncResult", "");
        try {
            // 声明入参
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(10);
            paramMap.put("version", version);
            paramMap.put("userId", idCard);
            paramMap.put("name", userName);
            paramMap.put("username", idCard);
            paramMap.put("password", password);
            paramMap.put("orgId", orgId);
            paramMap.put("initType", "4");
            paramMap.put("cellphone", mobile);
            paramMap.put("idnumber", idCard);
            paramMap.put("authentication", "1");
            log.info("电子签内部用户同步入参为：{}", JSONObject.toJSONString(paramMap));
            String resultStr = HttpClientUtil.HttpPostJson(basePath + syncInnerUser, paramMap);
            log.info("电子签内部用户同步结果为：{}", resultStr);
            if (StringUtils.isNotBlank(resultStr)) {
                JSONObject resultObj = JSONObject.parseObject(resultStr);
                if (ObjectUtil.isNotNull(resultObj.get("code")) && "0".equals(resultObj.getString("code"))) {
                    // 操作成功
                    resultMap.put("syncType", "1");
                } else {
                    resultMap.put("syncType", "3");
                    resultMap.put("syncResult", resultStr);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("syncResult", e.getMessage());
            log.error("电子签内部用户同步异常！异常信息为：{}", e);
        }
        return resultMap;
    }

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/15 10:52
     * @Param        electronCode   电子签合同编码
     * @Param        electronName   电子签合同名称
     * @Param        idCard         签约自然人身份证号
     * @Param        variableMap    变量map
     * @Return       java.util.Map<java.lang.String,java.lang.String>
     * @Description  TODO   电子签创建合同
     **/
    @Override
    public Map<String, String> createContract(String electronCode, String electronName, String idCard, Map<String, String> variableMap) {
        // 定义返回结果：syncType调用结果：1--操作成功;2--操作异常;3--电子签返回结果不是成功
        Map<String, String> resultMap = Maps.newHashMapWithExpectedSize(2);
        resultMap.put("syncType", "2");
        resultMap.put("syncResult", "");
        resultMap.put("electronId", "");
        resultMap.put("electronUrl", "");
        try {
            // 声明入参
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(20);
            paramMap.put("version", version);
            paramMap.put("serviceId", "1");
            paramMap.put("formSource", "3");
            paramMap.put("loginName", createLoginName);
            paramMap.put("code", electronCode);
            paramMap.put("ctCode", electronCode);
            paramMap.put("name", electronName);
            paramMap.put("contractName", electronName);
            paramMap.put("templateId", "461851204292608b6b364a75e0e1a803");
            paramMap.put("waitSite", "0");
            paramMap.put("notifyUrl", "");
            paramMap.put("statusNotifyUrl", statusNotifyUrl);
            paramMap.put("contractDataNotifyUrl", "");
            // 合同签约人列表
            List<Map<String, String>> subscriberList = Lists.newArrayListWithCapacity(2);
//            Map<String, String> firstSubscriberMap = Maps.newHashMapWithExpectedSize(5);
//            firstSubscriberMap.put("keyword", "单位签章|0|10|20");
//            firstSubscriberMap.put("orgNumber", "91110115MA7NFP7D15");
//            firstSubscriberMap.put("subType", "1");
//            firstSubscriberMap.put("autoSign", "0");
//            firstSubscriberMap.put("signsort", "2");
//            subscriberList.add(firstSubscriberMap);
            Map<String, String> secondSubscriberMap = Maps.newHashMapWithExpectedSize(5);
            secondSubscriberMap.put("keyword", "认租人|1|20|0");
            secondSubscriberMap.put("idNumber", idCard);
            secondSubscriberMap.put("subType", "2");
            secondSubscriberMap.put("signsort", "1");
            subscriberList.add(secondSubscriberMap);
            paramMap.put("subscriberList", subscriberList);
            // 合同签约人列表
            List<Map<String, String>> contractVariableList = Lists.newArrayListWithCapacity(20);
            Map<String, String> variableMap_xm = Maps.newHashMapWithExpectedSize(3);
            variableMap_xm.put("keyword", "xm");
            variableMap_xm.put("name", "姓名");
            variableMap_xm.put("value", variableMap.get("userName"));
            contractVariableList.add(variableMap_xm);
            Map<String, String> variableMap_sfzh = Maps.newHashMapWithExpectedSize(3);
            variableMap_sfzh.put("keyword", "sfzh");
            variableMap_sfzh.put("name", "身份证号");
            variableMap_sfzh.put("value", variableMap.get("idCard"));
            contractVariableList.add(variableMap_sfzh);
            Map<String, String> variableMap_lh = Maps.newHashMapWithExpectedSize(3);
            variableMap_lh.put("keyword", "lh");
            variableMap_lh.put("name", "租赁楼号");
            variableMap_lh.put("value", variableMap.get("buildingName"));
            contractVariableList.add(variableMap_lh);
            Map<String, String> variableMap_dy = Maps.newHashMapWithExpectedSize(3);
            variableMap_dy.put("keyword", "dy");
            variableMap_dy.put("name", "租赁单元和");
            variableMap_dy.put("value", variableMap.get("unitName"));
            contractVariableList.add(variableMap_dy);
            Map<String, String> variableMap_fjs = Maps.newHashMapWithExpectedSize(3);
            variableMap_fjs.put("keyword", "fjs");
            variableMap_fjs.put("name", "储藏间");
            variableMap_fjs.put("value", variableMap.get("roomNum"));
            contractVariableList.add(variableMap_fjs);
            Map<String, String> variableMap_wzbh = Maps.newHashMapWithExpectedSize(3);
            variableMap_wzbh.put("keyword", "wzbh");
            variableMap_wzbh.put("name", "位置与编号");
            variableMap_wzbh.put("value", variableMap.get("houseName"));
            contractVariableList.add(variableMap_wzbh);
            Map<String, String> variableMap_ccjmj = Maps.newHashMapWithExpectedSize(3);
            variableMap_ccjmj.put("keyword", "ccjmj");
            variableMap_ccjmj.put("name", "储藏间面积");
            variableMap_ccjmj.put("value", variableMap.get("houseArea"));
            contractVariableList.add(variableMap_ccjmj);
            Map<String, String> variableMap_rzjg = Maps.newHashMapWithExpectedSize(3);
            variableMap_rzjg.put("keyword", "rzjg");
            variableMap_rzjg.put("name", "认租价格");
            variableMap_rzjg.put("value", variableMap.get("totalPrice"));
            contractVariableList.add(variableMap_rzjg);
            Map<String, String> variableMap_yjzj = Maps.newHashMapWithExpectedSize(3);
            variableMap_yjzj.put("keyword", "yjzj");
            variableMap_yjzj.put("name", "已交租金");
            variableMap_yjzj.put("value", variableMap.get("alreadyPrice"));
            contractVariableList.add(variableMap_yjzj);
            Map<String, String> variableMap_qt = Maps.newHashMapWithExpectedSize(3);
            variableMap_qt.put("keyword", "qt");
            variableMap_qt.put("name", "其他");
            variableMap_qt.put("value", variableMap.get("other"));
            contractVariableList.add(variableMap_qt);
            Map<String, String> variableMap_nian = Maps.newHashMapWithExpectedSize(3);
            variableMap_nian.put("keyword", "nian");
            variableMap_nian.put("name", "日期年");
            variableMap_nian.put("value", variableMap.get("dateYear"));
            contractVariableList.add(variableMap_nian);
            Map<String, String> variableMap_yue = Maps.newHashMapWithExpectedSize(3);
            variableMap_yue.put("keyword", "yue");
            variableMap_yue.put("name", "日期月");
            variableMap_yue.put("value", variableMap.get("dateMonth"));
            contractVariableList.add(variableMap_yue);
            Map<String, String> variableMap_ri = Maps.newHashMapWithExpectedSize(3);
            variableMap_ri.put("keyword", "ri");
            variableMap_ri.put("name", "日期日");
            variableMap_ri.put("value", variableMap.get("dateDay"));
            contractVariableList.add(variableMap_ri);
            Map<String, String> variableMap_jcnina = Maps.newHashMapWithExpectedSize(3);
            variableMap_jcnina.put("keyword", "jcnina");
            variableMap_jcnina.put("name", "检查年");
            variableMap_jcnina.put("value", variableMap.get("checkDateYear"));
            contractVariableList.add(variableMap_jcnina);
            Map<String, String> variableMap_jcyue = Maps.newHashMapWithExpectedSize(3);
            variableMap_jcyue.put("keyword", "jcyue");
            variableMap_jcyue.put("name", "检查月");
            variableMap_jcyue.put("value", variableMap.get("checkDateMonth"));
            contractVariableList.add(variableMap_jcyue);
            Map<String, String> variableMap_jcri = Maps.newHashMapWithExpectedSize(3);
            variableMap_jcri.put("keyword", "jcri");
            variableMap_jcri.put("name", "检查日");
            variableMap_jcri.put("value", variableMap.get("checkDateDay"));
            contractVariableList.add(variableMap_jcri);

            paramMap.put("contractVariableList", contractVariableList);
            log.info("电子签创建合同入参为：{}", JSONObject.toJSONString(paramMap));
            String resultStr = HttpClientUtil.HttpPostJson(basePath + createContract, paramMap);
            log.info("电子签创建合同结果为：{}", resultStr);
            if (StringUtils.isNotBlank(resultStr)) {
                JSONObject resultObj = JSONObject.parseObject(resultStr);
                if (ObjectUtil.isNotNull(resultObj.get("code")) && "0".equals(resultObj.getString("code"))) {
                    // 操作成功
                    resultMap.put("syncType", "1");
                    // 获取data
                    if (ObjectUtil.isNotNull(resultObj.get("data"))) {
                        JSONObject dataObj = JSONObject.parseObject(resultObj.getString("data"));
                        // 电子签id
                        resultMap.put("electronId", dataObj.getString("id"));
                        // 获取自然人签约链接
                        List<JSONObject> subList = JSONArray.parseArray(dataObj.getString("subscriberList"), JSONObject.class);
                        if (CollectionUtils.isNotEmpty(subList)) {
                            for (JSONObject jsonObject : subList) {
                                if (StringUtils.isNotBlank(jsonObject.getString("idNumber")) && idCard.equalsIgnoreCase(jsonObject.getString("idNumber"))) {
                                    resultMap.put("electronUrl", jsonObject.getString("url"));
                                    break;
                                }
                            }
                        }
                    }
                } else {
                    resultMap.put("syncType", "3");
                    resultMap.put("syncResult", resultStr);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("syncResult", e.getMessage());
            log.error("电子签创建合同异常！异常信息为：{}", e);
        }
        return resultMap;
    }

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/15 10:52
     * @Param        electronCode   电子签合同编码
     * @Param        electronName   电子签合同名称
     * @Param        idCard         签约自然人身份证号
     * @Param        templateId     模板id
     * @Param        paramVoList    变量集合
     * @Return       java.util.Map<java.lang.String,java.lang.String>
     * @Description  TODO   电子签创建合同
     **/
    @Override
    public Map<String, String> createContract(String electronCode, String electronName, String idCard, String templateId, List<ContractParamVo> paramVoList) {
        // 定义返回结果：syncType调用结果：1--操作成功;2--操作异常;3--电子签返回结果不是成功
        Map<String, String> resultMap = Maps.newHashMapWithExpectedSize(2);
        resultMap.put("syncType", "2");
        resultMap.put("syncResult", "");
        resultMap.put("electronId", "");
        resultMap.put("electronUrl", "");
        try {
            // 声明入参
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(20);
            paramMap.put("version", version);
            paramMap.put("serviceId", "1");
            paramMap.put("formSource", "3");
            paramMap.put("loginName", createLoginName);
            paramMap.put("code", electronCode);
            paramMap.put("ctCode", electronCode);
            paramMap.put("name", electronName);
            paramMap.put("contractName", electronName);
            paramMap.put("templateId", templateId);
            paramMap.put("waitSite", "0");
            paramMap.put("notifyUrl", "");
            paramMap.put("statusNotifyUrl", statusNotifyUrl);
            paramMap.put("contractDataNotifyUrl", "");
            // 合同签约人列表
            List<Map<String, String>> subscriberList = Lists.newArrayListWithCapacity(2);
            Map<String, String> secondSubscriberMap = Maps.newHashMapWithExpectedSize(5);
            secondSubscriberMap.put("keyword", "@签名");
            secondSubscriberMap.put("idNumber", idCard);
            secondSubscriberMap.put("subType", "2");
            secondSubscriberMap.put("signsort", "1");
            subscriberList.add(secondSubscriberMap);
            paramMap.put("subscriberList", subscriberList);
            // 变量集合
            paramMap.put("contractVariableList", paramVoList);
            log.info("电子签创建合同入参为：{}", JSONObject.toJSONString(paramMap));
            String resultStr = HttpClientUtil.HttpPostJson(basePath + createContract, paramMap);
            log.info("电子签创建合同结果为：{}", resultStr);
            if (StringUtils.isNotBlank(resultStr)) {
                JSONObject resultObj = JSONObject.parseObject(resultStr);
                if (ObjectUtil.isNotNull(resultObj.get("code")) && "0".equals(resultObj.getString("code"))) {
                    // 操作成功
                    resultMap.put("syncType", "1");
                    // 获取data
                    if (ObjectUtil.isNotNull(resultObj.get("data"))) {
                        JSONObject dataObj = JSONObject.parseObject(resultObj.getString("data"));
                        // 电子签id
                        resultMap.put("electronId", dataObj.getString("id"));
                        // 获取自然人签约链接
                        List<JSONObject> subList = JSONArray.parseArray(dataObj.getString("subscriberList"), JSONObject.class);
                        if (CollectionUtils.isNotEmpty(subList)) {
                            for (JSONObject jsonObject : subList) {
                                if (StringUtils.isNotBlank(jsonObject.getString("idNumber")) && idCard.equalsIgnoreCase(jsonObject.getString("idNumber"))) {
                                    resultMap.put("electronUrl", jsonObject.getString("url"));
                                    break;
                                }
                            }
                        }
                    }
                } else {
                    resultMap.put("syncType", "3");
                    resultMap.put("syncResult", resultStr);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("syncResult", e.getMessage());
            log.error("电子签创建合同异常！异常信息为：{}", e);
        }
        return resultMap;
    }

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 9:42
     * @Param        paramVo    入参对象Vo
     * @Return       com.tahoecn.opening.model.electronVo.CallBackResult
     * @Description  TODO   签约人签章结果回调
     **/
    @Override
    public CallBackResult callBackSignResult(SignResultParamVo paramVo) {
        try {
            log.info("电子签签约人签章结果回调入参为：{}", JSONObject.toJSONString(paramVo));
            return CallBackResult.success("回调成功！");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("电子签签约人签章结果回调异常！合同id为：{}, 异常信息为：{}", paramVo.getId(), e);
            return CallBackResult.error("回调异常！");
        }
    }

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 9:42
     * @Param        paramVo    入参对象Vo
     * @Return       com.tahoecn.opening.model.electronVo.CallBackResult
     * @Description  TODO   合同状态变更回调
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CallBackResult callBackContractStatus(ContractStatusParamVo paramVo) {
        try {
            log.info("电子签合同状态变更回调入参为：{}", JSONObject.toJSONString(paramVo));
            OpOrder opOrder = getOpOrder(paramVo.getId());
            if ("3".equals(paramVo.getStatus())) {
                opOrder.setOrderStatus(2);
            } else if ("5".equals(paramVo.getStatus())
                    || "6".equals(paramVo.getStatus())
                    || "7".equals(paramVo.getStatus())
                    || "8".equals(paramVo.getStatus())) {
                opOrder.setOrderStatus(3);
            }
            opOrderMapper.updateById(opOrder);
            return CallBackResult.success("回调成功！");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("电子签合同状态变更回调异常！合同id为：{}, 异常信息为：{}", paramVo.getId(), e);
            return CallBackResult.error("回调异常！");
        }
    }

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 9:58
     * @Param        electronId    电子签id
     * @Return       com.tahoecn.opening.model.OpOrder
     * @Description  TODO   获取电子签订单
     **/
    private OpOrder getOpOrder(String electronId) {
        OpOrder opOrder = opOrderMapper.selectOne(new QueryWrapper<OpOrder>().lambda().eq(OpOrder::getElectronId, electronId));
        if (ObjectUtil.isNull(opOrder)) {
            throw new RuntimeException("根据electronId获取订单失败！");
        }
        return opOrder;
    }

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 9:42
     * @Param        paramVo    入参对象Vo
     * @Return       com.tahoecn.opening.model.electronVo.CallBackResult
     * @Description  TODO   合同文件数据回调
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CallBackResult callBackContractFile(ContractFileParamVo paramVo) {
        try {
            log.info("电子签合同文件数据回调入参为：{}", JSONObject.toJSONString(paramVo));
            return CallBackResult.success("回调成功！");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("电子签合同文件数据回调异常！合同id为：{}, 异常信息为：{}", paramVo.getId(), e);
            return CallBackResult.error("回调异常！");
        }
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/23 15:35
     * @Param        opUser             用户对象
     * @Param        housingResources   房源对象
     * @Return       java.util.List<com.tahoecn.opening.model.electronVo.ContractParamVo>
     * @Description  TODO 获取生成合同变量对象集合
     **/
    @Override
    public List<ContractParamVo> getContractParamVoList(OpUser opUser, OpHousingResources housingResources) {
        List<ContractParamVo> contractParamVoList = Lists.newArrayListWithCapacity(20);
        contractParamVoList.add(new ContractParamVo("userName", "姓名", ObjectUtil.isNotNull(opUser.getName()) ? opUser.getName() : ""));
        contractParamVoList.add(new ContractParamVo("userMobile", "手机号", ObjectUtil.isNotNull(opUser.getTel()) ? opUser.getTel() : ""));
        contractParamVoList.add(new ContractParamVo("userIdCard", "身份证号", ObjectUtil.isNotNull(opUser.getIdCard()) ? opUser.getIdCard() : ""));
        contractParamVoList.add(new ContractParamVo("houseStr", "房源详情", ObjectUtil.isNotNull(opUser.getHouseStr()) ? opUser.getHouseStr() : ""));
        contractParamVoList.add(new ContractParamVo("buildingName", "楼栋", ObjectUtil.isNotNull(housingResources.getBuildingName()) ? housingResources.getBuildingName() : ""));
        contractParamVoList.add(new ContractParamVo("unitName", "单元", ObjectUtil.isNotNull(housingResources.getUnitName()) ? housingResources.getUnitName() : ""));
        contractParamVoList.add(new ContractParamVo("roomNum", "房源号", ObjectUtil.isNotNull(housingResources.getRoomNum()) ? housingResources.getRoomNum() : ""));
        contractParamVoList.add(new ContractParamVo("houseFullName", "房源全名", ObjectUtil.isNotNull(housingResources.getBuildingName()+housingResources.getUnitName()+housingResources.getRoomNum()) ? housingResources.getBuildingName()+housingResources.getUnitName()+housingResources.getRoomNum() : ""));
        contractParamVoList.add(new ContractParamVo("houseArea", "面积", ObjectUtil.isNotNull(housingResources.getHouserArea()) ? housingResources.getHouserArea().toString() : ""));
        contractParamVoList.add(new ContractParamVo("totalPrice", "总价", ObjectUtil.isNotNull(housingResources.getTotalPrice()) ? housingResources.getTotalPrice().toString() : ""));
        contractParamVoList.add(new ContractParamVo("unitPrice", "单价", ObjectUtil.isNotNull(housingResources.getUnitPrice()) ? housingResources.getUnitPrice().toString() : ""));
        contractParamVoList.add(new ContractParamVo("discountTotalPrice", "优惠后总价", ObjectUtil.isNotNull(housingResources.getDiscountTotalPrice()) ? housingResources.getDiscountTotalPrice().toString() : ""));
        contractParamVoList.add(new ContractParamVo("discountUnitPrice", "优惠后单价", ObjectUtil.isNotNull(housingResources.getDiscountUnitPrice()) ? housingResources.getDiscountUnitPrice().toString() : ""));
        contractParamVoList.add(new ContractParamVo("houseType", "户型", ObjectUtil.isNotNull(housingResources.getHourseType()) ? housingResources.getHourseType() : ""));
        // 获取当前时间
        String dateString = DateUtils.getDateString(new Date(), "yyyy-MM-dd");
        String[] split = dateString.split("-");
        contractParamVoList.add(new ContractParamVo("currentYear", "当前年", split[0]));
        contractParamVoList.add(new ContractParamVo("currentMonth", "当前月", split[1]));
        contractParamVoList.add(new ContractParamVo("currentDay", "当前日", split[2]));
        contractParamVoList.add(new ContractParamVo("currentDate", "当前年月日", dateString));
        return contractParamVoList;
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/15 17:12
     * @Param        contractId         合同id
     * @Return       java.lang.String
     * @Description  TODO 获取电子签附件
     **/
    @Override
    public String getElectronFile(String contractId) {
        try {
            // 声明入参
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(2);
            paramMap.put("contractId", contractId);
            paramMap.put("category", "1");
            log.info("电子签获取附件入参为：{}", JSONObject.toJSONString(paramMap));
            String resultStr = HttpClientUtil.HttpPostJson(basePath + createContract, paramMap);
            log.info("电子签获取附件结果为：{}", resultStr);
            return resultStr;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("电子签获取附件异常！异常信息为：{}", e);
        }
        return null;
    }
}
