package com.tahoecn.opening.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tahoecn.opening.model.OpProjectSale;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 销售系统项目 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
public interface IOpProjectSaleService extends IService<OpProjectSale> {


    Page<OpProjectSale> projectList(String areaId, String cityId, String projectId, Page page);
    boolean saveImportData(List<OpProjectSale> opProjectSaleList);
}
