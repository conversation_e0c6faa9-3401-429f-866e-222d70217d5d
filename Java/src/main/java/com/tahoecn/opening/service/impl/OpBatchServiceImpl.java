package com.tahoecn.opening.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.tahoecn.opening.common.utils.ThreadLocalUtils;
import com.tahoecn.opening.converter.ResponseMessage;
import com.tahoecn.opening.model.OpBatch;
import com.tahoecn.opening.mapper.OpBatchMapper;
import com.tahoecn.opening.model.vo.BatchParamVo;
import com.tahoecn.opening.service.IOpBatchService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 活动 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@Service
public class OpBatchServiceImpl extends ServiceImpl<OpBatchMapper, OpBatch> implements IOpBatchService {

    @Resource
    private OpBatchMapper opBatchMapper;

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/25 15:30
     * @Param        paramVo
     * @Return       com.baomidou.mybatisplus.core.metadata.IPage<com.tahoecn.opening.model.OpBatch>
     * @Description  TODO 条查批次列表
     **/
    @Override
    public IPage<OpBatch> selectBatchListByCondition(BatchParamVo paramVo) {
        Page page = new Page(paramVo.getPageNum(), paramVo.getPageSize());
        QueryWrapper<OpBatch> wrapper = new QueryWrapper<>();
        wrapper.eq("activity_id", paramVo.getActivityId());
        if (StringUtils.isNotBlank(paramVo.getBatchName())) {
            wrapper.like("batch_name", paramVo.getBatchName());
        }
        wrapper.eq("yn", "y");
        wrapper.orderByAsc("priority_level");
        IPage iPage = opBatchMapper.selectPage(page, wrapper);
        return iPage;
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/25 15:50
     * @Param        opBatch
     * @Return       com.tahoecn.opening.converter.ResponseMessage
     * @Description  TODO 新增修改批次
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseMessage saveOrUpdateBatch(OpBatch opBatch) {
        // 获取该活动所有批次数据
        List<OpBatch> batchList = selectAllBatchListByActivityId(opBatch.getActivityId());
        // 批次重名校验
        if (CollectionUtils.isNotEmpty(batchList)) {
            for (OpBatch batch : batchList) {
                if (opBatch.getBatchName().equals(batch.getBatchName())) {
                    if (null == opBatch.getId() || opBatch.getId().longValue() != batch.getId().longValue()) {
                        return ResponseMessage.error("该名称已存在！");
                    }
                }
                if (opBatch.getPriorityLevel().intValue() == batch.getPriorityLevel().intValue()) {
                    if (null == opBatch.getId() || opBatch.getId().longValue() != batch.getId().longValue()) {
                        return ResponseMessage.error("该优先级已存在！");
                    }
                }
            }
        }
        // 校验
        if (null == opBatch.getId()) {
            // 新增
            opBatch.setYn("y");
            opBatch.setCreatedBy(ThreadLocalUtils.getUserId());
            opBatch.setCreationDate(LocalDateTime.now());
            opBatchMapper.insert(opBatch);
        } else {
            opBatch.setLastUpdateBy(ThreadLocalUtils.getUserId());
            opBatch.setLastUpdateDate(LocalDateTime.now());
            opBatchMapper.updateById(opBatch);
        }
        return ResponseMessage.ok();
    }

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/25 15:52
     * @Param        activityId     活动id
     * @Return       java.util.List<com.tahoecn.opening.model.OpBatch>
     * @Description  TODO           获取该活动下所有批次数据
     **/
    @Override
    public List<OpBatch> selectAllBatchListByActivityId(String activityId) {
        if (StringUtils.isBlank(activityId)) {
            return Lists.newArrayListWithCapacity(1);
        }
        QueryWrapper<OpBatch> wrapper = new QueryWrapper<>();
        wrapper.eq("activity_id", activityId);
        wrapper.eq("yn", "y");
        wrapper.orderByAsc("priority_level");
        List<OpBatch> batchList = opBatchMapper.selectList(wrapper);
        return null != batchList ? batchList : Lists.newArrayListWithCapacity(1);
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/25 16:34
     * @Param        ids 数据id，多个用英文逗号分割
     * @Return       com.tahoecn.opening.converter.ResponseMessage
     * @Description  TODO 批量删除批次根据数据ids
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseMessage deleteBatchByIds(String ids) {
        // 转换
        QueryWrapper<OpBatch> wrapper = new QueryWrapper<>();
        wrapper.in("id", ids.split(","));
        OpBatch condition = new OpBatch();
        condition.setYn("n");
        condition.setLastUpdateBy(ThreadLocalUtils.getUserId());
        condition.setLastUpdateDate(LocalDateTime.now());
        opBatchMapper.update(condition, wrapper);
        return ResponseMessage.ok();
    }
}
