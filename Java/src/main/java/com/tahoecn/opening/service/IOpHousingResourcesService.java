package com.tahoecn.opening.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpHousingResources;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tahoecn.opening.model.dto.HouseResourceDTO;
import com.tahoecn.opening.model.dto.WhiteHouseResourceDTO;
import com.tahoecn.opening.model.vo.UserResourceImportVO;
import com.tahoecn.opening.model.vo.WhiteHouseResourceParamVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 楼栋房源 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-08
 */
public interface IOpHousingResourcesService extends IService<OpHousingResources> {

     List<HashMap<String,Object>> getBuildingListByActivityId(String activityId, Integer batchFlag, Integer formalFlag, List<String> batchNameList);

	List<HashMap<String, Object>> getUnitListByActivityIdAndBuildingName(String activityId, String string, Integer batchFlag, Integer formalFlag, List<String> batchNameList);

	List<HashMap<String, Object>> getRoomMsgByActivityIdAndBuildingAndUnit(String activityId, String buildingName,
                                                                           String unitName, String currentFloor, String isRegular, String userId);

	List<HashMap<String, Object>> getRoomMsgByActivityIdAndBuildingAndUnitMg(String activityId, String buildingName, String unitName, String currentFloor, String isRegular, String userId, Integer batchFlag, Integer formalFlag, List<String> batchNameList);

	List<HashMap<String, Object>> getFloorListByActivityIdAndBuildingAndUnit(String activityId, String buildingName,
                                                                             String unitName);

	String getHouseCountByActivityId(Integer activityId);

    boolean canBeDelete(List<String> idList);

	HashMap<String, Object> getActivityAnalysis(String activityId);


	JSONResult saveImportData(List<OpHousingResources> opHousingResourcesList, String activityIdNow);

	List<OpHousingResources> getOpHousingResourcesList(String activityId);

//	void testInsert(OpHousingResources opHousingResources);

	HashMap<String, Object> getOpHousingBaseData(String activityId, String regular);

	Integer saveOne(OpHousingResources opHousingResources);
	
	/**
	 * 大屏获取列表
	 * @param activityId
	 * @return
	 */
	List<Map<String, Object>> getScreenList(String activityId);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:35 2022/7/19
	 * @param activityId	活动id  必传
	 * @return java.util.List<com.tahoecn.opening.model.dto.HouseResourceDTO>
	 * @description // TODO 递归查询房源信息根据活动ID
	 **/
	List<HouseResourceDTO> selectHouseResourceListByActivityId(Long activityId);
	Integer getOptionalProperties(String activityId, String regular);
	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 14:35 2023/3/13
	 * @param paramVO 条查参数VO
	 * @return com.tahoecn.core.json.JSONResult
	 * @description // TODO 条查房源白名单列表
	 **/
	IPage<WhiteHouseResourceDTO> selectWhiteHouseResourceListByCondition(WhiteHouseResourceParamVO paramVO);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:53 2023/3/13
	 * @param activityId		活动ID
	 * @param saleControlFlag 	是否销控标识：y--是;n--否
	 * @param ids				房源ids，多个用英文逗号分隔
	 * @return com.tahoecn.core.json.JSONResult
	 * @description // TODO 更改房源销控状态
	 **/
	JSONResult updateSaleControlFlag(String activityId, String saleControlFlag, String ids);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 11:12 2023/9/15
	 * @param activityId
	 * @description // TODO 推送大屏
	 **/
	void sendWebSocket(String activityId);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 11:22 2023/11/7
	 * @param activityId 	活动id
	 * @description // TODO 推送websocket消息到MQ
	 **/
	void pushWebSocketMQ(String activityId);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 14:22 2023/11/7
	 * @param activityId 	活动id
	 * @description // TODO 推送websocket消息到连接处
	 **/
	void pushWebsocketData(String activityId);

	int getTodayLoginUserCountByActivityIdTwo(String activityId, String beginDate, String endDate);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:10 2023/10/23
	 * @param activity 		活动对象
	 * @return java.util.Map<java.lang.String,java.lang.Object>
	 * @description // TODO 获取推送大屏的map
	 **/
	Map<String, Object> getSocketMap(OpActivity activity);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:23 2023/10/26
	 * @param userResourceImportVOList	数据集合
	 * @param activityIdNow 			活动id
	 * @return com.tahoecn.core.json.JSONResult<java.lang.Object>
	 * @description // TODO 导入客户白名单
	 **/
	JSONResult<Object> saveUserResourceImportData(List<UserResourceImportVO> userResourceImportVOList, String activityIdNow);

	/**
	 *
	 * <AUTHOR> <EMAIL>
	 * @Date         2025/4/27 18:03
	 * @Param        activityId 活动id
	 * @Param        dataList   批次名称集合
	 * @Return       void
	 * @Description  TODO 修改活动模拟批次为公共
	 **/
	void updateBatchNameSimulation(String activityId, List<String> dataList);

	/**
	 *
	 * <AUTHOR> <EMAIL>
	 * @Date         2025/4/27 18:03
	 * @Param        activityId 活动id
	 * @Param        dataList   批次名称集合
	 * @Return       void
	 * @Description  TODO 修改活动正式批次为公共
	 **/
	void updateBatchNameFormal(String activityId, List<String> dataList);
}
