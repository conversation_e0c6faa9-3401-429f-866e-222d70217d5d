package com.tahoecn.opening.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.LocalDateTimeUtils;
import com.tahoecn.opening.model.OpHousingResources;
import com.tahoecn.opening.model.OpProjectSale;
import com.tahoecn.opening.mapper.OpProjectSaleMapper;
import com.tahoecn.opening.service.IOpProjectSaleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 销售系统项目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@Service
public class OpProjectSaleServiceImpl extends ServiceImpl<OpProjectSaleMapper, OpProjectSale> implements IOpProjectSaleService {

    @Autowired
    OpProjectSaleMapper projectSaleMapper;

    public Page<OpProjectSale> projectList(String areaId, String cityId, String projectId, Page page){
        return page.setRecords(projectSaleMapper.projectList(areaId,cityId,projectId,page));
    }

    /**
     * 项目Excel导入
     * @param opProjectSaleList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveImportData(List<OpProjectSale> opProjectSaleList) {
        boolean flag = true;
        List<OpProjectSale> opProjectSaleListImport = new ArrayList<>();
        for (OpProjectSale opProjectSale : opProjectSaleList) {
            OpProjectSale opProjectSaleImport = new OpProjectSale();
            opProjectSaleImport.setProjectId(opProjectSale.getProjectId());
            opProjectSaleImport.setProjectName(opProjectSale.getProjectName());
            opProjectSaleImport.setProjectAddress(opProjectSale.getProjectAddress());
            opProjectSaleImport.setStagesId(opProjectSale.getStagesId());
            opProjectSaleImport.setStagesName(opProjectSale.getStagesName());
            opProjectSaleImport.setAreaId(opProjectSale.getAreaId());
            opProjectSaleImport.setAreaName(opProjectSale.getAreaName());
            opProjectSaleImport.setCityId(opProjectSale.getCityId());
            opProjectSaleImport.setCityName(opProjectSale.getCityName());
            opProjectSaleImport.setLastUpdateDate(LocalDateTimeUtils.localDateTimeToDate(LocalDateTime.now()));
            opProjectSaleImport.setLastUpdateBy("ExcelImport");
            opProjectSaleImport.setYn(GlobalConstants.Y);
            opProjectSaleImport.setCreatedBy("ExcelImport");
            opProjectSaleImport.setCreationDate(LocalDateTimeUtils.localDateTimeToDate(LocalDateTime.now()));
            opProjectSaleListImport.add(opProjectSaleImport);
        }
        projectSaleMapper.delete(new QueryWrapper<OpProjectSale>());
        for (OpProjectSale opProjectSale : opProjectSaleListImport) {
            if (projectSaleMapper.insert(opProjectSale) != 1) {
                flag = false;
            }
        }
        return flag;
    }
}
