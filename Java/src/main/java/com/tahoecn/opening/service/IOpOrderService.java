package com.tahoecn.opening.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpHousingResources;
import com.tahoecn.opening.model.OpOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.model.vo.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
public interface IOpOrderService extends IService<OpOrder> {

    IPage<OrderInfoVO> getOrder(Page page, OpUser opUser, String activityIdNow, String buildingName, String unitName, String roomNum, String isRegular, boolean isAdmin);
    List<OrderInfoVO> getOrderEx(OpUser opUser, String activityIdNow, String buildingName, String unitName, String roomNum, String isRegular, boolean isAdmin);
    void initCanOrder(String activityId);
    void initCanOrderBySql(String activityId);
    JSONResult getCanOrder(OpOrder order);
    JSONResult getCanOrderByLua(OpOrder order);
    Set<String> getCanOrderTest(String activityId);
    void delActivityKeyById(String activityId);
    JSONResult getOrder(OpOrder order);
    String executeBackOrderLua(OpOrder order);
    void removeCanOrderRedis(String activityId);
    /**
     * redis中房源加减
     * @param activityId
     * @param houseId
     * @param type plus minus
     */
    void redisHouseModify(String activityId, String houseId, String type);
    void redisUserModify(String activityId, String userId, String type, Integer count);

	List<HashMap<String, Object>> getMyOrderByactivityIdAndUser(String activityId, String userId, String isRegular);
    IPage<HouseTypeList> getHouseTypeList(Page page, String activityIdNow, String isRegular);
    List<HouseTypeList> getHouseTypeListEx(String activityIdNow, String isRegular);
    SalesOverview getOverview(String activityIdNow, String isRegular);
	HashMap<String, Object> getUserFavoriteAndActivityFavorite(HashMap<String, Object> paramMap);

    IPage<SaleDataVO> getSaleData(Page page, String roleName, String activityIdNow, String isRegular);
    List<SaleDataVO> getSaleDataEx(String roleName, String activityIdNow, String isRegular);

    Map<String,String> canBuyOrNot(String userId, String houseId, String activityId);

    String deleteOrder(List<String> idList, String isRegular);

    JSONResult placeExcelOrder(List<OrderExcelImportVO> orderExcelImportVOList, String activityId);
	List<OpOrder> selectBatchIds(List<String> idList);

    void setBeforeOrderShow(String activityId);

    List<SalesOrderVO> getSalesOrder(String activityId);

    JSONResult  returnData(String followUpContent, OpUser opUser);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:12 2022/8/10
     * @param activityId    活动id
     * @param createType    生成类型：1--正式订单；2--模拟订单；3--全部订单
     * @return java.lang.Object
     * @description // TODO 根据活动id，初始化生成房源白名单下的 订单信息，模拟订单和正式订单均生成
     **/
    Object initHouseResourceWhiteOrder(String activityId, int createType);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:57 2023/10/23
     * @param activityId        活动id
     * @param formatFlag        是否正式活动
     * @description // TODO 更改订单数据并推送大屏
     **/
    void updateOrderData(String activityId, boolean formatFlag);

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/24 9:25
     * @Param        order      订单对象
     * @Param        activity   活动对象
     * @Param        opUser     用户
     * @Param        housingResources   房源
     * @Return       void
     * @Description  TODO   生成电子签
     **/
    void checkElectronData(OpOrder order, OpActivity activity, OpUser opUser, OpHousingResources housingResources);

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/6 16:33
     * @Param        activity 活动对象
     * @Return       void
     * @Description  TODO   刷新该活动下用户和定时任务数据
     **/
    void refreshActivityDataById(OpActivity activity);
}

