package com.tahoecn.opening.service.impl;

import com.tahoecn.opening.model.OpFavorite;
import com.tahoecn.opening.mapper.OpFavoriteMapper;
import com.tahoecn.opening.service.IOpFavoriteService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.HashMap;
import java.util.List;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 收藏 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@Service
public class OpFavoriteServiceImpl extends ServiceImpl<OpFavoriteMapper, OpFavorite> implements IOpFavoriteService {

	@Override
	public List<HashMap<String, Object>> getMyFavoriteByactivityIdAndUser(String activityId, String userId, String isRegular) {
		return baseMapper.getMyFavoriteByactivityIdAndUser(activityId,userId,isRegular);
	}

	@Override
	public List<HashMap<String, Object>> getFavoriteFailByactivityIdAndUser(String activityId, String userId,
			String isRegular) {
		return baseMapper.getFavoriteFailByactivityIdAndUser(activityId,userId,isRegular);
	}

}
