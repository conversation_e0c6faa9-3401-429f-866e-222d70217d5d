package com.tahoecn.opening.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.ApiResult;
import com.tahoecn.opening.common.utils.ThreadLocalUtils;
import com.tahoecn.opening.common.utils.UpLoadUtils;
import com.tahoecn.opening.model.OpProjectShare;
import com.tahoecn.opening.mapper.OpProjectShareMapper;
import com.tahoecn.opening.model.interfaceBean.OpProjectShareData;
import com.tahoecn.opening.model.interfaceBean.OpProjectShareRootBean;
import com.tahoecn.opening.service.IOpProjectShareService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 分享+项目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@Service
public class OpProjectShareServiceImpl extends ServiceImpl<OpProjectShareMapper, OpProjectShare> implements IOpProjectShareService {

    @Autowired
    OpProjectShareMapper projectShareMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updataProject(ResponseEntity<OpProjectShareRootBean> quote) {
        List<OpProjectShareData> data = quote.getBody().getData();
        System.out.println("data = " + data);
        List<OpProjectShare> opHouseTypeList = new ArrayList<>();
        for (OpProjectShareData opProjectShareData : data) {
            //创建人时间 可用设置
            OpProjectShare opProjectShare = new OpProjectShare();
            opProjectShare.setYn(GlobalConstants.Y);
            opProjectShare.setCreatedBy("interface");
            opProjectShare.setCreationDate(LocalDateTime.now());
            opProjectShare.setLastUpdateBy("sync");

            opProjectShare.setHouseType(opProjectShareData.getProductType());
            opProjectShare.setProjectId(opProjectShareData.getProjectID());
            opProjectShare.setProjectName(opProjectShareData.getName());
            opProjectShare.setPriceTotal(opProjectShareData.getProductPriceTotal());
            opProjectShare.setPriceAvg(opProjectShareData.getProductPriceAvg());
            opProjectShare.setImg(opProjectShareData.getImgSrc());
            opProjectShare.setAddress(opProjectShareData.getAddress());
            opProjectShare.setTel(opProjectShareData.getTel());
            opHouseTypeList.add(opProjectShare);
        }
        boolean flag=true;
            QueryWrapper<OpProjectShare> wrapper = new QueryWrapper<>();
            projectShareMapper.delete(wrapper);
            for (OpProjectShare opProjectShare : opHouseTypeList) {
         if (projectShareMapper.insert(opProjectShare)!=1){
             flag=false;
         }

        }
        return flag;
    }

    @Override
    public String upLoadImg(OpProjectShareData opProjectShareData, String webPath, String path) {
         return UpLoadUtils.downloadPicture(opProjectShareData.getImgSrc(),opProjectShareData.getProjectID(),webPath,path);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:33 2024/8/19
     * @param projectId
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     查看项目详情表根据项目id
     **/
    @Override
    public ApiResult getProjectShareByProjectId(String projectId) {
        // 获取数据
        LambdaQueryWrapper<OpProjectShare> lambda = new QueryWrapper<OpProjectShare>().lambda();
        lambda.eq(OpProjectShare::getProjectId, projectId).eq(OpProjectShare::getYn, "y");
        List<OpProjectShare> list = projectShareMapper.selectList(lambda);
        if (CollectionUtils.isEmpty(list)) {
            return ApiResult.getFailedApiResponse("明细数据不存在！");
        }
        return ApiResult.getSuccessApiResponse(list.get(0));
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:37 2024/8/19
     * @param share
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     更改项目详情表
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApiResult updateProjectShare(OpProjectShare share) {
        // 校验
        if (null == share.getId() || StringUtils.isBlank(share.getProjectId())) {
            return ApiResult.getFailedApiResponse("数据id和项目id均不可为空！");
        }
        share.setLastUpdateDate(LocalDateTime.now());
        projectShareMapper.updateById(share);
        return ApiResult.getSuccessApiResponse();
    }
}
