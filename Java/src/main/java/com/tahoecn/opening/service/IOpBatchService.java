package com.tahoecn.opening.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tahoecn.opening.converter.ResponseMessage;
import com.tahoecn.opening.model.OpBatch;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tahoecn.opening.model.vo.BatchParamVo;

import java.util.List;

/**
 * <p>
 * 活动 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
public interface IOpBatchService extends IService<OpBatch> {

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/25 15:30
     * @Param        paramVo
     * @Return       com.baomidou.mybatisplus.core.metadata.IPage<com.tahoecn.opening.model.OpBatch>
     * @Description  TODO 条查批次列表
     **/
    IPage<OpBatch> selectBatchListByCondition(BatchParamVo paramVo);

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/25 15:50
     * @Param        opBatch
     * @Return       com.tahoecn.opening.converter.ResponseMessage
     * @Description  TODO 新增修改批次
     **/
    ResponseMessage saveOrUpdateBatch(OpBatch opBatch);

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/25 15:52
     * @Param        activityId     活动id
     * @Return       java.util.List<com.tahoecn.opening.model.OpBatch>
     * @Description  TODO           获取该活动下所有批次数据
     **/
    List<OpBatch> selectAllBatchListByActivityId(String activityId);

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/25 16:34
     * @Param        ids 数据id，多个用英文逗号分割
     * @Return       com.tahoecn.opening.converter.ResponseMessage
     * @Description  TODO 批量删除批次根据数据ids
     **/
    ResponseMessage deleteBatchByIds(String ids);
}
