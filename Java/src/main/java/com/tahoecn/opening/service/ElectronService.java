package com.tahoecn.opening.service;

import com.tahoecn.opening.model.OpHousingResources;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.model.electronVo.*;

import java.util.List;
import java.util.Map;

/**
 * @Author: Zhengwencha<PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.service
 * @Description:// TODO 电子签接口
 * @Date: 2025/1/8 10:25
 * @Version: 1.0
 */
public interface ElectronService {

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/8 10:32
     * @Return       java.lang.String
     * @Description  TODO   获取电子签token
     **/
    String getElectronToken();

    String queryOrgOrUserInfo();

    String syncInnerUser();

    String getContractVariableInfoList();

    String createContract();

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/14 16:09
     * @Param        userName       用户名
     * @Param        idCard         身份证号
     * @Param        mobile         手机号
     * @Return       java.util.Map<java.lang.String,java.lang.String>
     * @Description  TODO   电子签同步用户
     **/
    Map<String, String> syncInnerUser(String userName, String idCard, String mobile);

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/15 10:52
     * @Param        electronCode   电子签合同编码
     * @Param        electronName   电子签合同名称
     * @Param        idCard         签约自然人身份证号
     * @Param        variableMap    变量map
     * @Return       java.util.Map<java.lang.String,java.lang.String>
     * @Description  TODO   电子签创建合同
     **/
    Map<String, String> createContract(String electronCode, String electronName, String idCard, Map<String, String> variableMap);

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/15 10:52
     * @Param        electronCode   电子签合同编码
     * @Param        electronName   电子签合同名称
     * @Param        idCard         签约自然人身份证号
     * @Param        templateId     模板id
     * @Param        paramVoList    变量集合
     * @Return       java.util.Map<java.lang.String,java.lang.String>
     * @Description  TODO   电子签创建合同
     **/
    Map<String, String> createContract(String electronCode, String electronName, String idCard, String templateId, List<ContractParamVo> paramVoList);

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 9:42
     * @Param        paramVo    入参对象Vo
     * @Return       com.tahoecn.opening.model.electronVo.CallBackResult
     * @Description  TODO   签约人签章结果回调
     **/
    CallBackResult callBackSignResult(SignResultParamVo paramVo);

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 9:42
     * @Param        paramVo    入参对象Vo
     * @Return       com.tahoecn.opening.model.electronVo.CallBackResult
     * @Description  TODO   合同状态变更回调
     **/
    CallBackResult callBackContractStatus(ContractStatusParamVo paramVo);

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 9:42
     * @Param        paramVo    入参对象Vo
     * @Return       com.tahoecn.opening.model.electronVo.CallBackResult
     * @Description  TODO   合同文件数据回调
     **/
    CallBackResult callBackContractFile(ContractFileParamVo paramVo);

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/23 15:35
     * @Param        opUser             用户对象
     * @Param        housingResources   房源对象
     * @Return       java.util.List<com.tahoecn.opening.model.electronVo.ContractParamVo>
     * @Description  TODO 获取生成合同变量对象集合
     **/
    List<ContractParamVo> getContractParamVoList(OpUser opUser, OpHousingResources housingResources);

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/15 17:12
     * @Param        contractId         合同id
     * @Return       java.lang.String
     * @Description  TODO 获取电子签附件
     **/
    String getElectronFile(String contractId);
}
