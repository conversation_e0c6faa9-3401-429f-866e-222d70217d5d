package com.tahoecn.opening.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import com.tahoecn.opening.common.enums.SyncEnum;
import com.tahoecn.opening.common.utils.ApiResult;
import com.tahoecn.opening.common.utils.HttpClientUtil;
import com.tahoecn.opening.mapper.CsUcOrgMapper;
import com.tahoecn.opening.mapper.CsUcUserMapper;
import com.tahoecn.opening.mapper.OpProjectSaleMapper;
import com.tahoecn.opening.mapper.OpProjectShareMapper;
import com.tahoecn.opening.model.CsUcOrg;
import com.tahoecn.opening.model.CsUcUser;
import com.tahoecn.opening.model.OpProjectSale;
import com.tahoecn.opening.model.OpProjectShare;
import com.tahoecn.opening.model.vo.SyncOrgVO;
import com.tahoecn.opening.model.vo.SyncUserVO;
import com.tahoecn.opening.service.SyncService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.service.impl
 * @ClassName: SyncServiceImpl
 * @Description:// TODO
 * @Date: 2024/8/16 14:16
 * @Version: 1.0
 */
@Service
public class SyncServiceImpl implements SyncService {

    @Resource
    CsUcUserMapper csUcUserMapper;

    @Resource
    CsUcOrgMapper csUcOrgMapper;

    @Resource
    OpProjectSaleMapper opProjectSaleMapper;

    @Resource
    OpProjectShareMapper opProjectShareMapper;

    public static final Log log = LogFactory.get();

    @Value("${syncData.address}")
    private String address;

    @Value("${syncData.key}")
    private String key;

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:25 2024/8/16
     * @description // TODO     同步全量组织
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApiResult syncOrg() {
        // 定义入参对象
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("sysKp", key);
        try {
            // 发起调用
            log.info("调用主数据全量组织机构入参为：" + JSONObject.toJSONString(paramMap));
            String resultStr = HttpClientUtil.HttpPostJson(address + "/api/permissions/selectOrgList", paramMap);
            // 校验并转换数据
            log.info("调用主数据全量组织机构结果为：" + resultStr);
            if (null != resultStr) {
                // 转换数据
                JSONObject resultObject = JSONObject.parseObject(resultStr);
                if (null != resultObject.get("resultCode") && 200 == resultObject.getIntValue("resultCode")) {
                    // 调用成功！获取结果集
                    Object resultList = resultObject.get("result");
                    if (null != resultList) {
                        List<SyncOrgVO> dataList = JSONArray.parseArray(resultList.toString(), SyncOrgVO.class);
                        saveOrg(dataList);
                    }
                }
            }
            return ApiResult.getSuccessApiResponse();
        } catch (Exception e) {
            e.printStackTrace();
            return ApiResult.getFailedApiResponse();
        }
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 9:57 2024/8/19
     * @param dataList 
     * @description // TODO     新增
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveOrg(List<SyncOrgVO> dataList) {
        Date nowDate = new Date();
        LocalDateTime now = LocalDateTime.now();
        log.info("同步组织机构开始时间为：" + nowDate);

        // 处理数据，将组织机构：中国化学--->子集公司  中间加一个板块概念，改为：中国化学---->业务板块---->子集公司
        // 获取到父级根目录节点集合（默认只有一个父级且是中国化学）
        Map<String, SyncOrgVO> dataMap = dataList.stream().collect(Collectors.toMap(SyncOrgVO::getId, Function.identity(), (k, v) -> v));

        // 获取所有分期
        LambdaQueryWrapper<OpProjectSale> saleWrapper = new QueryWrapper<OpProjectSale>().lambda();
        List<OpProjectSale> saleList = opProjectSaleMapper.selectList(saleWrapper);
        Map<String, OpProjectSale> existSaleMap = new HashMap<>();
        // 校验数据
        if (CollectionUtils.isNotEmpty(saleList)) {
            existSaleMap = saleList.stream().collect(Collectors.toMap(OpProjectSale::getProjectId, Function.identity(), (k, v) -> v));
        }
        // 获取所有分期明细
        LambdaQueryWrapper<OpProjectShare> saleDetailWrapper = new QueryWrapper<OpProjectShare>().lambda();
        List<OpProjectShare> saleDetailList = opProjectShareMapper.selectList(saleDetailWrapper);
        Map<String, OpProjectShare> existSaleDetailMap = new HashMap<>();
        // 校验数据
        if (CollectionUtils.isNotEmpty(saleDetailList)) {
            existSaleDetailMap = saleDetailList.stream().collect(Collectors.toMap(OpProjectShare::getProjectId, Function.identity(), (k, v) -> v));
        }

        // 获取所有数据
        LambdaQueryWrapper<CsUcOrg> orgWrapper = new QueryWrapper<CsUcOrg>().lambda();
        List<CsUcOrg> existList = csUcOrgMapper.selectList(orgWrapper);
        Map<String, CsUcOrg> existMap = new HashMap<>();
        // 校验数据
        if (CollectionUtils.isNotEmpty(existList)) {
            existMap = existList.stream().collect(Collectors.toMap(CsUcOrg::getFdSid, Function.identity(), (k, v) -> v));
        }
        // 比对数据
        for (SyncOrgVO vo : dataList) {
            if (null == vo.getParentId() || "0".equals(vo.getParentId())) {
                vo.setParentId("");
            }
            CsUcOrg data = new CsUcOrg();
            data.setFdSid(vo.getId());
            data.setFdName(vo.getName());
            data.setFdPsid(vo.getParentId());
            data.setFdAvailable(1);
            data.setFdIsdelete(vo.getDeletedStatus().equals(SyncEnum.DELETE_FALSE.getCode()) ? -1 : 1);
            data.setFdType(vo.getType());
            // 是否已存在
            CsUcOrg org = existMap.get(vo.getId());
            if (null == org) {
                data.setFdOrder(1);
                data.setCreateDate(nowDate);
                data.setUpdateDate(nowDate);
                // 数据不多，不必优化，直接新增，如果数据多则需要优化批量新增sql
                csUcOrgMapper.insert(data);
            } else {
                // 获取删除标识
                int deleteFlag = org.getFdIsdelete().intValue() == -1 ? 0 : 1;
                // 校验是否需要更改
                if (!(vo.getId() + ":" + vo.getName() + ":" + vo.getParentId() + ":" + vo.getDeletedStatus() + ":" + vo.getType())
                        .equals((org.getFdSid() + ":" + org.getFdName() + ":" + org.getFdPsid() + ":" + deleteFlag + ":" + org.getFdType()))) {
                    // 更改数据
                    data.setId(org.getId());
                    data.setUpdateDate(nowDate);
                    csUcOrgMapper.updateById(data);
                }
            }
            // 校验是否为分期
            if (vo.getType().equals(SyncEnum.ORG_TYPE_05.getCode())) {
                // 分期
                // 定义对象
                OpProjectSale sale = new OpProjectSale();
                sale.setProjectId(vo.getId());
                sale.setProjectName(vo.getName());
                // 赋值项目
                SyncOrgVO projectVO = dataMap.get(vo.getParentId());
                if (null != projectVO) {
                    sale.setCityId(projectVO.getId());
                    sale.setCityName(projectVO.getName());
                    // 赋值公司
                    SyncOrgVO companyVO = dataMap.get(projectVO.getParentId());
                    if (null != companyVO) {
                        sale.setAreaId(companyVO.getId());
                        sale.setAreaName(companyVO.getName());
                    }
                }
                sale.setYn(vo.getDeletedStatus().equals(SyncEnum.DELETE_FALSE.getCode()) ? "y" : "n");
                // 校验该分期是否存在
                OpProjectSale existSale = existSaleMap.get(vo.getId());
                // 校验
                if (null == existSale) {
                    // 新增分期
                    sale.setCreationDate(nowDate);
                    sale.setLastUpdateDate(nowDate);
                    opProjectSaleMapper.insert(sale);
                    // 新增其明细
                    OpProjectShare share = new OpProjectShare();
                    share.setProjectId(vo.getId());
                    share.setYn(sale.getYn());
                    share.setCreationDate(now);
                    share.setLastUpdateDate(now);
                    opProjectShareMapper.insert(share);
                } else {
                    // 校验是否需要更改
                    if (!(existSale.getProjectId() + ":" + existSale.getProjectName() + ":" + existSale.getCityId() + ":" + existSale.getCityName() + ":" + existSale.getAreaId() + ":" + existSale.getAreaName() + ":" + existSale.getYn())
                            .equals(sale.getProjectId() + ":" + sale.getProjectName() + ":" + sale.getCityId() + ":" + sale.getCityName() + ":" + sale.getAreaId() + ":" + sale.getAreaName() + ":" + sale.getYn())) {
                        // 更改数据
                        sale.setId(existSale.getId());
                        sale.setLastUpdateDate(nowDate);
                        opProjectSaleMapper.updateById(sale);
                        // 获取明细
                        OpProjectShare existShare = existSaleDetailMap.get(vo.getId());
                        if (null != existShare) {
                            existShare.setProjectId(sale.getProjectId());
                            existShare.setLastUpdateDate(now);
                            opProjectShareMapper.updateById(existShare);
                        }
                    }
                }
            }
        }

        log.info("同步组织机构耗时毫秒数为：" + (System.currentTimeMillis() - nowDate.getTime()));
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 9:57 2024/8/19  
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     同步全量用户
     **/
    @Override
    public ApiResult syncUser() {
        // 定义入参对象
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("sysKp", key);
        try {
            // 发起调用
            log.info("调用主数据全量用户入参为：" + JSONObject.toJSONString(paramMap));
            String resultStr = HttpClientUtil.HttpPostJson(address + "/api/permissions/selectSdBaseUserList", paramMap);
            // 校验并转换数据
            log.info("调用主数据全量用户结果为：" + resultStr);
            if (null != resultStr) {
                // 转换数据
                JSONObject resultObject = JSONObject.parseObject(resultStr);
                if (null != resultObject.get("resultCode") && 200 == resultObject.getIntValue("resultCode")) {
                    // 调用成功！获取结果集
                    Object resultList = resultObject.get("result");
                    if (null != resultList) {
                        List<SyncUserVO> dataList = JSONArray.parseArray(resultList.toString(), SyncUserVO.class);
                        saveUser(dataList);
                    }
                }
            }
            return ApiResult.getSuccessApiResponse();
        } catch (Exception e) {
            e.printStackTrace();
            return ApiResult.getFailedApiResponse();
        }
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 9:57 2024/8/19
     * @param dataList
     * @description // TODO     新增
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveUser(List<SyncUserVO> dataList) {
        Date nowDate = new Date();
        LocalDateTime now = LocalDateTime.now();
        log.info("同步用户开始时间为：" + nowDate);

        // 获取所有用户
        LambdaQueryWrapper<CsUcUser> userWrapper = new QueryWrapper<CsUcUser>().lambda();
        List<CsUcUser> userList = csUcUserMapper.selectList(userWrapper);
        Map<String, CsUcUser> existUserMap = new HashMap<>();
        // 校验数据
        if (CollectionUtils.isNotEmpty(userList)) {
            existUserMap = userList.stream().collect(Collectors.toMap(CsUcUser::getFdSid, Function.identity(), (k, v) -> v));
        }

        for (SyncUserVO vo : dataList) {
            CsUcUser existUser = existUserMap.get(vo.getUserUuid());
            if (null == existUser) {
                // 新增
                existUser = new CsUcUser();
                existUser.setFdSid(vo.getUserUuid());
                existUser.setFdUsername(vo.getUserCode());
                existUser.setFdPassword(DigestUtils.md5Hex(vo.getUserCode() + "_" + "123456" + "_" + vo.getUserCode()).toLowerCase());
                existUser.setFdName(vo.getUserName());
                existUser.setFdOrgId(vo.getBelongOrgCode());
                existUser.setFdOrder(1);
                existUser.setFdAvailable(1);
                existUser.setFdIsdelete(vo.getDeletedStatus().equals(SyncEnum.DELETE_FALSE.getCode()) ? -1 : 1);
                existUser.setCreateTime(nowDate);
                existUser.setUpdateTime(nowDate);
                csUcUserMapper.insert(existUser);
            } else {
                // 获取删除标识
                int deleteFlag = existUser.getFdIsdelete().intValue() == -1 ? 0 : 1;
                // 校验是否需要更改
                if (!(vo.getUserUuid() + ":" + vo.getUserCode() + ":" + vo.getUserName() + ":" + vo.getDeletedStatus() + ":" + vo.getBelongOrgCode())
                        .equals((existUser.getFdSid() + ":" + existUser.getFdUsername() + ":" + existUser.getFdName() + ":" + deleteFlag + ":" + existUser.getFdOrgId()))) {
                    // 更改数据
                    existUser.setFdSid(vo.getUserUuid());
                    existUser.setFdUsername(vo.getUserCode());
                    existUser.setFdName(vo.getUserName());
                    existUser.setFdOrgId(vo.getBelongOrgCode());
                    existUser.setFdIsdelete(vo.getDeletedStatus().equals(SyncEnum.DELETE_FALSE.getCode()) ? -1 : 1);
                    existUser.setUpdateTime(nowDate);
                    csUcUserMapper.updateById(existUser);
                }
            }
        }

        log.info("同步用户耗时毫秒数为：" + (System.currentTimeMillis() - nowDate.getTime()));
    }
}
