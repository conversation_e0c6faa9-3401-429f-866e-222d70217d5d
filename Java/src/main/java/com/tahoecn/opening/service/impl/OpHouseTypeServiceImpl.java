package com.tahoecn.opening.service.impl;

import com.tahoecn.opening.common.utils.UpLoadUtils;
import com.tahoecn.opening.model.OpHouseType;
import com.tahoecn.opening.mapper.OpHouseTypeMapper;
import com.tahoecn.opening.service.IOpHouseTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 户型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@Service
public class OpHouseTypeServiceImpl extends ServiceImpl<OpHouseTypeMapper, OpHouseType> implements IOpHouseTypeService {


	@Override
	public List<HashMap<String, Object>> getHouseTypeListByActivityId(String activityId) {
		return baseMapper.getHouseTypeListByActivityId(activityId);
	}

	@Override
	public HashMap<String, Object> getHouseTypeAreaMinToMax(String activityId) {
		return baseMapper.getHouseTypeAreaMinToMax(activityId);
	}

    @Override
    public String upLoadImg(OpHouseType opHouseType,String webPath,String path) {

            return UpLoadUtils.downloadPicture(opHouseType.getHouseImg(),opHouseType.getActivityId(),webPath,path);

    }

}
