package com.tahoecn.opening.service.impl;

import com.tahoecn.opening.model.SysUserRole;
import com.tahoecn.opening.mapper.SysUserRoleMapper;
import com.tahoecn.opening.service.ISysUserRoleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户-角色 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-09
 */
@Service
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements ISysUserRoleService {

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Override
    public List<SysUserRole> myActivityList(String userName){
        return sysUserRoleMapper.myActivityList(userName);
    }

    @Override
    public List<SysUserRole> myActivityListAll(String userName){
        return sysUserRoleMapper.myActivityListAll(userName);
    }
}
