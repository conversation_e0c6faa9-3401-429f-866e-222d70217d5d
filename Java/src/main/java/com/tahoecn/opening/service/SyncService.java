package com.tahoecn.opening.service;

import com.tahoecn.opening.common.utils.ApiResult;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.service
 * @ClassName: SyncService
 * @Description:// TODO 同步接口
 * @Date: 2024/8/16 14:16
 * @Version: 1.0
 */
public interface SyncService {

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:25 2024/8/16
     * @description // TODO     同步全量组织
     **/
    ApiResult syncOrg();

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:25 2024/8/16
     * @description // TODO     同步全量用户
     **/
    ApiResult syncUser();
}
