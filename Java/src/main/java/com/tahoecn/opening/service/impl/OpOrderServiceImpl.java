package com.tahoecn.opening.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.constants.MQConstant;
import com.tahoecn.opening.common.utils.DateUtils;
import com.tahoecn.opening.common.utils.LocalDateTimeUtils;
import com.tahoecn.opening.common.utils.UUIDUtil;
import com.tahoecn.opening.converter.ShareinterJson2HttpMessageConverter;
import com.tahoecn.opening.mapper.*;
import com.tahoecn.opening.common.utils.ThreadLocalUtils;
import com.tahoecn.opening.model.*;
import com.tahoecn.opening.model.dto.UserDto;
import com.tahoecn.opening.model.dto.UserHouseResourceDTO;
import com.tahoecn.opening.model.interfaceBean.CustomerFollowData;
import com.tahoecn.opening.model.interfaceBean.CustomerFollowRootBean;
import com.tahoecn.opening.model.interfaceBean.JsonRootBean;
import com.tahoecn.opening.model.interfaceBean.Param;
import com.tahoecn.opening.model.vo.*;
import com.tahoecn.opening.quartz.QuartzService;
import com.tahoecn.opening.quartz.UpdateBatchQTask;
import com.tahoecn.opening.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.AbstractJavaTypeMapper;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.*;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@Service
public class OpOrderServiceImpl extends ServiceImpl<OpOrderMapper, OpOrder> implements IOpOrderService {


    @Autowired
    private IOpUserService iOpUserService;

    @Autowired
    private OpOrderMapper opOrderMapper;

    @Autowired IOpOrderService opOrderService;

    @Autowired
    private OpOrderGarbageMapper opOrderGarbageMapper;

    @Autowired
    private OpUserMapper opUserMapper;

    @Autowired
    private OpActivityMapper opActivityMapper;

    @Autowired
    private OpHousingResourcesMapper opHousingResourcesMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IOpHousingResourcesService iOpHousingResourcesService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private IOpActivityService activityService;

    @Autowired
    private CsSendSmsLogService csSendSmsLogService;

    @Autowired
    private IMessageService messageService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private  IOpMassageJobService massageJobService;

    @Resource
    private OpUserHouseResourceMapper userHouseResourceMapper;

    @Resource
    private ElectronService electronService;

    @Resource
    private IOpBatchService batchService;

    @Value("${const_HOST}")
    private String ConstHOST;

    @Autowired
    private QuartzService quartzService;

    private static final Logger log = LoggerFactory.getLogger(OpOrderServiceImpl.class);

    @Override
    public void initCanOrder(String activityId) {//todo 是否模拟

        OpActivity opActivity = activityService.getById(activityId);

        Set<String> keys = redisTemplate.keys(activityId+"Activity*");
        redisTemplate.delete(keys);     //删除当前活动下的redis

        QueryWrapper<OpUser> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpUser::getActivityId, activityId);
        wrapper.lambda().eq(OpUser::getYn, GlobalConstants.Y);
        wrapper.lambda().eq(OpUser::getRoleName, "customer");
        List<OpUser> opUsers = iOpUserService.list(wrapper);

        QueryWrapper<OpHousingResources> hrWrapper = new QueryWrapper<>();
        hrWrapper.lambda().eq(OpHousingResources::getActivityId, activityId);
        hrWrapper.lambda().eq(OpHousingResources::getYn, GlobalConstants.Y);
        List<OpHousingResources> hrs = iOpHousingResourcesService.list(hrWrapper);

        QueryWrapper<OpOrder> orderWrapper = new QueryWrapper<>();
        orderWrapper.lambda().eq(OpOrder::getActivityId, activityId);
        orderWrapper.lambda().eq(OpOrder::getYn, GlobalConstants.Y);
        List<OpOrder> orders = opOrderMapper.selectList(orderWrapper);

        for (OpUser opUser : opUsers) {
            int tempRegular = 0;    //正式订单
            int tempV = 0;  //模拟订单      --理论上初始化时不存在模拟订单 以防万一
            for (OpOrder order : orders) {
                if (opUser.getId().toString().equals(order.getUserId()) && GlobalConstants.Y.equals(order.getIsRegular())){   //如果存在订单    &&  订单为正式订单
                    tempRegular += 1;
                }
                if (opUser.getId().toString().equals(order.getUserId()) && GlobalConstants.N.equals(order.getIsRegular())){
                    tempV += 1;
                }
            }
            redisTemplate.opsForValue().set(opUser.getActivityId()+"Activity_T_User"+opUser.getId(),opUser.getSelectCount() - tempRegular);    //每人能买几套

            if (GlobalConstants.Y.equals(opActivity.getIsSimulation())) {//模拟   不算预制订单
                redisTemplate.opsForValue().set(opUser.getActivityId()+"Activity_V_User"+opUser.getId(),opUser.getSelectCount()- tempV);    //每人能买几套
            }
        }

        for (OpHousingResources hr : hrs) {
            int tempRegular = 0;    //正式订单
            int tempV = 0;  //模拟订单
            for (OpOrder order : orders) {
                if (hr.getId().toString().equals(order.getHouseId()) && GlobalConstants.Y.equals(order.getIsRegular())){   //如果存在订单    &&  订单为正式订单
                    tempRegular += 1;
                }
                if (hr.getId().toString().equals(order.getHouseId()) && GlobalConstants.N.equals(order.getIsRegular())){
                    tempV += 1;
                }
            }
            redisTemplate.opsForValue().set(hr.getActivityId()+"Activity_T_House"+hr.getId(),0 - tempRegular);    //房源    如果预制订单存在,房源数标记为-1

            if (GlobalConstants.Y.equals(opActivity.getIsSimulation())) {//模拟   不算预制订单
                redisTemplate.opsForValue().set(hr.getActivityId()+"Activity_V_House"+hr.getId(),1 - tempV);    //房源    模拟订单不存在预制，所以为0
            }
        }

    }

    //test 是否有订单   订单是否正式   是否模拟   是否预制  订单是否删除   人员是否删除  房源是否删除
    @Override
    public void initCanOrderBySql(String activityId) {

        OpActivity activity = activityService.getById(activityId);

        Set<String> keys = redisTemplate.keys(activityId+"Activity*");
        redisTemplate.delete(keys);     //删除当前活动下的redis

        List<HashMap<String, Object>> lists = opOrderMapper.getUserCanBuy(activityId,GlobalConstants.Y);
        for (HashMap<String, Object> opUser : lists) {
            Integer temp =(Integer)opUser.get("select_count") - Integer.parseInt(opUser.get("order_cound") == null ? "0" : opUser.get("order_cound").toString());
            redisTemplate.opsForValue().set(opUser.get("activity_id")+"Activity_T_User"+opUser.get("id"),temp);    //每人能买几套
        }

        List<HashMap<String, Object>> houseLists = opOrderMapper.getHouseCanBuy(activityId);
        for (HashMap<String, Object> house : houseLists) {
            Integer tempT = 1;
            Integer tempV = 1;
            if (house.get("is_regular") != null){
                if (GlobalConstants.Y.equals(house.get("is_regular").toString())){  //如果是正式订单
                    tempT = -1; //房源    如果预制订单存在,房源数标记为-1
                }
                if (GlobalConstants.N.equals(house.get("is_regular").toString())){  //如果是模拟订单
                    tempV = 0;
                }
            }
            redisTemplate.opsForValue().set(activityId+"Activity_T_House"+house.get("id"),tempT);
            if (GlobalConstants.Y.equals(activity.getIsSimulation())) {//模拟   不算预制订单
                redisTemplate.opsForValue().set(activityId+"Activity_V_House"+house.get("id"),tempV);
            }
        }

        if (GlobalConstants.Y.equals(activity.getIsSimulation())) {//模拟   不算预制订单 但算模拟订单--理论上着个时候不存在模拟订单
            List<HashMap<String, Object>> vlists = opOrderMapper.getUserCanBuy(activityId,GlobalConstants.N);
            for (HashMap<String, Object> opUser : vlists) {
                redisTemplate.opsForValue().set(opUser.get("activity_id") + "Activity_V_User" + opUser.get("id"), opUser.get("select_count"));    //每人能买几套
            }
        }

//        // 刷新活动批次数据
//        refreshActivityDataById(activity);
    }

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2024/11/22 17:26
     * @Param        timeStr	选房区间字符串
     * @Return       java.util.List<com.tahoecn.opening.model.vo.BuyTimeVO>
     * @Description  TODO 		转换选房时间区间集合
     **/
    public List<BuyTimeVO> convertBuyTimeByTimeStr(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            return null;
        }
        List<BuyTimeVO> timeVOList = JSONArray.parseArray(timeStr, BuyTimeVO.class);
        // 剔除非区间数据
        Iterator<BuyTimeVO> iterator = timeVOList.iterator();
        while (iterator.hasNext()) {
            BuyTimeVO next = iterator.next();
            if (null == next.getBeginDate() || null == next.getEndDate()) {
                iterator.remove();
            }
        }
        if (CollectionUtils.isNotEmpty(timeVOList)) {
            // 按开始时间正叙排序
            timeVOList = timeVOList.stream().sorted(Comparator.comparing(BuyTimeVO::getBeginDate)).collect(Collectors.toList());
        }
        return timeVOList;
    }

    public List<UserDto> convertUserDtoList(List<BuyTimeVO> timeVOList, List<UserDto> userDtoList, Integer checkMinutes) {
        List<UserDto> newUserDtoList = Lists.newArrayListWithCapacity(userDtoList.size());
        // 校验
        if (CollectionUtils.isNotEmpty(timeVOList)) {
            // 上一个执行序号、上一个执行分钟数、上一个执行开始时间、上一个执行结束时间
            int lastSortNum = 0;
            int lastMinutes = 0;
            Date lastBeginDate = null;
            // 超出分钟数
            int morethanMinutes = 0;
            // 处理数据
            for (BuyTimeVO vo : timeVOList) {
                // 定义开始时间和结束时间
                Date thisBeginDate = DateUtils.getDate(vo.getBeginDate(), DateUtils.PATTERN_SECOND);
                Date thisEndDate = DateUtils.getDate(vo.getEndDate(), DateUtils.PATTERN_SECOND);
                Date userBeginDate = null;
                Date userEndDate = null;
                // 校验当前时间
                Iterator<UserDto> iterator = userDtoList.iterator();
                while (iterator.hasNext()) {
                    UserDto userDto = iterator.next();
                    if (0 == userDto.getSelectCount().intValue() || 0 == userDto.getUserSort().intValue()) {
                        // 该用户未配置可选房数 或 排序号
                        newUserDtoList.add(userDto);
                        iterator.remove();
                    } else {
                        // 校验是否上一区间有超出分钟数
                        if (morethanMinutes > 0) {
                            // 有超出
                            userBeginDate = thisBeginDate;
                        } else {
                            // 无超出
                            // 校验上一执行序号与当前序号是否相同
                            if (lastSortNum == userDto.getUserSort().intValue()) {
                                userBeginDate = lastBeginDate;
                            } else {
                                userBeginDate = thisBeginDate;
                            }
                        }
                        // 获取该区间分钟数
                        int minutes = DateUtils.getSeconds(userBeginDate, thisEndDate);
                        if (0 >= minutes) {
                            break;
                        }
                        // 获取分钟数
                        Integer userCheckMinutes = getUserCheckMinutes(checkMinutes, userDto.getSelectCount());
                        // 校验是否上一区间有超出分钟数
                        if (morethanMinutes > 0) {
                            // 获取该用户结束
                            userEndDate = DateUtils.addDateSecond(userBeginDate, morethanMinutes);
                        } else {
                            // 获取该用户结束
                            userEndDate = DateUtils.addDateSecond(userBeginDate, userCheckMinutes);
                        }
                        // 校验序号是否相同
                        if (lastSortNum == userDto.getUserSort().intValue()) {
                            // 校验是否上一区间有超出分钟数
                            if (morethanMinutes > 0) {
                                thisBeginDate = userEndDate;
                            } else {
                                // 校验是否更新上一个序号数据
                                if (userCheckMinutes.intValue() > lastMinutes) {
                                    // 赋值上次执行时间
                                    lastBeginDate = userBeginDate;
                                    lastMinutes = userCheckMinutes;
                                    thisBeginDate = userEndDate;
                                }
                            }
                        } else {
                            thisBeginDate = userEndDate;
                            // 赋值上次执行时间
                            lastBeginDate = userBeginDate;
                            lastMinutes = userCheckMinutes;
                            lastSortNum = userDto.getUserSort().intValue();
                        }
//						System.err.println("lastSortNum = " + lastSortNum + "==lastMinutes = " + lastMinutes + "==lastBeginDate = " + lastBeginDate + "==thisBeginDate = " + thisBeginDate + "==morethanMinutes = " + morethanMinutes);
                        // 校验是否上一区间有超出分钟数
                        if (morethanMinutes > 0) {
                            morethanMinutes = 0;
                        } else {
                            userDto.setBeginDate(userBeginDate);
                        }
                        userDto.setEndDate(userEndDate.after(thisEndDate) ? thisEndDate : userEndDate);
//						System.err.println("----" + userDto);
                        // 校验是否移除
                        if (userCheckMinutes.intValue() > minutes) {
                            morethanMinutes = userCheckMinutes.intValue() - minutes;
                        } else {
                            newUserDtoList.add(userDto);
                            iterator.remove();
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(userDtoList)) {
                newUserDtoList.addAll(userDtoList);
            }
        }
        return newUserDtoList;
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/21 17:18
     * @Param        checkMinutes	互动配置选房分钟数
     * @Param        selectCount 	该用户配置可选房数
     * @Return       java.lang.Integer
     * @Description  TODO 相乘
     **/
    public Integer getUserCheckMinutes(Integer checkMinutes, Integer selectCount) {
        return checkMinutes * selectCount;
    }

    @Override
    public void removeCanOrderRedis(String activityId) {
        Set<String> keys = redisTemplate.keys(activityId + "Activity*");
        redisTemplate.delete(keys);     //删除当前活动下的redis
        // 清空该活动所有批次任务
        quartzService.deleteJobsByGroup("Activity_" + activityId);
    }

        @Override
    @Deprecated
    public JSONResult getCanOrder(OpOrder order){
        JSONResult jsonResult = new JSONResult();
        //redisTemplate.watch(order.getActivityId()+"Activity_T_User"+order.getUserId());
        redisTemplate.watch(order.getActivityId()+"Activity_T_House"+order.getHouseId());

        Integer userNum = (Integer) redisTemplate.opsForValue().get(order.getActivityId()+"Activity_T_User"+order.getUserId());
        Integer houseNum = (Integer) redisTemplate.opsForValue().get(order.getActivityId()+"Activity_T_House"+order.getHouseId());
        if (houseNum == null || houseNum <= 0){
            jsonResult.setData("房源已售");
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("房源已售");
            return jsonResult;
        }

        if (userNum == null || userNum <= 0){
            jsonResult.setData("已到达购买上限");
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("已到达购买上限");
            return jsonResult;
        }

        //都大于一 当前可购
        jsonResult.setData("可以买");
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");

        redisTemplate.multi();
        redisTemplate.opsForValue().set(order.getActivityId()+"Activity_T_User"+order.getUserId(),userNum -1);
        redisTemplate.opsForValue().set(order.getActivityId()+"Activity_T_House"+order.getHouseId(),0);
        redisTemplate.exec();

        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        rabbitTemplate.setExchange(MQConstant.ORDER_EXCHANGE);
        rabbitTemplate.setRoutingKey(MQConstant.ORDER_KEY);

        Message message= MessageBuilder.withBody("abcdefg".getBytes()).setDeliveryMode(MessageDeliveryMode.PERSISTENT).build();
        message.getMessageProperties().setHeader(AbstractJavaTypeMapper.DEFAULT_CONTENT_CLASSID_FIELD_NAME, MessageProperties.CONTENT_TYPE_JSON);
        //rabbitTemplate.convertAndSend(message);

        return jsonResult;
    }

    @Override
    public JSONResult getCanOrderByLua(OpOrder order){
        JSONResult jsonResult = new JSONResult();

        // ********  zwc  校验 抢购房源白名单
        // 根据 活动id + 房源id 查询房源白名单列表是否存在数据
        QueryWrapper<OpUserHouseResource> wrapper = new QueryWrapper<>();
        wrapper.eq("activity_id", order.getActivityId());
        wrapper.eq("house_resource_id", order.getHouseId());
        // 查询
        List<OpUserHouseResource> existsUserHouseResourceList = userHouseResourceMapper.selectList(wrapper);
        // 校验
        if (CollectionUtils.isNotEmpty(existsUserHouseResourceList)) {
            // 获取 用户id是否是当前用户
            if (!order.getUserId().equals(String.valueOf(existsUserHouseResourceList.get(0).getUserId()))) {
                // 证明 当前房源已经被配置白名单给其他用户了，当前用户无法抢购
                jsonResult.setData("抢购失败");
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("抢购失败");
                return jsonResult;
            }
        }

        String result = executeCanOrderLua(order);

        if ("Houses_before".equals(result)){    //预制订单
            QueryWrapper<OpOrder> orderWrapper = new QueryWrapper<>();
            orderWrapper.lambda().eq(OpOrder::getActivityId, order.getActivityId());
            orderWrapper.lambda().eq(OpOrder::getHouseId, order.getHouseId());
            orderWrapper.lambda().eq(OpOrder::getIsRegular, GlobalConstants.Y);
            order.setIsBeforehand("s");
            opOrderMapper.update(order,orderWrapper);
            jsonResult.setData("房源已售");
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("房源已售");
            return jsonResult;
        }

        if ("Houses_null".equals(result)){
            jsonResult.setData("房源不存在");
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("房源不存在");
            return jsonResult;
        }
        if ("User_null".equals(result)){
            jsonResult.setData("您未被配置到该活动中");
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("您未被配置到该活动中");
            return jsonResult;
        }

        if ("Houses_sold".equals(result)){
            jsonResult.setData("房源已售");
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("房源已售");
            return jsonResult;
        }

        if ("maximum".equals(result)){
            jsonResult.setData("已到达购买上限");
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("已到达购买上限");
            return jsonResult;
        }

        if ("success".equals(result)){
            jsonResult.setData("加入订单队列");
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
        }


        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        rabbitTemplate.setExchange(MQConstant.ORDER_EXCHANGE);
        rabbitTemplate.setRoutingKey(MQConstant.ORDER_KEY);

        try {
            order.setCreationDate(LocalDateTime.now());
            byte[] bytes=getBytesFromObject(order);
            Message message= MessageBuilder.withBody(bytes).setDeliveryMode(MessageDeliveryMode.PERSISTENT).build();
            message.getMessageProperties().setHeader(AbstractJavaTypeMapper.DEFAULT_CONTENT_CLASSID_FIELD_NAME, MessageProperties.CONTENT_TYPE_JSON);
            rabbitTemplate.convertAndSend(message);
            //rabbitTemplate.convertAndSend("exchange","topic.messages",bytes);
        }catch (Exception e){
            executeBackOrderLua(order);     //订单队列生产失败 redis可购房源信息回滚
            jsonResult.setData("订单队列生产失败");
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("订单队列生产失败");
            return jsonResult;
        }

        return jsonResult;
    }

    /*
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/15 11:08
     * @Param        order          订单对象
     * @Param        activity       活动
     * @Param        opUser         用户
     * @Return       void
     * @Description  TODO   校验是否需要处理电子签合同
     **/
    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/24 9:25
     * @Param        order      订单对象
     * @Param        activity   活动对象
     * @Param        opUser     用户
     * @Param        housingResources   房源
     * @Return       void
     * @Description  TODO   生成电子签
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void checkElectronData(OpOrder order, OpActivity activity, OpUser opUser, OpHousingResources housingResources) {
        try {
            // 校验该活动是否开启电子签功能
            if (Objects.isNull(activity.getOpenElectronFlag()) || 1 != activity.getOpenElectronFlag().intValue()) {
                return;
            }
            // 同步人员
            if (StringUtils.isBlank(opUser.getSyncType()) || !"1".equals(opUser.getSyncType())) {
                Map<String, String> syncUserMap = electronService.syncInnerUser(opUser.getName(), opUser.getIdCard(), opUser.getTel());
                opUser.setSyncType(syncUserMap.get("syncType"));
                opUser.setSyncResult(syncUserMap.get("syncResult"));
                opUserMapper.updateById(opUser);
            }
            // 创建电子签合同
            String electronCode = UUIDUtil.getUUID32();
            String electronName = order.getUserName() + "-" + order.getHouseName()+"认租确认书";
            Map<String, String> contractMap = electronService.createContract(electronCode, electronName, opUser.getIdCard(), activity.getTemplateId(),
                    electronService.getContractParamVoList(opUser, housingResources));
            order.setElectronCode(electronCode);
            order.setElectronName(electronName);
            order.setOrderType(Integer.valueOf(contractMap.get("syncType")));
            order.setElectronId(contractMap.get("electronId"));
            order.setElectronUrl(contractMap.get("electronUrl"));
            order.setElectronResult(contractMap.get("syncResult"));
            order.setOrderStatus(0);
            opOrderMapper.updateById(order);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("校验电子签逻辑异常！订单信息为：{}，异常信息为：{}", order, e);
        }
    }

    //对象转化为字节码
    public byte[] getBytesFromObject(Serializable obj) throws Exception {
        if (obj == null) {
            return null;
        }
        ByteArrayOutputStream bo = new ByteArrayOutputStream();
        ObjectOutputStream oo = new ObjectOutputStream(bo);
        oo.writeObject(obj);
        return bo.toByteArray();
    }

    //字节码转化为对象
    public Object getObjectFromBytes(byte[] objBytes) throws Exception {
        if (objBytes == null || objBytes.length == 0) {
            return null;
        }
        ByteArrayInputStream bi = new ByteArrayInputStream(objBytes);
        ObjectInputStream oi = new ObjectInputStream(bi);
        return oi.readObject();
    }


    //执行可下订单脚本
    private String executeCanOrderLua(OpOrder order){
        String[] keyp = getKeys(order);
        System.out.println(keyp[0]+"-->"+redisTemplate.opsForValue().get(keyp[0]));
        DefaultRedisScript<String> redisScript = new DefaultRedisScript<>();    //todo  null
        String script = " if not redis.call('get', KEYS[1]) " +
                        " then " +
                        " return \"Houses_null\" " +
                        " end " +
                " if not redis.call('get', KEYS[2]) " +
                " then " +
                " return \"User_null\" " +
                " end " +
                        " if " +
                        " tonumber(redis.call('get', KEYS[1])) == -1 " +
                        " then " +
                        " return \"Houses_before\" " +
                        " end " +
                        " if " +
                        " tonumber(redis.call('get', KEYS[1])) <= 0 " +
                        " then " +
                        " return \"Houses_sold\" " +
                        " end " +
                        " if " +
                        " tonumber(redis.call('get', KEYS[2])) <= 0 " +
                        " then " +
                        " return \"maximum\"  " +
                        " end " +
                        " redis.call('DECR', KEYS[1]) " +
                        " redis.call('DECR', KEYS[2])" +
                        " return \"success\"";
        redisScript.setScriptText(script);
        redisScript.setResultType(String.class);
        log.debug("currentTimeMillis--->"+System.currentTimeMillis());
        return stringRedisTemplate.execute(redisScript, Arrays.asList(keyp));
    }

    //执行回退可买脚本
    @Override
    public String executeBackOrderLua(OpOrder order){
        String[] keyp = getKeys(order);
        DefaultRedisScript<String> redisScript = new DefaultRedisScript<>();
        String script = "if " +
                " tonumber(redis.call('get', KEYS[1])) == 0 or tonumber(redis.call('get', KEYS[1])) == -1" + //保证房源不会超过1 并由此确定可购数也应加1
                " then " +
                " redis.call('set', KEYS[1],1) " +
                " redis.call('INCR', KEYS[2])" +
                " end " ;
        redisScript.setScriptText(script);
        redisScript.setResultType(String.class);
        return stringRedisTemplate.execute(redisScript, Arrays.asList(keyp));
    }


    @Override
    public void redisHouseModify(String activityId,String houseId,String type){
        if ("plus".equals(type)){
            OpActivity opActivity = activityService.getById(activityId);
            String keyHouse = activityId+"Activity_T_House"+houseId;
            if (null == redisTemplate.opsForValue().get(keyHouse)){
                redisTemplate.opsForValue().set(keyHouse,1);
            }

            if (GlobalConstants.Y.equals(opActivity.getIsSimulation())) {//模拟
                String keyHouseV = activityId+"Activity_V_House"+houseId;
                if (null == redisTemplate.opsForValue().get(keyHouseV)){
                    redisTemplate.opsForValue().set(keyHouseV,1);
                }
            }
        }
        if ("minus".equals(type)){
            OpActivity opActivity = activityService.getById(activityId);
            String keyHouse = activityId+"Activity_T_House"+houseId;
            if (null != redisTemplate.opsForValue().get(keyHouse)){
                redisTemplate.opsForValue().set(keyHouse,0);
            }
            if (GlobalConstants.Y.equals(opActivity.getIsSimulation())) {//模拟
                String keyHouseV = activityId+"Activity_V_House"+houseId;
                if (null != redisTemplate.opsForValue().get(keyHouseV)){
                    redisTemplate.opsForValue().set(keyHouseV,0);
                }
            }
        }
    }

    @Override
    public void redisUserModify(String activityId, String userId, String type, Integer count){
        if ("plus".equals(type)){
            QueryWrapper<OpOrder> orderCustomerWrapper = new QueryWrapper<>();
            orderCustomerWrapper.lambda().eq(OpOrder::getActivityId, activityId);
            orderCustomerWrapper.lambda().eq(OpOrder::getYn, GlobalConstants.Y);
            orderCustomerWrapper.lambda().eq(OpOrder::getUserId, userId);
            orderCustomerWrapper.lambda().eq(OpOrder::getIsRegular, GlobalConstants.Y);
            List<OpOrder> userOrders = opOrderMapper.selectList(orderCustomerWrapper);
            Integer userCount = count - userOrders.size();

            OpActivity opActivity = activityService.getById(activityId);
            String keyHouse = activityId+"Activity_T_User"+userId;
            redisTemplate.opsForValue().set(keyHouse,userCount);

            if (GlobalConstants.Y.equals(opActivity.getIsSimulation())) {//模拟

                QueryWrapper<OpOrder> orderCustomerWrapperV = new QueryWrapper<>();
                orderCustomerWrapperV.lambda().eq(OpOrder::getActivityId, activityId);
                orderCustomerWrapperV.lambda().eq(OpOrder::getYn, GlobalConstants.Y);
                orderCustomerWrapperV.lambda().eq(OpOrder::getUserId, userId);
                orderCustomerWrapperV.lambda().eq(OpOrder::getIsRegular, GlobalConstants.N);
                List<OpOrder> userOrdersV = opOrderMapper.selectList(orderCustomerWrapper);
                Integer userCountV = count - userOrdersV.size();

                String keyHouseV = activityId+"Activity_V_User"+userId;
                redisTemplate.opsForValue().set(keyHouseV,userCountV);
            }
        }
        if ("minus".equals(type)){

            OpActivity opActivity = activityService.getById(activityId);
            String keyHouse = activityId+"Activity_T_User"+userId;
            redisTemplate.opsForValue().set(keyHouse,0);

            if (GlobalConstants.Y.equals(opActivity.getIsSimulation())) {//模拟

                String keyHouseV = activityId+"Activity_V_User"+userId;
                redisTemplate.opsForValue().set(keyHouseV,0);
            }
        }
    }

    private String[] getKeys(OpOrder order) {
        //String keyp[] = {"1Activity_T_House10","1Activity_T_User85"};
        String keyHouse = null;
        String keyUser = null;
        if (GlobalConstants.Y.equals(order.getIsRegular())){
            keyHouse = "Activity_T_House";
            keyUser = "Activity_T_User";
        }else if (GlobalConstants.N.equals(order.getIsRegular())){
            keyHouse = "Activity_V_House";
            keyUser = "Activity_V_User";
        }else{
            try {
                throw new Exception("订单没标记正式模拟");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return new String[]{order.getActivityId()+keyHouse+order.getHouseId(),order.getActivityId()+keyUser+order.getUserId()};
    }



    /**
     *订单正式生成！
     */
    @RabbitListener(queues= MQConstant.PLACE_ORDER_QUEUE)    //监听器监听指定的Queue
    public void placeOrder(byte[] bytes) {
        System.out.println(("OrderReceive:"+ Arrays.toString(bytes)));
        OpOrder order = null;
        try {
            order=(OpOrder)getObjectFromBytes(bytes);
            OpUser user = iOpUserService.getById(order.getUserId());
            String[] keyp = getKeys(order);
            QueryWrapper<OpOrder> orderWrapper = new QueryWrapper<>();
            orderWrapper.lambda().eq(OpOrder::getActivityId, order.getActivityId());
            orderWrapper.lambda().eq(OpOrder::getYn, GlobalConstants.Y);
            orderWrapper.lambda().eq(OpOrder::getHouseId, order.getHouseId());
            orderWrapper.lambda().eq(OpOrder::getIsRegular, order.getIsRegular());
            List<OpOrder> orders = opOrderMapper.selectList(orderWrapper);
            if (orders.size() > 0){
                log.info("房源已售，订单生成失败");
                redisTemplate.opsForValue().set(order.getActivityId()+"Activity"+order.getUserId()+"Order"+order.getHouseId()+order.getIsRegular(),"OrderFailHousesSold");
                redisTemplate.opsForValue().increment(keyp[1],1);   //todo:若房源已售则回退客户可购买数，需测试
                return;
            }

            QueryWrapper<OpOrder> orderCustomerWrapper = new QueryWrapper<>();
            orderCustomerWrapper.lambda().eq(OpOrder::getActivityId, order.getActivityId());
            orderCustomerWrapper.lambda().eq(OpOrder::getYn, GlobalConstants.Y);
            orderCustomerWrapper.lambda().eq(OpOrder::getUserId, order.getUserId());
            orderCustomerWrapper.lambda().eq(OpOrder::getIsRegular, order.getIsRegular());
            List<OpOrder> userOrders = opOrderMapper.selectList(orderCustomerWrapper);

            if (userOrders.size() >= user.getSelectCount()){
                log.info("到达购买上限，订单生成失败");
                redisTemplate.opsForValue().set(order.getActivityId()+"Activity"+order.getUserId()+"Order"+order.getHouseId()+order.getIsRegular(),"OrderFailUserToplimit");
                redisTemplate.opsForValue().increment(keyp[0],1);   //todo:若到达可够上限则回退房源状态，需测试
                return;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        OpUser user = iOpUserService.getById(order.getUserId());
        try {
            LocalDateTime now = LocalDateTime.now();
            order.setCreationDate(now);
            int i = opOrderMapper.insert(order);
            redisTemplate.opsForValue().set(order.getActivityId()+"Activity"+order.getUserId()+"Order"+order.getHouseId()+order.getIsRegular(),"success");
            if (i==1){
                OpHousingResources house = opHousingResourcesMapper.selectById(order.getHouseId());
                csSendSmsLogService.sendSms(user.getTel(), String.format("祝贺您(%s，%s)选房成功，房间信息:%s-%s-%s，%s", user.getName(), user.getTel(), house.getBuildingName(), house.getUnitName(), house.getRoomNum(),LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))),user.getName());    //选房成功短信提醒
                // 写入大屏
                iOpHousingResourcesService.sendWebSocket(order.getActivityId());
            }
        } catch (Exception e) {
            executeBackOrderLua(order); //写入数据库失败，购买数房源数同时回滚
            OpHousingResources house = opHousingResourcesMapper.selectById(order.getHouseId());
            csSendSmsLogService.sendSms(user.getTel(),house.getHouseName()+"选房失败",user.getName());
            e.printStackTrace();
        }
    }

    @Override
    public JSONResult getOrder(OpOrder order){
        String orderStatus = (String)redisTemplate.opsForValue().get(order.getActivityId()+"Activity"+order.getUserId()+"Order"+order.getHouseId()+order.getIsRegular());
        if ("success".equals(orderStatus)){
            QueryWrapper<OpOrder> orderWrapper = new QueryWrapper<>();
            orderWrapper.lambda().eq(OpOrder::getActivityId, order.getActivityId());
            orderWrapper.lambda().eq(OpOrder::getYn, GlobalConstants.Y);
            orderWrapper.lambda().eq(OpOrder::getUserId, order.getUserId());
            orderWrapper.lambda().eq(OpOrder::getHouseId, order.getHouseId());
            orderWrapper.lambda().eq(OpOrder::getIsRegular, order.getIsRegular());
            OpOrder opOrder = this.getOne(orderWrapper);
            redisTemplate.delete(order.getActivityId()+"Activity"+order.getUserId()+"Order"+order.getHouseId()+order.getIsRegular());
            JSONResult jsonResult = new JSONResult();
            jsonResult.setData(opOrder);
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
            return jsonResult;
        }else if ("OrderFailHousesSold".equals(orderStatus)){
            redisTemplate.delete(order.getActivityId()+"Activity"+order.getUserId()+"Order"+order.getHouseId()+order.getIsRegular());
            JSONResult jsonResult = new JSONResult();
            jsonResult.setData("房源已售");
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("房源已售");
            return jsonResult;
        }else if ("OrderFailUserToplimit".equals(orderStatus)){
            redisTemplate.delete(order.getActivityId()+"Activity"+order.getUserId()+"Order"+order.getHouseId()+order.getIsRegular());
            JSONResult jsonResult = new JSONResult();
            jsonResult.setData("客户购买上限");
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("客户购买上限");
            return jsonResult;
        }
        JSONResult jsonResult = new JSONResult();
        jsonResult.setData("wait");
        jsonResult.setCode(GlobalConstants.E_CODE);
        jsonResult.setMsg("wait");
        return jsonResult;
    }


    public void iMassageTest(){
        System.out.println("发送时间:" + new Date());
        String message = "测试延迟消息";
        messageService.send(MQConstant.MASSAGE_QUEUE_NAME, message, 60);
        message = "测试普通消息";
        messageService.send(MQConstant.MASSAGE_QUEUE_NAME, message);
    }

    @Override
    public Set<String> getCanOrderTest(String activityId){
        Set<String> keys = redisTemplate.keys(activityId+"Activity*");
        Set<String> keysValues = new CopyOnWriteArraySet<>();
        for (String key : keys) {
            keysValues.add(key+"-->"+redisTemplate.opsForValue().get(key));
        }
        //System.out.println(redisTemplate.opsForValue().multiGet(keys));
        return keysValues;
    }

    @Override
    public void delActivityKeyById(String activityId){
        Set<String> keys = redisTemplate.keys(activityId+"Activity*");
        redisTemplate.delete(keys);
    }


    @Override
    public IPage<OrderInfoVO> getOrder(Page page, OpUser opUser, String activityIdNow, String buildingName, String unitName, String roomNum, String isRegular, boolean isAdmin) {
        OrderInfoVO orderInfoVo = new OrderInfoVO();
        orderInfoVo.setBuildingName(buildingName);
        orderInfoVo.setName(opUser.getName());
        orderInfoVo.setTel(opUser.getTel());
        orderInfoVo.setSaleName(opUser.getSaleName());
        orderInfoVo.setUnitName(unitName);
        orderInfoVo.setRoomNum(roomNum);
        orderInfoVo.setActivityIdNow(activityIdNow);
        return opOrderMapper.getOrder(page, orderInfoVo,isRegular,String.valueOf(isAdmin));
    }

    @Override
    public List<OrderInfoVO> getOrderEx(OpUser opUser, String activityIdNow, String buildingName, String unitName, String roomNum, String isRegular, boolean isAdmin) {
        OrderInfoVO orderInfoVo = new OrderInfoVO();
        orderInfoVo.setBuildingName(buildingName);
        orderInfoVo.setName(opUser.getName());
        orderInfoVo.setTel(opUser.getTel());
        orderInfoVo.setSaleName(opUser.getSaleName());
        orderInfoVo.setUnitName(unitName);
        orderInfoVo.setRoomNum(roomNum);
        orderInfoVo.setActivityIdNow(activityIdNow);
        return opOrderMapper.getOrder(orderInfoVo, isRegular, String.valueOf(isAdmin));
    }
    @Override
    public List<HashMap<String, Object>> getMyOrderByactivityIdAndUser(String activityId, String userId,
                                                                       String isRegular) {
        return baseMapper.getMyOrderByactivityIdAndUser(activityId, userId, isRegular);
    }

    @Override
    public SalesOverview getOverview(String activityIdNow, String isRegular) {
        SalesOverview salesOverview = opOrderMapper.getOverview1(activityIdNow,isRegular);
        SalesOverview salesOverview2 = opOrderMapper.getOverview2(activityIdNow,isRegular);
        SalesOverview salesOverview3 = opOrderMapper.getOverview3(activityIdNow,isRegular);
        salesOverview.setTotalNum(salesOverview2.getTotalNum());
        salesOverview.setSoldNum(salesOverview2.getSoldNum());
        salesOverview.setTotalHouseResources(salesOverview2.getTotalHouseResources());
        salesOverview.setSoldHouseResources(salesOverview3.getSoldHouseResources());
        salesOverview.setNotSaleNum(salesOverview.getTotalNum() != null && salesOverview.getSoldNum() != null ? salesOverview.getTotalNum().intValue() - salesOverview.getSoldNum().intValue() : 0);
        return salesOverview;
    }

    @Override
    public IPage<SaleDataVO> getSaleData(Page page, String roleName, String activityIdNow,String isRegular) {
        List<HashMap<String,Object>> maps=opOrderMapper.getSalesUserNum(roleName,activityIdNow,isRegular);
        IPage<SaleDataVO> saleData = opOrderMapper.getSaleData(page, roleName, activityIdNow,isRegular);
        List<SaleDataVO> records = saleData.getRecords();
        for (SaleDataVO record : records) {
            for (HashMap<String, Object> map : maps) {
                Set<String> strings = map.keySet();
                for (String string : strings) {
                    record.getId().equals(string);
                    record.setSalesUserNum(map.get(string).toString());
                }
            }
        }
        return saleData.setRecords(records);
    }
    @Override
    public List<SaleDataVO> getSaleDataEx(String roleName, String activityIdNow,String isRegular) {
        List<HashMap<String,Object>> maps=opOrderMapper.getSalesUserNum(roleName,activityIdNow,isRegular);
        List<SaleDataVO> saleData = opOrderMapper.getSaleData(roleName, activityIdNow,isRegular);
        for (SaleDataVO record : saleData) {
            for (HashMap<String, Object> map : maps) {
                Set<String> strings = map.keySet();
                for (String string : strings) {
                    record.getId().equals(string);
                    record.setSalesUserNum(map.get(string).toString());
                }
            }
        }
        return saleData;
    }

    @Override
    public Map<String,String> canBuyOrNot(String userId, String houseId,String activityId) {
        Map<String,String> map=new HashMap<>();
        QueryWrapper<OpUser> userQueryWrapper = new QueryWrapper<>();
        QueryWrapper<OpOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.eq("user_sync_id", userId);
        orderQueryWrapper.eq("yn",GlobalConstants.Y);
        orderQueryWrapper.eq("activity_id",activityId);
        userQueryWrapper.eq("cstguid", userId);
        userQueryWrapper.eq("yn",GlobalConstants.Y);
        userQueryWrapper.eq("activity_id",activityId);
        List<OpOrder> opOrders = opOrderMapper.selectList(orderQueryWrapper);
        List<OpUser> opUserList = opUserMapper.selectList(userQueryWrapper);
        //用户已经无可选房数 不可购买
        if ((opUserList.get(0).getSelectCount()-opOrders.size())<=0){
            map.put("userCanBuy","fail");
            return map;
        }
        QueryWrapper<OpOrder> orderQueryWrapper1=new QueryWrapper<>();
        orderQueryWrapper1.eq("house_sync_id",houseId);
        orderQueryWrapper1.eq("yn",GlobalConstants.Y);
        orderQueryWrapper1.eq("activity_id",activityId);
        List<OpOrder> orderList = opOrderMapper.selectList(orderQueryWrapper1);
        //房源已在订单中存在 不可购买
        if (orderList.size() != 0){
            map.put("houseCanBuy","fail");
            return map;
        }
        return map;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONResult placeExcelOrder(List<OrderExcelImportVO> orderExcelImportVOList,String activityId){
        JSONResult<Object> jsonResult = new JSONResult<>();
        // 每次导入的订单为同一活动中
        OpActivity opActivity = opActivityMapper.selectById(activityId);
//        Iterator<OrderExcelImportVO> itr = orderExcelImportVOList.iterator();
        Set<OrderExcelImportVO> repeatDataSet=new HashSet<>();
        if (orderExcelImportVOList.size()==2){
            if(orderExcelImportVOList.get(0).getHouseId().equals(orderExcelImportVOList.get(1).getHouseId())){
                repeatDataSet.add(orderExcelImportVOList.get(0));
                repeatDataSet.add(orderExcelImportVOList.get(1));
            }
        }else {
            for (int i = 0; i < orderExcelImportVOList.size(); i++) {
                for (int  j  =  orderExcelImportVOList.size()  -   1 ; j  >  i; j --) {
                    if (orderExcelImportVOList.get(i).getHouseId().equals(orderExcelImportVOList.get(j).getHouseId())) {
                        repeatDataSet.add(orderExcelImportVOList.get(i));
                        repeatDataSet.add(orderExcelImportVOList.get(j));
                    }
                }
            }
        }
        if (repeatDataSet.size()!=0){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("订单导入房源有重复,请检查");
            return jsonResult;
        }
        //删除同活动下所有预制订单
        QueryWrapper<OpOrder> wrapper=new QueryWrapper<>();
        wrapper.eq("activity_id",activityId);
        //只删除预制订单  is_beforehand不为空
        wrapper.isNotNull("is_beforehand");
        opOrderMapper.delete(wrapper);
        for (OrderExcelImportVO orderExcelImportVO : orderExcelImportVOList) {

            Map<String,String>  map= canBuyOrNot(orderExcelImportVO.getUserId(), orderExcelImportVO.getHouseId(),activityId);
            if ("fail".equals(map.get("userCanBuy"))){
                //手动回滚
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("用户ID"+orderExcelImportVO.getUserId()+"已超出购房限制");
                return jsonResult;
            }
            if ("fail".equals(map.get("houseCanBuy"))){
                //手动回滚
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("房源ID"+orderExcelImportVO.getHouseId()+"已被购买");
                return jsonResult;
            }
            QueryWrapper<OpUser> userQueryWrapper=new QueryWrapper<>();
            userQueryWrapper.eq("cstguid",orderExcelImportVO.getUserId()).eq("activity_id",activityId).eq("yn",GlobalConstants.Y);
            List<OpUser> opUser = opUserMapper.selectList(userQueryWrapper);

            if (opUser.size()==0){
                //手动回滚
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("用户ID"+orderExcelImportVO.getUserId()+"不存在,请检查导入表");
                return jsonResult;
            }
            QueryWrapper<OpHousingResources> housingResourcesQueryWrapper=new QueryWrapper<>();
            housingResourcesQueryWrapper.eq("house_sync_id",orderExcelImportVO.getHouseId()).eq("activity_id",activityId).eq("yn",GlobalConstants.Y);
            OpHousingResources opHousingResources = opHousingResourcesMapper.selectOne(housingResourcesQueryWrapper);
            if (opHousingResources==null){
                //手动回滚
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("房源ID"+orderExcelImportVO.getUserId()+"不存在,请检查导入表");
                return jsonResult;
            }
            OpOrder opOrder=new OpOrder();
            opOrder.setUserId(opUser.get(0).getId().toString());
            opOrder.setUserName(opUser.get(0).getName());
            opOrder.setHouseId(opHousingResources.getId().toString());
            opOrder.setHouseName(opHousingResources.getHouseName());
            opOrder.setYn("y");
            opOrder.setActivityId(activityId);
            opOrder.setProjectId(opActivity.getProjectId());
            opOrder.setIsBeforehand("h");
            opOrder.setIsRegular("y");
            opOrder.setCreatedBy("ExcelImport");
            //导入订单的时间 是当前时间还是 给定时间
            opOrder.setCreationDate(LocalDateTime.now());
            opOrder.setUserSyncId(opUser.get(0).getCstguid());
            opOrder.setHouseSyncId(opHousingResources.getHouseSyncId());
            opOrderMapper.insert(opOrder);
        }
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())){
//            massageJobService.saveBeforehandOrderJob(opActivity);
            opOrderService.initCanOrderBySql(activityId);
        }

        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("导入成功!");
        return jsonResult;
    }

    @Override
    public void setBeforeOrderShow(String activityId) {
        opOrderMapper.setBeforeOrderShow(activityId);
    }

    @Override
    public List<SalesOrderVO> getSalesOrder(String activityId) {
        return opOrderMapper.getSalesOrder(activityId);
    }

    @Override
    public IPage<HouseTypeList> getHouseTypeList(Page page, String activityIdNow,String isRegular) {
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMaximumFractionDigits(2);
        IPage<HouseTypeList> iPage = opOrderMapper.getHouseTypeList(page, activityIdNow,isRegular);
        List<HouseTypeList> houseTypeLists = iPage.getRecords();
        //通过户型名 用已售数除以户型总数 得出去化率*100 未加%
        List<HashMap<String, Object>> allHouseTypeNum = opOrderMapper.getAllHouseTypeNum(activityIdNow);
        for (HouseTypeList houseTypeList : houseTypeLists) {
            for (HashMap<String, Object> stringDoubleHashMap : allHouseTypeNum) {
                if (houseTypeList.getTypeName().equals(stringDoubleHashMap.get("typeName"))){
                    houseTypeList.setPercentage(Double.parseDouble(nf.format((Double.parseDouble(houseTypeList.getSalesNum())/Double.parseDouble(stringDoubleHashMap.get("Num").toString()))*100)));
                }
                if (houseTypeList.getPercentage()==null){
                    houseTypeList.setPercentage(0d);
                }
            }
        }
        iPage.setRecords(houseTypeLists);
        return iPage;
    }
    @Override
    public List<HouseTypeList> getHouseTypeListEx(String activityIdNow,String isRegular) {
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMaximumFractionDigits(2);
        List<HouseTypeList>  houseTypeLists= opOrderMapper.getHouseTypeList(activityIdNow,isRegular);
        //通过户型名 用已售数除以户型总数 得出去化率*100 未加%
        List<HashMap<String, Object>> allHouseTypeNum = opOrderMapper.getAllHouseTypeNum(activityIdNow);
        for (HouseTypeList houseTypeList : houseTypeLists) {
            for (HashMap<String, Object> stringDoubleHashMap : allHouseTypeNum) {
                if (houseTypeList.getTypeName().equals(stringDoubleHashMap.get("typeName"))){
                    houseTypeList.setPercentage(Double.parseDouble(nf.format((Double.parseDouble(houseTypeList.getSalesNum())/Double.parseDouble(stringDoubleHashMap.get("Num").toString()))*100)));
                }
                if (houseTypeList.getPercentage()==null){
                    houseTypeList.setPercentage(0d);
                }
            }
        }

        return houseTypeLists;
    }

	@Override
	public HashMap<String, Object> getUserFavoriteAndActivityFavorite(HashMap<String, Object> paramMap) {
		return baseMapper.getUserFavoriteAndActivityFavorite(paramMap);
	}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteOrder(List<String> idList,String isRegular) {
        QueryWrapper<OpOrder> wrapper=new QueryWrapper<>();
        wrapper.eq("is_regular",isRegular);
        List<OpOrder> opOrders = opOrderMapper.selectBatchIds(idList);

//        for (OpOrder opOrder : opOrders) {
//            if (!isRegular.equals(opOrder.getIsRegular())){
//                opOrders.remove(opOrder);
//            }
//        }
        //判断订单是否预制
        Iterator<OpOrder> itr = opOrders.iterator();
        while (itr.hasNext()) {
            if (!isRegular.equals(itr.next().getIsRegular())){
                itr.remove();
            }
        }
        List<Integer> userId=new ArrayList<>();
        try {
        for (OpOrder opOrder : opOrders) {
            OpOrderGarbage opOrderGarbage=new OpOrderGarbage();
            //设置YN值为n
            opOrderGarbage.setYn(GlobalConstants.N);
            opOrderGarbage.setProjectId(opOrder.getProjectId());
            opOrderGarbage.setActivityId(opOrder.getActivityId());
            opOrderGarbage.setUserId(opOrder.getUserId());
            opOrderGarbage.setUserName(opOrder.getUserName());
            opOrderGarbage.setHouseId(opOrder.getHouseId());
            opOrderGarbage.setHouseName(opOrder.getHouseName());
            opOrderGarbage.setIsRegular(opOrder.getIsRegular());
            opOrderGarbage.setStatus(opOrder.getStatus());
            opOrderGarbage.setLastUpdateBy(ThreadLocalUtils.getUserName());
            opOrderGarbage.setLastUpdateDate(LocalDateTime.now());
            //删除order表中数据
            opOrderMapper.deleteById(opOrder.getId());
            //在OrderGarbage表中添加已删除的数据
            opOrderGarbageMapper.insert(opOrderGarbage);
            userId.add(Integer.parseInt(opOrder.getUserId()));
            }
        }catch (Exception e){
            //手动回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return "订单删除失败!";
        }
        //查询订单手机号和用户名
        List<OpUser> opUserList = opUserMapper.selectBatchIds(userId);
        for (OpUser opUser : opUserList) {
            List<OrderInfoVO> records = this.getOrderEx(opUser, opUser.getActivityId(), null, null, null, isRegular,true);

            System.out.println("records = " + records);
            for (OrderInfoVO record : records) {
                if (!"h".equals(record.getIsBeforehand())){
                    if (record.getCstguid().equals(opUser.getCstguid())) {
                        csSendSmsLogService.sendSms(opUser.getTel(), "订单已被取消,房间信息:" + record.getBuildingName() + record.getUnitName() + "-" + record.getRoomNum(), opUser.getName());
                    }
                }
            }
        }
        return "SUCCESS";
    }

	@Override
	public List<OpOrder> selectBatchIds(List<String> idList) {
		return opOrderMapper.selectBatchIds(idList);
	}


    /**
     * 接口回传
     * @param followUpContent
     * @param opUser
     * @return
     */
	@Override
    public JSONResult  returnData(String followUpContent, OpUser opUser){
        HttpHeaders headers = new HttpHeaders();
        restTemplate.getMessageConverters().add(new ShareinterJson2HttpMessageConverter());
        Param param=new Param();
        List<CustomerFollowData> list=new ArrayList<>();
        if ("login".equals(followUpContent)){
            QueryWrapper<OpUser> wrapper=new QueryWrapper<>();
            wrapper.eq("tel",opUser.getTel()).eq("activity_id",opUser.getActivityId()).eq("yn","y");
            OpUser user = iOpUserService.getOne(wrapper);
            System.out.println("user = " + user);
            CustomerFollowData customerFollowData=new CustomerFollowData();
            customerFollowData.setCustomerID(user.getCstguid());
            customerFollowData.setProjectID(user.getProjectId());
            customerFollowData.setFollowUpUserID(user.getSaleId());
            customerFollowData.setFollowUpContent(followUpContent);
            customerFollowData.setFollowUpDate(LocalDateTimeUtils.localDateTimeToDate(user.getLastLoginDate()));
            list.add(customerFollowData);
            param.setCustomerFollowData(list);
        }
        JsonRootBean jsonRootBean=new JsonRootBean();
        jsonRootBean.setParam(param);
        Object o = JSON.toJSONString(jsonRootBean);

        HttpEntity<String> requestEntity = new HttpEntity<String>( o.toString(), headers);
        ResponseEntity<CustomerFollowRootBean> customerFollowRootBean = restTemplate.postForEntity(ConstHOST + "/FrameWeb/FrameService/Api.ashx?option=func&funcid=mShareOnlineCustomerFollowUpList_Insert", requestEntity, CustomerFollowRootBean.class);
        //更改用户标记
        opUser.setFirstLogin(1);
        iOpUserService.updateById(opUser);

        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(customerFollowRootBean.getBody().getErrcode());
        jsonResult.setMsg(customerFollowRootBean.getBody().getErrmsg());
        return jsonResult;
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:12 2022/8/10
     * @param activityId    活动id
     * @param createType    生成类型：1--正式订单；2--模拟订单；3--全部订单
     * @return java.lang.Object
     * @description // TODO 根据活动id，初始化生成房源白名单下的 订单信息，模拟订单和正式订单均生成
     **/
    @Override
    public Object initHouseResourceWhiteOrder(String activityId, int createType) {
        // 获取活动详情
        OpActivity opActivity = activityService.getById(activityId);
        // 校验
        if (null == opActivity) {
            log.error("初始生成房源白名单订单数据失败，失败原因为：" + "活动不存在！");
            return "初始生成房源白名单订单数据失败，失败原因为：" + "活动不存在！";
        }

        // 获取到该活动下所有的 白名单数据
        List<UserHouseResourceDTO> dataList = opOrderMapper.selectUserHourseResourceListByActivityId(activityId);
        // 校验
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("初始生成房源白名单订单数据完成，生成结果为：0条！");
            return "初始生成房源白名单订单数据完成，生成结果为：0条！";
        }

        // 是 模拟订单n  还是   正式订单y
        String regular = "y";
        int createNum = 0;
        // 校验 生成类型：1--正式订单；2--模拟订单；3--全部订单
        if (1 == createType) {
            createNum = convertOrder(opActivity, dataList, "y", createNum);
        } else if (2 == createType) {
            createNum = convertOrder(opActivity, dataList, "n", createNum);
        } else {
            createNum = convertOrder(opActivity, dataList, "y", createNum);
            createNum = convertOrder(opActivity, dataList, "n", createNum);
        }
        log.info("初始生成房源白名单订单数据完成，生成结果为：" + createNum + "条！");
        return "初始生成房源白名单订单数据完成，生成结果为：" + createNum + "条！";
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:57 2023/10/23
     * @param activityId        活动id
     * @param formatFlag        是否正式活动
     * @description // TODO 更改订单数据并推送大屏
     **/
    @Override
    public void updateOrderData(String activityId, boolean formatFlag) {
        if (formatFlag) {
            // 正式订单
            opActivityMapper.updateOrderByData(activityId, 0, "y");
        } else {
            // 模拟订单
            opActivityMapper.updateOrderByData(activityId, 0, "n");
        }
        log.info("--------------------定时推送MQ开始：" + new Date() + "活动id：" + activityId);
        // 写入大屏数据
        iOpHousingResourcesService.sendWebSocket(activityId);
        log.info("--------------------定时推送MQ结束：" + new Date() + "活动id：" + activityId);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:30 2023/10/23
     * @param opActivity    活动对象
     * @param dataList      房源白名单列表
     * @param regular       是 模拟订单n  还是   正式订单y
     * @param createNum     创建的数量
     * @return int          返回的数量
     * @description // TODO 私有化生成房源白名单订单方法
     **/
    public int convertOrder(OpActivity opActivity, List<UserHouseResourceDTO> dataList, String regular, int createNum) {
        // 定义订单对象集合
        List<OpOrder> insertList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        // 定义 order对象
        OpOrder order = null;
        // 遍历 生成订单数据
        for (UserHouseResourceDTO dto : dataList) {
            order = new OpOrder();
            order.setProjectId(opActivity.getProjectId());
            order.setActivityId(opActivity.getId().toString());
            order.setUserId(dto.getUserId().toString());
            order.setUserName(dto.getUserName());
            order.setHouseId(dto.getHouseResourceId().toString());
            order.setHouseName(dto.getHourseName());
            // 赋值 是 模拟订单n  还是   正式订单y
            order.setIsRegular(regular);
            order.setYn("y");
            order.setCreationDate(now);
            order.setUserSyncId(dto.getUserSyncId());
            order.setHouseSyncId(dto.getHourseSyncId());
            order.setIsReturn("n");
            // 有效标识设置为无效
            order.setEffectiveFlag(1);

            // 处理数据
            try {
                // 给缓存数据赋值
                String result = executeCanOrderLua(order);
                if ("success".equals(result)){
                    insertList.add(order);
                    createNum++;
                    log.info("本次生成房源白名单成功！生成用户id为：{}, 房源id为：{}, 订单类型为：{}",
                            dto.getUserId(), dto.getHouseResourceId(), regular);
                } else {
                    log.info("本次生成房源白名单非success！生成用户id为：{}, 房源id为：{}, 订单类型为：{}, 原因为：{}",
                            dto.getUserId(), dto.getHouseResourceId(), regular, result);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.info("本次生成房源白名单错误异常！生成用户id为：{}, 房源id为：{}, 订单类型为：{}",
                        dto.getUserId(), dto.getHouseResourceId(), regular);
                executeBackOrderLua(order);     //订单队列生产失败 redis可购房源信息回滚
            }
        }
        // 批量入库
        if (CollectionUtils.isNotEmpty(insertList)) {
            opOrderMapper.insertList(insertList);
        }
        return createNum;
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 15:47 2022/8/10
     * @param opActivity    活动对象
     * @param dataList      房源白名单列表
     * @param regular       是 模拟订单n  还是   正式订单y
     * @param createNum     创建的数量
     * @return int          返回的数量
     * @description // TODO 私有化生成房源白名单订单方法
     **/
    public int backconvertOrder(OpActivity opActivity, List<UserHouseResourceDTO> dataList, String regular, int createNum) {
        // 定义 order对象
        OpOrder order = null;
        // 遍历 生成订单数据
        for (UserHouseResourceDTO dto : dataList) {
            order = new OpOrder();
            order.setProjectId(opActivity.getProjectId());
            order.setActivityId(opActivity.getId().toString());
            order.setHouseId(dto.getHouseResourceId().toString());
            order.setHouseName(dto.getHourseName());
            order.setHouseSyncId(dto.getHourseSyncId());
            order.setUserId(dto.getUserId().toString());
            order.setUserName(dto.getUserName());
            order.setUserSyncId(dto.getUserSyncId());
            // 赋值 是 模拟订单n  还是   正式订单y
            order.setIsRegular(regular);

            // 处理数据
            try {
                // 给缓存数据赋值
                String result = executeCanOrderLua(order);
                if ("success".equals(result)){
                    rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
                    rabbitTemplate.setExchange(MQConstant.ORDER_EXCHANGE);
                    rabbitTemplate.setRoutingKey(MQConstant.ORDER_KEY);
                    // 加入订单队列
                    order.setCreationDate(LocalDateTime.now());
                    byte[] bytes=getBytesFromObject(order);
                    Message message= MessageBuilder.withBody(bytes).setDeliveryMode(MessageDeliveryMode.PERSISTENT).build();
                    message.getMessageProperties().setHeader(AbstractJavaTypeMapper.DEFAULT_CONTENT_CLASSID_FIELD_NAME, MessageProperties.CONTENT_TYPE_JSON);
                    rabbitTemplate.convertAndSend(message);
                    createNum++;
                    log.info("本次生成房源白名单成功！生成用户id为：{}, 房源id为：{}, 订单类型为：{}",
                            dto.getUserId(), dto.getHouseResourceId(), regular);
                } else {
                    log.info("本次生成房源白名单非success！生成用户id为：{}, 房源id为：{}, 订单类型为：{}, 原因为：{}",
                            dto.getUserId(), dto.getHouseResourceId(), regular, result);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.info("本次生成房源白名单错误异常！生成用户id为：{}, 房源id为：{}, 订单类型为：{}",
                        dto.getUserId(), dto.getHouseResourceId(), regular);
                executeBackOrderLua(order);     //订单队列生产失败 redis可购房源信息回滚
            }
        }
        return createNum;
    }

    /**
     * 每3小时执行一次 查询正式订单 标记为未返回的订单 执行接口
     */
//    @Scheduled(cron = "0 0 */3 * * ?")
    public void returnOrderData(){
        boolean lock = false;
        try {
            lock = redisTemplate.opsForValue().setIfAbsent("returnOrderDataKey", "returnOrderData");
            if (lock) {
        QueryWrapper<OpOrder> wrapper=new QueryWrapper<>();
        wrapper.eq("is_return",GlobalConstants.N).eq("is_regular",GlobalConstants.Y);
        List<OpOrder> orderList = this.list(wrapper);
        HttpHeaders headers = new HttpHeaders();
        restTemplate.getMessageConverters().add(new ShareinterJson2HttpMessageConverter());
        Param param=new Param();
        List<CustomerFollowData> list=new ArrayList<>();
        for (OpOrder order : orderList) {
            CustomerFollowData customerFollowData = new CustomerFollowData();
            customerFollowData.setProjectID(order.getProjectId());
            customerFollowData.setFollowUpDate(LocalDateTimeUtils.localDateTimeToDate(order.getCreationDate()));
            customerFollowData.setFollowUpContent("closeActivity");
            customerFollowData.setCustomerID(order.getUserSyncId());
            customerFollowData.setFollowUpUserID(order.getHouseSyncId());
            list.add(customerFollowData);
            order.setIsReturn(GlobalConstants.Y);
            opOrderService.updateById(order);
        }
        if (list.size()!=0){
            param.setCustomerFollowData(list);
            JsonRootBean jsonRootBean=new JsonRootBean();
            jsonRootBean.setParam(param);
            Object o = JSON.toJSONString(jsonRootBean);

            HttpEntity<String> requestEntity = new HttpEntity<String>( o.toString(), headers);
            ResponseEntity<CustomerFollowRootBean> customerFollowRootBean = restTemplate.postForEntity(ConstHOST + "/FrameWeb/FrameService/Api.ashx?option=func&funcid=mShareOnlineCustomerFollowUpList_Insert", requestEntity, CustomerFollowRootBean.class);


         }
            }
        } finally {
            if (lock) {
                redisTemplate.delete("returnOrderData");
            }
        }

        }


    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/6 16:33
     * @Param        activity 活动对象
     * @Return       void
     * @Description  TODO   刷新该活动下用户和定时任务数据
     **/
    @Override
    public void refreshActivityDataById(OpActivity activity) {
        String activityId = activity.getId().toString();
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(3);
        paramMap.put("activityId", activityId);
        // 校验当前活动类型
        if (null != activity.getCheckMode() && 1 == activity.getCheckMode().intValue()) {
            // 选房模式
            // 获取全量用户
            LambdaQueryWrapper<OpUser> lambda = new QueryWrapper<OpUser>().lambda();
            lambda.eq(OpUser::getActivityId, activityId);
            lambda.eq(OpUser::getYn, "y");
            lambda.orderByAsc(OpUser::getUserSort);
            List<OpUser> opUserList = opUserMapper.selectList(lambda);
            // 校验
            if (CollectionUtils.isNotEmpty(opUserList)) {
                // 获取该活动所有批次
                List<OpBatch> batchList = batchService.selectAllBatchListByActivityId(activityId);
                // 定义每个批次对应最小序号的人员id
                Map<Integer, BatchConditionVo> minBatchMap = Maps.newHashMapWithExpectedSize(batchList.size());
                Map<Integer, BatchConditionVo> maxBatchMap = Maps.newHashMapWithExpectedSize(batchList.size());
                if (CollectionUtils.isNotEmpty(batchList)) {
                    // 存在批次数据
                    // 将所有用户按照批次分组
                    Map<String, List<OpUser>> batchUserMap = opUserList.stream().filter(s -> StringUtils.isNotBlank(s.getBatchNameStr())).collect(Collectors.groupingBy(OpUser::getBatchNameStr));
                    // 遍历批次匹配数据
                    for (OpBatch batch : batchList) {
                        // 定义该批次所有用户集合
                        List<OpUser> thisUserList = Lists.newArrayList();
                        Iterator<Map.Entry<String, List<OpUser>>> iterator = batchUserMap.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, List<OpUser>> next = iterator.next();
                            // 拆分key
                            List<String> list = Arrays.asList(next.getKey().split(","));
                            if (list.contains(batch.getBatchName())) {
                                // 如果用户可以看批次A、B、C，那么用户归类到优先级最高的那个批次
                                thisUserList.addAll(next.getValue());
                                // 移除该用户元素
                                iterator.remove();
                            }
                        }
                        // 校验
                        if (CollectionUtils.isNotEmpty(thisUserList)) {
                            // 取最小序号
                            int minValue = Integer.MAX_VALUE;
                            OpUser minUser = null;
                            int maxValue = Integer.MIN_VALUE;
                            OpUser maxUser = null;
                            for (OpUser opUser : thisUserList) {
                                opUser.setUserSort(null != opUser.getUserSort() ? opUser.getUserSort() : 0);
                                opUser.setSelectCount(null != opUser.getSelectCount() ? opUser.getSelectCount() : 0);
                                // 比较最小
                                if (opUser.getUserSort() < minValue) {
                                    minValue = opUser.getUserSort();
                                    minUser = opUser;
                                } else if (opUser.getUserSort() == minValue) {
                                    // 相同的时候比较可选房数量
                                    if (opUser.getUserSort() * opUser.getSelectCount() < minUser.getUserSort() * minUser.getSelectCount()) {
                                        minValue = opUser.getUserSort();
                                        minUser = opUser;
                                    }
                                }
                                // 比较最大
                                if (opUser.getUserSort() > maxValue) {
                                    maxValue = opUser.getUserSort();
                                    maxUser = opUser;
                                } else if (opUser.getUserSort() == maxValue) {
                                    // 相同的时候比较可选房数量
                                    if (opUser.getUserSort() * opUser.getSelectCount() > maxUser.getUserSort() * maxUser.getSelectCount()) {
                                        maxValue = opUser.getUserSort();
                                        maxUser = opUser;
                                    }
                                }
                            }
                            minBatchMap.put(minUser.getId(), new BatchConditionVo(batch.getId(), batch.getPriorityLevel(), batch.getBatchName(), batch.getSyncFlag(), minUser.getId()));
                            maxBatchMap.put(maxUser.getId(), new BatchConditionVo(batch.getId(), batch.getPriorityLevel(), batch.getBatchName(), batch.getSyncFlag(), maxUser.getId()));
                        }
                    }
                }

                // 校验是否有模拟活动
                if (null != activity.getSimulationStart() && null != activity.getSimulationEnd()) {
                    // 获取选房区间
                    List<BuyTimeVO> buyTimeVOList = convertBuyTimeByTimeStr(activity.getCheckTimeStr());
                    if (CollectionUtils.isNotEmpty(buyTimeVOList)) {
                        List<UserDto> userDtoList = Lists.newArrayListWithCapacity(opUserList.size());
                        for (OpUser opUser : opUserList) {
                            userDtoList.add(new UserDto(opUser.getId(), opUser.getName(),
                                    opUser.getSelectCount() != null ? opUser.getSelectCount() : 0,
                                    opUser.getUserSort() != null ? opUser.getUserSort() : 0, opUser.getTel()));
                        }
                        // 重新排序
                        userDtoList = userDtoList.stream().sorted(Comparator.comparing(UserDto::getUserSort)).collect(Collectors.toList());
                        List<UserDto> newUserDtoList = convertUserDtoList(buyTimeVOList, userDtoList, activity.getCheckMinutes());
                        // 赋值模拟时间
                        for (Map.Entry<Integer, BatchConditionVo> entry : minBatchMap.entrySet()) {
                            for (UserDto userDto : newUserDtoList) {
                                if (userDto.getId().intValue() == entry.getKey().intValue()) {
                                    // 赋值模拟时间
                                    BatchConditionVo conditionVo = entry.getValue();
                                    conditionVo.setSimulationBeginDate(userDto.getBeginDate());
                                    minBatchMap.put(entry.getKey(), conditionVo);
                                    break;
                                }
                            }
                        }
                        for (Map.Entry<Integer, BatchConditionVo> entry : maxBatchMap.entrySet()) {
                            for (UserDto userDto : newUserDtoList) {
                                if (userDto.getId().intValue() == entry.getKey().intValue()) {
                                    // 赋值模拟时间
                                    BatchConditionVo conditionVo = entry.getValue();
                                    conditionVo.setSimulationEndDate(userDto.getEndDate());
                                    maxBatchMap.put(entry.getKey(), conditionVo);
                                    break;
                                }
                            }
                        }
                        // 写入缓存
                        redisTemplate.opsForValue().set(activityId + "Activity_ALL_V_User", newUserDtoList);
                    }
                }
                // 校验是否有正式活动
                if (null != activity.getFormalStart() && null != activity.getFormalEnd()) {
                    // 获取选房区间
                    List<BuyTimeVO> buyTimeVOList = convertBuyTimeByTimeStr(activity.getFormalCheckTimeStr());
                    if (CollectionUtils.isNotEmpty(buyTimeVOList)) {
                        List<UserDto> userDtoList = Lists.newArrayListWithCapacity(opUserList.size());
                        for (OpUser opUser : opUserList) {
                            userDtoList.add(new UserDto(opUser.getId(), opUser.getName(),
                                    opUser.getSelectCount() != null ? opUser.getSelectCount() : 0,
                                    opUser.getUserSort() != null ? opUser.getUserSort() : 0, opUser.getTel()));
                        }
                        // 重新排序
                        userDtoList = userDtoList.stream().sorted(Comparator.comparing(UserDto::getUserSort)).collect(Collectors.toList());
                        List<UserDto> newUserDtoList = convertUserDtoList(buyTimeVOList, userDtoList, activity.getCheckMinutes());
                        // 赋值正式时间
                        for (Map.Entry<Integer, BatchConditionVo> entry : minBatchMap.entrySet()) {
                            for (UserDto userDto : newUserDtoList) {
                                if (userDto.getId().intValue() == entry.getKey().intValue()) {
                                    // 赋值正式时间
                                    BatchConditionVo conditionVo = entry.getValue();
                                    conditionVo.setFormalBeginDate(userDto.getBeginDate());
                                    minBatchMap.put(entry.getKey(), conditionVo);
                                    break;
                                }
                            }
                        }
                        for (Map.Entry<Integer, BatchConditionVo> entry : maxBatchMap.entrySet()) {
                            for (UserDto userDto : newUserDtoList) {
                                if (userDto.getId().intValue() == entry.getKey().intValue()) {
                                    // 赋值正式时间
                                    BatchConditionVo conditionVo = entry.getValue();
                                    conditionVo.setFormalEndDate(userDto.getEndDate());
                                    maxBatchMap.put(entry.getKey(), conditionVo);
                                    break;
                                }
                            }
                        }
                        // 写入缓存
                        redisTemplate.opsForValue().set(activityId + "Activity_ALL_T_User", newUserDtoList);
                    }
                }

                // 校验
                if (CollectionUtils.isNotEmpty(batchList)) {
                    // 清空该活动所有批次任务
                    quartzService.deleteJobsByGroup("Activity_" + activityId);
                    // 校验是否所有批次都没有人员
                    if (minBatchMap.size() == 0) {
                        // 只设置两个任务，模拟开始时和正式开始时
                        List<String> allBatchNameList = batchList.stream().map(OpBatch::getBatchName).collect(Collectors.toList());
                        paramMap.put("batchList", allBatchNameList);
                        // 校验是否有模拟活动
                        if (null != activity.getSimulationStart()) {
                            paramMap.put("formalFlag", false);
                            Date startDate = LocalDateTimeUtils.localDateTimeToDate(activity.getSimulationStart());
                            quartzService.addSimpleJobs(UpdateBatchQTask.class, "Batch_V_" + activityId + "_All", "Activity_" + activityId,
                                    startDate, paramMap, "任务类型：模拟, 批次类型：" + "全部" + ", 执行时间：" + DateUtils.getDateString(startDate));
                        }
                        // 校验是否有模拟活动
                        if (null != activity.getFormalStart()) {
                            paramMap.put("formalFlag", true);
                            Date startDate = LocalDateTimeUtils.localDateTimeToDate(activity.getFormalStart());
                            quartzService.addSimpleJobs(UpdateBatchQTask.class, "Batch_T_" + activityId + "_All", "Activity_" + activityId,
                                    startDate, paramMap, "任务类型：正式, 批次类型：" + "全部" + ", 执行时间：" + DateUtils.getDateString(startDate));
                        }
                    } else {
                        // 定义批次名称
                        List<String> simulationNameList = Lists.newArrayListWithCapacity(batchList.size());
                        List<String> formalNameList = Lists.newArrayListWithCapacity(batchList.size());
                        List<BatchConditionVo> minValues = new ArrayList<>(minBatchMap.values());
                        List<BatchConditionVo> maxValues = new ArrayList<>(maxBatchMap.values());
                        // 定义最大时间
                        Date simulationBigDate = null;
                        Date formalBigDate = null;
                        for (OpBatch batch : batchList) {
                            // 校验是否同步
                            if (batch.getSyncFlag().intValue() == 0) {
                                // 同步
                                boolean simulationNeedAddFlag = true;
                                boolean formalNeedAddFlag = true;
                                // 匹配
                                for (BatchConditionVo vo : maxValues) {
                                    if (batch.getBatchName().equals(vo.getBatchName())) {
                                        // 获取该批次最小用户对象
                                        BatchConditionVo minVo = null;
                                        for (BatchConditionVo thisVo : minValues) {
                                            if (batch.getBatchName().equals(thisVo.getBatchName())) {
                                                minVo = thisVo;
                                                break;
                                            }
                                        }
                                        // 校验是否有模拟活动
                                        if (null != activity.getSimulationStart()) {
                                            // 校验最小用户
                                            if (simulationNameList.size() > 0) {
                                                // 校验
                                                if (null != minVo.getSimulationBeginDate()) {
                                                    // 本批次开始任务
                                                    paramMap.put("formalFlag", false);
                                                    paramMap.put("batchList", simulationNameList);
                                                    quartzService.addSimpleJobs(UpdateBatchQTask.class, "Batch_V_" + activityId + "_" + batch.getBatchName() + "_Begin", "Activity_" + activityId,
                                                            minVo.getSimulationBeginDate(), paramMap, "任务类型：模拟, 批次类型：" + batch.getBatchName() + ", 开始执行时间：" + DateUtils.getDateString(minVo.getSimulationBeginDate()));
                                                    // 重新置为空
                                                    simulationNeedAddFlag = false;
                                                    simulationNameList = Lists.newArrayListWithCapacity(batchList.size());
                                                }
                                            }
                                            // 校验最大用户
                                            if (null != vo.getSimulationEndDate()) {
                                                if (null == simulationBigDate || simulationBigDate.before(vo.getSimulationEndDate())) {
                                                    simulationBigDate = vo.getSimulationEndDate();
                                                }
                                                // 本批次结束任务
                                                paramMap.put("formalFlag", false);
                                                paramMap.put("batchList", Lists.newArrayList(batch.getBatchName()));
                                                quartzService.addSimpleJobs(UpdateBatchQTask.class, "Batch_V_" + activityId + "_" + batch.getBatchName() + "_End", "Activity_" + activityId,
                                                        vo.getSimulationEndDate(), paramMap, "任务类型：模拟, 批次类型：" + batch.getBatchName() + ", 结束执行时间：" + DateUtils.getDateString(vo.getSimulationEndDate()));
                                                simulationNeedAddFlag = false;
                                            } else {
                                                simulationNameList.add(batch.getBatchName());
                                            }
                                        }
                                        // 校验是否有正式活动
                                        if (null != activity.getFormalStart()) {
                                            // 校验最小用户
                                            if (formalNameList.size() > 0) {
                                                // 校验
                                                if (null != minVo.getFormalBeginDate()) {
                                                    // 本批次开始任务
                                                    paramMap.put("formalFlag", true);
                                                    paramMap.put("batchList", formalNameList);
                                                    quartzService.addSimpleJobs(UpdateBatchQTask.class, "Batch_T_" + activityId + "_" + batch.getBatchName() + "_Begin", "Activity_" + activityId,
                                                            minVo.getFormalBeginDate(), paramMap, "任务类型：正式, 批次类型：" + batch.getBatchName() + ", 开始执行时间：" + DateUtils.getDateString(minVo.getFormalBeginDate()));
                                                    // 重新置为空
                                                    formalNeedAddFlag = false;
                                                    formalNameList = Lists.newArrayListWithCapacity(batchList.size());
                                                }
                                            }
                                            // 校验最大用户
                                            if (null != vo.getFormalEndDate()) {
                                                if (null == formalBigDate || formalBigDate.before(vo.getFormalEndDate())) {
                                                    formalBigDate = vo.getFormalEndDate();
                                                }
                                                // 本批次结束任务
                                                paramMap.put("formalFlag", true);
                                                paramMap.put("batchList", Lists.newArrayList(batch.getBatchName()));
                                                quartzService.addSimpleJobs(UpdateBatchQTask.class, "Batch_T_" + activityId + "_" + batch.getBatchName() + "_End", "Activity_" + activityId,
                                                        vo.getFormalEndDate(), paramMap, "任务类型：正式, 批次类型：" + batch.getBatchName() + ", 结束执行时间：" + DateUtils.getDateString(vo.getFormalEndDate()));
                                                formalNeedAddFlag = false;
                                            } else {
                                                formalNameList.add(batch.getBatchName());
                                            }
                                        }
                                        break;
                                    }
                                }
                                // 本批次未配置用户
                                // 校验是否有模拟活动
                                if (null != activity.getSimulationStart() && simulationNeedAddFlag) {
                                    simulationNameList.add(batch.getBatchName());
                                }
                                // 校验是否有正式活动
                                if (null != activity.getFormalStart() && formalNeedAddFlag) {
                                    formalNameList.add(batch.getBatchName());
                                }
                            }
                        }
                        // 校验模拟批次
                        if (simulationNameList.size() > 0) {
                            // 执行
                            if (null == simulationBigDate) {
                                simulationBigDate = LocalDateTimeUtils.localDateTimeToDate(activity.getSimulationStart());
                            }
                            // 本批次最终任务
                            paramMap.put("formalFlag", false);
                            paramMap.put("batchList", simulationNameList);
                            quartzService.addSimpleJobs(UpdateBatchQTask.class, "Batch_V_" + activityId + "_Final", "Activity_" + activityId,
                                    simulationBigDate, paramMap, "任务类型：模拟, 批次类型：" + "全部" + ", 最终执行时间：" + DateUtils.getDateString(simulationBigDate));
                        }
                        // 校验正式批次
                        if (formalNameList.size() > 0) {
                            // 执行
                            if (null == formalBigDate) {
                                formalBigDate = LocalDateTimeUtils.localDateTimeToDate(activity.getFormalStart());
                            }
                            // 本批次最终任务
                            paramMap.put("formalFlag", false);
                            paramMap.put("batchList", simulationNameList);
                            quartzService.addSimpleJobs(UpdateBatchQTask.class, "Batch_T_" + activityId + "_Final", "Activity_" + activityId,
                                    formalBigDate, paramMap, "任务类型：正式, 批次类型：" + "全部" + ", 最终执行时间：" + DateUtils.getDateString(formalBigDate));
                        }
                    }
                    return;
                }
            }
        }
        // 清空该活动所有批次任务
        quartzService.deleteJobsByGroup("Activity_" + activityId);
    }
//
//    /**
//     *
//     * <AUTHOR> <EMAIL>
//     * @Date         2025/5/6 16:33
//     * @Param        activity 活动对象
//     * @Return       void
//     * @Description  TODO   刷新该活动下用户和定时任务数据
//     **/
//    @Override
//    public void refreshActivityDataById(OpActivity activity) {
//        String activityId = activity.getId().toString();
//        // 校验当前活动类型
//        if (null != activity.getCheckMode() && 1 == activity.getCheckMode().intValue()) {
//            // 选房模式
//            // 获取全量用户
//            LambdaQueryWrapper<OpUser> lambda = new QueryWrapper<OpUser>().lambda();
//            lambda.eq(OpUser::getActivityId, activityId);
//            lambda.eq(OpUser::getYn, "y");
//            lambda.orderByAsc(OpUser::getUserSort);
//            List<OpUser> opUserList = opUserMapper.selectList(lambda);
//            // 校验
//            if (CollectionUtils.isNotEmpty(opUserList)) {
//                // 获取该活动所有批次
//                List<OpBatch> batchList = batchService.selectAllBatchListByActivityId(activityId);
//                // 定义每个批次对应最小序号的人员id
//                Map<Integer, BatchConditionVo> minBatchMap = Maps.newHashMapWithExpectedSize(batchList.size());
//                if (CollectionUtils.isNotEmpty(batchList)) {
//                    // 存在批次数据
//                    // 将所有用户按照批次分组
//                    Map<String, List<OpUser>> batchUserMap = opUserList.stream().filter(s -> StringUtils.isNotBlank(s.getBatchNameStr())).collect(Collectors.groupingBy(OpUser::getBatchNameStr));
//                    // 遍历批次匹配数据
//                    for (OpBatch batch : batchList) {
//                        // 定义该批次所有用户集合
//                        List<OpUser> thisUserList = Lists.newArrayList();
//                        Iterator<Map.Entry<String, List<OpUser>>> iterator = batchUserMap.entrySet().iterator();
//                        while (iterator.hasNext()) {
//                            Map.Entry<String, List<OpUser>> next = iterator.next();
//                            // 拆分key
//                            List<String> list = Arrays.asList(next.getKey().split(","));
//                            if (list.contains(batch.getBatchName())) {
//                                // 如果用户可以看批次A、B、C，那么用户归类到优先级最高的那个批次
//                                thisUserList.addAll(next.getValue());
//                                // 移除该用户元素
//                                iterator.remove();
//                            }
//                        }
//                        // 校验
//                        if (CollectionUtils.isNotEmpty(thisUserList)) {
//                            // 取最小序号
//                            int minValue = Integer.MAX_VALUE;
//                            OpUser minUser = null;
//                            for (OpUser opUser : thisUserList) {
//                                opUser.setUserSort(null != opUser.getUserSort() ? opUser.getUserSort() : 0);
//                                opUser.setSelectCount(null != opUser.getSelectCount() ? opUser.getSelectCount() : 0);
//                                // 比较
//                                if (opUser.getUserSort() < minValue) {
//                                    minValue = opUser.getUserSort();
//                                    minUser = opUser;
//                                } else if (opUser.getUserSort() == minValue) {
//                                    // 相同的时候比较可选房数量
//                                    if (opUser.getUserSort() * opUser.getSelectCount() < minUser.getUserSort() * minUser.getSelectCount()) {
//                                        minValue = opUser.getUserSort();
//                                        minUser = opUser;
//                                    }
//                                }
//                            }
//                            minBatchMap.put(minUser.getId(), new BatchConditionVo(batch.getId(), batch.getPriorityLevel(), batch.getBatchName(), batch.getSyncFlag()));
//                        }
//                    }
//                }
//
//                // 校验是否有模拟活动
//                if (null != activity.getSimulationStart() && null != activity.getSimulationEnd()) {
//                    // 获取选房区间
//                    List<BuyTimeVO> buyTimeVOList = convertBuyTimeByTimeStr(activity.getCheckTimeStr());
//                    if (CollectionUtils.isNotEmpty(buyTimeVOList)) {
//                        List<UserDto> userDtoList = Lists.newArrayListWithCapacity(opUserList.size());
//                        for (OpUser opUser : opUserList) {
//                            userDtoList.add(new UserDto(opUser.getId(), opUser.getName(),
//                                    opUser.getSelectCount() != null ? opUser.getSelectCount() : 0,
//                                    opUser.getUserSort() != null ? opUser.getUserSort() : 0));
//                        }
//                        // 重新排序
//                        userDtoList = userDtoList.stream().sorted(Comparator.comparing(UserDto::getUserSort)).collect(Collectors.toList());
//                        List<UserDto> newUserDtoList = convertUserDtoList(buyTimeVOList, userDtoList, activity.getCheckMinutes());
//                        // 赋值模拟时间
//                        for (Map.Entry<Integer, BatchConditionVo> entry : minBatchMap.entrySet()) {
//                            for (UserDto userDto : newUserDtoList) {
//                                if (userDto.getId().intValue() == entry.getKey().intValue()) {
//                                    // 赋值模拟时间
//                                    BatchConditionVo conditionVo = entry.getValue();
//                                    conditionVo.setSimulationBeginDate(userDto.getBeginDate());
//                                    minBatchMap.put(entry.getKey(), conditionVo);
//                                    break;
//                                }
//                            }
//                        }
//                        // 写入缓存
//                        redisTemplate.opsForValue().set(activityId + "Activity_ALL_V_User", newUserDtoList);
//                    }
//                }
//                // 校验是否有正式活动
//                if (null != activity.getFormalStart() && null != activity.getFormalEnd()) {
//                    // 获取选房区间
//                    List<BuyTimeVO> buyTimeVOList = convertBuyTimeByTimeStr(activity.getFormalCheckTimeStr());
//                    if (CollectionUtils.isNotEmpty(buyTimeVOList)) {
//                        List<UserDto> userDtoList = Lists.newArrayListWithCapacity(opUserList.size());
//                        for (OpUser opUser : opUserList) {
//                            userDtoList.add(new UserDto(opUser.getId(), opUser.getName(),
//                                    opUser.getSelectCount() != null ? opUser.getSelectCount() : 0,
//                                    opUser.getUserSort() != null ? opUser.getUserSort() : 0));
//                        }
//                        // 重新排序
//                        userDtoList = userDtoList.stream().sorted(Comparator.comparing(UserDto::getUserSort)).collect(Collectors.toList());
//                        List<UserDto> newUserDtoList = convertUserDtoList(buyTimeVOList, userDtoList, activity.getCheckMinutes());
//                        // 赋值正式时间
//                        for (Map.Entry<Integer, BatchConditionVo> entry : minBatchMap.entrySet()) {
//                            for (UserDto userDto : newUserDtoList) {
//                                if (userDto.getId().intValue() == entry.getKey().intValue()) {
//                                    // 赋值正式时间
//                                    BatchConditionVo conditionVo = entry.getValue();
//                                    conditionVo.setFormalBeginDate(userDto.getBeginDate());
//                                    minBatchMap.put(entry.getKey(), conditionVo);
//                                    break;
//                                }
//                            }
//                        }
//                        // 写入缓存
//                        redisTemplate.opsForValue().set(activityId + "Activity_ALL_T_User", newUserDtoList);
//                    }
//                }
//
//                // 校验
//                if (minBatchMap.size() > 0) {
//                    List<BatchConditionVo> values = new ArrayList<>(minBatchMap.values());
//                    // 按照优先级正序
//                    values = values.stream().sorted(Comparator.comparing(BatchConditionVo::getPriorityLevel)).collect(Collectors.toList());
//                    // 写入缓存
//                    redisTemplate.opsForValue().set(activityId + "Activity_ALL_BATCH_", values);
//                }
//            }
//        }
//    }
}
