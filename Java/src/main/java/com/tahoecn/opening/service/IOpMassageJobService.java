package com.tahoecn.opening.service;

import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpMassageJob;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 定时短信表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-25
 */
public interface IOpMassageJobService extends IService<OpMassageJob> {
    void saveMessageRemindJob(OpActivity activity, String jobType);
    void removeMessageRemindJob(OpActivity activity);
    void saveBeforehandOrderJob(OpActivity activity);
}
