package com.tahoecn.opening.service;

import com.tahoecn.opening.model.OpFavorite;

import java.util.HashMap;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 收藏 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
public interface IOpFavoriteService extends IService<OpFavorite> {

	List<HashMap<String, Object>> getMyFavoriteByactivityIdAndUser(String activityId, String userId, String isRegular);

	List<HashMap<String, Object>> getFavoriteFailByactivityIdAndUser(String activityId, String userId,
                                                                     String isRegular);

}
