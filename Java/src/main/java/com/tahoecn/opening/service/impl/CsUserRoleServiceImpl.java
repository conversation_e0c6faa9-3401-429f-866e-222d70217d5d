package com.tahoecn.opening.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import com.tahoecn.opening.mapper.CsUcOrgMapper;
import com.tahoecn.opening.mapper.CsUserRoleMapper;
import com.tahoecn.opening.model.CsUserRole;
import com.tahoecn.opening.model.vo.OrgInfoVo;
import com.tahoecn.opening.service.CsUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-16
 */
@Transactional
@Service
public class CsUserRoleServiceImpl extends ServiceImpl<CsUserRoleMapper, CsUserRole> implements CsUserRoleService {

    private static final Log log = LogFactory.get();


    @Autowired
    private CsUcOrgMapper csUcOrgMapper;


    @Override
    public List<OrgInfoVo> orgTree() {
        String json;
        // redis 没数据，重新查一遍，在放入redis，30分钟过期
        // 1. 查询全部组织
        List<OrgInfoVo> orgs = csUcOrgMapper.findAllOrgInfo();
        Map<String, List<OrgInfoVo>> maps = new HashMap<String, List<OrgInfoVo>>((int) (orgs.size() / 0.75 + 1));
        OrgInfoVo root = null;
        // 2. 递归组织
        if (orgs != null && orgs.size() > 0) {
            // 2.1 给每个节点分配一个 子节点集合
            for (OrgInfoVo o : orgs) {
                maps.put(o.getSid(), new ArrayList<OrgInfoVo>());
            }
            // 2.2 填充每个节点的子节点
            for (OrgInfoVo o : orgs) {
                if ("".equals(o.getParentSid())) {
                    root = o;
                }
                if (maps.get(o.getParentSid()) != null) {
                    maps.get(o.getParentSid()).add(o);
                }
            }
            // 2.2 递归全部节点，组装成完成的树
            mapToList(root, maps);
        }
        List<OrgInfoVo> roots = new ArrayList<OrgInfoVo>();
        roots.add(root);
        json = JSON.toJSONString(roots);
        if (roots != null && roots.size() > 0) {
            return roots;
        } else {
            return null;
        }
    }

    /**
     * 递归组织数  第一种实现方式
     *
     * @param org  当前节点
     * @param maps 所有的节点，已挂了子节点
     */
    private void mapToList(OrgInfoVo org, Map<String, List<OrgInfoVo>> maps) {
        if (org != null) {
            List<OrgInfoVo> childs = maps.get(org.getSid());
            for (OrgInfoVo o : childs) {
                mapToList(o, maps);
            }
            org.setChilds(childs);
        }
    }

}
