package com.tahoecn.opening.service;

import com.tahoecn.opening.model.SysUserRole;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户-角色 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-09
 */
public interface ISysUserRoleService extends IService<SysUserRole> {
    List<SysUserRole> myActivityList(String userName);

    List<SysUserRole> myActivityListAll(String userName);
}
