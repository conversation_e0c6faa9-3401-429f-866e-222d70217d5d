package com.tahoecn.opening.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.model.dto.UserDto;
import com.tahoecn.opening.model.vo.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 活动 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
public interface IOpActivityService extends IService<OpActivity> {





    /**
     * 根据授权手机号或openId获取集合对象
     *
     * @param user {@link OpUser}
     * @return {@link List<OpActivity>}
     */
    List<OpActivity> queryByTelNoOrOpenId(OpUser user);

    /**
     * 根据授权手机号或openId获取集合对象的数量
     *
     * @param user {@link OpUser}
     * @return 当前系统中存在的登录用户的数量
     */
    int queryCoundByPageNoOrOpenId(OpUser user);

    /**
     * 根据 用户信息中的 电话 openId  projectId 及 activityId进行判断当前登录用户是否有查看权限,若有则返回正确信息
     *
     * @param user       {@link OpUser}
     */
    void checkProjectLogin(OpUser user);



    IPage<CustomerAnalysisVO> getActivityAnalysisCustomer(Page page, String activityIdNow, Integer loginNum, Integer orderNum, OpUser opUser);
    List<CustomerAnalysisVO> getActivityAnalysisCustomerEx(String activityIdNow, Integer loginNum, Integer orderNum, OpUser opUser);
    IPage<FavoriteDetil> getFavoriteUserList(Page page, String houseId);
    List<FavoriteDetil> getFavoriteUserList(String houseId);

    IPage<HouseResAnalysisVO> getActivityHouseRes(Page page, String activityIdNow, String houseName, String buildingName, String unitName, String roomNum, String minFavo, String maxFavo);
    List<HouseResAnalysisVO> getActivityHouseResEx(String activityIdNow, String houseName, String buildingName, String unitName, String roomNum, String minFavo, String maxFavo);

    IPage<HashMap<String, Object>> getActivityHouseType(Page page, String activityIdNow, String houseType);

    IPage<SginInVO> sginInList(Page page, String sginOrNot, OpUser opUser);

    String getQr(String activityId, String width, String page);

    String getActivityIdStatus(String activityId);

    String userSginIn(String activityId, String userId, String address, LocalDateTime now);
    /**
     * 传入活动名  测试活动名是否以存在
     * @param activityName
     * @return
     */
    boolean repeatActivityName(String activityName);

    int modifyUserSelectCount(Integer id, Integer buyNumber);
    void resetData(String activityId);

	List<HashMap<String, Object>> getLoadTestData(String houseIds, String activityId);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:31 2023/5/24
     * @param opActivity      活动对象
     * @return com.tahoecn.core.json.JSONResult
     * @description // TODO 更改大屏设置参数根据活动id
     **/
	void updateScreenSetStrByActivityId(OpActivity opActivity);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:45 2024/8/13
     * @param timeStr               活动可选房区间字符串
     * @param sortNum               排序号
     * @param checkMinutes          选房分钟数
	 * @return com.tahoecn.opening.model.vo.BuyTimeVO
	 * @description // TODO         获取该排序号对应的抢购时间
	 **/
	BuyTimeVO getBuyTimeByTimeStr(String timeStr, Integer sortNum, Integer checkMinutes);

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/21 15:50
     * @Param        activityId 活动id
     * @Param        formalFlag true--正式;false--模拟
     * @Return       java.util.List<com.tahoecn.opening.model.dto.UserDto>
     * @Description  TODO 查询全部未删除用户、根据活动id
     **/
    List<UserDto> selectAllUserDtoByActivityId(String activityId, boolean formalFlag);

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/22 16:46
     * @Param        activity   活动id
     * @Param        userId     用户id
     * @Return       com.tahoecn.opening.model.dto.UserDto
     * @Description  TODO 获取用户dto
     **/
    UserDto getUserDto(OpActivity activity, Integer userId);

    /**
     * 按活动一键摇号并返回结果
     * @param activityIdNow
     * @return
     */
    JSONResult<Object> autoLottery(Integer activityIdNow);

    /**
     * 个人摇号并返回结果
     * @param activityIdNow
     * @param userId
     * @return
     */
    JSONResult personalLottery(Integer activityIdNow, Integer userId);

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/13 10:18
     * @Param        opActivity     活动对象
     * @Return       void
     * @Description  TODO 保存开始选房短信通知
     **/
    void saveOpenCheckSmsJob(OpActivity opActivity);

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/13 10:18
     * @Param        opActivity     活动对象
     * @Return       void
     * @Description  TODO 保存开始摇号短信通知
     **/
    void saveOpenLotterySmsJob(OpActivity opActivity);

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/13 10:18
     * @Param        opActivity     活动对象
     * @Return       void
     * @Description  TODO 保存一键摇号结果短信通知
     **/
    void saveLotteryResultSmsJob(OpActivity opActivity);
}
