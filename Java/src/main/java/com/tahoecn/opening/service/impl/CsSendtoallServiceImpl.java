package com.tahoecn.opening.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tahoecn.opening.mapper.CsSendtoallMapper;
import com.tahoecn.opening.model.CsSendtoall;
import com.tahoecn.opening.service.CsSendtoallService;

/**
 * <p>
 * 期初发送短信通知表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-29
 */
@Transactional
@Service
public class CsSendtoallServiceImpl extends ServiceImpl<CsSendtoallMapper, CsSendtoall> implements CsSendtoallService {

}
