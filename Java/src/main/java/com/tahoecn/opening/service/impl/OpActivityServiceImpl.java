package com.tahoecn.opening.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.tahoecn.core.collection.CollectionUtil;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.http.HttpUtil;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.*;
import com.tahoecn.opening.config.AliyunSmsConfig;
import com.tahoecn.opening.converter.ResponseMessage;
import com.tahoecn.opening.converter.ShareinterJson2HttpMessageConverter;
import com.tahoecn.opening.mapper.OpActivityMapper;
import com.tahoecn.opening.mapper.OpMassageJobMapper;
import com.tahoecn.opening.mapper.OpUserMapper;
import com.tahoecn.opening.model.*;
import com.tahoecn.opening.model.dto.UserDto;
import com.tahoecn.opening.model.interfaceBean.CustomerFollowRootBean;
import com.tahoecn.opening.model.interfaceBean.HouseTypeRootBean;
import com.tahoecn.opening.model.interfaceBean.SnCalData;
import com.tahoecn.opening.model.interfaceBean.SnCalRootBean;
import com.tahoecn.opening.model.vo.*;
import com.tahoecn.opening.schedule.OrderTask;
import com.tahoecn.opening.service.IOpActivityService;
import com.tahoecn.opening.service.IOpFavoriteService;
import com.tahoecn.opening.service.IOpOrderService;
import com.tahoecn.opening.service.IOpUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 活动 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@Service
public class OpActivityServiceImpl extends ServiceImpl<OpActivityMapper, OpActivity> implements IOpActivityService {

    private static final Logger log = LoggerFactory.getLogger(OpActivityServiceImpl.class);

    @Autowired
    private IOpUserService iOpUserService;
    @Autowired
    private OpActivityMapper activityMapper;

    @Value("${const_HOST}")
    private String ConstHOST;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IOpFavoriteService favoriteService;

    @Autowired
    private IOpOrderService orderService;

    @Resource
    private OpUserMapper opUserMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private AliyunSmsConfig smsConfig;

    @Autowired
    private AliyunServiceImpl aliyunService;

    @Autowired
    private OpMassageJobMapper opMassageJobMapper;

    @Override
    public String getQr(String activityId, String width, String page){
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<String> requestEntity = new HttpEntity<String>( "{\"_datatype\":\"text\",\"_param\":{\"scene\":\""+activityId+"\",\"page\":\"pages/opening/paramsEnter/index\",\"width\":\""+width+"\"}}", headers);
        //需要try
        String str = restTemplate.postForObject(ConstHOST+"/FrameWeb/FrameService/Api.ashx?option=func&funcid=mShareOnlineQRCode_Select", requestEntity, String.class);
        JSONObject object = JSONObject.parseObject(str);
        return JSONObject.parseObject(object.getString("data")).get("imgUrl")+"";
    }






    @Override
    public List<OpActivity> queryByTelNoOrOpenId(OpUser user) {
        if (user == null) {
            // TODO 待改为抛出自定义异常
            throw new RuntimeException("用户信息为空");
        }
        //查询用户下属活动
        OpUser opUser = checkUserLogin(user.getTel(), user.getOpenId());
        String activityId = opUser.getActivityId();
        QueryWrapper<OpActivity> activityWrapper = new QueryWrapper<>();
        activityWrapper.eq("yn", GlobalConstants.Y).eq("id", activityId)
//                    .eq("status", GlobalConstants.STATUS_R)
        ;
        return list(activityWrapper);
    }

    @Override
    public int queryCoundByPageNoOrOpenId(OpUser opUser) {
        return queryByTelNoOrOpenId(opUser).size();
    }

    @Override
    public void checkProjectLogin(OpUser user) {
        List<OpActivity> opActivities = queryByTelNoOrOpenId(user);
        if (CollectionUtil.isEmpty(opActivities)) {
            // TODO 待改为抛出自定义异常
            throw new RuntimeException("电话号码" + user.getTel() + "无权访问,请填写正确的预留手机号码！");
        }
        for (int i = 0; i < opActivities.size(); i++) {
            OpActivity opActivity = opActivities.get(i);
            if (user.getProjectId().equals(opActivity.getProjectId()) && user.getActivityId().equals(opActivity.getId())) {
                return;
            }
        }
        // TODO 待改为抛出自定义异常
        throw new RuntimeException("电话号码" + user.getTel() + "无权访问当前项目,请重新认证！");
    }



    /**
     * 校验用户登录状态
     *
     * @param telNum
     * @param openId
     * @return
     */
    private OpUser checkUserLogin(String telNum, String openId) {
        OpUser oneByTelNumOrOpenId = iOpUserService.getOneByTelNumOrOpenId(telNum, openId);
        if (oneByTelNumOrOpenId == null) {
            // TODO 待改为抛出自定义异常
            throw new RuntimeException("电话号码" + telNum + "无活动可参加,请填写正确的预留手机号码！");
        }
        return oneByTelNumOrOpenId;
    }





    @Override
    public IPage<CustomerAnalysisVO> getActivityAnalysisCustomer(Page page, String activityIdNow, Integer loginNum, Integer orderNum, OpUser opUser) {

        IPage<CustomerAnalysisVO> iPage = activityMapper.getActivityAnalysisCustomer(page, activityIdNow, loginNum, orderNum, opUser);
        List<CustomerAnalysisVO> records = iPage.getRecords();
        for (CustomerAnalysisVO record : records) {
            if (record!=null){
                if (record.getOrderYN()!=null){
                    record.setOrderYN("有");
                }else {
                 record.setOrderYN("无");
                }
            }
        }
        return iPage.setRecords(records);
    }

    @Override
    public List<CustomerAnalysisVO> getActivityAnalysisCustomerEx(String activityIdNow, Integer loginNum, Integer orderNum, OpUser opUser) {

        List<CustomerAnalysisVO> records = activityMapper.getActivityAnalysisCustomer(activityIdNow, loginNum, orderNum, opUser);
        for (CustomerAnalysisVO record : records) {
            if (record!=null){
                if (record.getOrderYN()!=null){
                    record.setOrderYN("有");
                }else {
                    record.setOrderYN("无");
                }
            }
        }
        return records;
    }




    @Override
    public IPage<FavoriteDetil> getFavoriteUserList(Page page, @Param("houseId") String houseId) {
        return activityMapper.getFavoriteUserList(page,houseId);
    }

    @Override
    public List<FavoriteDetil> getFavoriteUserList(@Param("houseId") String houseId) {
        return activityMapper.getFavoriteUserList(houseId);
    }
    @Override
    public IPage<HouseResAnalysisVO> getActivityHouseRes(Page page,String activityIdNow,String houseName, String buildingName, String unitName, String roomNum,String minFavo, String maxFavo) {
        OpHousingResources opHousingResources=new OpHousingResources();
        opHousingResources.setActivityId(activityIdNow);
        opHousingResources.setHouseName(houseName);
        opHousingResources.setBuildingName(buildingName);
        opHousingResources.setUnitName(unitName);
        opHousingResources.setRoomNum(roomNum);
        IPage<HouseResAnalysisVO> houseFavoriteNum= activityMapper.getHouseFavoriteNum(page,opHousingResources,minFavo,maxFavo);
        return houseFavoriteNum;
    }

    @Override
    public List<HouseResAnalysisVO> getActivityHouseResEx(String activityIdNow,String houseName, String buildingName, String unitName, String roomNum,String minFavo, String maxFavo) {
        OpHousingResources opHousingResources=new OpHousingResources();
        opHousingResources.setActivityId(activityIdNow);
        opHousingResources.setHouseName(houseName);
        opHousingResources.setBuildingName(buildingName);
        opHousingResources.setUnitName(unitName);
        opHousingResources.setRoomNum(roomNum);
        List<HouseResAnalysisVO> houseFavoriteNum= activityMapper.getHouseFavoriteNum(opHousingResources,minFavo,maxFavo);
        return houseFavoriteNum;
    }

    @Override
    public IPage<HashMap<String, Object>> getActivityHouseType(Page page, String activityIdNow, String houseType) {
        OpHousingResources opHousingResources=new OpHousingResources();
        opHousingResources.setActivityId(activityIdNow);
        opHousingResources.setHourseType(houseType);
//        Long max=(long) 0;
//        max=activityMapper.getMaxFavoriteTypeNum(activityIdNow);
//        List<HashMap<String, Object>> records = houseFavoriteType.getRecords();
//        DecimalFormat df = new DecimalFormat("0.00");
//        //判断热度
//        Double hotOne=max*0.2;
//        Double hotTwo=max*0.4;
//        Double hotThree=max*0.6;
//        Double hotFour=max*0.8;
//        for (HashMap<String, Object> house : records) {
//            Double d= Double.parseDouble(house.get("num").toString());
//            if (d<hotOne){
//                house.put("hotNum",1);
//            }else if (d<=hotTwo){
//                house.put("hotNum",2);
//            }
//            else if (d<=hotThree){
//                house.put("hotNum",3);
//            }
//            else if (d<=hotFour){
//                house.put("hotNum",4);
//            }
//            else{
//                house.put("hotNum",5);
//            }
//        }
//        return houseFavoriteType.setRecords(records);
        return activityMapper.getHouseFavoriteType(page,opHousingResources);
    }

    @Override
    public IPage<SginInVO> sginInList(Page page, String sginOrNot, OpUser opUser) {
        IPage<SginInVO> iPage=activityMapper.getSginInList(page,sginOrNot,opUser);
        List<SginInVO> records = iPage.getRecords();
        for (SginInVO record : records) {
            if (record.getSginInDate()==null){
                record.setSginyn("否");
            }else {
                record.setSginyn("是");
            }
        }
        return iPage.setRecords(records);
    }

    /**
     * 判断String activityId 状态
     */
    @Override
    public String getActivityIdStatus(String activityId){

        OpActivity opActivity = activityMapper.selectById(activityId);
        return opActivity.getStatusCode();

    }

    /**
     * 用户签到方法
     * @param activityId
     * @param userId
     * @param address
     * @param now
     * @return
     */
    @Override
    public String userSginIn(String activityId, String userId, String address, LocalDateTime now) {
        if (StringUtils.isBlank(userId) ||StringUtils.isBlank(activityId) ||StringUtils.isBlank(address) ){
            return "签到失败!";
        }
        OpActivity opActivity = activityMapper.selectById(activityId);
        if (opActivity.getSignRange()==0||opActivity.getSignRange()==null){
            activityMapper.userSginIn(activityId, userId, address, now);
            return "签到成功!";
        }
        String centerCoordinate = opActivity.getCenterCoordinate();
        String[] center = centerCoordinate.split(",");
        String snCal = this.getSnCal(address);
        String[] addr = snCal.split(",");

        System.out.println("addr = " + addr);
        if(center.length >= 0 &&  addr.length >= 0){
            double distanceFromTwoPoints = PointToDistance.getDistanceFromTwoPoints(Double.parseDouble(center[0]), Double.parseDouble(center[1]), Double.parseDouble(addr[0]), Double.parseDouble(addr[1]));
            if (Math.abs(distanceFromTwoPoints)>opActivity.getSignRange()){
            	return "签到失败!超出签到范围";
            }
        }else{
        	return "签到失败!物理位置信息错误";
        }
        activityMapper.userSginIn(activityId, userId, address, now);
        return "签到成功!";
    }

    public String getSnCal(String address){
        SnCal snCal = new SnCal();
        Map paramsMap = new LinkedHashMap<String, String>();
        //String temp = URLEncoder.encode("114.21892734521,29.575429778924");
        paramsMap.put("coords", address);
        paramsMap.put("from", "1");
        paramsMap.put("to", "5");
        //paramsMap.put("ak", "fUucxTwD4oG0IjxgCaQlBqLTHFIClQWE");//test
        paramsMap.put("ak", "6Bmfjxp9K71bPCnTII1IGLervq1BP0Gv");
        String wholeStr = null;
        String tempStr = null;
        String whole = null;
        try {
            // 调用下面的toQueryString方法，对LinkedHashMap内所有value作utf8编码，拼接返回结果address=%E7%99%BE%E5%BA%A6%E5%A4%A7%E5%8E%A6&output=json&ak=yourak
            String paramsStr = snCal.toQueryString(paramsMap);
            // 对paramsStr前面拼接上/geocoder/v2/?，后面直接拼接yoursk得到/geocoder/v2/?address=%E7%99%BE%E5%BA%A6%E5%A4%A7%E5%8E%A6&output=json&ak=yourakyoursk
            //String wholeStr = new String("/geoconv/v1/?" + paramsStr + "DD6mdqG0GVXOXqi17UrbclF6XgfTVLzU");   //test
//            wholeStr = new String("http://api.map.baidu.com/geoconv/v1/?" + paramsStr+"&sn=");
             wholeStr = new String("/geoconv/v1/?" + paramsStr + "FzWr2KeMU7yLsTw3YIe9NIufWTUGvy3f");
            // 对上面wholeStr再作utf8编码
            tempStr = URLEncoder.encode(wholeStr, "UTF-8");

            String s = snCal.MD5(tempStr);

            whole = new String("http://api.map.baidu.com/geoconv/v1/?" + paramsStr+"&sn="+s);
            System.out.println("whole = " + whole);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();  //todo
        }
        HttpHeaders headers = new HttpHeaders();
        restTemplate.getMessageConverters().add(new ShareinterJson2HttpMessageConverter());

        HttpEntity<String> requestEntity = new HttpEntity<String>(headers);

        //String object = restTemplate.getForObject(whole,String.class);
        String object = HttpUtil.httpGet(whole);
        JSONObject jsonObject = JSONObject.parseObject(object);
        JSONArray list= (JSONArray) jsonObject.get("result");
        String addr= ((JSONObject) list.get(0)).get("x") + "," + ((JSONObject) list.get(0)).get("y");

       return addr;

    }



    @Override
    public boolean repeatActivityName(String activityName) {
        QueryWrapper<OpActivity> wrapper=new QueryWrapper<>();
        wrapper.eq("activity_name",activityName);
        wrapper.eq("yn","y");
        List<OpActivity> opActivityList = activityMapper.selectList(wrapper);
        return opActivityList.size()==0;
    }

    @Override
    public int modifyUserSelectCount(Integer id, Integer buyNumber) {
        return activityMapper.modifyUserSelectCount(id,buyNumber);
    }

    //模拟活动开始时删除模拟订单
    @Override
    public void resetData(String activityId){
        QueryWrapper<OpOrder> orderQueryWrapper=new QueryWrapper<>();
        orderQueryWrapper.eq("activity_id",activityId).eq("is_regular",GlobalConstants.N);
        orderService.remove(orderQueryWrapper);
    }

	@Override
	public List<HashMap<String, Object>> getLoadTestData(String houseIds, String activityId) {
		// TODO Auto-generated method stub
		return baseMapper.getLoadTestData(houseIds,activityId);
	}

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:31 2023/5/24
     * @param opActivity      活动对象
     * @return com.tahoecn.core.json.JSONResult
     * @description // TODO 更改大屏设置参数根据活动id
     *                      fullFlg---全屏切换（Boolean）
     *                      isTiling---是否平铺（Boolean）
     *                      isPage---是否翻页（Boolean）
     *                      rowNum--显示行数（Number）
     *                      columnNum--显示列数（Number）
     *                      timerRun--滚动频率（Number）
     *                      pageRun--翻页频率（Boolean）
     **/
    @Override
    public void updateScreenSetStrByActivityId(OpActivity opActivity) {
        // 验证 是否存在基本参数设置
        if (StringUtils.isBlank(opActivity.getScreenSetStr())) {
            // 生成基础数据并赋值
            Map<String, Object> screenMap = Maps.newHashMap();
            screenMap.put("fullFlg", false);
            screenMap.put("isTiling", false);
            screenMap.put("isPage", true);
            screenMap.put("rowNum", 20);
            screenMap.put("columnNum", 20);
            screenMap.put("timerRun", 3);
            screenMap.put("pageRun", 6);
            opActivity.setScreenSetStr(JSONObject.toJSON(screenMap).toString());
        }
        activityMapper.updateById(opActivity);
    }

    /**
     * @param timeStr               活动可选房区间字符串
     * @param sortNum               排序号
     * @param checkMinutes          选房分钟数
     * @return com.tahoecn.opening.model.vo.BuyTimeVO
     * <AUTHOR>  <EMAIL>
     * @date 16:45 2024/8/13
     * @description // TODO         获取该排序号对应的抢购时间
     **/
    @Override
    public BuyTimeVO getBuyTimeByTimeStr(String timeStr, Integer sortNum, Integer checkMinutes) {
        // 定义默认开始结束时间
        BuyTimeVO buyTimeVO = new BuyTimeVO("2050-01-01 00:00:00", "2050-01-01 00:00:00");
        if (StringUtils.isBlank(timeStr) || null == sortNum || null == checkMinutes) {
            return buyTimeVO;
        }
        try {
            List<BuyTimeVO> timeVOList = JSONArray.parseArray(timeStr, BuyTimeVO.class);
            // 剔除非区间数据
            Iterator<BuyTimeVO> iterator = timeVOList.iterator();
            while (iterator.hasNext()) {
                BuyTimeVO next = iterator.next();
                if (null == next.getBeginDate() || null == next.getEndDate()) {
                    iterator.remove();
                }
            }
            if (CollectionUtils.isNotEmpty(timeVOList)) {
                // 按开始时间倒叙排序
                timeVOList = timeVOList.stream().sorted(Comparator.comparing(BuyTimeVO::getBeginDate)).collect(Collectors.toList());
                // 定义总序号
                int totalNum = 0;
                // 处理数据
                for (BuyTimeVO vo : timeVOList) {
                    int thisSortNum = DateUtils.getSortNum(vo.getBeginDate(), vo.getEndDate(), checkMinutes);
                    totalNum += thisSortNum;
                    // 校验
                    if (totalNum >= sortNum.intValue()) {
                        // 获取时间区间
                        // 判断第几个区间
                        if (totalNum == thisSortNum) {
                            // 第一个区间
                            Date thisBegin = DateUtils.addDateMinute(DateUtils.getDate(vo.getBeginDate(), "yyyy-MM-dd HH:mm:ss"), (sortNum - 1) * checkMinutes);
                            Date thisEnd = DateUtils.addDateMinute(thisBegin, checkMinutes);
                            buyTimeVO.setBeginDate(DateUtils.getDateString(thisBegin, "yyyy-MM-dd HH:mm:ss"));
                            buyTimeVO.setEndDate(DateUtils.getDateString(thisEnd, "yyyy-MM-dd HH:mm:ss"));
                        } else {
                            // 后续区间
                            int stract = sortNum - (totalNum - thisSortNum);
                            Date thisBegin = DateUtils.addDateMinute(DateUtils.getDate(vo.getBeginDate(), "yyyy-MM-dd HH:mm:ss"), (stract - 1) * checkMinutes);
                            Date thisEnd = DateUtils.addDateMinute(thisBegin, checkMinutes);
                            buyTimeVO.setBeginDate(DateUtils.getDateString(thisBegin, "yyyy-MM-dd HH:mm:ss"));
                            buyTimeVO.setEndDate(DateUtils.getDateString(thisEnd, "yyyy-MM-dd HH:mm:ss"));
                        }
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return buyTimeVO;
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/22 16:46
     * @Param        activity   活动id
     * @Param        userId     用户id
     * @Return       com.tahoecn.opening.model.dto.UserDto
     * @Description  TODO 获取用户dto
     **/
    @Override
    public UserDto getUserDto(OpActivity activity, Integer userId) {
        // 定义默认开始结束时间
        try {
            LocalDateTime now = LocalDateTime.now();
            // 校验是否有模拟活动
            if (null != activity.getSimulationStart() && null != activity.getSimulationEnd()) {
                if (now.isBefore(activity.getSimulationEnd())) {
                    // 当前时间在模拟活动结束时间之前
                    // 获取全量用户
                    List<UserDto> userDtoList = selectAllUserDtoByActivityId(activity.getId().toString(), false);
                    if (CollectionUtils.isEmpty(userDtoList)) {
                        return null;
                    }
                    // 获取当前用户
                    for (UserDto userDto : userDtoList) {
                        if (userDto.getId().longValue() == userId.longValue()) {
                            userDto.setEndDate(LocalDateTimeUtils.localDateTimeToDate(activity.getSimulationEnd()));
                            return userDto;
                        }
                    }
                    return null;
                }
            }
            // 校验是否有正式活动
            if (null != activity.getFormalStart() && null != activity.getFormalEnd()) {
                // 获取全量用户
                List<UserDto> userDtoList = selectAllUserDtoByActivityId(activity.getId().toString(), true);
                if (CollectionUtils.isEmpty(userDtoList)) {
                    return null;
                }
                // 获取当前用户
                for (UserDto userDto : userDtoList) {
                    if (userDto.getId().longValue() == userId.longValue()) {
                        userDto.setEndDate(LocalDateTimeUtils.localDateTimeToDate(activity.getFormalEnd()));
                        return userDto;
                    }
                }
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONResult<Object> autoLottery(Integer activityIdNow) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        // 查询活动信息
        OpActivity opActivity = this.getById(activityIdNow);
        if (null != opActivity.getLotteryFlag() && opActivity.getLotteryFlag() != 0) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("未启用一键摇号");
            return jsonResult;
        }
        // 查询活动时间 判断未超过期限
        if (null != opActivity.getLotteryStartDate() && null != opActivity.getLotteryEndDate()) {
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(opActivity.getLotteryStartDate()) || now.isAfter(opActivity.getLotteryEndDate())) {
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("未在摇号时间");
                return jsonResult;
            }
        }
        // 查询客户信息
        LambdaQueryWrapper<OpUser> opUserWrapper = new QueryWrapper<OpUser>()
                .lambda()
                .select(OpUser::getId, OpUser::getProjectId, OpUser::getName,
                        OpUser::getTel, OpUser::getUserSort, OpUser::getLotterySort
                        , OpUser::getActivityId)
                .eq(OpUser::getActivityId, activityIdNow)
                .eq(OpUser::getYn, GlobalConstants.Y)
                .eq(OpUser::getRoleName, "customer");
        List<OpUser> opUsers = iOpUserService.list(opUserWrapper);
        // 摇号
        List<OpUser> list = assignLotteryNumbers(opUsers, null);
        iOpUserService.updateBatchById(list);


        // 是否开启短信执行
        if (null != smsConfig.getOpenSms() && smsConfig.getOpenSms()) {
            // 校验活动
            if (StringUtils.isBlank(smsConfig.getSpecialId()) || smsConfig.getSpecialId().equals(activityIdNow.toString())) {
                saveLotteryResultSmsJob(opActivity);
            }
        }

        // 数据脱敏
        list.forEach(user -> {
            user.setName(DataMaskingUtil.maskName(user.getName()));
            user.setTel(DataMaskingUtil.maskPhone(user.getTel()));
            user.setLotterySort(null);
        });
        // 按摇号排序
        list.sort(Comparator.comparing(OpUser::getUserSort));
        Map<String, Object> res = Maps.newHashMap();
        res.put("activityName", Optional.ofNullable(opActivity)
                .map(OpActivity::getLotteryTitle)
                .orElse(""));
        res.put("list", list);

        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(res);
        return jsonResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONResult personalLottery(Integer activityIdNow, Integer userId) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        // 查询活动信息
        OpActivity opActivity = this.getById(activityIdNow);
        if (null != opActivity.getLotteryFlag() && opActivity.getLotteryFlag() != 1) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("未启用个人键摇号");
            return jsonResult;
        }
        // 查询活动时间 判断未超过期限
        if (null != opActivity.getLotteryStartDate() && null != opActivity.getLotteryEndDate()) {
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(opActivity.getLotteryStartDate()) || now.isAfter(opActivity.getLotteryEndDate())) {
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("未在摇号时间");
                return jsonResult;
            }
        }
        // 查询客户信息
        LambdaQueryWrapper<OpUser> opUserWrapper = new QueryWrapper<OpUser>()
                .lambda()
                .select(OpUser::getId, OpUser::getProjectId, OpUser::getName,
                        OpUser::getTel, OpUser::getUserSort, OpUser::getLotterySort
                        , OpUser::getActivityId)
                .eq(OpUser::getActivityId, activityIdNow)
                .eq(OpUser::getYn, GlobalConstants.Y)
                .eq(OpUser::getRoleName, "customer");
        List<OpUser> opUsers = iOpUserService.list(opUserWrapper);
        // 摇号
        List<OpUser> list = assignLotteryNumbers(opUsers, userId);
        iOpUserService.updateById(list.get(0));
        // 活动选房模式：0--抢购模式；1--选房模式（默认为0）
        // 摇号结果短信通知标识：0--不发送;1--发送(默认为0)
        if (null != opActivity.getCheckMode() && 1 == opActivity.getCheckMode().intValue()
                && null != opActivity.getLotteryResultFlag() && 1 == opActivity.getLotteryResultFlag().intValue()) {
            // 是否开启短信执行
            if (null != smsConfig.getOpenSms() && smsConfig.getOpenSms()) {
                // 校验活动
                if (StringUtils.isBlank(smsConfig.getSpecialId()) || smsConfig.getSpecialId().equals(activityIdNow.toString())) {
                    OpUser user = list.get(0);
                    AliyunVariableVo vo = new AliyunVariableVo(smsConfig.SYSTEM_NAME, opActivity.getLotteryTitle());
                    vo.setUserSort(user.getUserSort() + "");
                    vo.setUserName(user.getName());
                    boolean b = aliyunService.sendSms(user.getTel(), smsConfig.TEMP_CODE_LOTTERY_RESULT, JSON.toJSONString(vo));
                    log.info("个人摇号结果短信发送活动id：{}, 模板：{}, 手机号：{}, 发送结果：{}", activityIdNow, smsConfig.TEMP_CODE_LOTTERY_RESULT, user.getTel(), b);
                }
            }
        }
        // 数据脱敏
        list.forEach(user -> {
            user.setName(DataMaskingUtil.maskName(user.getName()));
            user.setTel(DataMaskingUtil.maskPhone(user.getTel()));
            user.setLotterySort(null);
        });
        Map<String, Object> res = Maps.newHashMap();
        res.put("activityName", Optional.ofNullable(opActivity)
                .map(OpActivity::getLotteryTitle)
                .orElse(""));
        res.put("opUser", list.get(0));

        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(res);
        return jsonResult;
    }

    /**
     * 摇号
     * 类型 一键摇号  个人摇号(传入userId)
     * @param userList 活动用户
     * @param userId 摇号用户
     * @return
     */
    public List<OpUser> assignLotteryNumbers(List<OpUser> userList, Integer userId) {
        int totalSize = userList.size();
        List<Integer> numberPool = new ArrayList<>();

        // 初始化号码池：1 ~ totalSize
        for (int i = 1; i <= totalSize; i++) {
            numberPool.add(i);
        }

        // 存放已分配的号码（包括预设和后来分配的）
        Set<Integer> assignedNumbers = new HashSet<>();

        // 1. 处理已有 lotterySort 的用户
        List<OpUser> fixedUsers = new ArrayList<>();
        List<OpUser> randomUsers = new ArrayList<>();

        for (OpUser user : userList) {
            if (user.getLotterySort() != null && numberPool.contains(user.getLotterySort())) {
                // 如果 lotterySort 有效，保留并标记为已用
                assignedNumbers.add(user.getLotterySort());
                user.setUserSort(user.getLotterySort());
                fixedUsers.add(user);
            } else if (null != userId && user.getUserSort() != null && numberPool.contains(user.getUserSort()))  {
                // 个人摇号情况 如果 userSort 有效，保留并标记为已用
                assignedNumbers.add(user.getLotterySort());
                user.setUserSort(user.getLotterySort());
                fixedUsers.add(user);
            } else {
                // 待随机分配
                // 个人摇号仅处理指定id
                if (null != userId && !userId.equals(user.getId()))
                    continue;
                randomUsers.add(user);
            }
        }

        // 2. 剩余可用号码 = 号码池 - 已分配号码
        List<Integer> availableNumbers = numberPool.stream()
                .filter(num -> !assignedNumbers.contains(num))
                .collect(Collectors.toList());

        // 打乱可用号码，用于随机分配
        Collections.shuffle(availableNumbers);

        // 3. 给剩余用户分配号码
        if (availableNumbers.size() < randomUsers.size()) {
            throw new RuntimeException("号码池不足，无法完成公平分配");
        }

        for (int i = 0; i < randomUsers.size(); i++) {
            OpUser user = randomUsers.get(i);
            user.setUserSort(availableNumbers.get(i)); // 分配新号码
        }

        // 合并结果 个人摇号直接返回
        List<OpUser> result = new ArrayList<>(randomUsers);
        if (null == userId)
            result.addAll(fixedUsers);

        return result;
    }

    public int getCurrentSortNum(List<UserDto> userDtoList, Date nowDate) {
        // 校验
        int currentSortNum = 0;
        if (CollectionUtils.isNotEmpty(userDtoList)) {
            // 定义上一个对象
            UserDto lastUserDto = null;
            // 处理数据
            for (UserDto dto : userDtoList) {
                if (null == dto.getBeginDate() || null == dto.getEndDate()) {
                    continue;
                }
                if (nowDate.before(dto.getBeginDate())) {
                    return currentSortNum;
                }
//				// 校验区间
//				if (nowDate.after(dto.getBeginDate()) && nowDate.before(dto.getEndDate())) {
//					return dto.getUserSort().intValue();
//				}
                currentSortNum = dto.getUserSort().intValue();
                if (null != lastUserDto) {
                    // 比较日期区间
                    if (nowDate.after(lastUserDto.getBeginDate()) && nowDate.before(dto.getBeginDate())) {
                        return lastUserDto.getUserSort().intValue();
                    }
                    continue;
                }
                lastUserDto = dto;
            }
        }
        return currentSortNum;
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/21 15:50
     * @Param        activityId 活动id
     * @Param        formalFlag true--正式;false--模拟
     * @Return       java.util.List<com.tahoecn.opening.model.dto.UserDto>
     * @Description  TODO 查询全部未删除用户、根据活动id
     **/
    @Override
    public List<UserDto> selectAllUserDtoByActivityId(String activityId, boolean formalFlag) {
        // 查询redis
        String key = formalFlag ? activityId + "Activity_ALL_T_User" : activityId + "Activity_ALL_V_User";
        Object o = redisTemplate.opsForValue().get(key);
        if (Objects.isNull(o)) {
            return null;
        }
        return (List<UserDto>) o;
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/13 10:18
     * @Param        opActivity     活动对象
     * @Return       void
     * @Description  TODO 保存开始选房短信通知
     **/
    @Override
    public void saveOpenCheckSmsJob(OpActivity opActivity) {
        // 活动选房模式：0--抢购模式；1--选房模式（默认为0）
        // 开始选房短信通知标识：0--不发送;1--发送(默认为0)
        if (null == opActivity || null == opActivity.getCheckMode() || 1 != opActivity.getCheckMode().intValue()
                || null == opActivity.getOpenCheckFlag() || 1 != opActivity.getOpenCheckFlag().intValue()) {
            return;
        }
        // 获取用户
        List<UserDto> userDtoList = selectAllUserDtoByActivityId(opActivity.getId().toString(), true);
        if (CollectionUtils.isEmpty(userDtoList)) {
            return;
        }
        QueryWrapper<OpMassageJob> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpMassageJob::getActivityId, opActivity.getId());
        wrapper.lambda().eq(OpMassageJob::getJobType, smsConfig.TEMP_CODE_PERSON);
        wrapper.lambda().eq(OpMassageJob::getIsSent, GlobalConstants.N);
        // 前置删除
        opMassageJobMapper.delete(wrapper);
        // 校验前置发送时间
        LocalDateTime now = LocalDateTime.now();
        int everyBodyBeforeMinutes = null == opActivity.getEveryBodyBeforeMinutes() ? 0 : opActivity.getEveryBodyBeforeMinutes().intValue();
        // 遍历新增（可根据性能考虑批量写入）
        for (UserDto userDto : userDtoList) {
            if (null == userDto.getBeginDate()) {
                continue;
            }
            OpMassageJob job = new OpMassageJob();
            if (0 == everyBodyBeforeMinutes) {
                job.setSendTime(LocalDateTimeUtils.dateToLocalDateTime(userDto.getBeginDate()));
            } else {
                job.setSendTime(LocalDateTimeUtils.dateToLocalDateTime(DateUtils.addDateMinute(userDto.getBeginDate(), -everyBodyBeforeMinutes)));
            }
            job.setActivityId(opActivity.getId() + "");
            job.setIsSent(GlobalConstants.N);
            job.setIsFormal(GlobalConstants.Y);
            job.setMsgContent(JSON.toJSONString(new AliyunVariableVo(smsConfig.SYSTEM_NAME, opActivity.getActivityName(), DateUtils.getDateString(userDto.getBeginDate()), userDto.getUserTel())));
            job.setJobType(smsConfig.TEMP_CODE_PERSON);
            job.setCreateTime(now);
            opMassageJobMapper.insert(job);
        }
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/13 10:18
     * @Param        opActivity     活动对象
     * @Return       void
     * @Description  TODO 保存开始摇号短信通知
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOpenLotterySmsJob(OpActivity opActivity) {
        // 活动选房模式：0--抢购模式；1--选房模式（默认为0）
        // 开始摇号短信通知标识：0--不发送;1--发送(默认为0)
        if (null == opActivity || null == opActivity.getCheckMode() || 1 != opActivity.getCheckMode().intValue()
                || null == opActivity.getOpenLotteryFlag() || 1 != opActivity.getOpenLotteryFlag().intValue() || null == opActivity.getLotteryStartDate()) {
            return;
        }
        QueryWrapper<OpMassageJob> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpMassageJob::getActivityId, opActivity.getId());
        wrapper.lambda().eq(OpMassageJob::getJobType, smsConfig.TEMP_CODE_OPEN_LOTTERY);
        wrapper.lambda().eq(OpMassageJob::getIsSent, GlobalConstants.N);
        // 前置删除
        opMassageJobMapper.delete(wrapper);
        // 校验前置发送时间
        LocalDateTime now = LocalDateTime.now();
        int openLotteryBeforeMinutes = null == opActivity.getOpenLotteryBeforeMinutes() ? 0 : opActivity.getOpenLotteryBeforeMinutes().intValue();
        OpMassageJob job = new OpMassageJob();
        if (0 == openLotteryBeforeMinutes) {
            job.setSendTime(opActivity.getLotteryStartDate());
        } else {
            job.setSendTime(opActivity.getLotteryStartDate().minusMinutes(openLotteryBeforeMinutes));
        }
        job.setActivityId(opActivity.getId() + "");
        job.setIsSent(GlobalConstants.N);
        job.setIsFormal(GlobalConstants.Y);
        job.setMsgContent(JSON.toJSONString(new AliyunVariableVo(smsConfig.SYSTEM_NAME, opActivity.getLotteryTitle(), LocalDateTimeUtils.formatDateTime(opActivity.getLotteryStartDate()))));
        job.setJobType(smsConfig.TEMP_CODE_OPEN_LOTTERY);
        job.setCreateTime(now);
        opMassageJobMapper.insert(job);
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/13 10:18
     * @Param        opActivity     活动对象
     * @Return       void
     * @Description  TODO 保存一键摇号结果短信通知
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveLotteryResultSmsJob(OpActivity opActivity) {
        // 活动选房模式：0--抢购模式；1--选房模式（默认为0）
        // 摇号结果短信通知标识：0--不发送;1--发送(默认为0)
        if (null == opActivity || null == opActivity.getCheckMode() || 1 != opActivity.getCheckMode().intValue()
                || null == opActivity.getLotteryResultFlag() || 1 != opActivity.getLotteryResultFlag().intValue()) {
            return;
        }
        QueryWrapper<OpMassageJob> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpMassageJob::getActivityId, opActivity.getId());
        wrapper.lambda().eq(OpMassageJob::getJobType, smsConfig.TEMP_CODE_LOTTERY_RESULT);
        wrapper.lambda().eq(OpMassageJob::getIsSent, GlobalConstants.N);
        // 前置删除
        opMassageJobMapper.delete(wrapper);
        // 校验前置发送时间
        LocalDateTime now = LocalDateTime.now();
        OpMassageJob job = new OpMassageJob();
        job.setSendTime(now);
        job.setActivityId(opActivity.getId() + "");
        job.setIsSent(GlobalConstants.N);
        job.setIsFormal(GlobalConstants.Y);
        job.setMsgContent(JSON.toJSONString(new AliyunVariableVo(smsConfig.SYSTEM_NAME, opActivity.getLotteryTitle())));
        job.setJobType(smsConfig.TEMP_CODE_LOTTERY_RESULT);
        job.setCreateTime(now);
        opMassageJobMapper.insert(job);
    }
}
