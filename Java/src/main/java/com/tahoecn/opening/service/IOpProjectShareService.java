package com.tahoecn.opening.service;

import com.tahoecn.opening.common.utils.ApiResult;
import com.tahoecn.opening.model.OpProjectShare;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tahoecn.opening.model.interfaceBean.OpProjectShareData;
import com.tahoecn.opening.model.interfaceBean.OpProjectShareRootBean;
import org.springframework.http.ResponseEntity;

/**
 * <p>
 * 分享+项目 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
public interface IOpProjectShareService extends IService<OpProjectShare> {

    boolean updataProject(ResponseEntity<OpProjectShareRootBean> quote);

    String upLoadImg(OpProjectShareData opProjectShareData, String webPath, String path);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:32 2024/8/19
     * @param projectId
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     查看项目详情表根据项目id
     **/
    ApiResult getProjectShareByProjectId(String projectId);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:37 2024/8/19
     * @param share
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     更改项目详情表
     **/
    ApiResult updateProjectShare(OpProjectShare share);
}
