package com.tahoecn.opening.service.impl;

import com.tahoecn.opening.common.constants.MQConstant;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Date;

//监听MASSAGE_QUEUE_NAME队列，有消息时进行消费
@Component
@RabbitListener(queues = MQConstant.MASSAGE_QUEUE_NAME)
public class ReceiverMessage {

    @RabbitHandler
    public void process(String content) {
        System.out.println("接受时间:"+ new Date());
        System.out.println("接受消息:" + content);
    }
}

