package com.tahoecn.opening.service;

import com.tahoecn.opening.model.OpHouseType;

import java.util.HashMap;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 户型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
public interface IOpHouseTypeService extends IService<OpHouseType> {

	List<HashMap<String, Object>> getHouseTypeListByActivityId(String string);

	HashMap<String, Object> getHouseTypeAreaMinToMax(String id);

    String upLoadImg(OpHouseType opHouseType, String webPath, String path);
}
