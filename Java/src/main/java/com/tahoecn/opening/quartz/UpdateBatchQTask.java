package com.tahoecn.opening.quartz;

import com.tahoecn.opening.service.IOpHousingResourcesService;
import org.quartz.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: Zhengwenchao  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.schedule
 * @Description:// TODO 更改活动选房批次任务
 * @Date: 2025/5/6 10:13
 * @Version: 1.0
 **/
@DisallowConcurrentExecution
@Service
public class UpdateBatchQTask extends QuartzJobBean {

    private static final Logger log = LoggerFactory.getLogger(UpdateBatchQTask.class);

    @Autowired
    private IOpHousingResourcesService iOpHousingResourcesService;

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobKey key = jobExecutionContext.getJobDetail().getKey();
        JobDataMap jobDataMap = jobExecutionContext.getTrigger().getJobDataMap();
        String activityId = jobDataMap.getString("activityId");
        boolean formalFlag = jobDataMap.getBoolean("formalFlag");
        Object object = jobDataMap.get("batchList");
        log.info("定时更改批次任务：{}, 活动id：{}, 是正式批次：{}, 批次集合为：{}", key, activityId, formalFlag, object);
        List<String> batchList = (List<String>) object;
        if (formalFlag) {
            // 正式
            iOpHousingResourcesService.updateBatchNameFormal(activityId, batchList);
        } else {
            // 模拟
            iOpHousingResourcesService.updateBatchNameSimulation(activityId, batchList);
        }
    }
}
