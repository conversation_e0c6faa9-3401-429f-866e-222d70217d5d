package com.tahoecn.opening.quartz;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Set;

/**
 * @Author: Zhengwenchao  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.schedule
 * @Description:// TODO 任务接口
 * @Date: 2025/5/6 10:16
 * @Version: 1.0
 **/
@Component
public class QuartzService {

    private static final Logger log = LoggerFactory.getLogger(QuartzService.class);

    @Autowired
    private Scheduler scheduler;

    /**
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/6 13:52
     * @Param        clazz  Job类
     * @Param        name   名称
     * @Param        group  组名
     * @Param        cron   表达式
     * @Param        data   入参对象
     * @Param        desc   描述
     * @Return       void
     * @Description  TODO 新增定时任务cron模式
     **/
    public void addCronJobs(Class clazz, String name, String group, String cron, Object data, String desc){
        JobDataMap jobDataMap = new JobDataMap();
        String paramStr = "";
        if(data != null){
            paramStr = JSON.toJSONString(data);
            jobDataMap = JSON.parseObject(paramStr, JobDataMap.class);
        }
        if (StringUtils.isNotBlank(desc) && desc.length() > 250) {
            desc = desc.substring(0, 250);
        }
        log.info("定时任务创建名称：{}, cron表达式：{}, 组名：{}, 入参：{}", name, cron, group, paramStr);
        JobDetail jobDetail = JobBuilder.newJob(clazz)
                .withIdentity(name, group)
                .build();
        CronTrigger trigger = TriggerBuilder.newTrigger()
                .usingJobData(jobDataMap)
                .withIdentity(name, group)
                .withDescription(desc)
                .startNow()
                .withSchedule(CronScheduleBuilder.cronSchedule(cron))
                .build();
        try {
            scheduler.scheduleJob(jobDetail, trigger);
            log.info("定时任务创建成功！名称：{}, cron表达式：{}, 组名：{}, 入参：{}", name, cron, group, paramStr);
        } catch (SchedulerException e) {
            e.printStackTrace();
            log.error("定时任务创建异常！名称：{}, cron表达式：{}, 组名：{}, 入参：{}, 异常信息为：{}", name, cron, group, paramStr, e);
        }
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/6 13:52
     * @Param        clazz  Job类
     * @Param        name   名称
     * @Param        group  组名
     * @Param        cron   表达式
     * @Param        data   入参对象
     * @Param        desc   描述
     * @Return       void
     * @Description  TODO 更改定时任务
     **/
    public void updateCronJobs(Class classes, String name, String group, String cron, Object data, String desc){
        // 删除原来的
        deleteJobs(name, group);
        // 新增
        addCronJobs(classes, name, group, cron, data, desc);
    }

    /**
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/6 14:02
     * @Param        name   名称
     * @Param        group  组名
     * @Return       void
     * @Description  TODO 删除定时任务
     **/
    public void deleteJobs(String name, String group){
        log.info("定时任务删除名称：{}, 组名：{}", name, group);
        JobKey jobKey = new JobKey(name, group);
        try {
            if (scheduler.checkExists(jobKey)) {
                scheduler.deleteJob(jobKey);
                log.info("定时任务删除成功！名称：{}, 组名：{}", name, group);
            }
        } catch (SchedulerException e) {
            e.printStackTrace();
            log.error("定时任务删除异常！名称：{}, 组名：{}, 异常信息为：{}", name, group, e);
        }
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/6 13:52
     * @Param        clazz  Job类
     * @Param        name   名称
     * @Param        group  组名
     * @Param        date   执行时间
     * @Param        data   入参对象
     * @Param        desc   描述
     * @Return       void
     * @Description  TODO 新增定时任务单一模式
     **/
    public void addSimpleJobs(Class clazz, String name, String group, Date date, Object data, String desc){
        JobDataMap jobDataMap = new JobDataMap();
        String paramStr = "";
        if(data != null){
            paramStr = JSON.toJSONString(data);
            jobDataMap = JSON.parseObject(paramStr, JobDataMap.class);
        }
        if (StringUtils.isNotBlank(desc) && desc.length() > 250) {
            desc = desc.substring(0, 250);
        }
        log.info("定时任务创建名称：{}, 执行时间：{}, 组名：{}, 入参：{}", name, date, group, paramStr);
        JobDetail jobDetail = JobBuilder.newJob(clazz)
                .withIdentity(name, group)
                .build();
        SimpleTrigger trigger = TriggerBuilder.newTrigger()
                .usingJobData(jobDataMap)
                .withIdentity(name, group)
                .withDescription(desc)
                .startAt(date)
                .withSchedule(SimpleScheduleBuilder.simpleSchedule().withRepeatCount(0))
                .build();
        try {
            scheduler.scheduleJob(jobDetail, trigger);
            log.info("定时任务创建成功！名称：{}, 执行时间：{}, 组名：{}, 入参：{}", name, date, group, paramStr);
        } catch (SchedulerException e) {
            e.printStackTrace();
            log.error("定时任务创建异常！名称：{}, 执行时间：{}, 组名：{}, 入参：{}, 异常信息为：{}", name, date, group, paramStr, e);
        }
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/6 13:52
     * @Param        clazz  Job类
     * @Param        name   名称
     * @Param        group  组名
     * @Param        date   执行时间
     * @Param        data   入参对象
     * @Param        desc   描述
     * @Return       void
     * @Description  TODO 更改定时任务
     **/
    public void updateSimpleJobs(Class classes, String name, String group, Date date, Object data, String desc){
        // 删除原来的
        deleteJobs(name, group);
        // 新增
        addSimpleJobs(classes, name, group, date, data, desc);
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/6 14:02
     * @Param        name   名称
     * @Param        group  组名
     * @Return       void
     * @Description  TODO 删除该分组所有定时任务
     **/
    public void deleteJobsByGroup(String group){
        log.info("删除该分组所有定时任务组名：{}", group);
        try {
            GroupMatcher<JobKey> groupMatcher = GroupMatcher.jobGroupEquals(group);
            Set<JobKey> jobKeys = scheduler.getJobKeys(groupMatcher);
            // 遍历删除任务
            for (JobKey jobKey : jobKeys) {
                scheduler.deleteJob(jobKey);
            }
        } catch (SchedulerException e) {
            e.printStackTrace();
            log.error("删除该分组所有定时任务组名：{}, 异常信息为：{}", group, e);
        }
    }
}
