package com.tahoecn.opening.quartz;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Map;

/**
 * @Author: Zhengwenchao  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.quartz
 * @Description:// TODO 定时任务入参vo
 * @Date: 2025/5/6 10:28
 * @Version: 1.0
 **/
public class QuartzParamVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "任务组别")
    private String group;

    @ApiModelProperty(value = "cron模式时的表达式")
    private String cron;

    @ApiModelProperty(value = "入参map")
    private Map<String, Object> paramMap;

    @ApiModelProperty(value = "描述")
    private String desc;

    @ApiModelProperty(value = "单一模式时的任务开始时间")
    private String startDate;

    @ApiModelProperty(value = "是否cron模式标识：true--是;false--否;（默认为true）")
    private boolean cronFlag = true;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public Map<String, Object> getParamMap() {
        return paramMap;
    }

    public void setParamMap(Map<String, Object> paramMap) {
        this.paramMap = paramMap;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public boolean isCronFlag() {
        return cronFlag;
    }

    public boolean getCronFlag() {
        return cronFlag;
    }

    public void setCronFlag(boolean cronFlag) {
        this.cronFlag = cronFlag;
    }
}
