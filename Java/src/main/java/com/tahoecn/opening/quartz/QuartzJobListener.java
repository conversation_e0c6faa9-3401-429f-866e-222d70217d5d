package com.tahoecn.opening.quartz;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.JobListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.quartz
 * @Description:// TODO 定时任务执行监听器
 * @Date: 2025/5/6 14:07
 * @Version: 1.0
 **/
public class QuartzJobListener implements JobListener {

    private static final Logger log = LoggerFactory.getLogger(QuartzJobListener.class);

    @Override
    public String getName() {
        return "quartzJobListener";
    }

    @Override
    public void jobToBeExecuted(JobExecutionContext jobExecutionContext) {

    }

    @Override
    public void jobExecutionVetoed(JobExecutionContext jobExecutionContext) {

    }

    @Override
    public void jobWasExecuted(JobExecutionContext context, JobExecutionException e) {
        JobKey key = context.getJobDetail().getKey();
        if (null == e) {
            log.info("quartz监听执行结果为：{}, 任务：{}", true, key);
        } else {
            log.error("quartz监听执行结果为：{}, 任务：{}, 异常原因为：{}", false, key, e);
        }
    }
}
