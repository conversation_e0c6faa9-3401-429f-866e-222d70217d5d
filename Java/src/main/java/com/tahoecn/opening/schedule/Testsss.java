package com.tahoecn.opening.schedule;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.tahoecn.opening.common.utils.DateUtils;
import com.tahoecn.opening.model.vo.BuyTimeVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.schedule
 * @ClassName: Testsss
 * @Description:// TODO ss
 * @Date: 2022/8/17 15:56
 * @Version: 1.0
 */
public class Testsss {

    public static void main(String[] args) {
        // 定义集合
        List<BuyTimeVO> list = Lists.newArrayListWithCapacity(6);
        list.add(new BuyTimeVO("2024-08-21 10:00:00", "2024-08-21 11:00:00"));// 30
        list.add(new BuyTimeVO("2024-08-21 13:00:00", "2024-08-21 15:00:00"));// 90
        list.add(new BuyTimeVO("2024-08-21 17:00:00", "2024-08-21 18:00:00"));// 120
        list.add(new BuyTimeVO("2024-08-22 10:00:00", "2024-08-22 11:00:00"));// 150
        list.add(new BuyTimeVO("2024-08-22 13:00:00", "2024-08-22 15:00:00"));// 210
        list.add(new BuyTimeVO("2024-08-22 17:00:00", "2024-08-22 18:00:00"));// 240
        // 转换
        String timeStr = JSONArray.toJSONString(list, SerializerFeature.WriteMapNullValue);
        System.out.println("timeStr = " + timeStr);
        num(timeStr, 0, 2);
        num(timeStr, 1, 2);
        num(timeStr, 29, 2);
        num(timeStr, 30, 2);
        num(timeStr, 31, 2);
        num(timeStr, 90, 2);
        num(timeStr, 91, 2);
        num(timeStr, 120, 2);
        num(timeStr, 121, 2);
        num(timeStr, 150, 2);
        num(timeStr, 151, 2);
        num(timeStr, 210, 2);
        num(timeStr, 240, 2);
        num(timeStr, 241, 2);

    }

    public static void num(String timeStr, Integer sortNum, Integer checkMinutes) {
        BuyTimeVO v1 = getBuyTimeByTimeStr(timeStr, sortNum, 2);
        System.out.println("第（" + sortNum + "）位 = " + JSONObject.toJSONString(v1));
    }

    public static BuyTimeVO getBuyTimeByTimeStr(String timeStr, Integer sortNum, Integer checkMinutes) {
        // 定义默认开始结束时间
        BuyTimeVO buyTimeVO = new BuyTimeVO("2000-01-01 00:00:00", "2000-01-01 00:00:00");
        if (StringUtils.isBlank(timeStr) || null == sortNum || null == checkMinutes) {
            return buyTimeVO;
        }
        try {
            List<BuyTimeVO> timeVOList = JSONArray.parseArray(timeStr, BuyTimeVO.class);
            // 剔除非区间数据
            Iterator<BuyTimeVO> iterator = timeVOList.iterator();
            while (iterator.hasNext()) {
                BuyTimeVO next = iterator.next();
                if (null == next.getBeginDate() || null == next.getEndDate()) {
                    iterator.remove();
                }
            }
            if (CollectionUtils.isNotEmpty(timeVOList)) {
                // 按开始时间倒叙排序
                timeVOList = timeVOList.stream().sorted(Comparator.comparing(BuyTimeVO::getBeginDate)).collect(Collectors.toList());
                // 定义总序号
                int totalNum = 0;
                // 处理数据
                for (BuyTimeVO vo : timeVOList) {
                    int thisSortNum = DateUtils.getSortNum(vo.getBeginDate(), vo.getEndDate(), checkMinutes);
                    totalNum += thisSortNum;
                    // 校验
                    if (totalNum >= sortNum.intValue()) {
                        // 获取时间区间
                        // 判断第几个区间
                        if (totalNum == thisSortNum) {
                            // 第一个区间
                            Date thisBegin = DateUtils.addDateMinute(DateUtils.getDate(vo.getBeginDate(), "yyyy-MM-dd HH:mm"), (sortNum - 1) * checkMinutes);
                            Date thisEnd = DateUtils.addDateMinute(thisBegin, checkMinutes);
                            buyTimeVO.setBeginDate(DateUtils.getDateString(thisBegin, "yyyy-MM-dd HH:mm:ss"));
                            buyTimeVO.setEndDate(DateUtils.getDateString(thisEnd, "yyyy-MM-dd HH:mm:ss"));
                        } else {
                            // 后续区间
                            int stract = sortNum - (totalNum - thisSortNum);
                            Date thisBegin = DateUtils.addDateMinute(DateUtils.getDate(vo.getBeginDate(), "yyyy-MM-dd HH:mm"), (stract - 1) * checkMinutes);
                            Date thisEnd = DateUtils.addDateMinute(thisBegin, checkMinutes);
                            buyTimeVO.setBeginDate(DateUtils.getDateString(thisBegin, "yyyy-MM-dd HH:mm:ss"));
                            buyTimeVO.setEndDate(DateUtils.getDateString(thisEnd, "yyyy-MM-dd HH:mm:ss"));
                        }
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return buyTimeVO;
    }




    public static void user() {
        // 不足两位小数补0
        DecimalFormat format = new DecimalFormat("0.00#");
        System.out.println("zero = " + BigDecimal.ZERO);
        System.out.println("0.00 = " + new BigDecimal("0.00"));
        System.out.println("format = " + format.format(new BigDecimal("8805.6")));
        System.out.println("format2 = " + new BigDecimal(format.format(new BigDecimal("8805.6"))));

        /**
         * 销控随机姓氏
         */
        List<String> nameList = Arrays.asList("赵", "王", "张", "李", "杨", "马", "关", "刘", "郑", "田");

        Random random = new Random();
        for (int i = 0; i < 10; i++) {
            String s = nameList.get(random.nextInt(nameList.size()));
            System.out.println("s = " + s);
        }
    }
}
