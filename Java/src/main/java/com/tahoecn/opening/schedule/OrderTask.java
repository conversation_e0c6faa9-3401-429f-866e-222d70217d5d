package com.tahoecn.opening.schedule;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.ApiResult;
import com.tahoecn.opening.common.utils.DateUtils;
import com.tahoecn.opening.config.AliyunSmsConfig;
import com.tahoecn.opening.mapper.OpActivityMapper;
import com.tahoecn.opening.mapper.OpMassageJobMapper;
import com.tahoecn.opening.mapper.OpUserMapper;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpMassageJob;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.model.dto.OpActivityDTO;
import com.tahoecn.opening.model.vo.AliyunVariableVo;
import com.tahoecn.opening.model.vo.BatchConditionVo;
import com.tahoecn.opening.service.IOpHousingResourcesService;
import com.tahoecn.opening.service.IOpOrderService;
import com.tahoecn.opening.service.SyncService;
import com.tahoecn.opening.service.impl.AliyunServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.schedule
 * @ClassName: OrderTask
 * @Description:// TODO
 * @Date: 2022/8/10 17:40
 * @Version: 1.0
 */
@Component
public class OrderTask {

    public static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");

    private static final Logger log = LoggerFactory.getLogger(OrderTask.class);

    @Autowired
    private IOpOrderService orderService;

    @Autowired
    private IOpHousingResourcesService iOpHousingResourcesService;

    @Resource
    private OpActivityMapper opActivityMapper;

    @Autowired
    RedisTemplate redisTemplate;

    @Qualifier("taskExecutor")
    @Autowired
    ThreadPoolTaskExecutor taskExecutor;

    @Value("${control_white_minutes}")
    private Integer controlWhiteMinutes;

    @Value("${server.port}")
    private Integer serverPort;

    @Autowired
    private SyncService syncService;

    @Value("${sms_environment}")
    private String smsEnvironment;

    @Autowired
    private AliyunSmsConfig smsConfig;

    @Autowired
    private AliyunServiceImpl aliyunService;

    @Autowired
    private OpMassageJobMapper opMassageJobMapper;

    @Autowired
    private OpUserMapper opUserMapper;

    /**
     * <AUTHOR>  <EMAIL>
     * @date 9:36 2022/8/11
     * @description // TODO 定时生成活动房源白名单任务
     *              逻辑：查询数据库活动表中，正式选房时间和模拟选房时间在 该时间段内的已经运行中的活动数据，将其关联的房源白名单生成订单
     **/
//    @Scheduled(cron = "0 0/1 * * * ?")
//    @Scheduled(cron = "0 * * * * ?")
    public void autoOrder() {
        // 获取当前开始时间  结束时间
        Date now = new Date();
        String startTime = sdf.format(now);
        log.info("定时生成房源白名单任务开始时间为：{}, 结束时间为：{}", startTime + ":00", startTime + ":59");

        // 校验锁
        Object object = redisTemplate.opsForValue().get("AUTO_ORDER_LOCK");
        if (null != object) {
            log.info("定时生成房源白名单任务开始时间为：{}, 结束时间为：{} 的业务锁被占用，本次任务跳过！被占用的任务时间为：{}", startTime + ":00", startTime + ":59", object.toString());
            return;
        }
        // 获取锁
        Boolean lock = redisTemplate.opsForValue().setIfAbsent("AUTO_ORDER_LOCK", startTime);
        if (lock) {
            redisTemplate.expire("AUTO_ORDER_LOCK", 5, TimeUnit.MINUTES);

//            // 处理模拟选房结束时间推送大屏的逻辑
//            // 查模拟  查询活动表中模拟选房结束时间为当前时间的数据
//            QueryWrapper<OpActivity> wrapper = new QueryWrapper<>();
//            wrapper.ge("simulation_end", startTime + ":00").le("simulation_end", startTime + ":59")
//                    .eq("is_simulation", "y").eq("status_code", "running").eq("yn", "y");
//            List<OpActivity> activityEndList = opActivityMapper.selectList(wrapper);
//            log.info("定时处理大屏模拟结束时间任务开始时间为：{}, 结束时间为：{}的查询模拟活动结束时间在当前时间区间的结果为：{}", startTime + ":00", startTime + ":59", JSONObject.toJSON(activityEndList).toString());
//            // 校验 是否存在数据
//            if (CollectionUtils.isNotEmpty(activityEndList)) {
//                // 不为空，遍历处理数据
//                for (OpActivity activity : activityEndList) {
//                    // 推送大屏数据为空
//                    // 获取 当前时间属于该活动的哪一个时间段，模拟选房时间，还是 正式选房时间
//                    HashMap<String, Object> resultMap = iOpHousingResourcesService.getOpHousingBaseData(activity.getId().toString(), DateUtils.regular(activity, LocalDateTime.now()));
//                    resultMap.put("opActivity", activity);
//                    int todayLoginUserCount = (int)(resultMap.get("todayLoginUserCount") == null ? 0: resultMap.get("todayLoginUserCount"))+(activity.getJoinNumber() == null ? 0 : activity.getJoinNumber());
//                    resultMap.put("todayLoginUserCount", todayLoginUserCount);
//                    resultMap.put("sysDate", new Date().getTime());
//                    JSONResult<Object> jsonResult = new JSONResult<>();
//                    jsonResult.setCode(GlobalConstants.S_CODE);
//                    jsonResult.setMsg("SUCCESS");
//                    jsonResult.setData(resultMap);
//
//                    try {
//                        WebSocketServer.sendInfo(JSON.toJSONString(jsonResult), activity.getId().toString());
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//                }
//            }

            // 查模拟  查询活动表中为生成房源白名单的活动数据根据所传时间和查询类型
            List<OpActivityDTO> simulationDataList = opActivityMapper.selectListByTime(startTime + ":00", startTime + ":59", 2);
            log.info("定时生成房源白名单任务开始时间为：{}, 结束时间为：{}的查询模拟活动在当前时间区间的结果为：{}", startTime + ":00", startTime + ":59", JSONObject.toJSON(simulationDataList).toString());
            // 校验 是否存在数据
            if (CollectionUtils.isNotEmpty(simulationDataList)) {
                // 不为空，遍历处理数据
                for (OpActivityDTO dto : simulationDataList) {
                    // 生成订单数据
                    orderService.initHouseResourceWhiteOrder(dto.getId().toString(), 2);
                    // 修改活动的 白名单生成状态  自动生成订单结果标识：0--未生成；1--正式已生成；2--模拟已生成；3--全部生成（默认为0）
                    opActivityMapper.updateAutoOrderResultFlagById(dto.getId(), dto.getAutoOrderResultFlag().intValue() == 1 ? 3 : 2);
                }
            }

            // 查正式  查询活动表中为生成房源白名单的活动数据根据所传时间和查询类型
            List<OpActivityDTO> formatDataList = opActivityMapper.selectListByTime(startTime + ":00", startTime + ":59", 1);
            log.info("定时生成房源白名单任务开始时间为：{}, 结束时间为：{}的查询正式活动在当前时间区间的结果为：{}", startTime + ":00", startTime + ":59", JSONObject.toJSON(formatDataList).toString());
            // 校验 是否存在数据
            if (CollectionUtils.isNotEmpty(formatDataList)) {
                try {
                    // 正式生成订单时候，休眠2秒
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                // 不为空，遍历处理数据
                for (OpActivityDTO dto : formatDataList) {
                    // 生成订单数据
                    orderService.initHouseResourceWhiteOrder(dto.getId().toString(), 1);
                    // 修改活动的 白名单生成状态  自动生成订单结果标识：0--未生成；1--正式已生成；2--模拟已生成；3--全部生成（默认为0）
                    opActivityMapper.updateAutoOrderResultFlagById(dto.getId(), dto.getAutoOrderResultFlag().intValue() == 2 ? 3 : 1);
                }
            }

            // 释放锁
            redisTemplate.delete("AUTO_ORDER_LOCK");
        }

        log.info("定时生成房源白名单任务开始时间为：{}, 结束时间为：{}的执行时间为：{}", startTime + ":00", startTime + ":59", (System.currentTimeMillis() - now.getTime()) + "ms");
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 11:30 2023/10/23
     * @description // TODO 定时更改白名单订单数据为有效数据
     **/
    @Scheduled(cron = "0 * * * * ?")
    public void autoOrderEffective() {
        // 获取当前开始时间  结束时间
        Date now = new Date();
        String startTime = sdf.format(now);

        CompletableFuture updateOrderEffectiveFuture = CompletableFuture.supplyAsync(() -> {
            // 更改 订单有效状态
            updateOrderEffective(now, startTime);
            return null;
        }, taskExecutor);

        CompletableFuture createOrderFuture = CompletableFuture.supplyAsync(() -> {
            // 定时生成房源白名单数据
            Date addDateMinute = DateUtils.addDateMinute(now, controlWhiteMinutes);
            createOrder(now, startTime, addDateMinute);
            return null;
        }, taskExecutor);

//        CompletableFuture updateResourceBatch = CompletableFuture.supplyAsync(() -> {
//            // 定时刷新活动批次数据
//            updateResourceBatch(now, startTime);
//            return null;
//        }, taskExecutor);

        CompletableFuture closeActivityFuture = CompletableFuture.supplyAsync(() -> {
            // 定时结束活动
            closeActivity(now, startTime);
            return null;
        }, taskExecutor);

        // 是否开启短信执行
        if (null != smsConfig.getOpenSms() && smsConfig.getOpenSms()) {
            CompletableFuture sendOpenActivirySmsFuture = CompletableFuture.supplyAsync(() -> {
                // 定时活动开始
                sendOpenActivirySms(now, startTime);
                return null;
            }, taskExecutor);
            CompletableFuture sendOpenLotterySmsFuture = CompletableFuture.supplyAsync(() -> {
                // 定时摇号开始
                sendOpenLotterySms(now, startTime);
                return null;
            }, taskExecutor);
            CompletableFuture sendOpenCheckSmsFuture = CompletableFuture.supplyAsync(() -> {
                // 定时个人选房开始
                sendOpenCheckSms(now, startTime);
                return null;
            }, taskExecutor);
            CompletableFuture sendLotteryResultSmsFuture = CompletableFuture.supplyAsync(() -> {
                // 定时一键摇号结果
                sendLotteryResultSms(now, startTime);
                return null;
            }, taskExecutor);
        }
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:52 2023/10/24
     * @param now 
     * @param startTime
     * @description // TODO 修改订单有效状态
     **/
    public void updateOrderEffective(Date now, String startTime) {
        try {
            // 获取锁
            Boolean lock = redisTemplate.opsForValue().setIfAbsent("AUTO_ORDER_EFFECTIVE_LOCK", startTime);
            if (lock) {
                // 定时释放锁
                redisTemplate.expire("AUTO_ORDER_EFFECTIVE_LOCK", 30, TimeUnit.SECONDS);
                // 查询 当前开始结束时间内的所有正式或者模拟的的进行中的活动数据
                List<OpActivityDTO> activityDTOList = opActivityMapper.selectActivityListByTime(startTime + ":00", startTime + ":59");
                log.info("端口号：" + serverPort + "定时更改订单有效任务————开始时间为：{}, 结束时间为：{}的查询所有活动在当前时间区间的结果为：{}", startTime + ":00", startTime + ":59", JSONObject.toJSON(activityDTOList).toString());
                // 校验 是否存在数据
                if (CollectionUtils.isNotEmpty(activityDTOList)) {
                    // 不为空，遍历处理数据
                    for (OpActivityDTO dto : activityDTOList) {
                        // 校验 正式还是模拟
                        if (null != dto.getIsSimulation() && "y".equalsIgnoreCase(dto.getIsSimulation())) {
                            // 模拟活动
                            orderService.updateOrderData(dto.getId().toString(), false);
                        } else {
                            // 正式活动
                            orderService.updateOrderData(dto.getId().toString(), true);
                        }
                    }
                }
            } else {
                log.info("端口号：" + serverPort + "定时更改订单有效任务开始时间为：{}, 结束时间为：{} 的业务锁被占用，本次任务跳过！被占用的任务时间为：{}", startTime + ":00", startTime + ":59", redisTemplate.opsForValue().get("AUTO_ORDER_EFFECTIVE_LOCK"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("端口号：" + serverPort + "定时更改订单有效任务开始时间为：{}, 结束时间为：{}的执行出现异常！异常为：{}", startTime + ":00", startTime + ":59", e);
        } finally {
            redisTemplate.delete("AUTO_ORDER_EFFECTIVE_LOCK");
        }
        log.info("端口号：" + serverPort + "定时更改订单有效任务开始时间为：{}, 结束时间为：{}的执行时间为：{}", startTime + ":00", startTime + ":59", (System.currentTimeMillis() - now.getTime()) + "ms");
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:47 2023/10/23
     * @param now
     * @param startTime
     * @param addDateMinute 
     * @description // TODO 生成房源白名单
     **/
    public void createOrder(Date now, String startTime, Date addDateMinute) {
        String beginTime = sdf.format(addDateMinute);
        try {
            // 获取锁
            Boolean lock = redisTemplate.opsForValue().setIfAbsent("AUTO_ORDER_LOCK", startTime);
            if (lock) {
                redisTemplate.expire("AUTO_ORDER_LOCK", 30, TimeUnit.SECONDS);
                // 查模拟  查询活动表中为生成房源白名单的活动数据根据所传时间和查询类型
                List<OpActivityDTO> simulationDataList = opActivityMapper.selectListByTime(beginTime + ":00", beginTime + ":59", 2);
                log.info("端口号：" + serverPort + "定时生成房源白名单任务————开始时间为：{}, 结束时间为：{}的查询模拟活动在当前时间区间的结果为：{}", startTime + ":00", startTime + ":59", JSONObject.toJSON(simulationDataList).toString());
                // 校验 是否存在数据
                if (CollectionUtils.isNotEmpty(simulationDataList)) {
                    // 不为空，遍历处理数据
                    for (OpActivityDTO dto : simulationDataList) {
                        // 生成订单数据
                        orderService.initHouseResourceWhiteOrder(dto.getId().toString(), 2);
                        // 修改活动的 白名单生成状态  自动生成订单结果标识：0--未生成；1--正式已生成；2--模拟已生成；3--全部生成（默认为0）
                        opActivityMapper.updateAutoOrderResultFlagById(dto.getId(), dto.getAutoOrderResultFlag().intValue() == 1 ? 3 : 2);
                    }
                }

                // 查正式  查询活动表中为生成房源白名单的活动数据根据所传时间和查询类型
                List<OpActivityDTO> formatDataList = opActivityMapper.selectListByTime(beginTime + ":00", beginTime + ":59", 1);
                log.info("端口号：" + serverPort + "定时生成房源白名单任务————开始时间为：{}, 结束时间为：{}的查询正式活动在当前时间区间的结果为：{}", startTime + ":00", startTime + ":59", JSONObject.toJSON(formatDataList).toString());
                // 校验 是否存在数据
                if (CollectionUtils.isNotEmpty(formatDataList)) {
                    // 不为空，遍历处理数据
                    for (OpActivityDTO dto : formatDataList) {
                        // 生成订单数据
                        orderService.initHouseResourceWhiteOrder(dto.getId().toString(), 1);
                        // 修改活动的 白名单生成状态  自动生成订单结果标识：0--未生成；1--正式已生成；2--模拟已生成；3--全部生成（默认为0）
                        opActivityMapper.updateAutoOrderResultFlagById(dto.getId(), dto.getAutoOrderResultFlag().intValue() == 2 ? 3 : 1);
                    }
                }
            } else {
                log.info("端口号：" + serverPort + "定时生成房源白名单任务开始时间为：{}, 结束时间为：{} 的业务锁被占用，本次任务跳过！被占用的任务时间为：{}", startTime + ":00", startTime + ":59", redisTemplate.opsForValue().get("AUTO_ORDER_LOCK"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("端口号：" + serverPort + "定时生成房源白名单任务开始时间为：{}, 结束时间为：{}的执行出现异常！异常为：{}", startTime + ":00", startTime + ":59", e);
        } finally {
            redisTemplate.delete("AUTO_ORDER_LOCK");
        }
        log.info("端口号：" + serverPort + "定时生成房源白名单任务开始时间为：{}, 结束时间为：{}的执行时间为：{}", startTime + ":00", startTime + ":59", (System.currentTimeMillis() - now.getTime()) + "ms");
    }
    
    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:26 2023/10/24
     * @param now
     * @param startTime
     * @description // TODO 更改活动状态
     **/
    public void closeActivity(Date now, String startTime) {
        LocalDateTime localDateTime = LocalDateTime.now();
        try {
            // 获取锁
            Boolean lock = redisTemplate.opsForValue().setIfAbsent("CLOSE_ACTIVITY_LOCK", startTime);
            if (lock) {
                redisTemplate.expire("CLOSE_ACTIVITY_LOCK", 30, TimeUnit.SECONDS);
                // 查 进行中且时间小于当前时间的活动
                QueryWrapper<OpActivity> wrapper = new QueryWrapper<>();
                wrapper.lambda().lt(OpActivity::getFormalEnd, localDateTime);
                wrapper.lambda().eq(OpActivity::getStatusCode, GlobalConstants.STATUS_R);
                List<OpActivity> activityList = opActivityMapper.selectList(wrapper);
                log.info("端口号：" + serverPort + "定时更改活动状态任务————开始时间为：{}, 结束时间为：{}的查询所有活动的结果为：{}", startTime + ":00", startTime + ":59", JSONObject.toJSON(activityList).toString());
                if (CollectionUtils.isNotEmpty(activityList)) {
                    for (OpActivity job : activityList) {
                        //删除当前活动下的redis
                        Set<String> keys = redisTemplate.keys(job.getId().toString() + "Activity*");
                        redisTemplate.delete(keys);
                        // 更改入库
                        job.setStatusCode(GlobalConstants.STATUS_CLOSE);
                        job.setStatusName(GlobalConstants.STATUS_CLOSE_NAME);
                        job.setLastUpdateBy("autoClose");
                        job.setLastUpdateDate(localDateTime);
                        opActivityMapper.updateById(job);
                    }
                }
            } else {
                log.info("端口号：" + serverPort + "定时更改活动状态任务开始时间为：{}, 结束时间为：{} 的业务锁被占用，本次任务跳过！被占用的任务时间为：{}", startTime + ":00", startTime + ":59", redisTemplate.opsForValue().get("CLOSE_ACTIVITY_LOCK"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("端口号：" + serverPort + "定时生成房源白名单任务开始时间为：{}, 结束时间为：{}的执行出现异常！异常为：{}", startTime + ":00", startTime + ":59", e);
        } finally {
            redisTemplate.delete("CLOSE_ACTIVITY_LOCK");
        }
        log.info("端口号：" + serverPort + "定时更改活动状态任务开始时间为：{}, 结束时间为：{}的执行时间为：{}", startTime + ":00", startTime + ":59", (System.currentTimeMillis() - now.getTime()) + "ms");
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/28 14:32
     * @Param        now        当前时间
     * @Param        startTime  开始时间
     * @Return       void
     * @Description  TODO 定时刷新活动批次数据
     **/
    public void updateResourceBatch(Date now, String startTime) {
        LocalDateTime localDateTime = LocalDateTime.now();
        Date nowDate = new Date();
        try {
            // 获取锁
            Boolean lock = redisTemplate.opsForValue().setIfAbsent("UPDATE_BATCH_LOCK", startTime);
            if (lock) {
                redisTemplate.expire("UPDATE_BATCH_LOCK", 30, TimeUnit.SECONDS);
                // 查 进行中 && 是选房模式 && 开启按批次选房 && 正式结束时间小于当前时间的活动
                QueryWrapper<OpActivity> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(OpActivity::getStatusCode, GlobalConstants.STATUS_R);
                wrapper.lambda().eq(OpActivity::getCheckMode, 1);
                wrapper.lambda().eq(OpActivity::getBatchFlag, 1);
                wrapper.lambda().lt(OpActivity::getFormalEnd, localDateTime);
                List<OpActivity> activityList = opActivityMapper.selectList(wrapper);
                log.info("端口号：" + serverPort + "定时更改房源批次任务————开始时间为：{}, 结束时间为：{}的查询所有活动的结果为：{}", startTime + ":00", startTime + ":59", JSONObject.toJSON(activityList).toString());
                if (CollectionUtils.isNotEmpty(activityList)) {
                    for (OpActivity job : activityList) {
                        // 校验
                        Object redisObj = redisTemplate.opsForValue().get(job.getId().toString() + "Activity_ALL_BATCH");
                        // 校验
                        if (null == redisObj) {
                            continue;
                        }
                        List<BatchConditionVo> batchConditionVoList = (List<BatchConditionVo>) redisObj;
                        // 定义待删除模拟批次集合 定义待删除正式批次集合
                        List<String> waitDelSimulationBatchNameList = Lists.newArrayListWithCapacity(batchConditionVoList.size());
                        List<String> waitDelFormalBatchNameList = Lists.newArrayListWithCapacity(batchConditionVoList.size());
                        for (BatchConditionVo batch : batchConditionVoList) {
                            // 校验当前批次是否需要同步至下一批次
                            if (null == batch.getSyncFlag() || 0 != batch.getSyncFlag().intValue()) {
                                continue;
                            }

                            // 处理模拟批次
                            // 校验当前时间处于哪个时间区间
                            if (null != batch.getSimulationBeginDate() || null != job.getSimulationStart()) {
                                // 校验
                                if (null != batch.getSimulationBeginDate()) {
                                    if (nowDate.after(batch.getSimulationBeginDate())) {
                                        // 汇总当前批次之前的所有批次
                                        for (BatchConditionVo vo : batchConditionVoList) {
                                            // 校验当前批次是否需要同步至下一批次
                                            if (null == vo.getSyncFlag() || 0 != vo.getSyncFlag().intValue()) {
                                                continue;
                                            }
                                            // 校验优先级
                                            if (vo.getPriorityLevel().intValue() < batch.getPriorityLevel().intValue() && !vo.getSimulationFlag()) {
                                                if (!waitDelSimulationBatchNameList.contains(vo.getBatchName())) {
                                                    waitDelSimulationBatchNameList.add(vo.getBatchName());
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    if (localDateTime.isAfter(job.getSimulationStart())) {
                                        // 汇总当前批次之前的所有批次
                                        for (BatchConditionVo vo : batchConditionVoList) {
                                            // 校验当前批次是否需要同步至下一批次
                                            if (null == vo.getSyncFlag() || 0 != vo.getSyncFlag().intValue()) {
                                                continue;
                                            }
                                            // 校验优先级
                                            if (vo.getPriorityLevel().intValue() < batch.getPriorityLevel().intValue() && !vo.getSimulationFlag()) {
                                                if (!waitDelSimulationBatchNameList.contains(vo.getBatchName())) {
                                                    waitDelSimulationBatchNameList.add(vo.getBatchName());
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            // 处理正式批次
                            // 校验当前时间处于哪个时间区间
                            if (null != batch.getFormalBeginDate() || null != job.getFormalStart()) {
                                // 校验
                                if (null != batch.getFormalBeginDate()) {
                                    if (nowDate.after(batch.getFormalBeginDate())) {
                                        // 汇总当前批次之前的所有批次
                                        for (BatchConditionVo vo : batchConditionVoList) {
                                            // 校验当前批次是否需要同步至下一批次
                                            if (null == vo.getSyncFlag() || 0 != vo.getSyncFlag().intValue()) {
                                                continue;
                                            }
                                            // 校验优先级
                                            if (vo.getPriorityLevel().intValue() < batch.getPriorityLevel().intValue() && !vo.getFormalFlag()) {
                                                if (!waitDelFormalBatchNameList.contains(vo.getBatchName())) {
                                                    waitDelFormalBatchNameList.add(vo.getBatchName());
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    if (localDateTime.isAfter(job.getFormalStart())) {
                                        // 汇总当前批次之前的所有批次
                                        for (BatchConditionVo vo : batchConditionVoList) {
                                            // 校验当前批次是否需要同步至下一批次
                                            if (null == vo.getSyncFlag() || 0 != vo.getSyncFlag().intValue()) {
                                                continue;
                                            }
                                            // 校验优先级
                                            if (vo.getPriorityLevel().intValue() < batch.getPriorityLevel().intValue() && !vo.getFormalFlag()) {
                                                if (!waitDelFormalBatchNameList.contains(vo.getBatchName())) {
                                                    waitDelFormalBatchNameList.add(vo.getBatchName());
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // 校验本次操作是否需要变更批次
                        boolean needUpdateRedis = false;
                        // 模拟
                        if (waitDelSimulationBatchNameList.size() > 0) {
                            // 批量刷模拟数据
                            iOpHousingResourcesService.updateBatchNameSimulation(job.getId().toString(), waitDelSimulationBatchNameList);
                            // 更新模拟刷新状态
                            for (BatchConditionVo batch : batchConditionVoList) {
                                if (waitDelSimulationBatchNameList.contains(batch.getBatchName())) {
                                    batch.setSimulationFlag(true);
                                }
                            }
                            needUpdateRedis = true;
                            log.info("端口号：" + serverPort + "定时更改房源批次任务开始时间为：{}, 活动ID为：{}, 刷新模拟批次集合为：{}", startTime + ":00", job.getId(), waitDelSimulationBatchNameList);
                        }
                        // 正式
                        if (waitDelFormalBatchNameList.size() > 0) {
                            // 批量刷正式数据
                            iOpHousingResourcesService.updateBatchNameFormal(job.getId().toString(), waitDelFormalBatchNameList);
                            // 更新正式刷新状态
                            for (BatchConditionVo batch : batchConditionVoList) {
                                if (waitDelFormalBatchNameList.contains(batch.getBatchName())) {
                                    batch.setFormalFlag(true);
                                }
                            }
                            needUpdateRedis = true;
                            log.info("端口号：" + serverPort + "定时更改房源批次任务开始时间为：{}, 活动ID为：{}, 刷新正式批次集合为：{}", startTime + ":00", job.getId(), waitDelFormalBatchNameList);
                        }
                        if (needUpdateRedis) {
                            redisTemplate.opsForValue().set(job.getId().toString() + "Activity_ALL_BATCH", batchConditionVoList);
                        }
                    }
                }
            } else {
                log.info("端口号：" + serverPort + "定时更改房源批次任务开始时间为：{}, 结束时间为：{} 的业务锁被占用，本次任务跳过！被占用的任务时间为：{}", startTime + ":00", startTime + ":59", redisTemplate.opsForValue().get("UPDATE_BATCH_LOCK"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("端口号：" + serverPort + "定时更改房源批次任务开始时间为：{}, 结束时间为：{}的执行出现异常！异常为：{}", startTime + ":00", startTime + ":59", e);
        } finally {
            redisTemplate.delete("UPDATE_BATCH_LOCK");
        }
        log.info("端口号：" + serverPort + "定时更改房源批次任务开始时间为：{}, 结束时间为：{}的执行时间为：{}", startTime + ":00", startTime + ":59", (System.currentTimeMillis() - now.getTime()) + "ms");
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:26 2023/10/24
     * @param now
     * @param startTime
     * @description // TODO 发送短信
     **/
    public void sendOpenActivirySms(Date now, String startTime) {
        LocalDateTime localDateTime = LocalDateTime.now();
        try {
            // 获取锁
            Boolean lock = redisTemplate.opsForValue().setIfAbsent("SEND_MESSAGE_OPEN_ACTIVITY_LOCK", startTime);
            if (lock) {
                redisTemplate.expire("SEND_MESSAGE_OPEN_ACTIVITY_LOCK", 30, TimeUnit.MINUTES);
                // 查短信任务
                QueryWrapper<OpMassageJob> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(OpMassageJob::getIsSent, GlobalConstants.N);
                wrapper.lambda().lt(OpMassageJob::getSendTime, localDateTime);
                wrapper.lambda().eq(OpMassageJob::getJobType, smsConfig.TEMP_CODE_ACTIVITY);
                List<OpMassageJob> jobList = opMassageJobMapper.selectList(wrapper);
                if (CollectionUtils.isNotEmpty(jobList)) {
                    for (OpMassageJob job : jobList) {
                        // 校验活动
                        if (StringUtils.isBlank(smsConfig.getSpecialId()) || smsConfig.getSpecialId().equals(job.getActivityId())) {
                            // 校验该任务是否有短信内容
                            if (StringUtils.isNotBlank(job.getMsgContent())) {
                                // 查所有用户
                                QueryWrapper<OpUser> userQueryWrapper = new QueryWrapper<>();
                                userQueryWrapper.lambda().eq(OpUser::getActivityId, job.getActivityId());
                                userQueryWrapper.lambda().eq(OpUser::getYn, GlobalConstants.Y);
                                userQueryWrapper.lambda().eq(OpUser::getRoleName, "customer");
                                List<OpUser> opUserList = opUserMapper.selectList(userQueryWrapper);
                                if (CollectionUtils.isNotEmpty(opUserList)) {
                                    // 遍历发短信
                                    for (OpUser user : opUserList) {
                                        boolean b = aliyunService.sendSms(user.getTel(), smsConfig.TEMP_CODE_ACTIVITY, job.getMsgContent());
                                        log.info("活动开始短信发送活动id：{}, 模板：{}, 手机号：{}, 发送结果：{}", job.getActivityId(), smsConfig.TEMP_CODE_ACTIVITY, user.getTel(), b);
                                    }
                                }
                                job.setIsSent(GlobalConstants.Y);
                                job.setUpdateTime(localDateTime);
                                opMassageJobMapper.updateById(job);
                            }
                        }
                    }
                }
                log.info("端口号：" + serverPort + "定时发送活动开始短信任务————开始时间为：{}, 结束时间为：{}", startTime + ":00", startTime + ":59");
            } else {
                log.info("端口号：" + serverPort + "定时发送活动开始短信任务开始时间为：{}, 结束时间为：{} 的业务锁被占用，本次任务跳过！被占用的任务时间为：{}", startTime + ":00", startTime + ":59", redisTemplate.opsForValue().get("SEND_MESSAGE_OPEN_ACTIVITY_LOCK"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("端口号：" + serverPort + "定时发送活动开始短信任务开始时间为：{}, 结束时间为：{}的执行出现异常！异常为：{}", startTime + ":00", startTime + ":59", e);
        } finally {
            redisTemplate.delete("SEND_MESSAGE_OPEN_ACTIVITY_LOCK");
        }
        log.info("端口号：" + serverPort + "定时发送活动开始短信任务开始时间为：{}, 结束时间为：{}的执行时间为：{}", startTime + ":00", startTime + ":59", (System.currentTimeMillis() - now.getTime()) + "ms");
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:26 2023/10/24
     * @param now
     * @param startTime
     * @description // TODO 发送个人开始选房短信
     **/
    public void sendOpenCheckSms(Date now, String startTime) {
        LocalDateTime localDateTime = LocalDateTime.now();
        try {
            // 获取锁
            Boolean lock = redisTemplate.opsForValue().setIfAbsent("SEND_MESSAGE_OPEN_CHECK_LOCK", startTime);
            if (lock) {
                redisTemplate.expire("SEND_MESSAGE_OPEN_CHECK_LOCK", 30, TimeUnit.MINUTES);
                // 查短信任务
                QueryWrapper<OpMassageJob> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(OpMassageJob::getIsSent, GlobalConstants.N);
                wrapper.lambda().lt(OpMassageJob::getSendTime, localDateTime);
                wrapper.lambda().eq(OpMassageJob::getJobType, smsConfig.TEMP_CODE_PERSON);
                List<OpMassageJob> jobList = opMassageJobMapper.selectList(wrapper);
                if (CollectionUtils.isNotEmpty(jobList)) {
                    for (OpMassageJob job : jobList) {
                        // 校验活动
                        if (StringUtils.isBlank(smsConfig.getSpecialId()) || smsConfig.getSpecialId().equals(job.getActivityId())) {
                            // 校验该任务是否有短信内容
                            if (StringUtils.isNotBlank(job.getMsgContent())) {
                                AliyunVariableVo vo = JSONObject.parseObject(job.getMsgContent(), AliyunVariableVo.class);
                                boolean b = aliyunService.sendSms(vo.getUserMobile(), smsConfig.TEMP_CODE_PERSON, job.getMsgContent());
                                log.info("个人选房短信发送活动id：{}, 模板：{}, 手机号：{}, 发送结果：{}", job.getActivityId(), smsConfig.TEMP_CODE_PERSON, vo.getUserMobile(), b);
                                job.setIsSent(GlobalConstants.Y);
                                job.setUpdateTime(localDateTime);
                                opMassageJobMapper.updateById(job);
                            }
                        }
                    }
                }
                log.info("端口号：" + serverPort + "定时发送个人选房短信任务————开始时间为：{}, 结束时间为：{}", startTime + ":00", startTime + ":59");
            } else {
                log.info("端口号：" + serverPort + "定时发送个人选房短信任务开始时间为：{}, 结束时间为：{} 的业务锁被占用，本次任务跳过！被占用的任务时间为：{}", startTime + ":00", startTime + ":59", redisTemplate.opsForValue().get("SEND_MESSAGE_OPEN_CHECK_LOCK"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("端口号：" + serverPort + "定时发送个人选房短信任务开始时间为：{}, 结束时间为：{}的执行出现异常！异常为：{}", startTime + ":00", startTime + ":59", e);
        } finally {
            redisTemplate.delete("SEND_MESSAGE_OPEN_CHECK_LOCK");
        }
        log.info("端口号：" + serverPort + "定时发送个人选房短信任务开始时间为：{}, 结束时间为：{}的执行时间为：{}", startTime + ":00", startTime + ":59", (System.currentTimeMillis() - now.getTime()) + "ms");
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:26 2023/10/24
     * @param now
     * @param startTime
     * @description // TODO 发送开始摇号短信
     **/
    public void sendOpenLotterySms(Date now, String startTime) {
        LocalDateTime localDateTime = LocalDateTime.now();
        try {
            // 获取锁
            Boolean lock = redisTemplate.opsForValue().setIfAbsent("SEND_MESSAGE_OPEN_LOTTERY_LOCK", startTime);
            if (lock) {
                redisTemplate.expire("SEND_MESSAGE_OPEN_LOTTERY_LOCK", 30, TimeUnit.MINUTES);
                // 查短信任务
                QueryWrapper<OpMassageJob> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(OpMassageJob::getIsSent, GlobalConstants.N);
                wrapper.lambda().lt(OpMassageJob::getSendTime, localDateTime);
                wrapper.lambda().eq(OpMassageJob::getJobType, smsConfig.TEMP_CODE_OPEN_LOTTERY);
                List<OpMassageJob> jobList = opMassageJobMapper.selectList(wrapper);
                if (CollectionUtils.isNotEmpty(jobList)) {
                    for (OpMassageJob job : jobList) {
                        // 校验活动
                        if (StringUtils.isBlank(smsConfig.getSpecialId()) || smsConfig.getSpecialId().equals(job.getActivityId())) {
                            // 校验该任务是否有短信内容
                            if (StringUtils.isNotBlank(job.getMsgContent())) {
                                // 查所有用户
                                QueryWrapper<OpUser> userQueryWrapper = new QueryWrapper<>();
                                userQueryWrapper.lambda().eq(OpUser::getActivityId, job.getActivityId());
                                userQueryWrapper.lambda().eq(OpUser::getYn, GlobalConstants.Y);
                                userQueryWrapper.lambda().eq(OpUser::getRoleName, "customer");
                                List<OpUser> opUserList = opUserMapper.selectList(userQueryWrapper);
                                if (CollectionUtils.isNotEmpty(opUserList)) {
                                    // 遍历发短信
                                    for (OpUser user : opUserList) {
                                        boolean b = aliyunService.sendSms(user.getTel(), smsConfig.TEMP_CODE_OPEN_LOTTERY, job.getMsgContent());
                                        log.info("活动摇号短信发送活动id：{}, 模板：{}, 手机号：{}, 发送结果：{}", job.getActivityId(), smsConfig.TEMP_CODE_OPEN_LOTTERY, user.getTel(), b);
                                    }
                                }
                                job.setIsSent(GlobalConstants.Y);
                                job.setUpdateTime(localDateTime);
                                opMassageJobMapper.updateById(job);
                            }
                        }
                    }
                }
                log.info("端口号：" + serverPort + "定时发送活动摇号短信任务————开始时间为：{}, 结束时间为：{}", startTime + ":00", startTime + ":59");
            } else {
                log.info("端口号：" + serverPort + "定时发送活动摇号短信任务开始时间为：{}, 结束时间为：{} 的业务锁被占用，本次任务跳过！被占用的任务时间为：{}", startTime + ":00", startTime + ":59", redisTemplate.opsForValue().get("SEND_MESSAGE_OPEN_LOTTERY_LOCK"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("端口号：" + serverPort + "定时发送活动摇号短信任务开始时间为：{}, 结束时间为：{}的执行出现异常！异常为：{}", startTime + ":00", startTime + ":59", e);
        } finally {
            redisTemplate.delete("SEND_MESSAGE_OPEN_LOTTERY_LOCK");
        }
        log.info("端口号：" + serverPort + "定时发送活动摇号短信任务开始时间为：{}, 结束时间为：{}的执行时间为：{}", startTime + ":00", startTime + ":59", (System.currentTimeMillis() - now.getTime()) + "ms");
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:26 2023/10/24
     * @param now
     * @param startTime
     * @description // TODO 发送开始摇号短信
     **/
    public void sendLotteryResultSms(Date now, String startTime) {
        LocalDateTime localDateTime = LocalDateTime.now();
        try {
            // 获取锁
            Boolean lock = redisTemplate.opsForValue().setIfAbsent("SEND_MESSAGE_LOTTERY_RESULT_LOCK", startTime);
            if (lock) {
                redisTemplate.expire("SEND_MESSAGE_LOTTERY_RESULT_LOCK", 30, TimeUnit.MINUTES);
                // 查短信任务
                QueryWrapper<OpMassageJob> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(OpMassageJob::getIsSent, GlobalConstants.N);
                wrapper.lambda().lt(OpMassageJob::getSendTime, localDateTime);
                wrapper.lambda().eq(OpMassageJob::getJobType, smsConfig.TEMP_CODE_LOTTERY_RESULT);
                List<OpMassageJob> jobList = opMassageJobMapper.selectList(wrapper);
                if (CollectionUtils.isNotEmpty(jobList)) {
                    for (OpMassageJob job : jobList) {
                        // 校验活动
                        if (StringUtils.isBlank(smsConfig.getSpecialId()) || smsConfig.getSpecialId().equals(job.getActivityId())) {
                            // 校验该任务是否有短信内容
                            if (StringUtils.isNotBlank(job.getMsgContent())) {
                                AliyunVariableVo vo = JSONObject.parseObject(job.getMsgContent(), AliyunVariableVo.class);
                                // 查所有用户
                                QueryWrapper<OpUser> userQueryWrapper = new QueryWrapper<>();
                                userQueryWrapper.lambda().eq(OpUser::getActivityId, job.getActivityId());
                                userQueryWrapper.lambda().eq(OpUser::getYn, GlobalConstants.Y);
                                userQueryWrapper.lambda().eq(OpUser::getRoleName, "customer");
                                List<OpUser> opUserList = opUserMapper.selectList(userQueryWrapper);
                                if (CollectionUtils.isNotEmpty(opUserList)) {
                                    // 遍历发短信
                                    for (OpUser user : opUserList) {
                                        vo.setUserSort(user.getUserSort() + "");
                                        vo.setUserName(user.getName());
                                        boolean b = aliyunService.sendSms(user.getTel(), smsConfig.TEMP_CODE_LOTTERY_RESULT, JSON.toJSONString(vo));
                                        log.info("一键摇号结果短信发送活动id：{}, 模板：{}, 手机号：{}, 发送结果：{}", job.getActivityId(), smsConfig.TEMP_CODE_LOTTERY_RESULT, user.getTel(), b);
                                    }
                                }
                                job.setIsSent(GlobalConstants.Y);
                                job.setUpdateTime(localDateTime);
                                opMassageJobMapper.updateById(job);
                            }
                        }
                    }
                }
                log.info("端口号：" + serverPort + "一键摇号结果短信任务————开始时间为：{}, 结束时间为：{}", startTime + ":00", startTime + ":59");
            } else {
                log.info("端口号：" + serverPort + "一键摇号结果短信任务开始时间为：{}, 结束时间为：{} 的业务锁被占用，本次任务跳过！被占用的任务时间为：{}", startTime + ":00", startTime + ":59", redisTemplate.opsForValue().get("SEND_MESSAGE_LOTTERY_RESULT_LOCK"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("端口号：" + serverPort + "一键摇号结果短信任务开始时间为：{}, 结束时间为：{}的执行出现异常！异常为：{}", startTime + ":00", startTime + ":59", e);
        } finally {
            redisTemplate.delete("SEND_MESSAGE_LOTTERY_RESULT_LOCK");
        }
        log.info("端口号：" + serverPort + "一键摇号结果短信任务开始时间为：{}, 结束时间为：{}的执行时间为：{}", startTime + ":00", startTime + ":59", (System.currentTimeMillis() - now.getTime()) + "ms");
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 11:03 2024/8/28
     * @description // TODO     定时同步组织机构、用户数据
     **/
    @Scheduled(cron = "0 0 1 * * ?")
    public void autoSyncOrgAndUser() {
        Date nowDate = new Date();
        String dateString = DateUtils.getDateString(nowDate, DateUtils.PATTERN_SECOND);
        try {
            // 获取锁
            Boolean lock = redisTemplate.opsForValue().setIfAbsent("SYNC_ORG_AND_USER_LOCK", serverPort);
            if (lock) {
                redisTemplate.expire("SYNC_ORG_AND_USER_LOCK", 30, TimeUnit.SECONDS);
                ApiResult orgResult = syncService.syncOrg();
                log.info("端口号：" + serverPort + "定时同步组织和人员任务开始时间为：{}的结果为：{}", dateString, JSONObject.toJSON(orgResult).toString());
                ApiResult userResult = syncService.syncUser();
                log.info("端口号：" + serverPort + "定时同步组织和人员任务开始时间为：{}的结果为：{}", dateString, JSONObject.toJSON(userResult).toString());
            } else {
                log.info("端口号：" + serverPort + "定时同步组织和人员任务开始时间为：{}的业务锁被占用，本次任务跳过！被占用的任务为：{}", dateString, redisTemplate.opsForValue().get("SYNC_ORG_AND_USER_LOCK"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("端口号：" + serverPort + "定时同步组织和人员任务开始时间为：{}的执行出现异常！异常为：{}", dateString, e);
        } finally {
            redisTemplate.delete("SYNC_ORG_AND_USER_LOCK");
        }
        log.info("端口号：" + serverPort + "定时同步组织和人员任务开始时间为：{}的执行时间为：{}", dateString, (new Date().getTime() - nowDate.getTime()) + "ms");
    }
}
