package com.tahoecn.opening.server;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.DateUtils;
import com.tahoecn.opening.mapper.OpHousingResourcesMapper;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpHousingResources;
import com.tahoecn.opening.service.IOpActivityService;
import com.tahoecn.opening.service.IOpHousingResourcesService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;


@ServerEndpoint("/websocket/{sid}")
@Component
public class WebSocketServer {


    private static IOpHousingResourcesService iOpHousingResourcesService;


    private static IOpActivityService activityService;

    @Autowired
    public void setiOpHousingResourcesService(IOpHousingResourcesService iOpHousingResourcesService) {
        WebSocketServer.iOpHousingResourcesService = iOpHousingResourcesService;
    }

    @Autowired
    public void setActivityService(IOpActivityService activityService) {
        WebSocketServer.activityService = activityService;
    }

    private static Integer serverPort;

    @Value("${server.port}")
    public void setServerPort(Integer serverPort) {
        WebSocketServer.serverPort = serverPort;
    }

    static Log log=LogFactory.get(WebSocketServer.class);
    //静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
    //private static int onlineCount = 0;
    private final static AtomicInteger onlineCount = new AtomicInteger(0);
    //concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
    private static CopyOnWriteArraySet<WebSocketServer> webSocketSet = new CopyOnWriteArraySet<WebSocketServer>();

    //与某个客户端的连接会话，需要通过它来给客户端发送数据
    private Session session;

    //接收sid
    private String sid="";

    /**
     * 连接建立成功调用的方法*/
    @OnOpen
    public void onOpen(Session session,@PathParam("sid") String sid) {
        this.session = session;
        this.sid=sid;
        webSocketSet.add(this);     //加入set中
        //addOnlineCount();           //在线数加1
        WebSocketServer.onlineCount.incrementAndGet();//在线数加1
        log.info("端口号：{}, 有新窗口开始监听！活动id为：{}, 当前在线人数为：{}", serverPort, sid, onlineCount);
        try {
        	 sendMessage("连接成功");
        } catch (IOException e) {
            log.error("websocket IO异常");
        }
        execute();
    }

    private void execute(){
        iOpHousingResourcesService.pushWebsocketData(sid);
    }
//
//    private void execute(){
////        while (onlineCount.intValue() > 0){
//        OpActivity opActivity =  activityService.getById(sid);
//        HashMap<String, Object> resultMap = iOpHousingResourcesService.getOpHousingBaseData(sid, DateUtils.regular(opActivity, LocalDateTime.now()));
//        resultMap.put("opActivity", opActivity);
//        int todayLoginUserCount = (int)(resultMap.get("todayLoginUserCount") == null ? 0: resultMap.get("todayLoginUserCount"))+(opActivity.getJoinNumber() == null ? 0 : opActivity.getJoinNumber());
//        resultMap.put("todayLoginUserCount", todayLoginUserCount);
//        resultMap.put("sysDate", new Date().getTime());
//
//        // 销控逻辑
//        // 定义是否需要校验 销控逻辑标识
//        boolean saleControlFlag = false;
//        LocalDateTime nowDate = LocalDateTime.now();
//        // 校验 当前时间 在 模拟开盘的开始和结束时间区间内
//        if (null != opActivity.getSimulationStart() && null != opActivity.getSimulationEnd()) {
//            // 在 模拟开始结束时间区间内
//            if (nowDate.isAfter(opActivity.getSimulationStart()) && nowDate.isBefore(opActivity.getSimulationEnd())) {
//                saleControlFlag = true;
//            }
//        }
//        // 校验 当前时间 在 正式开盘的开始和结束时间区间内
//        if (null != opActivity.getFormalStart() && null != opActivity.getFormalEnd()) {
//            // 在 正式开始时间以后
//            if (nowDate.isAfter(opActivity.getFormalStart())) {
//                saleControlFlag = true;
//            }
//        }
//        if (saleControlFlag) {
//            // 获取该活动 对应销控的房源数据信息
//            QueryWrapper<OpHousingResources> resourceQueryWrapper = new QueryWrapper<>();
//            resourceQueryWrapper.eq("activity_id", sid).eq("sale_control_flag", "y").eq("yn", "y").orderByAsc("sale_control_date");
//            List<OpHousingResources> saleControlResourceList = iOpHousingResourcesService.list(resourceQueryWrapper);
//            // 校验
//            if (CollectionUtils.isNotEmpty(saleControlResourceList)) {
//                // 获取 已存在的订单数据
//                //可选套数
//                Integer optionalProperties = Integer.valueOf(resultMap.get("optionalProperties") + "");
//                //resultMap.put("optionalProperties", optionalProperties.intValue() - saleControlResourceList.size());
//                // 当前活动销控人数
//                resultMap.put("todayLoginUserCount", todayLoginUserCount + saleControlResourceList.size());
//                //订单滚动提示
//                List<OpHousingResources> orderDataList = (List<OpHousingResources>) resultMap.get("orderDataList");
//                // 定义总集合
//                // 校验赋值
//                if (CollectionUtils.isNotEmpty(orderDataList)) {
//                    saleControlResourceList.addAll(orderDataList);
//                }
//                // 按照 销控时间/订单创建时间，倒序排序
//                saleControlResourceList = saleControlResourceList.stream().sorted(Comparator.comparing(OpHousingResources::getSaleControlDate).reversed()).collect(Collectors.toList());
//
//                //已售金额
//                BigDecimal totalAcount = new BigDecimal("0.00");
//                //订单滚动提示
//                List<String> oderList = new ArrayList<>();
//                //成交房间Id
//                String houserIdsString = "";
//                BigDecimal partten = new BigDecimal("10000");
//                // 模拟生成销控房源记录数据
//                for (OpHousingResources resources : saleControlResourceList) {
//                    houserIdsString = houserIdsString +  resources.getId().toString() + ",";
//                    totalAcount = totalAcount.add(resources.getTotalPrice());
//                    oderList.add(//"恭喜" + resources.getRemark() + "**，成功选定-" +
//                            resources.getBuildingName() + resources.getUnitName() + resources.getRoomNum()+ " 已被成功选定");
//                }
//                resultMap.put("optionalProperties",iOpHousingResourcesService.getOptionalProperties(sid, DateUtils.regular(opActivity, nowDate)));
//                resultMap.put("houserIdsString", StringUtils.isBlank(houserIdsString) ? "" : houserIdsString.substring(0, houserIdsString.length() - 1));
//                totalAcount = totalAcount.divide(partten, 2, BigDecimal.ROUND_HALF_UP);
//                resultMap.put("totalAcount", totalAcount);
//                resultMap.put("oderList", oderList);
//            }
//        }
//
//        JSONResult<Object> jsonResult = new JSONResult<>();
//        jsonResult.setCode(GlobalConstants.S_CODE);
//        jsonResult.setMsg("SUCCESS");
//        jsonResult.setData(resultMap);
//
//        try {
//            WebSocketServer.sendInfo(JSON.toJSONString(jsonResult),sid);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
////        }
//    }


    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        webSocketSet.remove(this);  //从set中删除
        //subOnlineCount();           //在线数减1
        WebSocketServer.onlineCount.decrementAndGet();
        log.info("端口号：{}, 有连接关闭！当前在线人数为：{}", serverPort, onlineCount);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:39 2023/11/8
     * @param message
     * @param session 
     * @description // TODO 接收客户端心跳消息
     **/
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("端口号：{}, 收到来自活动id为：{}的信息！信息内容为：{}", serverPort, sid, message);
        //群发消息
        for (WebSocketServer item : webSocketSet) {
            try {
                // 匹配并返回心跳内容
                if(sid == null) {
                    // 全局不限制推送
                    item.sendMessage(message);
                } else if(item.sid.equals(sid)){
                    // 只推送匹配到的活动ID
                    item.sendMessage(message);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

	/**
	 * 
	 * @param session
	 * @param error
	 */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("发生错误");
        error.printStackTrace();
    }
	/**
	 * 实现服务器主动推送
	 */
    public void sendMessage(String message) throws IOException {
        this.session.getBasicRemote().sendText(message);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 11:02 2023/11/7
     * @param message   消息体
     * @param sid       唯一标识ID
     * @description // TODO 应用群发推送消息到前端
     **/
    public static void sendInfo(String message, @PathParam("sid") String sid) throws IOException {
        // 循环推送
        for (WebSocketServer item : webSocketSet) {
            try {
            	if(sid == null) {
                    log.info("端口号：{}全局推送消息！推送消息体为：{}", serverPort, message);
                    // 全局不限制推送
                    item.sendMessage(message);
                } else if(item.sid.equals(sid)){
                    // 只推送匹配到的活动ID
                    log.info("端口号：{}推送消息的活动id为：{}, 推送消息体为：{}", serverPort, sid, message);
                    item.sendMessage(message);
            	}
            } catch (IOException e) {
                e.printStackTrace();
                log.error("端口号：{}推送消息出现异常！异常活动id为：{}, 推送消息体为：{}, 异常内容为：{}", serverPort, sid, message, e);
                continue;
            }
        }
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 11:45 2023/11/7
     * @param activityId    活动id
     * @return boolean
     * @description // TODO 检查当前应用当前活动是否存在连接
     **/
    public static boolean checkWebsocket(String activityId, Integer serverPort) {
        log.info("端口号为：{}, 判断活动id为：{}, 当前应用连接为：{}", serverPort, activityId, JSONObject.toJSON(webSocketSet).toString());
        // 校验
        if (null == activityId) {
            return false;
        }
        if (webSocketSet.size() == 0) {
            return false;
        }
        for (WebSocketServer item : webSocketSet) {
            log.info("端口号为：{}, 判断活动id为：{}, 循环应用连接的sid为：{}", serverPort, activityId, item.sid);
            if (item.sid.equals(activityId)) {
                return true;
            }
        }
        return false;
    }
}

