package com.tahoecn.opening.controller.app;


import com.alibaba.fastjson.JSONObject;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.RqCodeUtils;
import com.tahoecn.opening.controller.webapi.OpActivityController;
import com.tahoecn.opening.converter.ResponseMessage;
import com.tahoecn.opening.service.CsUcUserService;
import com.tahoecn.opening.service.CsUserRoleService;
import com.tahoecn.opening.service.IOpActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import com.tahoecn.opening.controller.TahoeBaseController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 公用 前端控制器
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/app/common")
@Api(tags = "公用控制器", value = "公用控制器")
public class CommonController extends TahoeBaseController {

	@Autowired
	private CsUserRoleService csUserRoleService;

	@Autowired
	private IOpActivityService activityService;

	@Autowired
	private RestTemplate restTemplate;

	@Autowired
	private OpActivityController activityController;

	@Value("${tahoe.application.physicalPath}")
	private  String physicalPath;

	@Value("${system_HOST}")
	private  String system_HOST;

	@Qualifier("taskExecutor")
	@Autowired
	ThreadPoolTaskExecutor taskExecutor;


	@ApiOperation(value = "倒计时时间校验", notes = "倒计时时间校验")
	@RequestMapping(value = "/getDate", method = { RequestMethod.POST, RequestMethod.GET })
	public Date getDate() {
		return new Date();
	}

	@ApiOperation(value = "倒计时时间校验", notes = "倒计时时间校验")
	@RequestMapping(value = "/getDateTwo", method = { RequestMethod.POST, RequestMethod.GET })
	public CompletableFuture<Date> getDateTwo() {
		System.out.println("主线程：当前方法调用线程名称为= " + Thread.currentThread().getName());
		return CompletableFuture.supplyAsync(() -> {
			System.out.println("副线程：当前方法调用线程名称为= " + Thread.currentThread().getName());
			// do something
			return new Date();
		}, taskExecutor);
	}

	@Async("taskExecutor")
	@ApiOperation(value = "倒计时时间校验", notes = "倒计时时间校验")
	@RequestMapping(value = "/getDateThree", method = { RequestMethod.POST, RequestMethod.GET })
	public Date getDateThree() {
		System.out.println("当前方法调用线程名称为= " + Thread.currentThread().getName());
		return new Date();
	}

	@ApiOperation(value = "倒计时时间校验", notes = "倒计时时间校验")
	@RequestMapping(value = "/getDateFour", method = { RequestMethod.POST, RequestMethod.GET })
	public Map<String, Object> getDateFour() {
		Map<String, Object> poolInfo = new HashMap<>();
		ThreadPoolExecutor threadPoolExecutor = taskExecutor.getThreadPoolExecutor();
		poolInfo.put("activeCount", threadPoolExecutor.getActiveCount());
		poolInfo.put("completedTask", threadPoolExecutor.getCompletedTaskCount());
		poolInfo.put("poolSize", threadPoolExecutor.getPoolSize());
		poolInfo.put("queueSize", threadPoolExecutor.getQueue().size());
		poolInfo.put("taskCount", threadPoolExecutor.getTaskCount());
		poolInfo.put("corePoolSize", threadPoolExecutor.getCorePoolSize());
		poolInfo.put("largetPoolSize", threadPoolExecutor.getLargestPoolSize());
		poolInfo.put("maxPoolSize", threadPoolExecutor.getMaximumPoolSize());
		return poolInfo;
	}


	/**
	 * 组织数
	 */
	@ApiOperation(value = "组织树")
	@RequestMapping(value = "/orgTree", method = {RequestMethod.GET})
	public JSONResult orgTree() {
		JSONResult jsonResult = new JSONResult();
		jsonResult.setCode(0);
		jsonResult.setMsg("SUCCESS");
		jsonResult.setData(csUserRoleService.orgTree());
		return jsonResult;
	}



	/**
	 * 接收二维码 改用叁石云接口

	@ApiOperation(value = "生成小程序二维码")
	@RequestMapping(value="/twoCode",method=RequestMethod.POST,produces="text/html;charset=utf-8")
	@ResponseBody
	public Object twoCode(HttpServletRequest request,int width,String activityId) throws IOException {
		JSONObject data=new JSONObject();
		String accessToken = RqCodeUtils.getToken();
		String twoCodeUrl = RqCodeUtils.getminiqrQr(accessToken,request,width,activityId);//todo:根据活动id生成路径
		data.put("twoCodeUrl", twoCodeUrl);
		return data;

	}
	 */

	/**
	 * 接收二维码 改用叁石云接口
	 */
	@ApiOperation(value = "生成小程序二维码")
	@RequestMapping(value="/twoCode",method=RequestMethod.POST)
	@ResponseBody
	public Object twoCode(HttpServletRequest request, String width, String activityId) throws IOException {
		JSONResult jsonResult = new JSONResult();
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
//		jsonResult.setData(activityService.getQr(activityId,width,""));
		return jsonResult;
	}

	/**
	 * 二维码下载
	 */
	@ApiOperation(value = "二维码下载")
	@RequestMapping(value = "/downloadRqImg", method = RequestMethod.GET)
	public Object fileDownload(@RequestParam("activityId") String activityId, @RequestParam(value = "sign", required = false) String sign, @RequestParam("width") String width, final HttpServletResponse response, HttpServletRequest request) {
		try {
//			String p = request.getSession().getServletContext().getRealPath("/");
			//String codeFile = p + "rqcodeImg" + File.separator+ activityId + File.separator + "twoCode.png";
//			String codeFile = activityService.getQr(activityId,width,"");
			String codeFile = system_HOST;
			if (StringUtils.isNotBlank(sign)) {
                // 签到二维码下载
				codeFile = codeFile + activityController.createWXQRCode(activityId + ",signIn", physicalPath, width);
			} else {
				// 小程序二维码下载
				codeFile = codeFile + activityController.createWXQRCode(activityId, physicalPath, width);
			}

			URL url = new URL(codeFile);
			//打开链接
			HttpURLConnection conn = (HttpURLConnection)url.openConnection();
			conn.setRequestMethod("GET");
			//超时响应时间为5秒
			conn.setConnectTimeout(5 * 1000);
			//通过输入流获取图片数据
			InputStream inStream = conn.getInputStream();

			BufferedOutputStream bos = null;
			try {
				response.setHeader("Content-disposition", "attachment; filename=" + "twoCode.png");
				response.setContentType("multipart/form-data; charset=utf-8");
				//bis = new BufferedInputStream(new FileInputStream(codeFile));
				bos = new BufferedOutputStream(response.getOutputStream());


				byte[] buffer = new byte[1024];
				int len = 0;
				while( (len=inStream.read(buffer)) != -1 ){
					bos.write(buffer, 0, len);
				}

			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				try {
					if (inStream != null)
						inStream.close();
					if (bos != null)
						bos.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			return ResponseMessage.ok("");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseMessage.error("系统错误，请联系管理员");
	}

	/**
	 * 地图测试
	 */
	@ApiOperation(value = "地图测试")
	@RequestMapping(value="/geoconv",method=RequestMethod.POST,produces="text/html;charset=utf-8")
	@ResponseBody
	public Object geoconv(HttpServletRequest request, String width, String activityId) throws IOException {
		HttpHeaders headers = new HttpHeaders();
		HttpEntity<String> requestEntity = new HttpEntity<String>( "", headers);
		JSONResult jsonResult = new JSONResult();
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		//String str = restTemplate.postForObject("http://api.map.baidu.com/geoconv/v1/?coords=116.469948,39.913161&from=1&to=5&ak=tE34XwkKx72Q96QazuGXhwq0liGC0oWa", requestEntity, String.class);
		String str = restTemplate.getForObject("http://api.map.baidu.com/geoconv/v1/?coords=116.469948,39.913161&from=1&to=5&ak=tE34XwkKx72Q96QazuGXhwq0liGC0oWa",String.class);
		jsonResult.setData(str);
		return jsonResult;

	}


}
