package com.tahoecn.opening.controller.webapi;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.*;
import com.tahoecn.opening.controller.TahoeBaseController;
import com.tahoecn.opening.converter.ShareinterJson2HttpMessageConverter;
import com.tahoecn.opening.mapper.OpUserHouseResourceMapper;
import com.tahoecn.opening.model.*;
import com.tahoecn.opening.model.dto.UserHouseResourceDTO;
import com.tahoecn.opening.model.interfaceBean.UserData;
import com.tahoecn.opening.model.interfaceBean.UserRootBean;
import com.tahoecn.opening.model.vo.ConsultantVO;
import com.tahoecn.opening.model.vo.SaleListVO;
import com.tahoecn.opening.model.vo.UserHouseResourceParamVO;
import com.tahoecn.opening.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 客户 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@RestController
@Api(tags = "客户接口", value = "客户接口")
@RequestMapping("/webapi/opUser")
public class OpUserController extends TahoeBaseController {

    @Autowired
    private IOpUserService iOpUserService;
    @Autowired
    private IOpActivityService iOpActivityService;
    @Autowired
    private RestTemplate restTemplate;
    @Value("${control_time}")
    private Double controTime;

    @Value("${control_white_minutes}")
    private Integer controlWhiteMinutes;

    @Value("${const_HOST}")
    private String ConstHOST;

    @Autowired
    IOpOrderService iOpOrderService;

    @Autowired
    IOpFavoriteService favoriteService;

    @Resource
    private OpUserHouseResourceMapper userHouseResourceMapper;

    @Resource
    private IOpBatchService batchService;


    @ApiOperation(value = "客户列表", notes = "分页获取客户列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "pageNum", value = "当前页数", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", dataType = "int")})
    @RequestMapping(value = "/opUserPageList", method = {RequestMethod.GET})
    public JSONResult selectOpUser(@RequestParam(defaultValue = "1") int pageNum,
                                   @RequestParam(defaultValue = "10") int pageSize,
                                   String activityIdNow,
                                   String roleNameSelect,
                                   OpUser opUser) {
        //判空
        Page page = new Page<>(pageNum, pageSize);

        //添加项目id查询项
        opUser.setActivityId(activityIdNow);
        IPage<OpUser> iPage= iOpUserService.getUserList(page,opUser);
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(iPage);
        return jsonResult;
    }

    /**
     * 按活动获取客户列表
     * @param activityIdNow
     * @return
     */
    @ApiOperation(value = "客户列表", notes = "按活动获取客户列表")
    @RequestMapping(value = "/opUserActivityList", method = {RequestMethod.GET})
    public JSONResult selectOpUserActivityList(@RequestParam String activityIdNow) {
        // 查询活动信息
        OpActivity opActivity = iOpActivityService.getById(activityIdNow);
        // 查询客户信息
        LambdaQueryWrapper<OpUser> opUserWrapper = new QueryWrapper<OpUser>()
                .lambda()
                .select(OpUser::getId, OpUser::getProjectId, OpUser::getName,
                        OpUser::getTel, OpUser::getUserSort, OpUser::getActivityId)
                .eq(OpUser::getActivityId, activityIdNow)
                .eq(OpUser::getYn, GlobalConstants.Y)
                .eq(OpUser::getRoleName, "customer")
                .orderByAsc(OpUser::getUserSort);
        List<OpUser> list = iOpUserService.list(opUserWrapper);
        // 数据脱敏
        list.forEach(user -> {
            user.setName(DataMaskingUtil.maskName(user.getName()));
            user.setTel(DataMaskingUtil.maskPhone(user.getTel()));
        });

        Map<String, Object> res = Maps.newHashMap();
        res.put("activityName", Optional.ofNullable(opActivity)
                .map(OpActivity::getLotteryTitle)
                .orElse(""));
        res.put("lotteryFlag", Optional.ofNullable(opActivity)
                .map(OpActivity::getLotteryFlag)
                .orElse(0));
        res.put("lotteryDateFlag", true);
        // 查询活动时间 判断未超过期限 前端判断用
        if (null != opActivity.getLotteryStartDate() && null != opActivity.getLotteryEndDate()) {
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(opActivity.getLotteryStartDate()) || now.isAfter(opActivity.getLotteryEndDate())) {
                res.put("lotteryDateFlag", false);
            }
        }
        res.put("list", list);

        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(res);
        return jsonResult;
    }



    @ApiOperation(value = "置业顾问列表", notes = "分页获取置业顾问列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "pageNum", value = "当前页数", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", dataType = "int")})
    @RequestMapping(value = "/opUserSalePageList", method = {RequestMethod.GET})
    public JSONResult selectOpUserSale(@RequestParam(defaultValue = "1") int pageNum,
                                       @RequestParam(defaultValue = "10") int pageSize,
                                       String activityIdNow,
                                       OpUser opUser) {
        Page page = new Page(pageNum, pageSize);
        IPage<SaleListVO> iPage = iOpUserService.getUserSale(page, activityIdNow, opUser);
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(iPage);
        return jsonResult;
    }

    @ApiOperation(value = "角色下拉列表", notes = "获取角色下拉列表")
    @RequestMapping(value = "/roleName", method = {RequestMethod.GET})
    public JSONResult roleName() {
        List<String> roleName = iOpUserService.getRoleName();

        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(roleName);
        return jsonResult;
    }

    @ApiOperation(value = "认筹单数下拉框", notes = "认筹单数下拉框")
    @RequestMapping(value = "/bookingNum", method = {RequestMethod.GET})
    public JSONResult bookingNum(String activityId) {
        List<String> bookingNumList=iOpUserService.getBookingNum(activityId);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(bookingNumList);
        return jsonResult;
    }

    @ApiOperation(value = "可选房数下拉", notes = "可选房数下拉")
    @RequestMapping(value = "/selecCount", method = {RequestMethod.GET})
    public JSONResult selecCount(String activityId) {
        List<String> selectCount=iOpUserService.getSelecCount(activityId);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(selectCount);
        return jsonResult;
    }
    @ApiOperation(value = "置业顾问下拉", notes = "置业顾问下拉")
    @RequestMapping(value = "/saleName", method = { RequestMethod.GET})
    public JSONResult saleName(String activityId) {
        List<OpUser> list = iOpUserService.getSaleName(activityId);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(list);
        return jsonResult;
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:31 2022/7/19
     * @param userId        用户ID  必传
     * @return com.tahoecn.core.json.JSONResult
     * @description // TODO 根据用户id查询用户白名单房源列表
     **/
    @ApiOperation(value = "根据用户id查询用户白名单房源列表", notes = "根据用户id查询用户白名单房源列表")
    @GetMapping(value = "/getUserHouseResourceListByUserId")
    public JSONResult getUserHouseResourceListByUserId(@RequestParam("userId") Long userId) {
        // 查询到当前
        List<UserHouseResourceDTO> dataList = iOpUserService.getUserHouseResourceListByUserId(userId);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("操作成功！");
        jsonResult.setData(dataList);
        return jsonResult;
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:02 2022/7/19
     * @param paramVO   数据参数VO
     * @param result 
     * @return com.tahoecn.core.json.JSONResult
     * @description // TODO 新增修改用户房源白名单数据
     **/
    @ApiOperation(value = "新增修改用户房源白名单数据", notes = "新增修改用户房源白名单数据")
    @PostMapping(value = "/saveUserHouseResource")
    public JSONResult saveUserHouseResource(@RequestBody @Valid UserHouseResourceParamVO paramVO, BindingResult result) {
        // 获取 活动对象
        OpActivity opActivity = iOpActivityService.getById(paramVO.getActivityId());
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            if (opActivity.getFormalStart() != null) {
                // 校验 当前时间是否在白名单可操作性时间区间内
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime minusMinutes = opActivity.getFormalStart().minusMinutes(controlWhiteMinutes);
                if (now.isAfter(minusMinutes) && now.isBefore(opActivity.getFormalEnd())) {
                    return new JSONResult(GlobalConstants.E_CODE, "操作失败,活动开始前" + controlWhiteMinutes + "分钟或活动开盘期间信息不可操作！");
                }
            }
        }
        return iOpUserService.saveUserHouseResource(paramVO);
    }

    /**
     * 新增客户
     *
     * @param opUser
     * @return
     */
    @ApiOperation(value = "新增客户", notes = "新增单个客户")
    @RequestMapping(value = "/saveOpUser", method = {RequestMethod.POST})
    public JSONResult saveOpUser(@RequestBody OpUser opUser) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId, opUser.getActivityId());
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("操作失败,请先关闭活动再进行操作！");
            return jsonResult;
//            if (opActivity.getFormalStart() != null) {
//                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                    return jsonResult;
//                }
//            }
        }
        //判断手机号码和姓名必填
        if (StringUtils.isBlank(opUser.getTel())||StringUtils.isBlank(opUser.getName())){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("姓名或手机号未添加,不可新增");
            return jsonResult;
        }
        //手机位数校验
        if (opUser.getTel().length()!=11){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("手机号位数错误,不可新增");
            return jsonResult;
        }
        //判断同电话号码不可新增
        QueryWrapper<OpUser> opUserQueryWrapper=new QueryWrapper<>();
        opUserQueryWrapper.eq("tel",opUser.getTel()).eq("yn",GlobalConstants.Y).eq("activity_id",opUser.getActivityId());
        List<OpUser> list = iOpUserService.list(opUserQueryWrapper);
        if (list.size()!=0){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("手机号码重复,请确认手机号");
            jsonResult.setData(opUser);
            return jsonResult;
        }

        // 校验是 普通用户新增，还是置业顾问/销售经理新增
        if ("customer".equals(opUser.getRoleName())) {
            // 校验 身份证位数
            if (StringUtils.isBlank(opUser.getIdCard()) || 18 != opUser.getIdCard().length()) {
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("身份证号位数错误,不可新增");
                return jsonResult;
            } else {
//                // 重复 校验
//                opUserQueryWrapper = new QueryWrapper<>();
//                opUserQueryWrapper.eq("id_card",opUser.getIdCard()).eq("yn",GlobalConstants.Y).eq("activity_id",opUser.getActivityId());
//                list = iOpUserService.list(opUserQueryWrapper);
//                if (list.size() != 0) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("身份证号码重复,请确认身份证号");
//                    jsonResult.setData(opUser);
//                    return jsonResult;
//                }
            }
        }
        // 校验批次
        if (StringUtils.isNotBlank(opUser.getBatchNameStr())) {
            // 选房模式 && 开启按批次处理
            if (null != opActivity.getCheckMode() && 1 == opActivity.getCheckMode().intValue() && null != opActivity.getBatchFlag() && 1 == opActivity.getBatchFlag().intValue()) {
                // 校验批次
                // 获取该活动所有批次
                List<OpBatch> batchList = batchService.selectAllBatchListByActivityId(opUser.getActivityId());
                List<String> collect = batchList.stream().map(OpBatch::getBatchName).collect(Collectors.toList());
                collect.add(GlobalConstants.COMMON);
                // 分割遍历
                List<String> thisList = Arrays.asList(opUser.getBatchNameStr().split(","));
                for (String s : thisList) {
                    if (!collect.contains(s)) {
                        jsonResult.setCode(GlobalConstants.E_CODE);
                        jsonResult.setMsg("该用户批次名称必须在以下批次中：" + collect.stream().collect(Collectors.joining(",")));
                        return jsonResult;
                    }
                }
            }
        }

        //新增人添加
        opUser.setCreatedBy(ThreadLocalUtils.getUserName());
        //新增时间
        opUser.setCreationDate(LocalDateTimeUtils.localDateTimeToDate(LocalDateTime.now()));

        //添加角色名称
//        opUser.setRoleName("customer");

        opUser.setProjectId(opActivity.getProjectId());
        //置业顾问给的是用户id  置业顾问添加
//        QueryWrapper<OpUser> userQueryWrapperwrapper=new QueryWrapper<>();
//        userQueryWrapperwrapper.eq("id",opUser.getSaleId());
//        OpUser one = iOpUserService.getOne(userQueryWrapperwrapper);
//        if(one != null){
//        	opUser.setSaleName(one.getName());
//        	opUser.setSaleId(one.getCstguid());
//        }


//        //通过活动id  添加默认可选房数
//        QueryWrapper<OpActivity> queryWrapper = new QueryWrapper<>();
//        QueryWrapper<OpActivity> id = queryWrapper.select("id", opUser.getActivityId());
//        OpActivity one = iOpActivityService.getOne(id);
//        opUser.setSelectCount(one.getBuyNumber());


        // 赋值 cstguid
        opUser.setCstguid(UUIDUtil.getUUID32());

        boolean flag = iOpUserService.save(opUser);
        QueryWrapper<OpUser> wrapper=new QueryWrapper<>();
        wrapper.eq("tel",opUser.getTel()).eq("name",opUser.getName());
        OpUser opUserRedis = iOpUserService.getOne(wrapper);

        iOpOrderService.redisUserModify(opUserRedis.getActivityId(),opUserRedis.getId().toString(),"plus",opActivity.getBuyNumber());

        if (flag) {
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
            jsonResult.setData(opUser);
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.E_CODE);
        jsonResult.setMsg("新增失败!");
        jsonResult.setData(opUser);
        return jsonResult;

    }

    /**
     * 编辑客户页面展示
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "编辑客户展示", notes = "编辑单个客户")
    @RequestMapping(value = "/updateOpUserShow", method = {RequestMethod.GET})
    public JSONResult updateOpUserShow(Integer id) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        QueryWrapper<OpUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ID", id);
        OpUser one = iOpUserService.getOne(queryWrapper);
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId, one.getActivityId());
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("操作失败,请先关闭活动再进行操作！");
            return jsonResult;
//            if (opActivity.getFormalStart() != null) {
//                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                    return jsonResult;
//                }
//            }
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(one);
        return jsonResult;
    }

    /**
     * 判断当前时间是否可以修改
     *
     * @return
     */
    @ApiOperation(value = "判断当前时间是否可以修改", notes = "判断当前时间是否可以修改")
    @RequestMapping(value = "/canModify", method = {RequestMethod.GET})
    public JSONResult canModify(String activityId) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId, activityId);
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("操作失败,请先关闭活动再进行操作！");
            return jsonResult;
//            if (opActivity.getFormalStart() != null) {
//                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                    return jsonResult;
//                }
//            }
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }

    /**
     * 编辑客户信息
     *
     * @param opUser
     * @return
     */
    @ApiOperation(value = "编辑客户", notes = "编辑单个客户")
    @RequestMapping(value = "/updateOpUser", method = {RequestMethod.POST})
    public JSONResult updateOpUser(@RequestBody OpUser opUser) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        //判断同电话号码不可新增
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId, opUser.getActivityId());
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("操作失败,请先关闭活动再进行操作！");
            return jsonResult;
//            if (opActivity.getFormalStart() != null) {
//                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                    return jsonResult;
//                }
//            }
        }
        //判断手机号码和姓名必填
        if (StringUtils.isBlank(opUser.getTel())||StringUtils.isBlank(opUser.getName())){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("姓名或手机号未添加,不可编辑");
            return jsonResult;
        }
        //手机位数校验
        if (opUser.getTel().length()!=11){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("手机号位数错误,不可编辑");
            return jsonResult;
        }
        //修改后手机号的用户id如果不等于当前修改用户id 认为重复   如果相等 认为同用户修改其他信息 可保存
        QueryWrapper<OpUser> opUserQueryWrapper=new QueryWrapper<>();
        opUserQueryWrapper.eq("tel",opUser.getTel()).eq("yn",GlobalConstants.Y).eq("activity_id",opUser.getActivityId());
        List<OpUser> list = iOpUserService.list(opUserQueryWrapper);
        if (list.size()!=0&&!list.get(0).getId().equals(opUser.getId())){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("手机号码重复,请确认手机号");
            jsonResult.setData(opUser);
            return jsonResult;
        }

        // 校验是 普通用户新增，还是置业顾问/销售经理新增
        if ("customer".equals(opUser.getRoleName())) {
            // 校验 身份证位数
            if (18 != opUser.getIdCard().length()) {
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("身份证号位数错误,不可编辑");
                return jsonResult;
            } else {
                // 重复 校验
//                opUserQueryWrapper = new QueryWrapper<>();
//                opUserQueryWrapper.eq("id_card",opUser.getIdCard()).eq("yn",GlobalConstants.Y).eq("activity_id",opUser.getActivityId());
//                list = iOpUserService.list(opUserQueryWrapper);
//                if (list.size() != 0 && !list.get(0).getId().equals(opUser.getId())) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("身份证号码重复,请确认身份证号");
//                    jsonResult.setData(opUser);
//                    return jsonResult;
//                }
            }
        }

        // 校验批次
        if (StringUtils.isNotBlank(opUser.getBatchNameStr())) {
            // 选房模式 && 开启按批次处理
            if (null != opActivity.getCheckMode() && 1 == opActivity.getCheckMode().intValue() && null != opActivity.getBatchFlag() && 1 == opActivity.getBatchFlag().intValue()) {
                // 校验批次
                // 获取该活动所有批次
                List<OpBatch> batchList = batchService.selectAllBatchListByActivityId(opUser.getActivityId());
                List<String> collect = batchList.stream().map(OpBatch::getBatchName).collect(Collectors.toList());
                collect.add(GlobalConstants.COMMON);
                // 分割遍历
                List<String> thisList = Arrays.asList(opUser.getBatchNameStr().split(","));
                for (String s : thisList) {
                    if (!collect.contains(s)) {
                        jsonResult.setCode(GlobalConstants.E_CODE);
                        jsonResult.setMsg("该用户批次名称必须在以下批次中：" + collect.stream().collect(Collectors.joining(",")));
                        return jsonResult;
                    }
                }
            }
        }

        //修改人
        opUser.setLastUpdateBy(ThreadLocalUtils.getUserName());
        //修改时间
        opUser.setLastUpdateDate(LocalDateTimeUtils.localDateTimeToDate(LocalDateTime.now()));

        boolean flag = iOpUserService.updateById(opUser);


        OpUser opUserRedis = iOpUserService.getById(opUser.getId());
        if ("customer".equals(opUserRedis.getRoleName())){
            iOpOrderService.redisUserModify(opUserRedis.getActivityId(),opUserRedis.getId().toString(),"plus",opUserRedis.getSelectCount());
        }

        if (flag) {
            // 将 数据库中 所有的sale_id是当前用户的saleName改值
            if (!"customer".equals(opUserRedis.getRoleName()) && StringUtils.isNotBlank(opUserRedis.getCstguid())) {
                iOpUserService.updateSaleNameById(opUserRedis.getCstguid(), opUserRedis.getName());
            }

            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
            jsonResult.setData(opUser);
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.E_CODE);
        jsonResult.setMsg("编辑更新失败!");
        jsonResult.setData(opUser);
        return jsonResult;

    }

    @ApiOperation(value = "删除客户", notes = "批量/单个删除客户")
    @RequestMapping(value = "/deleteOpUsers", method = {RequestMethod.POST})
    public JSONResult deleteIopHouseType(@RequestBody List<Integer> idList) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        //判断删除的用户是否可被删除 (订单中是否存在userid为次id的)
        QueryWrapper<OpUser> wrapper = new QueryWrapper<>();
        wrapper.eq("id", idList.get(0));
        OpUser byId = iOpUserService.getOne(wrapper);
//        OpUser byId = iOpUserService.getById(idList.get(0).toString());
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId, byId.getActivityId());
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("操作失败,请先关闭活动再进行操作！");
            return jsonResult;
//            if (opActivity.getFormalStart() != null) {
//                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                    return jsonResult;
//                }
//            }
        }
        boolean b = iOpUserService.canBeDelete(idList);
        if (!b) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("删除的用户存在订单,不可删除,删除用户失败!");
            return jsonResult;
        }
//        for (Integer id : idList) {
////            OpUser opUser = new OpUser();
////            opUser.setId(id);
////            opUser.setYn(GlobalConstants.N);
////            opUser.setLastUpdateBy(ThreadLocalUtils.getUserName());
////            opUser.setLastUpdateDate(LocalDateTimeUtils.localDateTimeToDate(LocalDateTime.now()));
////            opUserList.add(opUser);
////            }
////        boolean flag = iOpUserService.updateBatchById(opUserList);
        for (Integer id : idList) {
            OpUser one = iOpUserService.getById(id);
            boolean flag = iOpUserService.removeById(id);
            if (flag) {

                //删除用户后删除此用户的所有收藏,同活动下
                QueryWrapper<OpFavorite> favoriteQueryWrapper=new QueryWrapper<>();
                favoriteQueryWrapper.eq("user_id",id).eq("activity_id",opActivity.getId());
                favoriteService.remove(favoriteQueryWrapper);

                //删除房源后删除此房源的所有白名单,同活动下
                QueryWrapper<OpUserHouseResource> userHouseResourceQueryWrapper = new QueryWrapper<>();
                userHouseResourceQueryWrapper.eq("user_id",id).eq("activity_id",opActivity.getId());
                userHouseResourceMapper.delete(userHouseResourceQueryWrapper);

                //修改redis
                if ("customer".equals(one.getRoleName())) {
                    iOpOrderService.redisUserModify(one.getActivityId(), one.getId().toString(), "minus", one.getSelectCount());
                }
            } else {
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("删除用户失败!");
                return jsonResult;
            }
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("删除成功");
        return jsonResult;
    }

    /**
     * 用户模板下载
     *
     * @param response
     * @return
     */
    @ApiOperation(value = "下载客户模板", notes = "下载客户模板")
    @RequestMapping(value = "/downLoadExcel", method = {RequestMethod.GET})
    public JSONResult downLoadExcel(@RequestParam("activityId") String activityId, HttpServletResponse response) {
        //声明一个工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        //表头行数据
        List<String> header = new ArrayList<>();
        header.add("CstGUID(唯一)");
        header.add("名字");
        header.add("性别(男女)");
        header.add("身份证号");
        header.add("置业顾问(请勿改动格式：名称_CstGUID)");
        header.add("电话(唯一)");
        header.add("创建日期:yyyy-MM-dd HH:mm:ss");
        header.add("更新日期:yyyy-MM-dd HH:mm:ss");
        header.add("可选房数(数字)");
        header.add("认筹次数(数字)");
        header.add("用户选房排序号(数字)");
        header.add("可看房源批次(多个用英文逗分割，如不填则默认查common批次房源)");
        header.add("房源信息");

        //生成一个表格，设置表格名称
        XSSFSheet sheet = workbook.createSheet("客户导入sheet");
        //设置表格列宽度为30个字节
        sheet.setDefaultColumnWidth(25);
        //创建标题的显示样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE.index);
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        //创建第一行表头
        Row headrow = sheet.createRow(0);
        String[] heads = header.toArray(new String[0]);
        for (int i = 0; i < heads.length; i++) {
            Cell cell = headrow.createCell(i);
            cell.setCellValue(heads[i]);
            cell.setCellStyle(headerStyle);
        }

        // 获取 当前活动下 所有的置业顾问数据
        List<OpUser> saleList = iOpUserService.getSaleName(activityId);
        List<String> saleNameList = Lists.newArrayList();
        String [] selectArray = null;
        // 校验
        if (CollectionUtils.isNotEmpty(saleList)) {
            for (OpUser user : saleList) {
                if (StringUtils.isNotBlank(user.getSaleId()) && StringUtils.isNotBlank(user.getSaleName())) {
                    saleNameList.add(user.getSaleName() + "_" + user.getSaleId());
                }
            }
        }
        if (CollectionUtils.isEmpty(saleNameList)) {
            selectArray = new String[]{"无"};
        } else {
            selectArray = saleNameList.toArray(new String[saleNameList.size()]);
        }
        System.out.println("selectArray ============== " + selectArray);

        DataValidationHelper helper = sheet.getDataValidationHelper();
        //设置下拉框位置   开始行 结束行  开始列 结束列
        CellRangeAddressList addressList = new CellRangeAddressList(1, 20000, 4, 4);
        //设置下拉框数据
        DataValidationConstraint constraint = helper.createExplicitListConstraint(selectArray);
        DataValidation dataValidation = helper.createValidation(constraint, addressList);

        sheet.addValidationData(dataValidation);

        JSONResult<Object> jsonResult = new JSONResult<>();
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("客户导入表" + ".xlsx", "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("客户信息模板导出错误");
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }
//
//    /**
//     * 用户模板下载
//     *
//     * @param response
//     * @return
//     */
//    @ApiOperation(value = "下载客户模板", notes = "下载客户模板")
//    @RequestMapping(value = "/downLoadExcel", method = {RequestMethod.GET})
//    public JSONResult downLoadExcel(HttpServletResponse response) {
//        List<OpUser> opUserList = new ArrayList<>();
//        JSONResult<Object> jsonResult = new JSONResult<>();
//        try {
//            ExcelUtilsTest.exportExcel(opUserList, null, "客户信息", OpUser.class, "客户导入表", response);
//        } catch (IOException e) {
//            e.printStackTrace();
//            jsonResult.setCode(GlobalConstants.E_CODE);
//            jsonResult.setMsg("客户信息模板导出错误");
//            return jsonResult;
//        }
//        jsonResult.setCode(GlobalConstants.S_CODE);
//        jsonResult.setMsg("SUCCESS");
//        return jsonResult;
//    }

    /**
     * 用户模板下载
     *
     * @param response
     * @return
     */
    @ApiOperation(value = "下载置业顾问模板", notes = "下载置业顾问模板")
    @RequestMapping(value = "/downLoadExcelConsultant", method = {RequestMethod.GET})
    public JSONResult downLoadExcelConsultant(HttpServletResponse response) {
        List<ConsultantVO> opUserList = new ArrayList<>();
        JSONResult<Object> jsonResult = new JSONResult<>();
        try {
            ExcelUtilsTest.exportExcel(opUserList, null, "置业顾问/经理信息", ConsultantVO.class, "置业顾问/经理导入表", response);
        } catch (IOException e) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("置业顾问/经理信息模板导出错误");
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }

    /**
     * 用户数据批量导入
     *
     * @param file
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量客户导入", notes = "批量客户导入")
    @RequestMapping(value = "/importCustomerExcel", method = {RequestMethod.POST})
    public JSONResult importExcel(MultipartFile file, String activityIdNow) {
        List<OpUser> opUserList = null;
        JSONResult<Object> jsonResult = new JSONResult<>();
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId,activityIdNow);
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("操作失败,请先关闭活动再进行操作！");
            return jsonResult;
//            if (opActivity.getFormalStart() != null) {
//                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                    return jsonResult;
//                }
//            }
        }
        if (opActivity.getBuyNumber()==null){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("活动可购买数未设置,不可导入客户");
            return jsonResult;
        }
        try {
            opUserList = ExcelUtilsTest.importExcel(file, OpUser.class);
            // 数据处理条数限制，限制一次不能超过两万条数据
            if (CollectionUtils.isEmpty(opUserList)) {
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("数据为空!");
                return jsonResult;
            }
            if (20000 < opUserList.size()) {
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("单次操作最多20000条数据!");
                return jsonResult;
            }
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("Excel导入失败!");
            return jsonResult;
        }try {
            jsonResult= iOpUserService.saveImportData(opUserList, opActivity);
        }catch (Exception e){
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("Excel导入失败,请检查模板");
            return jsonResult;
        }
        return jsonResult;
    }

    @ApiOperation(value = "批量置业顾问导入", notes = "批量置业顾问导入")
    @RequestMapping(value = "/importSaleExcel", method = {RequestMethod.POST})
    public JSONResult importSaleExcel(MultipartFile file, String activityIdNow) {
        List<ConsultantVO> consultantVOSList = null;
        JSONResult<Object> jsonResult = new JSONResult<>();
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId,activityIdNow);
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("操作失败,请先关闭活动再进行操作！");
            return jsonResult;
//            if (opActivity.getFormalStart() != null) {
//                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                    return jsonResult;
//                }
//            }
        }
        try {
            consultantVOSList = ExcelUtilsTest.importExcel(file, ConsultantVO.class);
            // 数据处理条数限制，限制一次不能超过两万条数据
            if (CollectionUtils.isEmpty(consultantVOSList)) {
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("数据为空!");
                return jsonResult;
            }
            if (20000 < consultantVOSList.size()) {
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("单次操作最多20000条数据!");
                return jsonResult;
            }
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("Excel导入失败!");
            return jsonResult;
        }
        // 处理数据
        try {
            jsonResult= iOpUserService.saveImportDataConsultant(consultantVOSList, opActivity);
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("Excel导入失败,请检查模板");
            return jsonResult;
        }
        return jsonResult;
    }



    /**
     * 同步置业顾问
     *
     * @param projectId
     * @param activityId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "同步置业顾问/经理", notes = "同步置业顾问/经理")
    @RequestMapping(value = "/updateSaleAll", method = {RequestMethod.POST})
    public JSONResult updateSaleAll(String projectId, String activityId) {

        JSONResult<Object> jsonResult = new JSONResult<>();
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId,activityId);
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            if (opActivity.getFormalStart() != null) {
                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
                    jsonResult.setCode(GlobalConstants.E_CODE);
                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
                    return jsonResult;
                }
            }
        }
        HttpHeaders headers = new HttpHeaders();
        restTemplate.getMessageConverters().add(new ShareinterJson2HttpMessageConverter());

        HttpEntity<String> requestEntity = new HttpEntity<String>("{\"_datatype\":\"text\",\"_param\":{\"ProjectID\":\"" + projectId + "\"}}", headers);

        ResponseEntity<UserRootBean> quote = restTemplate.postForEntity(ConstHOST+"/FrameWeb/FrameService/Api.ashx?option=func&funcid=mShareOnlineSalesUserList_Select", requestEntity, UserRootBean.class);
        List<UserData> userDataList = Objects.requireNonNull(quote.getBody()).getData();

        List<OpUser> opUserList = new ArrayList<>();
        for (UserData userData : userDataList) {
            //创建人时间 可用设置
            OpUser opUser = new OpUser();
            opUser.setYn(GlobalConstants.Y);
            opUser.setCreatedBy("interface");
            opUser.setCreationDate(LocalDateTimeUtils.localDateTimeToDate(LocalDateTime.now()));
            opUser.setProjectId(projectId);
            opUser.setActivityId(activityId);
            opUser.setCstguid(userData.getId());
            opUser.setOpenId(userData.getOpenID());
            opUser.setName(userData.getEmployeeName());
            opUser.setLoginName(userData.getUserName());
            opUser.setGender(userData.getGender());
            opUser.setTel(userData.getMobile());
            opUser.setRoleCode(userData.getRoleID());
            opUser.setRoleName(userData.getRoleName());
            opUser.setAccountType(userData.getAccountType());
            opUser.setAccountName(userData.getAccountTypeName());
            //设置默认可购房数
            opUserList.add(opUser);
        }
        QueryWrapper<OpUser> wrapper = new QueryWrapper<>();
        wrapper.eq("activity_id", activityId);
        wrapper.ne("role_name","customer");
        boolean remove = iOpUserService.remove(wrapper);
        boolean saveBatch = iOpUserService.saveBatch(opUserList);

        if (remove && saveBatch) {
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
            jsonResult.setData(userDataList);
            return jsonResult;
        }

        jsonResult.setCode(GlobalConstants.E_CODE);
        jsonResult.setMsg("同步失败!");
        jsonResult.setData(userDataList);
        return jsonResult;
    }


}
