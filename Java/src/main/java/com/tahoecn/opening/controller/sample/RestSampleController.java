package com.tahoecn.opening.controller.sample;

import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.controller.TahoeBaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
public class RestSampleController extends TahoeBaseController {


    @RequestMapping(value = "/hello",method = RequestMethod.GET)
    public JSONResult hello(){
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(0);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(null);
        return jsonResult;
    }
}