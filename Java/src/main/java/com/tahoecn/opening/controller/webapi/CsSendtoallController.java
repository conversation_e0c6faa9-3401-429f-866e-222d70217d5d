package com.tahoecn.opening.controller.webapi;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tahoecn.opening.converter.ResponseMessage;
import com.tahoecn.opening.service.CsSendSmsLogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 期初发送短信通知表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-29
 */
@Controller
@RequestMapping("/webapi/csSendtoall")
@Api(tags = "短信批量发送", value = "短信批量发送")
public class CsSendtoallController {
	@Autowired
	private CsSendSmsLogService csSendSmsLogService;
	@ApiOperation(value = "批量短信接口", notes = "数据库配置电话")
    @ApiImplicitParams({@ApiImplicitParam(name = "msg", value = "短信内容", dataType = "String")})
    @RequestMapping(value = "/sendToAll", method = {RequestMethod.POST,RequestMethod.GET })
    public ResponseMessage sendToAll( @RequestParam(name = "msg") String msg) {
		try {
			csSendSmsLogService.sendToAll(msg);
			return ResponseMessage.ok("发送成功");
		} catch (Exception e) {
			return ResponseMessage.error("系统错误，请联系管理员");
		}
       
    }
}

