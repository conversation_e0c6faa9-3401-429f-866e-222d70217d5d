package com.tahoecn.opening.controller.webapi;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import com.tahoecn.opening.common.annotation.EncryptionParameters;
import com.tahoecn.opening.common.constants.AesConstants;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.exception.AesException;
import com.tahoecn.opening.common.utils.*;
import com.tahoecn.opening.config.AliyunSmsConfig;
import com.tahoecn.opening.controller.TahoeBaseController;
import com.tahoecn.opening.converter.ResponseMessage;
import com.tahoecn.opening.mapper.OpUserMapper;
import com.tahoecn.opening.model.*;
import com.tahoecn.opening.model.dto.UserDto;
import com.tahoecn.opening.model.vo.*;
import com.tahoecn.opening.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 活动 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@Api(tags = "活动控制器")
@RestController
@RequestMapping("/webapi/opActivity")
public class OpActivityController extends TahoeBaseController {

    private static final Log log = LogFactory.get();

    @Autowired
    private IOpActivityService activityService;

    @Autowired
    private CsUserRoleService csUserRoleService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private ISysUserRoleService sysUserRoleService;

    @Autowired
    private IOpProjectShareService opProjectShareService;

    @Autowired
    IOpHousingResourcesService iOpHousingResourcesService;

    @Autowired
    IOpUserService iOpUserService;

    @Autowired
    private IOpHouseTypeService iOpHouseTypeService;

    @Autowired
    private CsUcUserService csUcUserService;

    @Autowired
    private IOpOrderService opOrderService;

    @Autowired
    private IOpMassageJobService massageJobService;

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    private AliyunSmsConfig smsConfig;

    @Value("${control_time}")
    private Double controTime;

    @Value("${tahoe.application.physicalPath}")
    private  String physicalPath;

    @Value("${system_HOST}")
    private  String system_HOST;

    @Value("${weixin.appId}")
    private  String appId;

    @Value("${weixin.appSecret}")
    private  String appSecret;


    @ApiOperation(value = "右上角活动列表切换", notes = "右上角活动列表切换")
    @RequestMapping(value = "/myActivityList", method = {RequestMethod.GET})
    public JSONResult myActivityList() {
        List<SysUserRole> list;
        if (true){
            list = sysUserRoleService.myActivityList(ThreadLocalUtils.getUserName());
        }else{  //系统管理员
            list = sysUserRoleService.myActivityListAll(ThreadLocalUtils.getUserName());
        }

        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(list);
        return jsonResult;
    }

    @ApiOperation(value = "右上角活动列表角色", notes = "右上角活动列表角色")
    @RequestMapping(value = "/myActivityRole", method = {RequestMethod.GET})
    public JSONResult myActivityRole(String activityId) {

        QueryWrapper<SysUserRole> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SysUserRole::getUserCode, ThreadLocalUtils.getUserName());
        wrapper.lambda().eq(SysUserRole::getActivityId, activityId);
        List<SysUserRole> list = sysUserRoleService.list(wrapper);

        List<String> roleCodes = new ArrayList<>();
        for (SysUserRole sysUserRole : list) {
            roleCodes.add(sysUserRole.getRoleCode());
        }

        QueryWrapper<SysRole> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().in(SysRole::getRoleCode, roleCodes);
        queryWrapper.lambda().eq(SysRole::getYn, GlobalConstants.Y);
        List<SysRole> roles = sysRoleService.list(queryWrapper);


        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(roles);
        return jsonResult;
    }


    @ApiOperation(value = "获取项目下面活动列表", notes = "获取项目下面活动列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "projectId", value = "项目ID", dataType = "String"),
            @ApiImplicitParam(name = "activityStatus", value = "活动状态running/close/end", dataType = "String")})
    @RequestMapping(value = "/projectPageList", method = {RequestMethod.GET})
    public JSONResult projectPageList(String projectId, String activityStatus) {
        QueryWrapper<OpActivity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpActivity::getYn, GlobalConstants.Y);
        wrapper.lambda().eq(StringUtils.isNotBlank(projectId), OpActivity::getProjectId, projectId);
        wrapper.lambda().eq(StringUtils.isNotBlank(activityStatus), OpActivity::getStatusCode, activityStatus);
        List<OpActivity> opActivityList = activityService.list(wrapper);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(opActivityList);
        return jsonResult;
    }



    @ApiOperation(value = "分页获取所有活动列表", notes = "分页获取所有活动列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "activityStatus", value = "活动状态running/close/end", dataType = "String")})
    @RequestMapping(value = "/activityPage", method = {RequestMethod.GET})
    public JSONResult activityPage(@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "10") Integer pageSize, String activityStatus) {
        QueryWrapper<OpActivity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpActivity::getYn, GlobalConstants.Y);
        wrapper.lambda().eq(StringUtils.isNotBlank(activityStatus), OpActivity::getStatusCode, activityStatus);
        IPage<OpActivity> page = new Page<>(pageNum, pageSize);
        IPage<OpActivity> opActivityList = activityService.page(page,wrapper);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(opActivityList);
        return jsonResult;
    }


    @ApiOperation(value = "活动增加角色初始化参数", notes = "活动增加角色初始化参数")
    @RequestMapping(value = "/activityRoleInit", method = {RequestMethod.GET})
    public JSONResult activityRoleInit() {

        QueryWrapper<SysRole> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SysRole::getYn, GlobalConstants.Y);
        wrapper.lambda().eq(SysRole::getUseable, GlobalConstants.USERABLE);
        List<SysRole> sysRoles = sysRoleService.list(wrapper);

        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        Map<String, Object> map = new HashMap<>();
        map.put("orgTree", csUserRoleService.orgTree());//todo:YYY 放缓存
        map.put("sysRoles", sysRoles);
        jsonResult.setData(map);
        return jsonResult;
    }

    @ApiOperation(value = "保存活动角色", notes = "保存活动角色")
    @RequestMapping(value = "/saveActivityRole", method = {RequestMethod.POST})
    public JSONResult saveActivityRole(@RequestBody SysUserRole sysUserRole) {

        JSONResult jsonResult = new JSONResult();
        if (sysUserRoleService.save(sysUserRole)) {
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
        } else {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("保存活动角色失败");
        }

        return jsonResult;
    }

    @ApiOperation(value = "删除活动角色", notes = "删除活动角色")
    @RequestMapping(value = "/delActivityRole", method = {RequestMethod.POST})
    public JSONResult delActivityRole(SysUserRole sysUserRole) {

        QueryWrapper<SysUserRole> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SysUserRole::getActivityId, sysUserRole.getActivityId());
        wrapper.lambda().eq(SysUserRole::getUserCode, sysUserRole.getUserCode());
        wrapper.lambda().eq(SysUserRole::getRoleCode, sysUserRole.getRoleCode());

        JSONResult jsonResult = new JSONResult();
        if (sysUserRoleService.remove(wrapper)) {
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
        } else {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("删除活动角色失败");
        }

        return jsonResult;
    }

    @ApiOperation(value = "活动下角色列表", notes = "获取活动下角色列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "activityId", value = "活动ID", dataType = "String")})
    @RequestMapping(value = "/activityRoleList", method = {RequestMethod.GET})
    public JSONResult activityRoleList(String activityId) {
        QueryWrapper<SysUserRole> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(StringUtils.isNotBlank(activityId), SysUserRole::getActivityId, activityId);
        List<SysUserRole> opActivityList = sysUserRoleService.list(wrapper);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(opActivityList);
        return jsonResult;
    }

    //未设置事务
    @ApiOperation(value = "保存活动", notes = "保存活动")
    @ApiImplicitParams({@ApiImplicitParam(name = "opActivity", value = "活动")})
    @RequestMapping(value = "/saveActivity", method = {RequestMethod.POST})
    public JSONResult saveActivity(@RequestBody OpActivity opActivity) {
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.E_CODE);

        // 校验电子签
        if (ObjectUtil.isNotNull(opActivity.getOpenElectronFlag()) && 1 == opActivity.getOpenElectronFlag().intValue() && StringUtils.isBlank(opActivity.getTemplateId())) {
            jsonResult.setMsg("操作失败,开启电子签的活动请填写电子签模板id！");
            return jsonResult;
        }

        OpActivity activitytemp;
        String flag = "first";
        if (opActivity.getId() == null) {
            opActivity.setStatusCode(GlobalConstants.STATUS_D);
            opActivity.setStatusName(GlobalConstants.STATUS_D_NAME);
            opActivity.setCreatedBy(ThreadLocalUtils.getUserName());
            opActivity.setCreationDate(LocalDateTime.now());
            activitytemp = opActivity;
        }else{
            if (opActivity.getMessageH() == null)
                opActivity.setMessageH(0);
            if (opActivity.getMessageM() == null)
                opActivity.setMessageM(0);
            if (opActivity.getSimulationStart() == null)
                opActivity.setSimulationStart(null);
            if (opActivity.getSimulationEnd() == null)
                opActivity.setSimulationEnd(null);
            if (opActivity.getFormalStart() == null)
                opActivity.setFormalStart(null);
            if (opActivity.getFormalEnd() == null)
                opActivity.setFormalEnd(null);

            if (GlobalConstants.N.equals(opActivity.getIsSimulation())){
                opActivity.setSimulationStart(null);
                opActivity.setSimulationEnd(null);
            }
            flag = "notFirst";
            activitytemp = activityService.getById(opActivity.getId());
            if (GlobalConstants.STATUS_R.equals(activitytemp.getStatusCode())){
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("操作失败,请先关闭活动再进行操作！");
                return jsonResult;
//                if (activitytemp.getFormalStart()!=null) {
//                    if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(activitytemp.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(activitytemp.getFormalEnd()), controTime)){
//                        jsonResult.setCode(GlobalConstants.E_CODE);
//                        jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                        return jsonResult;
//                    }
//                }
            }

            if (GlobalConstants.STATUS_R.equals(activitytemp.getStatusCode())) {
//                massageJobService.saveMessageRemindJob(opActivity);//写入短信发送表、预制订单定时
            }
        }

        // 校验当前活动类型
        if (null != opActivity.getCheckMode() && 1 == opActivity.getCheckMode().intValue()) {
            // 选房模式，校验时间区间
            // 是否有模拟模式
            if (null != opActivity.getIsSimulation() && GlobalConstants.N.equals(opActivity.getIsSimulation())){
                // 无模拟模式
                opActivity.setSimulationStart(null);
                opActivity.setSimulationEnd(null);
                opActivity.setCheckTimeStr(null);
            } else {
                // 有模拟模式
                // 校验模拟时间区间
                if (null == opActivity.getSimulationStart() && null == opActivity.getSimulationEnd()) {
                    // 活动开始结束都为空
                } else if (null != opActivity.getSimulationStart() && null != opActivity.getSimulationEnd()) {
                    // 两个都不为空
                    Boolean simulationFlag = checkBuyTime(opActivity.getCheckTimeStr(), LocalDateTimeUtils.localDateTimeToDate(opActivity.getSimulationStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getSimulationEnd()));
                    if (null == simulationFlag) {
                        jsonResult.setMsg("操作失败，模拟可选房区间列表数据有误！");
                        return jsonResult;
                    }
                    if (!simulationFlag) {
                        jsonResult.setMsg("操作失败，模拟可选房区间数据必须在模拟活动开始和模拟活动结束时间区间内！");
                        return jsonResult;
                    }
                } else {
                    // 有任一为空
                    jsonResult.setMsg("操作失败，模拟开始时间和模拟结束时间要么都为空要么都不为空！");
                    return jsonResult;
                }
            }

            // 校验正式时间区间
            if (null == opActivity.getFormalStart() && null == opActivity.getFormalEnd()) {
                // 活动开始结束都为空
            } else if (null != opActivity.getFormalStart() && null != opActivity.getFormalEnd()) {
                // 两个都不为空
                Boolean formalFlag = checkBuyTime(opActivity.getFormalCheckTimeStr(), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()));
                if (null == formalFlag) {
                    jsonResult.setMsg("操作失败，正式可选房区间列表数据有误！");
                    return jsonResult;
                }
                if (!formalFlag) {
                    jsonResult.setMsg("操作失败，正式可选房区间数据必须在正式活动开始和正式活动结束时间区间内！");
                    return jsonResult;
                }
            } else {
                // 有任一为空
                jsonResult.setMsg("操作失败，正式开始时间和正式结束时间要么都为空要么都不为空！");
                return jsonResult;
            }
        }

        QueryWrapper<OpProjectShare> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpProjectShare::getYn, GlobalConstants.Y);
        wrapper.lambda().eq(StringUtils.isNotBlank(activitytemp.getProjectId()), OpProjectShare::getProjectId, activitytemp.getProjectId());
        OpProjectShare opProjectShare = opProjectShareService.getOne(wrapper);
        if (opProjectShare != null) {
            // 给图片更新
            opActivity.setImgUrl(opProjectShare.getImg());
            // 校验本次是否有售楼电话
            if (StringUtils.isBlank(opActivity.getSalesTel())) {
                opActivity.setSalesTel(opProjectShare.getTel());
            }
        }

        opActivity.setLastUpdateBy(ThreadLocalUtils.getUserName());

        // 放入缓存展示map
        if (null != opActivity.getId()) {
            // 获取当前活动分列展示分组
            Map<String, Integer> showMap = null;
            if (StringUtils.isNotBlank(opActivity.getShowSplitSetStr())) {
                List<ShowSplitVO> splitVOList = JSONArray.parseArray(opActivity.getShowSplitSetStr(), ShowSplitVO.class);
                showMap = splitVOList.stream().collect(Collectors.toMap(ShowSplitVO::getRoomType, s -> Optional.ofNullable(s.getSplitNum()).orElse(ShowSplitVO.DEFAULT_NUM), (k, v) -> v));
//            showMap = splitVOList.stream().collect(HashMap::new, (map, item) -> map.put(item.getRoomType(), item.getSplitNum()), HashMap::putAll);
//            showMap = splitVOList.stream().collect(Collectors.toMap(ShowSplitVO::getRoomType, ShowSplitVO::getSplitNum, (k, v) -> v));
            } else {
                showMap = Maps.newHashMapWithExpectedSize(1);
            }
            // 写入缓存，这里不用设置过期时间，等活动正式结束时间到达的那一刻会自动清楚该key
            redisTemplate.opsForValue().set(opActivity.getId().toString() + "Activity" + "_SHOW_MAP", showMap);
        }

        if (activityService.saveOrUpdate(opActivity)) {
            if ("first".equals(flag)){

                //活动小程序二维码生成
                opActivity.setQrUrl(system_HOST + createWXQRCode(opActivity.getId().toString(), physicalPath, "200"));
                opActivity.setSignInUrl(system_HOST + createWXQRCode(opActivity.getId().toString() + ",signIn", physicalPath, "200"));
                activityService.saveOrUpdate(opActivity);
                // 更改 活动大屏基本设置
                activityService.updateScreenSetStrByActivityId(opActivity);
            }
            //判断模拟开始时间
            if (GlobalConstants.Y.equals(opActivity.getIsSimulation()) && opActivity.getSimulationStart() != null && activitytemp.getSimulationStart() != null ){
                boolean after = opActivity.getSimulationStart().isAfter(activitytemp.getSimulationStart());
                //如果修改时间在上次开始时间之后,删除模拟订单
                if (after){
                    activityService.resetData(opActivity.getId().toString());
                }
            }
//            //更新活动下所有用户的可购买数
//            activityService.modifyUserSelectCount(opActivity.getId(),opActivity.getBuyNumber());
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
        } else {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("保存活动失败");
        }

        return jsonResult;
    }

    @ApiOperation(value = "保存活动摇号信息", notes = "保存活动摇号信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "opActivity", value = "活动")})
    @RequestMapping(value = "/saveActivityLottery", method = {RequestMethod.POST})
    public JSONResult saveActivityLottery(@RequestBody OpActivity opActivity) {
        JSONResult jsonResult = new JSONResult();
        OpActivity activitytemp = activityService.getById(opActivity.getId());
        if (GlobalConstants.STATUS_R.equals(activitytemp.getStatusCode())){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("操作失败,请先关闭活动再进行操作！");
            return jsonResult;
        }
        activityService.updateById(opActivity);
        opActivity.setCheckMode(activitytemp.getCheckMode());
        // 前置摇号短信
        activityService.saveOpenLotterySmsJob(opActivity);
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }


    /**
     * <AUTHOR>  <EMAIL>
     * @date 11:06 2024/8/23
     * @param timeStr               可选时间字符
     * @param startDate             开始时间
     * @param endDate               结束时间
     * @return java.lang.Boolean
     * @description // TODO         校验时间
     **/
    public Boolean checkBuyTime(String timeStr, Date startDate, Date endDate) {
        try {
            List<BuyTimeVO> timeVOList = JSONArray.parseArray(timeStr, BuyTimeVO.class);
            for (BuyTimeVO vo : timeVOList) {
                Date thisStart = DateUtils.getDate(vo.getBeginDate(), "yyyy-MM-dd HH:mm:ss");
                Date thisEnd = DateUtils.getDate(vo.getEndDate(), "yyyy-MM-dd HH:mm:ss");
                if (null == vo.getBeginDate() || null == vo.getEndDate() || thisStart.after(thisEnd)) {
                    return null;
                }
                // 校验
                if (thisStart.before(startDate) || thisEnd.after(endDate)) {
                    return false;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }



    // todo zwc 20220705  小程序 生成二维码
    public String createWXQRCode(String activitiId, String physicalPath, String width) {
        String accessToken = getAccessToken();//获取二维码需要的Token
        String wxQRCode = "";
        String WXACodeUrl = "https://api.weixin.qq.com/wxa/getwxacodeunlimit";//获取微信小程序二维码的链接
        String getWXCodeUrl = WXACodeUrl + "?access_token=" + accessToken;
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("scene", activitiId);
        paramMap.put("width", width);
        paramMap.put("page", "pages/opening/projectEnter/index");
//        paramMap.put("page", "pages/opening/paramsEnter/index");
        paramMap.put("auto_color", false);
        paramMap.put("is_hyaline", false);
        Map<String, Object> colorMap = Maps.newHashMap();
        colorMap.put("r", 0);
        colorMap.put("g", 0);
        colorMap.put("b", 0);
        paramMap.put("line_color", JSONObject.toJSON(colorMap));

        wxQRCode = PostMoths(getWXCodeUrl, JSONObject.toJSON(paramMap).toString(), physicalPath);
        return wxQRCode;
    }

    public String getAccessToken() {
        String accessToken = "";

        String accessTokenUrl = "https://api.weixin.qq.com/cgi-bin/token";//获取小程序TokenUrl
        String appID = appId;
        String secret = appSecret;
        String getTokenUrl = accessTokenUrl + "?grant_type=client_credential&appid=" + appID + "&secret=" + secret;

        try {
            String result = HttpClientUtil.HttpGet(getTokenUrl);
            System.out.println("resultToken ======================== " + result);
            // 校验
            if (null != result) {
                JSONObject parse = JSONObject.parseObject(result, JSONObject.class);
                // 校验
                if (null != parse.get("access_token")) {
                    accessToken = parse.get("access_token").toString();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return accessToken;
    }

    //请求处理，返回二维码图片
    public String PostMoths(String url, String param, String physicalPath) {
        return HttpClientUtil.HttpPostJson(url, param, physicalPath);
    }




    @ApiOperation(value = "删除活动", notes = "删除活动")
    @ApiImplicitParams({@ApiImplicitParam(name = "opActivity", value = "活动角色")})
    @RequestMapping(value = "/delActivity", method = {RequestMethod.POST})
    public JSONResult delActivity(OpActivity opActivity) {
        opActivity = activityService.getById(opActivity.getId());
        JSONResult jsonResult = new JSONResult();
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("操作失败,请先关闭活动再进行操作！");
            return jsonResult;
//            if (opActivity.getFormalStart() != null) {
//                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                    return jsonResult;
//                }
//            }
        }
        if (!GlobalConstants.STATUS_D.equals(opActivity.getStatusCode())) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("非草稿状态活动不能删除");
            return jsonResult;
        }

        QueryWrapper<SysUserRole> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SysUserRole::getActivityId, opActivity.getId());

        if (sysUserRoleService.remove(wrapper)) {
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
        } else {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("删除活动角色失败");
            return jsonResult;
        }

        opActivity.setYn(GlobalConstants.N);
        opActivity.setLastUpdateBy(ThreadLocalUtils.getUserName());
        opActivity.setLastUpdateDate(LocalDateTime.now());

        if (activityService.removeById(opActivity.getId())) {
//            massageJobService.removeMessageRemindJob(opActivity);
            opOrderService.removeCanOrderRedis(opActivity.getId().toString());
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
        } else {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("保存活动角色失败");
        }

        return jsonResult;
    }



    @ApiOperation(value = "testgetQr", notes = "testgetQr")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动ID"),
            @ApiImplicitParam(name = "status", value = "活动状态:running/close/end"),
            @ApiImplicitParam(name = "statusName", value = "进行中,已关闭,已结束")})
    @RequestMapping(value = "/testgetQr", method = {RequestMethod.POST})
    public JSONResult testgetQr(Integer id, String status,String statusName) {

        JSONResult jsonResult = new JSONResult();
        activityService.getQr("","","");

        return jsonResult;
    }

    @ApiOperation(value = "开启/关闭活动", notes = "开启/关闭活动")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动ID"),
            @ApiImplicitParam(name = "status", value = "活动状态:running/close/end"),
            @ApiImplicitParam(name = "statusName", value = "进行中,已关闭,已结束")})
    @RequestMapping(value = "/activityStatus", method = {RequestMethod.POST})
    public JSONResult activityStatus(Integer id, String status,String statusName) {

        JSONResult jsonResult = new JSONResult();
        OpActivity opActivity = activityService.getById(id);

        if (GlobalConstants.STATUS_R.equals(status)) {
            QueryWrapper<OpActivity> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(OpActivity::getYn, GlobalConstants.Y);
            wrapper.lambda().eq(OpActivity::getStatusCode, GlobalConstants.STATUS_R);
            wrapper.lambda().eq(OpActivity::getProjectId, opActivity.getProjectId());
            List<OpActivity> opActivityList = activityService.list(wrapper);
            if (opActivityList.size() > 0) {
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("一个项目下只能有一个正在进行中的活动");
                return jsonResult;
            }
            try {
                // 初始化房源信息、客户信息到redis
                opOrderService.initCanOrderBySql(id.toString());
                // 写入批次
                opOrderService.refreshActivityDataById(opActivity);
                // 写入短信发送表
                massageJobService.saveMessageRemindJob(opActivity, smsConfig.TEMP_CODE_ACTIVITY);
                // 前置选房短信
                activityService.saveOpenCheckSmsJob(opActivity);
                // 修改 白名单标识
                opActivity.setAutoOrderResultFlag(0);
            } catch (Exception e) {
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("活动初始化redis失败");
                e.printStackTrace();
                return jsonResult;
            }
        }
        if (GlobalConstants.STATUS_CLOSE.equals(status) || GlobalConstants.STATUS_END.equals(status)) {
            massageJobService.removeMessageRemindJob(opActivity);
            opOrderService.removeCanOrderRedis(id.toString());
        }


        opActivity.setStatusCode(status);
        opActivity.setStatusName(statusName);
        opActivity.setLastUpdateBy(ThreadLocalUtils.getUserName());
        opActivity.setLastUpdateDate(LocalDateTime.now());

        if (activityService.saveOrUpdate(opActivity)) {
            if (GlobalConstants.STATUS_END_NAME.equals(status)){
//                if (opActivity.get)
                opOrderService.returnData("closeActivity",null);
            }
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
        } else {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("活动状态更新失败");
        }

        return jsonResult;
    }

    @ApiOperation(value = "能否下订单", notes = "能否下订单")
    @RequestMapping(value = "/getCanOrder", method = {RequestMethod.POST})
    public JSONResult getCanOrder(OpOrder opOrder) {
        return opOrderService.getCanOrderByLua(opOrder);
    }

    @ApiOperation(value = "查询成功订单", notes = "查询成功订单")
    @RequestMapping(value = "/getOrder", method = {RequestMethod.POST})
    public JSONResult getOrder(OpOrder opOrder) {
        return opOrderService.getOrder(opOrder);
    }


    @ApiOperation(value = "初始化房源信息、客户信息到redis并获取所有key", notes = "获取所有key")
    @RequestMapping(value = "/getCanOrderTest", method = {RequestMethod.GET})
    public JSONResult getCanOrderTest(String id) {
        opOrderService.initCanOrderBySql(id); //初始化房源信息、客户信息到redis
        JSONResult jsonResult = new JSONResult();
        jsonResult.setData(opOrderService.getCanOrderTest("*"));
        return jsonResult;
    }

    @ApiOperation(value = "获取所有keyByActivityId", notes = "获取所有keyByActivityId")
    @RequestMapping(value = "/getAllActivityKeyTest", method = {RequestMethod.GET})
    public JSONResult getAllActivityKeyTest(String id) {
        JSONResult jsonResult = new JSONResult();
        jsonResult.setData(opOrderService.getCanOrderTest(id));
        return jsonResult;
    }

    @ApiOperation(value = "删除所有keyByActivityId", notes = "删除所有keyByActivityId")
    @RequestMapping(value = "/delAllActivityKeyTest", method = {RequestMethod.GET})
    public void delAllActivityKeyTest(String id) {
        opOrderService.delActivityKeyById(id);
    }

    @ApiOperation(value = "活动详情", notes = "活动详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动ID")})
    @RequestMapping(value = "/activityDetails", method = {RequestMethod.POST})
    public JSONResult activityDetails(Integer id) {
        JSONResult jsonResult = new JSONResult();
        OpActivity opActivity = activityService.getById(id);
        // 查询所有房源类型
        QueryWrapper<OpHousingResources> resourcesQueryWrapper = new QueryWrapper<>();
        resourcesQueryWrapper.select("room_type as roomType").eq("activity_id", id.toString()).eq("yn", "y").groupBy("room_type");
        List<OpHousingResources> roomTypeList = iOpHousingResourcesService.list(resourcesQueryWrapper);
        // 校验
        if (CollectionUtils.isNotEmpty(roomTypeList)) {
            // 定义 返回集合
            List<ShowSplitVO> splitVOList = Lists.newArrayListWithCapacity(roomTypeList.size());
            // 处理数据
            for (OpHousingResources resources : roomTypeList) {
                splitVOList.add(new ShowSplitVO(null, resources.getRoomType(), false));
            }
            // 匹配 已存在数据集合
            if (StringUtils.isNotBlank(opActivity.getShowSplitSetStr())) {
                List<ShowSplitVO> parseArray = JSONArray.parseArray(opActivity.getShowSplitSetStr(), ShowSplitVO.class);
                for (ShowSplitVO vo : splitVOList) {
                    for (ShowSplitVO splitVO : parseArray) {
                        if (vo.getRoomType().equals(splitVO.getRoomType())) {
                            vo.setCheckFlag(splitVO.isCheckFlag());
                            vo.setSplitNum(splitVO.getSplitNum());
                            break;
                        }
                    }
                }
            }
            // 赋值并返回
            opActivity.setShowSplitSetStr(JSONArray.toJSONString(splitVOList, SerializerFeature.WriteMapNullValue));
        } else {
            opActivity.setShowSplitSetStr("");
        }

        jsonResult.setData(opActivity);
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }


    @ApiOperation(value = "项目简要信息", notes = "项目简要信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "activityId", value = "活动ID", dataType = "String")})
    @RequestMapping(value = "/projectInfo", method = {RequestMethod.GET})
    public JSONResult projectInfo(String activityId) {  //todo:看是否都可以放到活动中
        OpActivity opActivity = activityService.getById(activityId);
        QueryWrapper<OpProjectShare> projectWrapper = new QueryWrapper<>();
        projectWrapper.lambda().eq(OpProjectShare::getYn, GlobalConstants.Y);
        projectWrapper.lambda().eq(OpProjectShare::getProjectId, opActivity.getProjectId());
        OpProjectShare opProjectShare = opProjectShareService.getOne(projectWrapper);
        //改为直接显示项目名称opProjectShare.setProjectName(opActivity.getCaseName());
        JSONResult jsonResult = new JSONResult();
        if (null != opProjectShare) {
            // 赋值：项目名称
            opProjectShare.setProjectName(opActivity.getProjectName());
            opProjectShare.setTel(opActivity.getSalesTel());
            jsonResult.setData(opProjectShare);
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }


    @ApiOperation(value = "活动状态", notes = "活动设置,活动状态")
    @ApiImplicitParams({@ApiImplicitParam(name = "activityId", value = "活动ID", dataType = "String")})
    @RequestMapping(value = "/activityStatus", method = {RequestMethod.GET})
    public JSONResult activityStatus(String activityId) {
        Map<String, String> statusMap = new HashMap<>();

        QueryWrapper<OpActivity> activityWrapper = new QueryWrapper<>();   //3.预设开盘房源信息
        activityWrapper.lambda().eq(OpActivity::getId, activityId);
        activityWrapper.lambda().eq(OpActivity::getYn, GlobalConstants.Y);
        OpActivity opActivity = activityService.getOne(activityWrapper);    //1.预设活动管理信息
        statusMap.put("activityStatusName", opActivity.getStatusName()); //活动状态
        statusMap.put("activityStatusCode", opActivity.getStatusCode()); //活动状态
        statusMap.put("simulationStart", opActivity.getSimulationStart() == null ? "":opActivity.getSimulationStart().toString());
        statusMap.put("formalStart", opActivity.getFormalStart() == null ? "":opActivity.getFormalStart().toString());
        statusMap.put("isSignIn", opActivity.getIsSignIn()); //sh
        if (StringUtils.isEmpty(opActivity.getSuccessTips())) {      //必填项没填
            statusMap.put("activityManageStatus", GlobalConstants.N);
        } else {
            statusMap.put("activityManageStatus", GlobalConstants.Y);
        }


        QueryWrapper<OpHouseType> htWrapper = new QueryWrapper<>();   //2.预设开盘户型信息
        htWrapper.select("house_name as house_type");
        //htWrapper.lambda().select(OpHouseType::getHouseType);
        htWrapper.lambda().eq(OpHouseType::getActivityId, activityId);
        htWrapper.lambda().eq(OpHouseType::getYn, GlobalConstants.Y);
//        htWrapper.lambda().groupBy(OpHouseType::getHouseType);      //户型
        List<Map<String, Object>> htLists = iOpHouseTypeService.listMaps(htWrapper);

        QueryWrapper<OpHousingResources> hrTypeWrapper = new QueryWrapper<>();
        //hrTypeWrapper.lambda().select(OpHousingResources::getRoomStru);
        //todo 业务需要暂时用house_name
        hrTypeWrapper.select("hourse_type");
        hrTypeWrapper.lambda().eq(OpHousingResources::getActivityId, activityId);
        hrTypeWrapper.lambda().eq(OpHousingResources::getYn, GlobalConstants.Y);
        hrTypeWrapper.lambda().groupBy(OpHousingResources::getHourseType);      //房源户型    todo:回头看是不是这个
        List<OpHousingResources> hrTypes = iOpHousingResourcesService.list(hrTypeWrapper);

        List<Object> hoseTypes = new ArrayList<>();
        for (Map<String, Object> htList : htLists) {
            if (htList != null)
                hoseTypes.addAll(htList.values());
        }
        boolean flag = true;
        for (OpHousingResources hrType : hrTypes) {
            if (hrType != null) {
                if (!hoseTypes.contains(hrType.getHourseType())) {
                    flag = false;
                    break;
                }
            }
        }

        if (flag && hrTypes.size() > 0) {
            statusMap.put("houseTypeStatus", GlobalConstants.Y);
        } else {
            statusMap.put("houseTypeStatus", GlobalConstants.N);
        }


        QueryWrapper<OpHousingResources> hrWrapper = new QueryWrapper<>();   //3.预设开盘房源信息，可以去掉这个...
        hrWrapper.lambda().eq(OpHousingResources::getActivityId, activityId);
        hrWrapper.lambda().eq(OpHousingResources::getYn, GlobalConstants.Y);
        List<OpHousingResources> hrLists = iOpHousingResourcesService.list(hrWrapper);
        if (hrLists.size() > 0) {
            statusMap.put("housingResourcesStatus", GlobalConstants.Y);
        } else {
            statusMap.put("housingResourcesStatus", GlobalConstants.N);
        }

        QueryWrapper<OpUser> opUserWrapper = new QueryWrapper<>();    //4.预设客户管理信息
        opUserWrapper.lambda().eq(OpUser::getActivityId, activityId);
        opUserWrapper.lambda().eq(OpUser::getYn, GlobalConstants.Y);
        opUserWrapper.lambda().eq(OpUser::getRoleName, "customer");
        List<OpUser> customers = iOpUserService.list(opUserWrapper);
        if (customers.size() > 0) {
            statusMap.put("customerStatus", GlobalConstants.Y);
        } else {
            statusMap.put("customerStatus", GlobalConstants.N);
        }

        QueryWrapper<OpUser> saleWrapper = new QueryWrapper<>();    //5.预设置业顾问/营销经理信息
        saleWrapper.lambda().eq(OpUser::getActivityId, activityId);
        saleWrapper.lambda().eq(OpUser::getYn, GlobalConstants.Y);
        saleWrapper.lambda().eq(OpUser::getRoleName, "置业顾问");    //置业顾问
        List<OpUser> sales = iOpUserService.list(saleWrapper);
        if (sales.size() > 0) {
            statusMap.put("saleStatus", GlobalConstants.Y);
        } else {
            statusMap.put("saleStatus", GlobalConstants.N);
        }


        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(statusMap);
        return jsonResult;
    }

    @RequestMapping(value = "findUserByOrgId", method = {RequestMethod.GET})
    @ApiOperation("通过组织获取用户信息")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", dataType = "String", name = "orgId", value = "组织 Sid ")})
    public JSONResult findUserByOrgId(String orgId) {
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(csUcUserService.findUserByOrgId(orgId));
        return jsonResult;
    }

    /**
     * 查询用户信息，实现 添加管理员选人功能
     *
     * @param username 用户名或帐号
     * @return
     */
    @RequestMapping(value = "findUsersByNameOrCode", method = {RequestMethod.GET})
    @ApiOperation("通过名字获取用户信息")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(paramType = "query", dataType = "String", name = "username", value = "用户名或帐号")})
    public JSONResult findUsersByNameOrCode(String username) {
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(csUcUserService.findUsersByNameOrCode(username));
        return jsonResult;
    }


//开盘数据页面--收藏
//时间格式
    @ApiOperation(value = "活动分析--客户分析", notes = "活动分析--客户分析")
    @RequestMapping(value = "/activityAnalysisCustomer", method = {RequestMethod.GET})
    public JSONResult activityAnalysisCustomer(@RequestParam(defaultValue = "1") int pageNum,
                                               @RequestParam(defaultValue = "10") int pageSize,
                                               String activityIdNow,
                                               Integer loginNum,
                                               Integer orderNum,
                                               OpUser opUser) {

        /*
        *  <th-trem title="登录状态：">
                <el-select v-model="searchModel.loginNum" filterable placeholder="请选择">
                    <el-option label="历史未登录客户" :value="1"></el-option>
                    <el-option label="历史已登录客户" :value="2"></el-option>
                    <el-option label="今日未登录客户" :value="3"></el-option>
                    <el-option label="今日已登陆客户" :value="4"></el-option>
                    <el-option label="历史已登录无订单客户" :value="5"></el-option>
                </el-select>
            </th-trem>
            <th-trem title="有无订单：">
                <el-select v-model="searchModel.orderNum" filterable placeholder="请选择">
                    <el-option label="有" :value="0"></el-option>
                    <el-option label="无" :value="1"></el-option>
                </el-select>
            </th-trem>
        *
        * */

        JSONResult<Object> jsonResult = new JSONResult<>();
        String activityIdStatus = activityService.getActivityIdStatus(activityIdNow);
        if (GlobalConstants.STATUS_D.equals(activityIdStatus)){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("当前活动状态为草稿,分析数据为空");
            return jsonResult;
        }
        Page page = new Page(pageNum, pageSize);

        //loginNum,orderNum 为下拉菜单val值从0开始
        IPage<CustomerAnalysisVO> list = activityService.getActivityAnalysisCustomer(page, activityIdNow, loginNum, orderNum, opUser);
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(list);
        return jsonResult;
    }


    @ApiOperation(value = "活动分析--客户分析--导出", notes = "活动分析--客户分析--导出")
    @RequestMapping(value = "/activityAnalysisCustomerExcelExport", method = {RequestMethod.GET})
    public JSONResult activityAnalysisCustomerExcelExport(String activityIdNow,
                                                          Integer loginNum,
                                                          Integer orderNum,
                                                          OpUser opUser,
                                                          HttpServletResponse response) {

        JSONResult<Object> jsonResult = new JSONResult<>();
        String activityIdStatus = activityService.getActivityIdStatus(activityIdNow);
        if (GlobalConstants.STATUS_D.equals(activityIdStatus)){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("当前活动状态为草稿,分析数据为空");
            return jsonResult;
        }
        List<CustomerAnalysisVO> list = activityService.getActivityAnalysisCustomerEx(activityIdNow, loginNum, orderNum, opUser);
        try {
            ExcelUtilsTest.exportExcel(list, null, "活动分析客户", CustomerAnalysisVO.class, "活动分析客户导出表", response);
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("Excel导入错误!");
            jsonResult.setData(list);
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(list);
        return jsonResult;
    }

    @ApiOperation(value = "热度分析--房源收藏详情", notes = "热度分析--房源收藏详情")
    @RequestMapping(value = "/activityFavoriteDetails", method = {RequestMethod.GET})
    public JSONResult activityFavoriteDetails(@RequestParam(defaultValue = "1") int pageNum,
                                              @RequestParam(defaultValue = "10") int pageSize,
                                              String houseId) {
        JSONResult<Object> jsonResult = new JSONResult<>();

        Page page = new Page(pageNum, pageSize);
        IPage<FavoriteDetil> list = activityService.getFavoriteUserList(page, houseId);
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(list);
        return jsonResult;
    }


    @ApiOperation(value = "热度分析--房源收藏导出", notes = "热度分析--房源收藏导出")
    @RequestMapping(value = "/activityFavoriteDetailsExcelExport", method = {RequestMethod.GET})
    public JSONResult activityFavoriteDetailsExcelExport(String houseId,HttpServletResponse response) {
        List<FavoriteDetil> list = activityService.getFavoriteUserList(houseId);
        JSONResult<Object> jsonResult = new JSONResult<>();
        try {
            ExcelUtilsTest.exportExcel(list, null, "收藏详情", FavoriteDetil.class, "收藏详情导出表", response);
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("Excel导入错误!");
            jsonResult.setData(list);
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(list);
        return jsonResult;
    }

    @ApiOperation(value = "热度分析--房源", notes = "热度分析--房源")
    @RequestMapping(value = "/activityHouseRes", method = {RequestMethod.GET})
    public JSONResult activityHouseRes(@RequestParam(defaultValue = "1") int pageNum,
                                       @RequestParam(defaultValue = "10") int pageSize,
                                       @RequestParam(required = true) String activityIdNow,
                                       @RequestParam(required = false)String houseName,
                                       @RequestParam(required = false)String buildingName,
                                       @RequestParam(required = false)String unitName,
                                       @RequestParam(required = false)String roomNum,
                                       @RequestParam(required = false)String minFavo,
                                       @RequestParam(required = false)String maxFavo) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        String activityIdStatus = activityService.getActivityIdStatus(activityIdNow);
        if (GlobalConstants.STATUS_D.equals(activityIdStatus)){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("当前活动状态为草稿,分析数据为空");
            return jsonResult;
        }
        Page page = new Page(pageNum, pageSize);

        IPage<HouseResAnalysisVO> iPage=activityService.getActivityHouseRes(page,activityIdNow,houseName,buildingName,unitName,roomNum,minFavo,maxFavo);
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(iPage);
        return jsonResult;
    }

    @ApiOperation(value = "房源分析导出", notes = "房源分析导出")
    @RequestMapping(value = "/activityHouseResImport", method = {RequestMethod.GET})
    public JSONResult activityHouseResImport(
                                       @RequestParam(required = true) String activityIdNow,
                                       @RequestParam(required = false)String houseName,
                                       @RequestParam(required = false)String buildingName,
                                       @RequestParam(required = false)String unitName,
                                       @RequestParam(required = false)String roomNum,
                                       @RequestParam(required = false)String minFavo,
                                       @RequestParam(required = false)String maxFavo) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        String activityIdStatus = activityService.getActivityIdStatus(activityIdNow);
        if (GlobalConstants.STATUS_D.equals(activityIdStatus)){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("当前活动状态为草稿,分析数据为空");
            return jsonResult;
        }

//        IPage<HouseResAnalysisVO> iPage=activityService.getActivityHouseRes(page,activityIdNow,houseName,buildingName,unitName,roomNum,minFavo,maxFavo);
        List<HouseResAnalysisVO> list=activityService.getActivityHouseResEx(activityIdNow,houseName,buildingName,unitName,roomNum,minFavo,maxFavo);
        try {
            ExcelUtilsTest.exportExcel(list, null, "房源热度分析", HouseResAnalysisVO.class, "房源热度分析导出表", response);
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("Excel导入错误!");
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(list);
        return jsonResult;


    }



    @ApiOperation(value = "热度分析--户型", notes = "热度分析--户型")
    @RequestMapping(value = "/activityHouseType", method = {RequestMethod.GET})
    public JSONResult activityHouseType(@RequestParam(defaultValue = "1") int pageNum,
                                        @RequestParam(defaultValue = "10") int pageSize,
                                        @RequestParam(required = true)String activityIdNow,
                                        @RequestParam(required = false)String houseType) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        String activityIdStatus = activityService.getActivityIdStatus(activityIdNow);
        if (GlobalConstants.STATUS_D.equals(activityIdStatus)){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("当前活动状态为草稿,分析数据为空");
            return jsonResult;
        }
        Page page = new Page(pageNum, pageSize);

        IPage<HashMap<String,Object>> iPage=activityService.getActivityHouseType(page,activityIdNow,houseType);
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(iPage);
        return jsonResult;
    }

    @ApiOperation(value = "签到统计", notes = "签到统计")
    @RequestMapping(value = "/sginInList", method = {RequestMethod.GET})
    public JSONResult sginInList(@RequestParam(defaultValue = "1") int pageNum,
                                 @RequestParam(defaultValue = "10") int pageSize,
                                 String sginOrNot,
                                 OpUser opUser) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        String activityIdStatus = activityService.getActivityIdStatus(opUser.getActivityId());
        if (GlobalConstants.STATUS_D.equals(activityIdStatus)){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("当前活动状态为草稿,分析数据为空");
            return jsonResult;
        }
        Page page = new Page(pageNum, pageSize);
        IPage<SginInVO> iPage=activityService.sginInList(page,sginOrNot,opUser);
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(iPage);
        return jsonResult;
    }

    @ApiOperation(value = "签到统计导出", notes = "签到统计导出")
    @RequestMapping(value = "/sginInListExport", method = {RequestMethod.GET})
    public JSONResult sginInListExport(
                                 @RequestParam(required = true)String activityId,
                                 String sginOrNot,
                                 OpUser opUser) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        String activityIdStatus = activityService.getActivityIdStatus(activityId);
        if (GlobalConstants.STATUS_D.equals(activityIdStatus)){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("当前活动状态为草稿,分析数据为空");
            return jsonResult;
        }
        Page page = new Page(1, 999999);
        opUser.setActivityId(activityId);
        IPage<SginInVO> iPage=activityService.sginInList(page,sginOrNot,opUser);

        try {
            ExcelUtilsTest.exportExcel(iPage.getRecords(), null, "签到统计", SginInVO.class, "签到统计导出表", response);
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("Excel导入错误!");
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(iPage);
        return jsonResult;
    }


    @ApiOperation(value = "签到接口", notes = "签到接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "AES_DATA", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/sginIn", method = {RequestMethod.GET})
    public ResponseMessage sginIn(@ApiIgnore @EncryptionParameters String requestParamDto) {

        String replace = requestParamDto.replace("&quot;", "'");

        HashMap<String, String> hashMap =JSON.parseObject(replace,HashMap.class);

        String msg=activityService.userSginIn(hashMap.get("activityId"),hashMap.get("userId"),hashMap.get("address"),LocalDateTime.now());
      if("签到成功!".equals(msg)){
          return ResponseMessage.ok(msg);
      }
      return ResponseMessage.error(msg);
    }


    @ApiOperation(value = "活动名是否重复", notes = "活动名是否重复")
    @RequestMapping(value = "/repeatActivityName", method = {RequestMethod.GET})
    public JSONResult repeatActivityName(String name) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        boolean b = activityService.repeatActivityName(name);
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
            jsonResult.setData(b);
            return jsonResult;
    }
    /**
     * 开盘数据
     *
     * @return
     */
    @ApiOperation(value = "开盘数据", notes = "开盘数据")
    @RequestMapping(value = "/houseOpenHouse", method = {RequestMethod.POST,RequestMethod.GET})
    public JSONResult houseOpenHouse(
                                         @RequestParam(name = "activityIdNow", required = false) String activityId
                                        ) {
    	HashMap<String,Object> resultMap = new HashMap<String,Object>();
    	resultMap = iOpHousingResourcesService.getActivityAnalysis(activityId);
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(resultMap);
        return jsonResult;
    }
    @ApiOperation(value = "倒计时时间校验", notes = "倒计时时间校验")
	@RequestMapping(value = "/getDate", method = { RequestMethod.POST, RequestMethod.GET })
	public JSONResult getDate() {
    	HashMap<String,Object> resultMap = new HashMap<String,Object>();
    	resultMap.put("sysDate", new Date().getTime());
		JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(resultMap);
        return jsonResult;
	}
    @ApiOperation(value = "生成压测参数", notes = "生成压测参数")
	@RequestMapping(value = "/getLoadTestData", method = { RequestMethod.POST, RequestMethod.GET })
	public JSONResult getLoadTestData(String activityId) {
    	String houseIds = "";
    	HashMap<String,Object> resultMap = new HashMap<String,Object>();
    	List<String> listAesData = new ArrayList<String>();
    	List<HashMap<String,Object>> resultList = activityService.getLoadTestData(houseIds,activityId);
    	for(HashMap<String,Object> map : resultList){
    		String str = JSON.toJSONString(map);
    		System.out.println("====================str"+str);
//    		try {
//				String strAes = AesUtils.encrypt(str, AesConstants.AES_KEY);
//				System.out.println("====================strAes"+strAes);
				listAesData.add(str);
//			} catch (AesException e) {
//				// TODO Auto-generated catch block
//				e.printStackTrace();
//			}
    	}
    	int i = (int)(0+Math.random()*(listAesData.size()-1+0));
		JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(listAesData.get(i));
        return jsonResult;
	}

    /**
     * <AUTHOR>  <EMAIL>
     * @date 21:37 2023/5/13
     * @param activityId
     * @return com.tahoecn.core.json.JSONResult
     * @description // TODO 获取活动对象根据活动id
     **/
    @ApiOperation(value = "获取活动对象根据活动id", notes = "获取活动对象根据活动id")
    @GetMapping(value = "/getActivityByActivityId")
    public JSONResult getActivityByActivityId(@RequestParam("activityId") String activityId) {
        // 查询到当前
        OpActivity opActivity = activityService.getById(activityId);
        opActivity.setLastUpdateBy(new Date().getTime() + "");
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("操作成功！");
        jsonResult.setData(opActivity);
        return jsonResult;
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:31 2023/5/24
     * @return com.tahoecn.core.json.JSONResult
     * @description // TODO 更改大屏设置参数根据活动id
     **/
    @ApiOperation(value = "更改大屏设置参数根据活动id", notes = "更改大屏设置参数根据活动id")
    @PostMapping(value = "/updateScreenSetStrByActivityId")
    public JSONResult updateScreenSetStrByActivityId(@RequestBody Map<String, String> paramMap) {
        String activityId = paramMap.get("activityId");
        String screenSetStr = paramMap.get("screenSetStr");
        JSONResult jsonResult = new JSONResult();
        if (StringUtils.isBlank(activityId) || StringUtils.isBlank(screenSetStr)) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("入参有误！");
        }
        // 查询到当前
        OpActivity opActivity = activityService.getById(activityId);
        // 校验
        if (null == opActivity) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("活动不存在！");
        }
        opActivity.setScreenSetStr(screenSetStr);
        opActivity.setLastUpdateBy(ThreadLocalUtils.getUserName());
        opActivity.setLastUpdateDate(LocalDateTime.now());
        activityService.updateScreenSetStrByActivityId(opActivity);
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("操作成功！");
        jsonResult.setData(opActivity);
        return jsonResult;
    }

    /**
     * 查询摇号活动配置根据id
     */
    @ApiOperation(value = "查询摇号活动配置根据id", notes = "查询摇号活动配置根据id")
    @GetMapping(value = "/getLotteryActivityConfigById")
    public JSONResult getLotteryActivityConfigById(@RequestParam("activityId") String activityId) {
        JSONResult jsonResult = new JSONResult();
        if (StringUtils.isBlank(activityId)) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("入参有误！");
        }
        // 查询到当前
        OpActivity opActivity = activityService.getById(activityId);
        // 校验
        if (null == opActivity) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("活动不存在！");
        }

        Map<String, Object> lotteryConfigMap = new HashMap<>();
        lotteryConfigMap.put("id", opActivity.getId());
        lotteryConfigMap.put("lotteryFlag", opActivity.getLotteryFlag());
        lotteryConfigMap.put("lotteryTitle", opActivity.getLotteryTitle());
        lotteryConfigMap.put("lotteryStartDate", opActivity.getLotteryStartDate());
        lotteryConfigMap.put("lotteryEndDate", opActivity.getLotteryEndDate());
        lotteryConfigMap.put("lotteryExplain", opActivity.getLotteryExplain());
        lotteryConfigMap.put("lotteryQRcode", opActivity.getLotteryQRcode());
        lotteryConfigMap.put("lotteryDateFlag", true);

        // 查询活动时间 判断未超过期限 前端判断用
        if (null != opActivity.getLotteryStartDate() && null != opActivity.getLotteryEndDate()) {
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(opActivity.getLotteryStartDate()) || now.isAfter(opActivity.getLotteryEndDate())) {
                lotteryConfigMap.put("lotteryDateFlag", false);
            }
        }

        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("操作成功！");
        jsonResult.setData(lotteryConfigMap);
        return jsonResult;
    }

    /**
     * 一键摇号
     * @param activityIdNow
     * @return
     */
    @ApiOperation(value = "一键摇号", notes = "一键摇号")
    @GetMapping(value = "/autoLottery")
    public JSONResult autoLottery(@RequestParam Integer activityIdNow){
        return activityService.autoLottery(activityIdNow);
    }

    /**
     * 个人摇号
     * @param activityIdNow
     * @return
     */
    @ApiOperation(value = "个人摇号", notes = "个人摇号")
    @GetMapping(value = "/personalLottery")
    public JSONResult personalLottery(@RequestParam Integer activityIdNow, Integer userId){
        return activityService.personalLottery(activityIdNow, userId);
    }


}
