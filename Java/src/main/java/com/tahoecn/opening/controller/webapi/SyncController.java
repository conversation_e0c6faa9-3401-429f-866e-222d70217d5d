package com.tahoecn.opening.controller.webapi;

import com.tahoecn.opening.common.utils.ApiResult;
import com.tahoecn.opening.common.utils.DateUtils;
import com.tahoecn.opening.quartz.QuartzParamVo;
import com.tahoecn.opening.quartz.QuartzService;
import com.tahoecn.opening.quartz.UpdateBatchQTask;
import com.tahoecn.opening.service.SyncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.controller.webapi
 * @ClassName: SyncController
 * @Description:// TODO 同步控制器
 * @Date: 2024/8/16 14:12
 * @Version: 1.0
 */
@Api(tags = "数据控制器")
@RestController
@RequestMapping("/sync")
public class SyncController {

    @Autowired
    SyncService syncService;

    @Autowired
    QuartzService quartzService;

    /**
     * <AUTHOR>  <EMAIL>
     * @date 10:03 2024/8/16
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO         同步全量组织
     **/
    @ApiOperation(value = "同步全量组织", notes = "同步全量组织")
    @GetMapping("/syncOrg")
    public ApiResult syncOrg() {
        return syncService.syncOrg();
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 10:03 2024/8/16
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO         同步全量用户
     **/
    @ApiOperation(value = "同步全量用户", notes = "同步全量用户")
    @GetMapping("/syncUser")
    public ApiResult syncUser() {
        return syncService.syncUser();
    }

    /**
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/6 10:33
     * @Param        paramVo 
     * @Return       com.tahoecn.opening.common.utils.ApiResult
     * @Description  TODO 新增定时任务
     **/
    @ApiOperation(value = "新增定时任务", notes = "新增定时任务")
    @PostMapping("/addQuartz")
    public ApiResult addQuartz(@RequestBody QuartzParamVo paramVo) {
        if (paramVo.getCronFlag()) {
            quartzService.addCronJobs(UpdateBatchQTask.class, paramVo.getName(), paramVo.getGroup(), paramVo.getCron(), paramVo.getParamMap(), paramVo.getDesc());
        } else {
            quartzService.addSimpleJobs(UpdateBatchQTask.class, paramVo.getName(), paramVo.getGroup(), DateUtils.getDate(paramVo.getStartDate(), ""), paramVo.getParamMap(), paramVo.getDesc());
        }
        return ApiResult.getSuccessApiResponse();
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/6 10:33
     * @Param        paramVo
     * @Return       com.tahoecn.opening.common.utils.ApiResult
     * @Description  TODO 删除定时任务
     **/
    @ApiOperation(value = "删除定时任务", notes = "删除定时任务")
    @PostMapping("/deleteQuartz")
    public ApiResult deleteQuartz(@RequestBody QuartzParamVo paramVo) {
        quartzService.deleteJobs(paramVo.getName(), paramVo.getGroup());
        return ApiResult.getSuccessApiResponse();
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/6 10:33
     * @Param        paramVo
     * @Return       com.tahoecn.opening.common.utils.ApiResult
     * @Description  TODO 修改定时任务
     **/
    @ApiOperation(value = "修改定时任务", notes = "修改定时任务")
    @PostMapping("/updateQuartz")
    public ApiResult updateQuartz(@RequestBody QuartzParamVo paramVo) {
        if (paramVo.getCronFlag()) {
            quartzService.updateCronJobs(UpdateBatchQTask.class, paramVo.getName(), paramVo.getGroup(), paramVo.getCron(), paramVo.getParamMap(), paramVo.getDesc());
        } else {
            quartzService.updateSimpleJobs(UpdateBatchQTask.class, paramVo.getName(), paramVo.getGroup(), DateUtils.getDate(paramVo.getStartDate(), ""), paramVo.getParamMap(), paramVo.getDesc());
        }
        return ApiResult.getSuccessApiResponse();
    }
}
