package com.tahoecn.opening.controller.app;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.annotation.EncryptionParameters;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.DateUtils;
import com.tahoecn.opening.common.utils.LocalDateTimeUtils;
import com.tahoecn.opening.common.utils.UUIDUtil;
import com.tahoecn.opening.config.AliyunSmsConfig;
import com.tahoecn.opening.controller.TahoeBaseController;
import com.tahoecn.opening.converter.ResponseMessage;
import com.tahoecn.opening.model.AppRequstDto;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpHousingResources;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.model.dto.UserDto;
import com.tahoecn.opening.server.WebSocketServer;
import com.tahoecn.opening.service.*;
import com.tahoecn.opening.service.impl.AliyunServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 小程序登录 前端控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app/auth")
@Api(tags = "登录控制器", value = "登录控制器")
public class AuthController extends TahoeBaseController {

    @Autowired
    private IOpUserService iOpUserService;
    @Autowired
    private CsSendSmsLogService csSendSmsLogService;

    @Autowired
    private IOpActivityService iOpActivityService;
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IOpOrderService orderService;

    @Autowired
    private IOpHousingResourcesService opHousingResourcesService;

    @Autowired
    private AliyunSmsConfig smsConfig;

    @Autowired
    private AliyunServiceImpl aliyunService;

    /**
     * 公共入口可提供 openId,手机号telNum(微信绑定手机号)
     *
     * @param strDto get请求 key为: AES_DATA   值是一个被加密后的值
     * @return 活动数量(CMD会在后面进行统一包装, 异常信息通过异常处理机制处理)
     */
    @ApiOperation(value = "登录前校验活动", notes = "登录前校验活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "AES_DATA", value = "Json {\"activityId\":\"123\",\"projectId\":\"123\",\"tel\":\"123\",\"openId\":\"\"}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/checkPublicLogin", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage checkPublicLogin(@ApiIgnore @EncryptionParameters String requestParamDto) {
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	HashMap<String,Object> resultMap = new HashMap<String,Object>();
    	if(appRequstDto.getActivityId() != null && !"".equals(appRequstDto.getActivityId())){
    		OpActivity opActivity = iOpActivityService.getById(appRequstDto.getActivityId());
    		appRequstDto.setProjectId(opActivity.getProjectId());
    	}
		iOpUserService.updateUserLoginDateByTel(appRequstDto.getActivityId(),appRequstDto.getTel(),LocalDateTime.now());
    	List<HashMap<String,String>> activityRunOrCloseList = iOpUserService.getActivityRunOrCloseList(appRequstDto.getProjectId());
    	List<HashMap<String,String>> activityList = iOpUserService.getOpUserByProjectIdAndTelOrOpenId(appRequstDto.getProjectId(),appRequstDto.getTel(),appRequstDto.getOpenId());
    	resultMap.put("activityList", activityList);
    	resultMap.put("activityRunOrCloseSize", activityRunOrCloseList.size());
    	resultMap.put("msg", "您好，目前没有开盘中的活动，敬请期待后续活动！如有问题请联系置业顾问！");
        return ResponseMessage.ok(resultMap);
    }
    
    /**
     * 登录
     *
     * @param strDto get请求 key为: AES_DATA   值是一个被加密后的值
     * @return 
     */
    @ApiOperation(value = "验证登录", notes = "验证登录")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "AES_DATA", value = "Json {\"activityId\":\"123\",\"tel\":\"123\",\"openId\":\"123\",\"userFullName\":\"123\",\"idCard\":\"123\",\"verificationCode\":\"123\",\"projectId\":\"123\"}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/login", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage login(@ApiIgnore @EncryptionParameters String requestParamDto) {
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	HashMap<String,Object> resultMap = new HashMap<String,Object>();

		// 校验 活动id 必传
		if (StringUtils.isBlank(appRequstDto.getActivityId())) {
			return ResponseMessage.error("活动id必传！");
		}
		OpActivity opActivity = iOpActivityService.getById(appRequstDto.getActivityId());
		// 校验
		if (null == opActivity) {
			return ResponseMessage.error("活动不存在！");
		}
		// 校验验证码
		if (StringUtils.isNotBlank(opActivity.getIsMessageVal()) && "y".equalsIgnoreCase(opActivity.getIsMessageVal())) {
			// 短信验证开启
			if (StringUtils.isBlank(appRequstDto.getVerificationCode())) {
				return ResponseMessage.error("验证码不能为空！");
			}
			Object o = redisTemplate.opsForValue().get(appRequstDto.getTel());
			if (null == o || StringUtils.isBlank(o.toString())) {
				return ResponseMessage.error("验证码不存在或已失效，请重新获取！");
			} else if (!appRequstDto.getVerificationCode().equals(o.toString())) {
				return ResponseMessage.error("验证码错误，请重新输入！");
			}
		}

		appRequstDto.setProjectId(opActivity.getProjectId());
    	List<String> activityIds = new ArrayList<String>();
    	List<OpUser> opUserList = iOpUserService.getOpUserListByProjectIdAndTel(appRequstDto.getTel(),appRequstDto.getProjectId());

		// 获取 当前活动所在用户
		OpUser opUser = null;
    	if(opUserList.size() == 0){
    		return ResponseMessage.error("根据手机号未查询到可参与的活动，请确认输入的手机号同售楼处预留手机号码一致！如有问题请联系您的置业顾问。");
    	} else{
    		for(OpUser thisUser : opUserList){
    			if (thisUser.getActivityId().equals(appRequstDto.getActivityId())) {
					opUser = thisUser;
    				break;
				}
			}
    	}

		// 校验姓名
		if(opUser.getName() != null && !opUser.getName().equals(appRequstDto.getUserFullName())){
			return ResponseMessage.error("输入的姓名与售楼处预留姓名不一致，请重新输入！如有问题请联系您的置业顾问。");
		}

		// 获取当前用户的可选批次id集合
		List<String> batchNameList = Lists.newArrayList();
		batchNameList.add(GlobalConstants.COMMON);
		if (StringUtils.isNotBlank(opUser.getBatchNameStr())) {
			// 分割数据
			List<String> list = Arrays.asList(opUser.getBatchNameStr().split(","));
			for (String s : list) {
				if (!batchNameList.contains(s)) {
					batchNameList.add(s);
				}
			}
		}
		String prefix = "LOGIN_TOKEN_";
		String token = UUIDUtil.getUUID32();
		String tokenName = prefix + appRequstDto.getActivityId() + "_" + appRequstDto.getTel() + appRequstDto.getUserFullName();
		// 登录拦截校验
		Object o = redisTemplate.opsForValue().get(tokenName);
		if (o == null) {
			redisTemplate.opsForValue().set(tokenName, token);
			redisTemplate.opsForValue().set(prefix + token, opUser.getId().toString());
			redisTemplate.opsForValue().set(prefix + token + "_BATCH_", batchNameList);
		} else {
			// 将之前存在的值清空并赋新值
			redisTemplate.opsForValue().set(tokenName, token);
			redisTemplate.opsForValue().set(prefix + token, opUser.getId().toString());
			redisTemplate.opsForValue().set(prefix + token + "_BATCH_", batchNameList);
			redisTemplate.delete(prefix + o);
		}

		// 校验 当前活动 是否 需要传 身份证号
		if ("y".equals(opActivity.getIsIdVal())) {
			// 校验 是否是客户身份登录
			if ("customer".equals(opUser.getRoleName())) {
				// 校验 身份证号 必传
				if (StringUtils.isBlank(appRequstDto.getIdCard()) || 6 != appRequstDto.getIdCard().length()) {
					return ResponseMessage.error("当前活动身份证后六位必传！");
				}

				// 校验 数据库是否存了该用户身份证号
				if (StringUtils.isBlank(opUser.getIdCard())) {
					return ResponseMessage.error("置业顾问未将您的身份证件信息录入！如有问题请联系您的置业顾问。");
				}
				// 获取后六位身份证
				String s = opUser.getIdCard().toUpperCase();
				s = s.substring(s.length() - 6);
				if(!s.equalsIgnoreCase(appRequstDto.getIdCard())){
					return ResponseMessage.error("输入的身份证后六位与售楼处预留身份证后6位不一致，请重新输入！如有问题请联系您的置业顾问。");
				}
//				if(!opUser.getIdCard().toUpperCase().endsWith(appRequstDto.getIdCard())){
//					return ResponseMessage.error("输入的身份证后六位与售楼处预留身份证后6位不一致，请重新输入！如有问题请联系您的置业顾问。");
//				}
			}
		}

		opUser.setLastLoginDate(LocalDateTime.now());
		activityIds.add(opUser.getActivityId());
    	if(activityIds == null || activityIds.size() == 0){
    		return ResponseMessage.error("您输入的信息与售楼处预留信息不匹配，请检查后重新输入！如有问题请联系您的置业顾问。");
    	}
		//			opUser.setFirstLogin(1);
		iOpUserService.saveOrUpdate(opUser);

		//	重新写入大屏
    	opHousingResourcesService.sendWebSocket(appRequstDto.getActivityId());

    	List<HashMap<String,String>> activityList = iOpUserService.getOpUserByActivityIds(activityIds);
    	resultMap.put("activityList", activityList);
    	resultMap.put("loginToken", token);
		resultMap.put("userId", opUser.getId());
		resultMap.put("activityId", opActivity.getId());
    	return ResponseMessage.ok(resultMap);
    }

    /**
     * 摇号登录
     *
     * @param requestParamDto get请求 key为: AES_DATA   值是一个被加密后的值
     * @return
     */
    @ApiOperation(value = "验证登录", notes = "验证登录")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "AES_DATA", value = "Json {\"activityId\":\"123\",\"tel\":\"123\",\"openId\":\"123\",\"userFullName\":\"123\",\"idCard\":\"123\",\"verificationCode\":\"123\",\"projectId\":\"123\"}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/lotteryLogin", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage lotteryLogin(@ApiIgnore @EncryptionParameters String requestParamDto) {
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	HashMap<String,Object> resultMap = new HashMap<String,Object>();
//    	String verificationCode = (String) redisTemplate.opsForValue().get(appRequstDto.getTel());
//    	if(verificationCode == null || "".equals(verificationCode)){
//    		return ResponseMessage.error("验证码不存在或已失效，请重新获取！");
//    	}else if(!verificationCode.equals(appRequstDto.getVerificationCode())){
//    		return ResponseMessage.error("验证码错误，请重新输入！");
//    	}
		// 校验 活动id 必传
		if (StringUtils.isBlank(appRequstDto.getActivityId())) {
			return ResponseMessage.error("活动id必传！");
		}
		OpActivity opActivity = iOpActivityService.getById(appRequstDto.getActivityId());
		appRequstDto.setProjectId(opActivity.getProjectId());

    	List<String> activityIds = new ArrayList<String>();
    	List<OpUser> opUserList = iOpUserService.getOpUserListByProjectIdAndTel(appRequstDto.getTel(),appRequstDto.getProjectId());

		// 获取 当前活动所在用户
		OpUser opUser = null;
    	if(opUserList.size() == 0){
    		return ResponseMessage.error("根据手机号未查询到可参与的活动，请确认输入的手机号同售楼处预留手机号码一致！如有问题请联系您的置业顾问。");
    	} else{
    		for(OpUser thisUser : opUserList){
    			if (thisUser.getActivityId().equals(appRequstDto.getActivityId())) {
					opUser = thisUser;
    				break;
				}
			}
    	}

		// 校验姓名
		if(opUser.getName() != null && !opUser.getName().equals(appRequstDto.getUserFullName())){
			return ResponseMessage.error("输入的姓名与售楼处预留姓名不一致，请重新输入！如有问题请联系您的置业顾问。");
		}

		// 获取当前用户的可选批次id集合
		List<String> batchNameList = Lists.newArrayList();
		batchNameList.add(GlobalConstants.COMMON);
		if (StringUtils.isNotBlank(opUser.getBatchNameStr())) {
			// 分割数据
			List<String> list = Arrays.asList(opUser.getBatchNameStr().split(","));
			for (String s : list) {
				if (!batchNameList.contains(s)) {
					batchNameList.add(s);
				}
			}
		}
		String prefix = "LOGIN_TOKEN_";
		String token = UUIDUtil.getUUID32();
		String tokenName = prefix + appRequstDto.getActivityId() + "_" + appRequstDto.getTel() + appRequstDto.getUserFullName();
		// 登录拦截校验
		Object o = redisTemplate.opsForValue().get(tokenName);
		if (o == null) {
			redisTemplate.opsForValue().set(tokenName, token);
			redisTemplate.opsForValue().set(prefix + token, opUser.getId().toString());
			redisTemplate.opsForValue().set(prefix + token + "_BATCH_", batchNameList);
		} else {
			// 将之前存在的值清空并赋新值
			redisTemplate.opsForValue().set(tokenName, token);
			redisTemplate.opsForValue().set(prefix + token, opUser.getId().toString());
			redisTemplate.opsForValue().set(prefix + token + "_BATCH_", batchNameList);
			redisTemplate.delete(prefix + o);
		}

		// 校验 当前活动 是否 需要传 身份证号
		if ("y".equals(opActivity.getIsIdVal())) {
			// 校验 是否是客户身份登录
			if ("customer".equals(opUser.getRoleName())) {
				// 校验 身份证号 必传
				if (StringUtils.isBlank(appRequstDto.getIdCard()) || 6 != appRequstDto.getIdCard().length()) {
					return ResponseMessage.error("当前活动身份证后六位必传！");
				}

				// 校验 数据库是否存了该用户身份证号
				if (StringUtils.isBlank(opUser.getIdCard())) {
					return ResponseMessage.error("置业顾问未将您的身份证件信息录入！如有问题请联系您的置业顾问。");
				}
				// 获取后六位身份证
				String s = opUser.getIdCard().toUpperCase();
				s = s.substring(s.length() - 6);
				if(!s.equalsIgnoreCase(appRequstDto.getIdCard())){
					return ResponseMessage.error("输入的身份证后六位与售楼处预留身份证后6位不一致，请重新输入！如有问题请联系您的置业顾问。");
				}
//				if(!opUser.getIdCard().toUpperCase().endsWith(appRequstDto.getIdCard())){
//					return ResponseMessage.error("输入的身份证后六位与售楼处预留身份证后6位不一致，请重新输入！如有问题请联系您的置业顾问。");
//				}
			}
		}

		opUser.setLastLoginDate(LocalDateTime.now());
		activityIds.add(opUser.getActivityId());
    	if(activityIds == null || activityIds.size() == 0){
    		return ResponseMessage.error("您输入的信息与售楼处预留信息不匹配，请检查后重新输入！如有问题请联系您的置业顾问。");
    	}
		//			opUser.setFirstLogin(1);
		iOpUserService.saveOrUpdate(opUser);

		//	重新写入大屏
    	opHousingResourcesService.sendWebSocket(appRequstDto.getActivityId());

    	List<HashMap<String,String>> activityList = iOpUserService.getOpUserByActivityIds(activityIds);
    	resultMap.put("activityList", activityList);
    	resultMap.put("loginToken", token);
		resultMap.put("userId", opUser.getId());
		resultMap.put("activityId", opActivity.getId());
    	return ResponseMessage.ok(resultMap);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:52 2024/8/23
     * @param userId
     * @param activityId 
     * @return com.tahoecn.opening.converter.ResponseMessage
     * @description // TODO 	获取用户时间
     **/
	@ApiOperation(value = "获取用户时间", notes = "获取用户时间")
	@PostMapping(value = "/getTimeByUser")
	public ResponseMessage getTimeByUser(@RequestBody Map<String, Object> param) {
    	try {
			// 获取用户
			HashMap<String, Long> map = JSONObject.parseObject(param.get("AES_DATA").toString(), HashMap.class);

			OpUser opUser = iOpUserService.getById(map.get("userId"));
			OpActivity opActivity = iOpActivityService.getById(map.get("activityId"));
			Map<String, Object> resultMap = new HashMap<>();
			// 赋值时间
			resultMap.put("simulationStartDate", "");
	        resultMap.put("simulationEndDate", "");
			resultMap.put("formalStartDate", "");
			resultMap.put("formalEndDate", ""); 
			// 校验是否有模拟活动
			if (null != opActivity.getIsSimulation() && GlobalConstants.Y.equals(opActivity.getIsSimulation())) {
				// 有模拟活动
                // 校验
                if (null != opActivity.getSimulationStart() && null != opActivity.getSimulationEnd()) {
                    resultMap.put("simulationStartDate", LocalDateTimeUtils.formatDateTime(opActivity.getSimulationStart(), "yyyy-MM-dd HH:mm:ss"));
                    resultMap.put("simulationEndDate", LocalDateTimeUtils.formatDateTime(opActivity.getSimulationEnd(), "yyyy-MM-dd HH:mm:ss"));
                    // 校验当前活动类型
                    if (null != opActivity.getCheckMode() && 1 == opActivity.getCheckMode().intValue()) {
                        // 选房模式，追加返参
						List<UserDto> userDtoList = iOpActivityService.selectAllUserDtoByActivityId(opActivity.getId().toString(), false);
						if (CollectionUtils.isNotEmpty(userDtoList)) {
							for (UserDto userDto : userDtoList) {
								if (userDto.getId().longValue() == opUser.getId().longValue()) {
									if (null != userDto.getBeginDate()) {
										resultMap.put("simulationStartDate", DateUtils.getDateString(userDto.getBeginDate(), DateUtils.PATTERN_SECOND));
									}
									break;
								}
							}
						}
                    }
                }
			}
			// 校验
            if (null != opActivity.getFormalStart() && null != opActivity.getFormalEnd()) {
                resultMap.put("formalStartDate", LocalDateTimeUtils.formatDateTime(opActivity.getFormalStart(), "yyyy-MM-dd HH:mm:ss"));
                resultMap.put("formalEndDate", LocalDateTimeUtils.formatDateTime(opActivity.getFormalEnd(), "yyyy-MM-dd HH:mm:ss"));
                // 校验当前活动类型
                if (null != opActivity.getCheckMode() && 1 == opActivity.getCheckMode().intValue()) {
                    // 选房模式，追加返参
					List<UserDto> userDtoList = iOpActivityService.selectAllUserDtoByActivityId(opActivity.getId().toString(), true);
					if (CollectionUtils.isNotEmpty(userDtoList)) {
						for (UserDto userDto : userDtoList) {
							if (userDto.getId().longValue() == opUser.getId().longValue()) {
								if (null != userDto.getBeginDate()) {
									resultMap.put("formalStartDate", DateUtils.getDateString(userDto.getBeginDate(), DateUtils.PATTERN_SECOND));
								}
								break;
							}
						}
					}
                }
            }
			return ResponseMessage.ok(resultMap);
		} catch (Exception e) {
    		e.printStackTrace();
    		return ResponseMessage.error("请求异常！");
		}
	}

    /**
     * 登录并获取use
     *
     * @param strDto get请求 key为: AES_DATA   值是一个被加密后的值
     * @return 
     */
    @ApiOperation(value = "登录并获取user", notes = "登录并获取user")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "AES_DATA", value = "Json {\"activityId\":\"123\",\"openId\":\"123\",\"tel\":\"123\"}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/loginAndGetUser", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage loginAndGetUser(@ApiIgnore @EncryptionParameters String requestParamDto) {
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	HashMap<String,Object> resultMap = new HashMap<String,Object>();
    	// 校验 是否传了手机号
		if (StringUtils.isBlank(appRequstDto.getTel())) {
			resultMap.put("resultCode", "0");
		} else {
			iOpUserService.updateUserLoginDateByTel(appRequstDto.getActivityId(),appRequstDto.getTel(),LocalDateTime.now());
			List<OpUser> opUserList = iOpUserService.getOpUserListByActivityIdAndOpenId(appRequstDto.getActivityId(),appRequstDto.getOpenId(),appRequstDto.getTel());
    		resultMap.put("resultCode", "1");
    		resultMap.put("opUser", opUserList.get(0));
		}


//    	if(opUserList.size() != 1){
//    		resultMap.put("resultCode", "0");
//    	}else{
//    		resultMap.put("resultCode", "1");
//    		resultMap.put("opUser", opUserList.get(0));
//    		//接口推数据
//    		if (opUserList.get(0).getFirstLogin()!=1){
//    			orderService.returnData("login",opUserList.get(0));
//    		}
//    	}
    	return ResponseMessage.ok(resultMap);
    }
    
    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:46 2022/7/4
     * @param requestParamDto 
     * @return com.tahoecn.opening.converter.ResponseMessage
     * @description // TODO 退出登录
     **/
    @ApiOperation(value = "退出登录", notes = "退出登录")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "AES_DATA", value = "Json {\"activityId\":\"123\",\"openId\":\"123\",\"tel\":\"123\"}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/logout", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage logout(@ApiIgnore @EncryptionParameters String requestParamDto) {
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	// 校验 活动id 和 手机
        /*  暂不管退出登录操作.
        if (StringUtils.isBlank(appRequstDto.getActivityId()) || StringUtils.isBlank(appRequstDto.getTel())) {
            return ResponseMessage.error("活动及手机号必传！");
        }
        iOpUserService.logout(appRequstDto.getActivityId(), appRequstDto.getTel());
        */
    	return ResponseMessage.ok();
    }
    /**
     * 获取验证码
     *
     * @param strDto get请求 key为: AES_DATA   值是一个被加密后的值
     * @return 
     */
    @ApiOperation(value = "获取验证码", notes = "获取验证码")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "AES_DATA", value = "Json {\"tel\":\"123\"}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/getVerificationCode", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage getVerificationCode(@ApiIgnore @EncryptionParameters String requestParamDto) {
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	// 校验
		if (StringUtils.isBlank(appRequstDto.getTel())) {
			return ResponseMessage.error("手机号不能为空！");
		}
    	int verificationCode = (int) ((Math.random() * 9 + 1) * 100000);
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("code", verificationCode);
		// 发送验证码
		boolean b = aliyunService.sendSms(appRequstDto.getTel(), smsConfig.TEMP_CODE_VERIFY_CODE, jsonObject.toJSONString());
		if (b) {
			// 记录
			redisTemplate.opsForValue().set(appRequstDto.getTel(), String.valueOf(verificationCode) , 5 ,TimeUnit.MINUTES);
			Map<String, Integer> resultMap = Maps.newHashMapWithExpectedSize(1);
			resultMap.put("verificationCode", verificationCode);
			return ResponseMessage.ok(resultMap);
		}
		return ResponseMessage.error("验证码获取失败！");
    }

    @ApiOperation(value = "签到接口", notes = "签到接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "AES_DATA",value = "Json {\"activityId\":\"123\",\"userId\":\"123\",\"address\":\"123,123\"}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/sginIn", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage sginIn(@ApiIgnore @EncryptionParameters String requestParamDto) {
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
        /*String replace = requestParamDto.replace("&quot;", "'");

        HashMap<String, String> hashMap =JSON.parseObject(replace,HashMap.class);
*/
        String msg=iOpActivityService.userSginIn(appRequstDto.getActivityId(),appRequstDto.getUserId(),appRequstDto.getAddress(),LocalDateTime.now());
      if("签到成功!".equals(msg)){
          return ResponseMessage.ok(msg);
      }
      return ResponseMessage.error(msg);
    }

//    /**
//     * 获取验证码
//     *
//     * @param strDto get请求 key为: AES_DATA   值是一个被加密后的值
//     * @return 
//     */
//    @ApiOperation(value = "测试redis参数有效性", notes = "获取验证码")
//    @GetMapping("testRedisTimeOut")
//    public ResponseMessage testRedisTimeOut() {
//    	HashMap<String,Object> resultMap = new HashMap<String,Object>();
//    	redisTemplate.opsForValue().set("timeOutValue","timeOut",5,TimeUnit.SECONDS);  
//    	String timeOutValue = redisTemplate.opsForValue().get("timeOutValue")+"";  
//    	System.out.println("通过set(K key, V value, long timeout, TimeUnit unit)方法设置过期时间，过期之前获取的数据:"+timeOutValue);  
//    	resultMap.put("通过set(K key, V value, long timeout, TimeUnit unit)方法设置过期时间，过期之前获取的数据:", timeOutValue);
//    	try {
//			Thread.sleep(10*1000);
//		} catch (InterruptedException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}  
//    	timeOutValue = redisTemplate.opsForValue().get("timeOutValue")+"";  
//    	System.out.print(",等待10s过后，获取的值:"+timeOutValue);
//    	resultMap.put(",等待10s过后，获取的值:", timeOutValue);
//    	return ResponseMessage.ok(resultMap);
//    }
    public static void main(String[] args) {
		int verificationCode = (int) ((Math.random() * 9 + 1) * 100000);
		System.out.println("verificationCode = " + verificationCode);
    	String activityIds = "'430',";
    	System.out.println(activityIds.substring(0,activityIds.length() - 1));
	}
    
    
//    /**
//     * 公共入口可提供 openId,手机号telNum(微信绑定手机号)
//     *
//     * @param strDto get请求 key为: AES_DATA   值是一个被加密后的值
//     * @return 活动数量(CMD会在后面进行统一包装, 异常信息通过异常处理机制处理)
//     */
//    @ApiOperation(value = "公共入口校验", notes = "公共入口校验")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "AES_DATA", value = "Json {\"telNum\":\"123\",\"openId\":\"\"}", paramType = "query", dataType = "String", required = true)
//    })
//    @GetMapping("checkPublicLogin1")
//    public int checkPublicLogin1(@ApiIgnore @EncryptionParameters String strDto) {
//        OpUser opUser = JSON.parseObject(strDto, OpUser.class);
//
//        // 获取统计值 并返回
//        return iOpActivityService.queryCoundByPageNoOrOpenId(opUser);
//    }
//
//    /**
//     * 项目入口可提供 openId,手机号telNum,项目id projectId
//     *
//     * @param strDto get请求 key为: AES_DATA   值是一个被加密后的值
//     * @return
//     */
//    @ApiOperation(value = "活动入口校验", notes = "活动入口校验")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "AES_DATA", value = "Json {\"telNum\":\"123\",\"openId\":\"\",\"activityId\":\"1\",\"projectId\":\"2\"}", paramType = "query", dataType = "String", required = true)
//    })
//    @GetMapping("checkProjectLogin")
//    public String checkProjectLogin(@EncryptionParameters String strDto) {
//        OpUser opUser = JSON.parseObject(strDto, OpUser.class);
//        iOpActivityService.checkProjectLogin(opUser);
//
//        // TODO 魔法值最好抽取为常量
//        return "认证成功！";
//    }
//
//
//    /**
//     * 详细入口可提供 openid,手机号,项目id,身份证号,姓名 //TODO 增加短信校验
//     *
//     * @param strDto
//     * @return
//     */
//    @ApiOperation(value = "详细登录信息录入", notes = "详细登录信息录入")
//    @RequestMapping(value = "/checkDetailedLogin", method = {RequestMethod.POST, RequestMethod.GET})
//    public ResponseMessage checkDetailedLogin(@EncryptionParameters String strDto) {
//        JSONObject jsonDto = JSONObject.parseObject(strDto);
//        return null;
//    }
}
