package com.tahoecn.opening.controller.webapi;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.converter.ResponseMessage;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpBatch;
import com.tahoecn.opening.model.vo.BatchParamVo;
import com.tahoecn.opening.service.IOpActivityService;
import com.tahoecn.opening.service.IOpBatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: Zhengwenchao  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.controller.webapi
 * @Description:// TODO 批次控制器
 * @Date: 2025/4/25 15:22
 * @Version: 1.0
 **/
@Api(tags = "批次管理", value = "批次管理")
@RestController
@RequestMapping("/webapi/batch")
public class BatchController {

    @Autowired
    private IOpBatchService opBatchService;

    @Autowired
    private IOpActivityService opActivityService;

    /** 
     * 条查批次列表
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/25 15:30
     * @Param        paramVo 
     * @Return       com.tahoecn.core.json.JSONResult
     * @Description  TODO 条查批次列表
     **/
    @ApiOperation(value = "条查批次列表", notes = "条查批次列表")
    @PostMapping(value = "/selectBatchListByCondition")
    public ResponseMessage selectBatchListByCondition(@RequestBody BatchParamVo paramVo) {
        // 校验 活动id不能为空
        if (StringUtils.isBlank(paramVo.getActivityId())) {
            return ResponseMessage.error("活动ID不能为空！");
        }
        IPage<OpBatch> ipage = opBatchService.selectBatchListByCondition(paramVo);
        return ResponseMessage.ok(ipage);
    }

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/25 15:43
     * @Param        opBatch
     * @Return       com.tahoecn.core.json.JSONResult
     * @Description  TODO 新增修改批次
     **/
    @ApiOperation(value = "新增修改批次", notes = "新增修改批次")
    @PostMapping(value = "/saveOrUpdateBatch")
    public ResponseMessage saveOrUpdateBatch(@RequestBody OpBatch opBatch) {
        // 校验 活动id不能为空
        if (StringUtils.isBlank(opBatch.getActivityId())) {
            return ResponseMessage.error("活动ID不能为空！");
        }
        OpActivity opActivity = opActivityService.getById(opBatch.getActivityId());
        if (null == opActivity) {
            return ResponseMessage.error("活动不存在！");
        }
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            return ResponseMessage.error("操作失败,请先关闭活动再进行操作！");
        }
        return opBatchService.saveOrUpdateBatch(opBatch);
    }

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/25 16:30
     * @Param        ids 数据id，多个用英文逗号分割
     * @Return       com.tahoecn.opening.converter.ResponseMessage
     * @Description  TODO 批量删除批次根据数据ids
     **/
    @ApiOperation(value = "批量删除批次根据数据ids", notes = "批量删除批次根据数据ids")
    @PostMapping(value = "/deleteBatchByIds")
    public ResponseMessage deleteBatchByIds(@RequestParam("ids") String ids) {
        // 校验
        if (StringUtils.isBlank(ids)) {
            return ResponseMessage.error("至少选择1条数据！");
        }
        return opBatchService.deleteBatchByIds(ids);
    }
}
