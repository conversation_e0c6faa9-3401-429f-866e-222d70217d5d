package com.tahoecn.opening.controller.webapi;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tahoecn.opening.model.vo.BusLoginResponseVO;
import com.tahoecn.uc.sso.common.encrypt.Hash;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.ucapi.UcApiUtils;
import com.tahoecn.opening.common.utils.ThreadLocalUtils;
import com.tahoecn.opening.controller.TahoeBaseController;
import com.tahoecn.opening.model.SysRole;
import com.tahoecn.opening.service.ISysRoleService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 角色表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-09
 */
@Api(tags = "角色管理")
@SuppressWarnings({ "rawtypes", "unchecked" })
@RestController
@RequestMapping("/webapi/sysRole")
public class SysRoleController extends TahoeBaseController {

	@Autowired
	private ISysRoleService sysRoleService;

	@Autowired
	private UcApiUtils ucApiUtils;

	@ApiOperation(value = "是否管理员", notes = "是否管理员")
	@RequestMapping(value = "/isAdmin", method = RequestMethod.GET)
	public JSONResult isAdmin() {
		JSONResult jsonResult = new JSONResult();
		BusLoginResponseVO vo = ThreadLocalUtils.get();
		Map<String, Object> resultMap = new HashMap<>();
		if (vo.getAdminFlag()) {
			resultMap.put("isAdmin", 1);
		} else {
			resultMap.put("isAdmin", 0);
		}
		resultMap.put("userName", vo.getUserAccount());
		resultMap.put("realName", vo.getUserName());
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		jsonResult.setData(resultMap);
		return jsonResult;
	}

	@ApiOperation(value = "获取角色列表", notes = "获取角色列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "当前页", defaultValue = "1", dataType = "Integer"),
			@ApiImplicitParam(name = "pageSize", value = "每页显示条数", defaultValue = "20", dataType = "Integer"),
			@ApiImplicitParam(name = "roleName", value = "角色名称", required = false, dataType = "String") })
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public JSONResult list(Integer pageNum, Integer pageSize, String roleName) {
		IPage<SysRole> page = new Page<SysRole>(pageNum, pageSize);
		QueryWrapper<SysRole> queryWrapper = new QueryWrapper();
		if (StringUtils.isNotBlank(roleName)) {
			queryWrapper.lambda().like(SysRole::getRoleName, roleName);
		}
		queryWrapper.lambda().eq(SysRole::getYn, GlobalConstants.Y);
		JSONResult jsonResult = new JSONResult();
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		jsonResult.setData(sysRoleService.page(page, queryWrapper));
		return jsonResult;
	}

	@ApiOperation(value = "判断角色名称重复", notes = "判断角色名称重复")
	@ApiImplicitParams({ @ApiImplicitParam(name = "roleCode", value = "角色编码", dataType = "String"),
			@ApiImplicitParam(name = "roleName", value = "角色名称", dataType = "String"),
			@ApiImplicitParam(name = "id", value = "id", dataType = "String")})
	@RequestMapping(value = "/hasRoleCode", method = RequestMethod.GET)
	public JSONResult hasRoleCode(String roleCode,String roleName,String id) {
		QueryWrapper<SysRole> queryWrapper = new QueryWrapper();
		queryWrapper.lambda().and(rolewrapper -> rolewrapper.eq(SysRole::getRoleCode, roleCode).or().eq(SysRole::getRoleName,roleName)).ne(StringUtils.isNotBlank(id),SysRole::getId,id);
		//queryWrapper.lambda().eq(StringUtils.isNotBlank(roleCode),SysRole::getRoleCode, roleCode).or(StringUtils.isNotBlank(roleName)).eq(StringUtils.isNotBlank(roleName),SysRole::getRoleName,roleName);
		queryWrapper.lambda().eq(SysRole::getYn, GlobalConstants.Y);
		List<SysRole> lists = sysRoleService.list(queryWrapper);
		JSONResult jsonResult = new JSONResult();
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		jsonResult.setData(lists.size());
		return jsonResult;
	}

	@ApiOperation(value = "获取角色详情", notes = "获取角色详情")
	@ApiImplicitParams({ @ApiImplicitParam(name = "roleId", value = "角色Id", required = true, dataType = "String") })
	@RequestMapping(value = "/get", method = RequestMethod.GET)
	public JSONResult get(String roleId) {
		JSONResult jsonResult = new JSONResult();
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		jsonResult.setData(sysRoleService.getById(roleId));
		return jsonResult;
	}

	@ApiOperation(value = "保存角色详情", notes = "保存角色详情")
	@RequestMapping(value = "/saveRole", method = RequestMethod.POST)
	public JSONResult saveRole(@RequestBody SysRole sysRole) {
		JSONResult jsonResult = new JSONResult();
		if (sysRoleService.saveOrUpdate(sysRole)) {
			jsonResult.setCode(GlobalConstants.S_CODE);
			jsonResult.setMsg("SUCCESS");
		} else {
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("保存角色失败");
		}
		return jsonResult;
	}

	@ApiOperation(value = "删除角色", notes = "删除角色")
	@RequestMapping(value = "/removeRoles", method = RequestMethod.POST)
	public JSONResult removeRoles(@RequestBody List<SysRole> roles) {
		JSONResult jsonResult = new JSONResult();
		for (SysRole role : roles) {
			role.setYn(GlobalConstants.N);
		}
		if (sysRoleService.updateBatchById(roles)) {
			jsonResult.setCode(GlobalConstants.S_CODE);
			jsonResult.setMsg("SUCCESS");
		} else {
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("保存角色失败");
		}
		return jsonResult;
	}
}
