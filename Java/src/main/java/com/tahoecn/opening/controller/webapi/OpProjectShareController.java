package com.tahoecn.opening.controller.webapi;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.ApiResult;
import com.tahoecn.opening.common.utils.ThreadLocalUtils;
import com.tahoecn.opening.controller.TahoeBaseController;
import com.tahoecn.opening.converter.ShareinterJson2HttpMessageConverter;
import com.tahoecn.opening.model.OpHouseType;
import com.tahoecn.opening.model.OpProjectShare;
import com.tahoecn.opening.model.interfaceBean.HouseTypeData;
import com.tahoecn.opening.model.interfaceBean.HouseTypeRootBean;
import com.tahoecn.opening.model.interfaceBean.OpProjectShareData;
import com.tahoecn.opening.model.interfaceBean.OpProjectShareRootBean;
import com.tahoecn.opening.service.IOpProjectShareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 分享+项目 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@Api(tags = "叁石云项目")
@RestController
@RequestMapping("/webapi/opProjectShare")
public class OpProjectShareController extends TahoeBaseController {
    @Autowired
    private IOpProjectShareService opProjectShareService;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${const_HOST}")
    private String ConstHOST;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${tahoe.application.physicalPath}")
    private  String webPath;

    @Value("${system_HOST}")
    private  String system_HOST;


    @ApiOperation(value = "获取叁石云项目地址", notes = "根据项目ID获取叁石云项目地址")
    @ApiImplicitParams({@ApiImplicitParam(name = "projectId", value = "项目ID", dataType = "String")})
    @RequestMapping(value = "/projectPageList", method = {RequestMethod.GET})
    public JSONResult projectPageList(String projectId) {
        QueryWrapper<OpProjectShare> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpProjectShare::getYn, GlobalConstants.Y);
        wrapper.select("address");
        wrapper.lambda().eq(StringUtils.isNotBlank(projectId), OpProjectShare::getProjectId, projectId);
        OpProjectShare opProjectShare = opProjectShareService.getOne(wrapper);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(opProjectShare);
        return jsonResult;
    }

    @Scheduled(cron="0 0 1 * * ?")
    @ApiOperation(value = "同步项目", notes = "同步项目")
    @RequestMapping(value = "/updataProjectAUTO", method = {RequestMethod.GET})
    public JSONResult updataProjectAUTO() {
        JSONResult<Object> jsonResult = new JSONResult<>();
        try {
            // 获取锁
            Boolean lock = redisTemplate.opsForValue().setIfAbsent("UPDATE_PROJECT_AUTO_LOCK", new Date());
            if (lock) {
                // 定时释放锁
                redisTemplate.expire("UPDATE_PROJECT_AUTO_LOCK", 30, TimeUnit.SECONDS);
                HttpHeaders headers = new HttpHeaders();
                restTemplate.getMessageConverters().add(new ShareinterJson2HttpMessageConverter());
                HttpEntity<String> requestEntity = new HttpEntity<String>(headers);
                ResponseEntity<OpProjectShareRootBean> quote = restTemplate.postForEntity(ConstHOST + "/FrameWeb/FrameService/Api.ashx?option=func&funcid=mShareOnlineProjectList_Select", requestEntity, OpProjectShareRootBean.class);
                if (quote.getBody() == null) {
                    jsonResult.setCode(GlobalConstants.E_CODE);
                    jsonResult.setMsg("项目同步信息失败，叁石云没有同步过来信息");
                    return jsonResult;
                }

//                String basePath =request.getHeader("referer");
//                String path=basePath+"/opening/uploadfiles/";
                List<OpProjectShareData> list = quote.getBody().getData();
                for (OpProjectShareData opProjectShareData : list) {

                    opProjectShareData.setImgSrc(opProjectShareService.upLoadImg(opProjectShareData,webPath,system_HOST));
                }
                quote.getBody().setData(list);
                boolean flag = opProjectShareService.updataProject(quote);

                if (flag) {
                    jsonResult.setCode(GlobalConstants.S_CODE);
                    jsonResult.setMsg("SUCCESS");
                    return jsonResult;
                }
                jsonResult.setCode(GlobalConstants.E_CODE);
                jsonResult.setMsg("插入数据库失败!");
                return jsonResult;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        jsonResult.setCode(GlobalConstants.E_CODE);
        jsonResult.setMsg("同步失败!");
        return jsonResult;
    }

    @ApiOperation(value = "同步项目", notes = "同步项目")
    @RequestMapping(value = "/updataProject", method = {RequestMethod.POST})
    public JSONResult updataProject(String projectId) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        HttpHeaders headers = new HttpHeaders();
        restTemplate.getMessageConverters().add(new ShareinterJson2HttpMessageConverter());
        HttpEntity<String> requestEntity = new HttpEntity<String>( headers);
        ResponseEntity<OpProjectShareRootBean> quote = restTemplate.postForEntity(ConstHOST+"/FrameWeb/FrameService/Api.ashx?option=func&funcid=mShareOnlineProjectList_Select", requestEntity, OpProjectShareRootBean.class);
        if (quote.getBody() == null) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("当前活动项目同步失败");
            return jsonResult;
        }
//        String basePath =request.getHeader("referer");
//        String path=basePath+"/opening/uploadfiles/";
        boolean temp=false;
        List<OpProjectShareData> list = quote.getBody().getData();
        for (OpProjectShareData opProjectShareData : list) {
           if (projectId.equals(opProjectShareData.getProjectID())){
               temp=true;
           }
           opProjectShareData.setImgSrc(opProjectShareService.upLoadImg(opProjectShareData,webPath,system_HOST));
        }
        quote.getBody().setData(list);
        boolean flag = opProjectShareService.updataProject(quote);
        if (temp&&flag){
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("同步成功");
            return jsonResult;
        }else {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("项目同步信息失败，叁石云没有可同步信息");
            return jsonResult;
        }
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:32 2024/8/19
     * @param projectId 
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     查看项目详情表根据项目id
     **/
    @ApiOperation(value = "查看项目详情表根据项目id", notes = "查看项目详情表根据项目id")
    @GetMapping(value = "/getProjectShareByProjectId")
    public ApiResult getProjectShareByProjectId(@RequestParam("projectId") String projectId) {
        return opProjectShareService.getProjectShareByProjectId(projectId);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:36 2024/8/19
     * @param share 
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     更改项目详情表
     **/
    @ApiOperation(value = "更改项目详情表", notes = "更改项目详情表")
    @PostMapping(value = "/updateProjectShare")
    public ApiResult updateProjectShare(@RequestBody OpProjectShare share) {
        return opProjectShareService.updateProjectShare(share);
    }
}
