package com.tahoecn.opening.controller.webapi;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.ApiResult;
import com.tahoecn.opening.common.utils.CookieUtil;
import com.tahoecn.opening.common.utils.ThreadLocalUtils;
import com.tahoecn.opening.model.vo.BusLoginParamVO;
import com.tahoecn.opening.model.vo.BusLoginResponseVO;
import com.tahoecn.opening.service.LoginService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.controller.webapi
 * @ClassName: LoginController
 * @Description:// TODO 登录控制器
 * @Date: 2024/8/8 13:37
 * @Version: 1.0
 */
@RestController
@Api(tags = "登录模块", value = "登录控制器")
@RequestMapping("/login")
public class LoginController {

    @Autowired
    LoginService loginService;

    @Autowired
    RedisTemplate redisTemplate;

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:47 2024/8/8
     * @param paramVO 
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     业务用户登录
     **/
    @ApiOperation(value = "业务用户登录", httpMethod = "POST", notes = "业务用户登录")
    @PostMapping(value = "/busLogin")
    public ApiResult busLogin(@RequestBody @Valid BusLoginParamVO paramVO) {
        return loginService.busLogin(paramVO, null);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 15:30 2024/8/13
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     业务用户退出登录
     **/
    @ApiOperation(value = "业务用户退出登录", httpMethod = "GET", notes = "业务用户退出登录")
    @GetMapping(value = "/busLogout")
    public ApiResult busLogout(HttpServletRequest request) {
        BusLoginResponseVO loginResponseVO = ThreadLocalUtils.get();
        if (null != loginResponseVO) {
            // 删除对象信息
        	String token = request.getHeader("bus-user-token");
            redisTemplate.delete(GlobalConstants.REDIS_USER_TOKEN_KEY + token);
            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            //清除
            request.getSession().removeAttribute("busUserToken");
            request.getSession().invalidate();
            // 清除 Header（假设原 Header 名为 "Cookie"）
            response.setHeader("Cookie", "");
            // 清除 Cookie（假设原 Cookie 名为 "Cookie"）
            CookieUtil.removeCookie(request, response);
        }
        return ApiResult.getSuccessApiResponse();
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:25 2024/8/19
     * @param paramVO 
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     获取ticket
     **/
    @ApiOperation(value = "获取ticket", httpMethod = "POST", notes = "获取ticket")
    @PostMapping(value = "/getTicket")
    public ApiResult getTicket(@RequestBody @Valid BusLoginParamVO paramVO) {
        return loginService.getTicket(paramVO);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:45 2024/8/19
     * @param token 
     * @return com.tahoecn.opening.common.utils.ApiResult
     * @description // TODO     跳转登录
     **/
    @ApiOperation(value = "跳转登录", httpMethod = "GET", notes = "跳转登录")
    @GetMapping(value = "/jumpByTicket")
    public ApiResult jumpByTicket(@RequestParam("token") String token) {
        return loginService.jumpByTicket(token);
    }
}
