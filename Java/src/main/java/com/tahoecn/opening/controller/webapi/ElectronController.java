package com.tahoecn.opening.controller.webapi;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.tahoecn.opening.common.utils.HttpClientUtil;
import com.tahoecn.opening.model.electronVo.CallBackResult;
import com.tahoecn.opening.model.electronVo.ContractFileParamVo;
import com.tahoecn.opening.model.electronVo.ContractStatusParamVo;
import com.tahoecn.opening.model.electronVo.SignResultParamVo;
import com.tahoecn.opening.service.ElectronService;
import com.tahoecn.opening.service.impl.ElectronServiceImpl;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.List;
import java.util.Map;


/**
 * 电子签控制器
 * @Author: Zhengwenchao  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening.controller.webapi
 * @Description:// TODO 电子签控制器
 * @Date: 2025/1/8 10:23
 * @Version: 1.0
 **/
@RestController
@RequestMapping("/electron")
public class ElectronController {

    @Autowired
    private ElectronService electronService;
    private static final Logger log= LoggerFactory.getLogger(ElectronController.class);

    /**
     * 签约人签章结果回调
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 11:06
     * @Param        paramVo 入参对象Vo
     * @Return       com.tahoecn.opening.model.electronVo.CallBackResult
     * @Description  TODO 签约人签章结果回调
     **/
    @PostMapping("/callBackSignResult")
    public CallBackResult callBackSignResult(@RequestBody SignResultParamVo paramVo) {
        return electronService.callBackSignResult(paramVo);
    }

    /**
     * 合同状态变更回调
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 9:42
     * @Param        paramVo    入参对象Vo
     * @Return       com.tahoecn.opening.model.electronVo.CallBackResult
     * @Description  TODO   合同状态变更回调
     **/
    @PostMapping("/callBackContractStatus")
    public CallBackResult callBackContractStatus(@RequestBody Map map) {
    	log.info("电子签合同状态变更回调入参为：{}", JSONObject.toJSONString(map));
    	ContractStatusParamVo paramVo = new ContractStatusParamVo();
    	paramVo.setId((String)map.get("id"));
    	paramVo.setCode((String)map.get("code"));
    	paramVo.setStatus((String)map.get("status"));
    	paramVo.setCtCode((String)map.get("ctCode"));
    	paramVo.setType((String)map.get("type"));
        return electronService.callBackContractStatus(paramVo);
    }
    /**
     * 合同文件数据回调
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 9:42
     * @Param        paramVo    入参对象Vo
     * @Return       com.tahoecn.opening.model.electronVo.CallBackResult
     * @Description  TODO   合同文件数据回调
     **/
    @PostMapping("/callBackContractFile")
    public CallBackResult callBackContractFile(@RequestBody ContractFileParamVo paramVo) {
        return electronService.callBackContractFile(paramVo);
    }

    /**
     * 合同文件数据回调
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/17 9:42
     * @Param        paramVo    入参对象Vo
     * @Return       com.tahoecn.opening.model.electronVo.CallBackResult
     * @Description  TODO   合同文件数据回调
     **/
    @GetMapping("/getContracteBase64")
    public CallBackResult getContracteBase64(@RequestParam("orderId") String orderId, HttpServletResponse response) {
        


        Map<String, Object> secondSubscriberMap = Maps.newHashMapWithExpectedSize(5);
        secondSubscriberMap.put("contractId", "F96CB65D7B2D4D529C63BD2347A412C6");
        secondSubscriberMap.put("category", "1");
        String resultStr = HttpClientUtil.HttpPostJson("http://************:18088/contract" + "/platform/contract/getContracteBase64", secondSubscriberMap);
        System.out.println("resultStr = " + resultStr);
        if (StringUtils.isNotBlank(resultStr)) {
            JSONObject resultObj = JSONObject.parseObject(resultStr);
            if (ObjectUtil.isNotNull(resultObj.get("code")) && "0".equals(resultObj.getString("code"))) {
                // 操作成功
                // 获取data
                if (ObjectUtil.isNotNull(resultObj.get("data"))) {
                    Object o = resultObj.get("data");
                    byte[] decode = Base64.getDecoder().decode(o.toString());

                    //通过输入流获取图片数据

                    BufferedOutputStream bos = null;
                    try {
                        response.setHeader("Content-disposition", "attachment; filename=" + "twoCode.pdf");
                        response.setContentType("multipart/form-data; charset=utf-8");
                        //bis = new BufferedInputStream(new FileInputStream(codeFile));
                        bos = new BufferedOutputStream(response.getOutputStream());

                        bos.write(decode);

                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        try {
                            if (bos != null)
                                bos.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }


//
//                    try {
//                            FileOutputStream fileOutputStream = new FileOutputStream("/file/dd");
//
//                        fileOutputStream.write(decode);
//                    } catch (IOException e) {
//                        throw new RuntimeException(e);
//                    }

                }
            }
        }


        return null;
    }
}
