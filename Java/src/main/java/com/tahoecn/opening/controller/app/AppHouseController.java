package com.tahoecn.opening.controller.app;


import cn.hutool.core.util.ObjectUtil;
import static java.util.Comparator.comparing;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.tahoecn.opening.model.dto.UserDto;
import com.tahoecn.opening.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.EmptyWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import com.tahoecn.opening.common.annotation.EncryptionParameters;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.DateUtils;
import com.tahoecn.opening.common.utils.LocalDateTimeUtils;
import com.tahoecn.opening.common.utils.MatcherUtils;
import com.tahoecn.opening.controller.TahoeBaseController;
import com.tahoecn.opening.converter.ResponseMessage;
import com.tahoecn.opening.mapper.OpUserHouseResourceMapper;
import com.tahoecn.opening.model.AppRequstDto;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpFavorite;
import com.tahoecn.opening.model.OpHousingResources;
import com.tahoecn.opening.model.OpOrder;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.model.OpUserHouseResource;
import com.tahoecn.opening.model.OpUserSign;
import com.tahoecn.opening.model.dto.HouseResourceDTO;
import com.tahoecn.opening.model.vo.BuyTimeVO;
import com.tahoecn.opening.model.vo.ShowSplitVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 小程序首页房源
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app/house")
@Api(tags = "首页房源控制器", value = "首页房源控制器")
public class AppHouseController extends TahoeBaseController {
    private static final Log log = LogFactory.get();
	@Autowired
	private IOpHousingResourcesService iOpHousingResourcesService;
	@Autowired
    private IOpActivityService activityService;
	@Autowired
	private IOpHouseTypeService iOpHouseTypeService;
	@Autowired
	private IOpFavoriteService iOpFavoriteService;
	@Autowired
	private IOpOrderService iOpOrderService;
	@Autowired
	private IOpUserSignService iOpUserSignService;
	@Autowired
	private IOpUserService iOpUserService;
    @Autowired
    private CsSendSmsLogService csSendSmsLogService;

    @Autowired
    private OpUserHouseResourceMapper opUserHouseResourceMapper;

    @Autowired
    private ElectronService electronService;

	@Autowired
	RedisTemplate redisTemplate;
	/**
     * 查询楼栋单元
     * @param activityId
     * @return
     */
    @ApiOperation(value = "获取楼栋单元", notes = "通过活动id获取房源的楼栋和单元信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "AES_DATA", value = "Json {activityId}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/getBuildingListByActivityId", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage getBuildingListByActivityId(@ApiIgnore @EncryptionParameters String requestParamDto, HttpServletRequest request) {//@EncryptionParameters
		AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
		List<HashMap<String,Object>> resultList = new ArrayList<HashMap<String,Object>>();

		// 定义可查询权限集合
		List<String> batchNameList = Lists.newArrayList(GlobalConstants.COMMON);
		// 按批次展示房源标识：0--否;1--是(默认为0)
		Integer batchFlag = 0;
		// 查模拟还是正式标识：0--模拟;1--正式(默认为=1)
		Integer formalFlag = 1;
		// 获取活动对象
		OpActivity opActivity = activityService.getById(appRequstDto.getActivityId());
		// 校验是否按权限查询
		if (null != opActivity) {
			if (null != opActivity.getBatchFlag() && 1 == opActivity.getBatchFlag().intValue()) {
				batchFlag = 1;
				// 获取该用户可查看批次权限
				Object token = request.getSession().getAttribute("token");
				if (null != token) {
					// 从缓存获取
					Object redisObj = redisTemplate.opsForValue().get("LOGIN_TOKEN_" + token + "_BATCH_");
					if (null != redisObj) {
						batchNameList = (List<String>) redisObj;
					}
				}
				// 校验当前时间属于模拟批次还是正式批次
				LocalDateTime now = LocalDateTime.now();
				if (null != opActivity.getSimulationEnd() && now.isBefore(opActivity.getSimulationEnd())) {
					formalFlag = 0;
				}
			}
		}

		List<HashMap<String,Object>> buildingList = iOpHousingResourcesService.getBuildingListByActivityId(appRequstDto.getActivityId(), batchFlag, formalFlag, batchNameList);
		for(HashMap<String,Object> map : buildingList){
    		List<HashMap<String,Object>> unitList = iOpHousingResourcesService.getUnitListByActivityIdAndBuildingName(appRequstDto.getActivityId(),map.get("buildingName").toString(), batchFlag, formalFlag, batchNameList);
    		map.put("unit", unitList);
    		resultList.add(map);
    	}
    	return ResponseMessage.ok(resultList);
    }
    /**
     * 查询楼层房间信息
     * @param activityId
     * @param buildingName
     * @param unitName
     * @param userName
     * @param isRegular
     * @return
     */
    @ApiOperation(value = "查询楼层房间信息", notes = "通过活动id、楼栋、单元查询楼层房间信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "AES_DATA", value = "Json {activityId,buildingName,unitName,userId,isRegular}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/getRoomListByActivityId", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage getRoomListByActivityId(  @ApiIgnore @EncryptionParameters String requestParamDto
    		) {//@EncryptionParameters
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	List<HashMap<String,Object>> resultList = new ArrayList<HashMap<String,Object>>();
    	List<HashMap<String,Object>> floorList = iOpHousingResourcesService.getFloorListByActivityIdAndBuildingAndUnit(appRequstDto.getActivityId(),appRequstDto.getBuildingName(),appRequstDto.getUnitName());
    	if(floorList.size() == 0){
    		HashMap<String,Object> map = new HashMap<String,Object>();
    		List<HashMap<String,Object>> roomList = iOpHousingResourcesService.getRoomMsgByActivityIdAndBuildingAndUnit(appRequstDto.getActivityId(),appRequstDto.getBuildingName(),appRequstDto.getUnitName(),"",appRequstDto.getIsRegular(),appRequstDto.getUserId());
    		map.put("houseList", roomList);
    		resultList.add(map);
    	}
    	for(HashMap<String,Object> map : floorList){
    		List<HashMap<String,Object>> roomList = iOpHousingResourcesService.getRoomMsgByActivityIdAndBuildingAndUnit(appRequstDto.getActivityId(),appRequstDto.getBuildingName(),appRequstDto.getUnitName(),map.get("currentFloor").toString(),appRequstDto.getIsRegular(),appRequstDto.getUserId());
    		map.put("houseList", roomList);
    		resultList.add(map);
    	}
    	return ResponseMessage.ok(resultList);
    }
    
    /**
     * 查询楼层房间信息(魔改)
     * @param requestParamDto
     * @return
     */
    @ApiOperation(value = "查询楼层房间信息(魔改)", notes = "通过活动id、楼栋、单元查询楼层房间信息(魔改)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "AES_DATA", value = "Json {activityId,buildingName,unitName,userId,isRegular}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/getRoomListByActivityIdMg", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage getRoomListByActivityIdMg(@ApiIgnore @EncryptionParameters String requestParamDto, HttpServletRequest request) {
		long startTime = System.currentTimeMillis();
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	if(appRequstDto == null){
			System.out.println(requestParamDto+"======appRequstDto is null=======");
		}

		// 定义可查询权限集合
		List<String> batchNameList = Lists.newArrayList(GlobalConstants.COMMON);
		// 按批次展示房源标识：0--否;1--是(默认为0)
		Integer batchFlag = 0;
		// 查模拟还是正式标识：0--模拟;1--正式(默认为=1)
		Integer formalFlag = 1;
		// 获取活动对象
		OpActivity opActivity = activityService.getById(appRequstDto.getActivityId());
		// 校验是否按权限查询
		if (null != opActivity) {
			if (null != opActivity.getBatchFlag() && 1 == opActivity.getBatchFlag().intValue()) {
				batchFlag = 1;
				// 获取该用户可查看批次权限
				Object token = request.getSession().getAttribute("token");
				if (null != token) {
					// 从缓存获取
					Object redisObj = redisTemplate.opsForValue().get("LOGIN_TOKEN_" + token + "_BATCH_");
					if (null != redisObj) {
						batchNameList = (List<String>) redisObj;
					}
				}
				// 校验当前时间属于模拟批次还是正式批次
				LocalDateTime now = LocalDateTime.now();
				if (null != opActivity.getSimulationEnd() && now.isBefore(opActivity.getSimulationEnd())) {
					formalFlag = 0;
				}
			}
		}

		// 查 房源信息
		List<HashMap<String,Object>> resultList = iOpHousingResourcesService.getRoomMsgByActivityIdAndBuildingAndUnitMg(appRequstDto.getActivityId(),appRequstDto.getBuildingName(),appRequstDto.getUnitName(),"",appRequstDto.getIsRegular(),appRequstDto.getUserId()
				, batchFlag, formalFlag, batchNameList);
		// 校验
		if (CollectionUtils.isEmpty(resultList)) {
			log.info("getRoomListByActivityIdMg+===getUserId="+appRequstDto.getUserId()+"====程序运行时间：" + (System.currentTimeMillis() - startTime) + "ms");
			return ResponseMessage.ok(Lists.newArrayListWithCapacity(1));
		}
		// 定义是否需要校验 销控逻辑标识
		boolean saleControlFlag = false;
		LocalDateTime nowDate = LocalDateTime.now();
		// 定义返回结果
		Map<String, Object> valueMap = Maps.newHashMapWithExpectedSize(2);
		valueMap.put("showPriceFlag", false);
		// 给当前时间 加相应分钟数
		LocalDateTime localDateTime = nowDate.plusMinutes(null != opActivity.getShowPriceMinutes() ? opActivity.getShowPriceMinutes() : 0);
		// 校验 当前时间 在 模拟开盘的开始和结束时间区间内
		if (null != opActivity.getSimulationStart() && null != opActivity.getSimulationEnd()) {
			// 在 模拟开始结束时间区间内
			if (nowDate.isAfter(opActivity.getSimulationStart()) && nowDate.isBefore(opActivity.getSimulationEnd())) {
				saleControlFlag = true;
			}
			// 校验 当前是否显示房源价格
			if (localDateTime.isAfter(opActivity.getSimulationStart()) && nowDate.isBefore(opActivity.getSimulationEnd())) {
				valueMap.put("showPriceFlag", true);
			}
		}
		// 校验 当前时间 在 正式开盘的开始和结束时间区间内
		if (null != opActivity.getFormalStart() && null != opActivity.getFormalEnd()) {
			// 在 正式开始时间以后
			if (nowDate.isAfter(opActivity.getFormalStart())) {
				saleControlFlag = true;
			}
			// 校验 当前是否显示房源价格
			if (localDateTime.isAfter(opActivity.getFormalStart())) {
				valueMap.put("showPriceFlag", true);
			}
		}
		// 获取当前活动分列展示分组
		Map<String, Integer> showMap = null;
		Object object = redisTemplate.opsForValue().get(appRequstDto.getActivityId() + "Activity" + "_SHOW_MAP");
		if (null != object) {
			showMap = (Map<String, Integer>) object;
		} else {
			// 处理
			if (StringUtils.isNotBlank(opActivity.getShowSplitSetStr())) {
				List<ShowSplitVO> splitVOList = JSONArray.parseArray(opActivity.getShowSplitSetStr(), ShowSplitVO.class);
//                showMap = splitVOList.stream().collect(HashMap::new, (map, item) -> map.put(item.getRoomType(), item.getSplitNum()), HashMap::putAll);
				showMap = splitVOList.stream().collect(Collectors.toMap(ShowSplitVO::getRoomType, s -> Optional.ofNullable(s.getSplitNum()).orElse(ShowSplitVO.DEFAULT_NUM), (k, v) -> v));
				// 写入缓存，这里为防止成为垃圾KEY，设置一个过期时间
				redisTemplate.opsForValue().set(appRequstDto.getActivityId() + "Activity" + "_SHOW_MAP", showMap, 1, TimeUnit.DAYS);
			} else {
				showMap = Maps.newHashMapWithExpectedSize(1);
			}
		}
		// 校验 是否需要匹配销控数据
		if (saleControlFlag) {
			// 匹配销控逻辑
			// 处理 换行数
			for (HashMap<String, Object> map : resultList) {
				// 获取 房源数据
				List<HashMap<String, Object>> thisHouseList = (List<HashMap<String, Object>>) map.get("houseList");
				map.put("splitNum", showMap.get(thisHouseList.get(0).get("roomType")));
				// 获取 房源数据
				for (HashMap<String, Object> house : thisHouseList) {
					Object thisFlag = house.get("saleControlFlag");
					// 校验 销控的数据，将订单状态置为1
					if (null != thisFlag && thisFlag.toString().equals("y")) {
						house.put("orderStatus", 1);
					}
				}
				map.put("houseList", thisHouseList);
			}
		} else {
			// 不匹配销控逻辑
			// 处理 换行数
			for (HashMap<String, Object> map : resultList) {
				// 获取 房源数据
				List<HashMap<String, Object>> thisHouseList = (List<HashMap<String, Object>>) map.get("houseList");
				map.put("splitNum", showMap.get(thisHouseList.get(0).get("roomType")));
			}
		}
		valueMap.put("resultList", resultList);
    	log.info("getRoomListByActivityIdMg+===getUserId="+appRequstDto.getUserId()+"====程序运行时间：" + (System.currentTimeMillis() - startTime) + "ms");
    	return ResponseMessage.ok(valueMap);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 16:31 2022/7/19
     * @param activityId 	活动id 必传
     * @return com.tahoecn.opening.converter.ResponseMessage
     * @description // TODO 递归查询房源信息根据活动ID
     **/
	@ApiOperation(value = "递归查询房源信息根据活动ID", notes = "递归查询房源信息根据活动ID")
	@GetMapping(value = "/selectHouseResourceListByActivityId")
	public ResponseMessage selectHouseResourceListByActivityId(@RequestParam("activityId") Long activityId, @RequestParam("userId") Long userId) {
		List<HouseResourceDTO> dataList = iOpHousingResourcesService.selectHouseResourceListByActivityId(activityId);
		// 校验 数据是否存在
		if (CollectionUtils.isEmpty(dataList)) {
			return ResponseMessage.ok();
		}
		// 查询当前活动下 所有的房源白名单配置房源信息
        QueryWrapper<OpUserHouseResource> resourceQueryWrapper = new QueryWrapper<>();
		resourceQueryWrapper.eq("activity_id", activityId).ne("user_id", userId);
        List<OpUserHouseResource> objectList = opUserHouseResourceMapper.selectList(resourceQueryWrapper);
        if (CollectionUtils.isNotEmpty(objectList)) {
			List<Long> haveIdList = objectList.stream().map(OpUserHouseResource::getHouseResourceId).collect(Collectors.toList());
			// 过滤掉 一下所有房源数据
            Iterator<HouseResourceDTO> iterator = dataList.iterator();
            while (iterator.hasNext()) {
                HouseResourceDTO next = iterator.next();
                if (haveIdList.contains(next.getHouseResourceId())) {
                    iterator.remove();
                }
            }
        }

        // 校验 数据是否存在
        if (CollectionUtils.isEmpty(dataList)) {
            return ResponseMessage.ok();
        }

        // 按照 楼栋分组
		Map<String, List<HouseResourceDTO>> buildingMap = dataList.stream().collect(Collectors.groupingBy(HouseResourceDTO::getBuildingName));
		// 定义 最终的返回集合
		List<HouseResourceDTO> resultList = Lists.newArrayListWithCapacity(buildingMap.size());
		// 定义 单元map
		Map<String, List<HouseResourceDTO>> unitMap = null;
		int id = 999999999;
		// 遍历楼栋
		for (Map.Entry<String, List<HouseResourceDTO>> buildEntry : buildingMap.entrySet()) {
			// 按照 单元分组
			unitMap = buildEntry.getValue().stream().collect(Collectors.groupingBy(HouseResourceDTO::getUnitName));
			// 定义 所有的 单元集合
			List<HouseResourceDTO> unitList = Lists.newArrayListWithCapacity(unitMap.size());
			// 遍历 单元 封装数据
			for (Map.Entry<String, List<HouseResourceDTO>> unitEntry : unitMap.entrySet()) {
				List<HouseResourceDTO> value = unitEntry.getValue();
//				List<HouseResourceDTO> roomList = unitEntry.getValue().stream().sorted(comparing(HouseResourceDTO::getRoomNum)).collect(Collectors.toList());
				// 定义所有的房间集合
                List<HouseResourceDTO> sortRoomList = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(value)) {
                    for (HouseResourceDTO dto : value) {
                        dto.setSortNum(MatcherUtils.convertStringToLong(dto.getRoomNum(), ""));
                    }
                    sortRoomList = value.stream().sorted(comparing(HouseResourceDTO::getSortNum)).collect(Collectors.toList());
                }

				// 定义 单元对象并赋值
				HouseResourceDTO unitDTO = new HouseResourceDTO();
				id--;
				unitDTO.setHouseResourceId(Long.valueOf(id));
				unitDTO.setHouseName(unitEntry.getKey());
				unitDTO.setDisable(true);
				unitDTO.setChildList(sortRoomList);
				unitDTO.setSortNum(MatcherUtils.convertStringToLong(unitEntry.getKey(), ""));
				unitList.add(unitDTO);
			}
			// 定义 楼栋对象并赋值
			HouseResourceDTO buildingDTO = new HouseResourceDTO();
			id--;
			buildingDTO.setHouseResourceId(Long.valueOf(id));
			buildingDTO.setHouseName(buildEntry.getKey());
			buildingDTO.setDisable(true);
			buildingDTO.setChildList(unitList.stream().sorted(comparing(HouseResourceDTO::getSortNum)).collect(Collectors.toList()));
			buildingDTO.setSortNum(MatcherUtils.convertStringToLong(buildEntry.getKey(), ""));
			resultList.add(buildingDTO);
		}
		return ResponseMessage.ok(resultList.stream().sorted(Comparator.comparing(HouseResourceDTO::getSortNum)).collect(Collectors.toList()));
	}

    /**
     * 首页数据信息
     * @param projectId
     * @return
     */
    @ApiOperation(value = "首页数据信息", notes = "通过项目id、查询首页信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "AES_DATA",  value = "Json {projectId,userId}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/getHeadByProjectId", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage getHeadByProjectId( @ApiIgnore @EncryptionParameters String requestParamDto
    		) {//@EncryptionParameters
    	long startTime = System.currentTimeMillis(); 
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	HashMap<String,Object> resultMap = new HashMap<String,Object>();
    	//通过项目id查询进行中的活动
		QueryWrapper<OpActivity> wrapper = new QueryWrapper<>();
	    wrapper.lambda().eq(OpActivity::getYn, GlobalConstants.Y);
	    wrapper.lambda().ne(OpActivity::getStatusCode, GlobalConstants.STATUS_END);
	    wrapper.lambda().eq(StringUtils.isNotBlank(appRequstDto.getProjectId()),OpActivity::getProjectId,appRequstDto.getProjectId());
    	OpActivity opActivity = activityService.getOne(wrapper);
    	if(opActivity == null){
    		return ResponseMessage.error("亲，该项目下无进行中的活动或活动已经结束，请关注项目后续活动或参与其他活动，给您带来的不便敬请谅解，如有疑问请联系您的置业顾问。");
    	}
    	resultMap.put("opActivity", opActivity);
    	List<HashMap<String,Object>> opHouseTypeList = iOpHouseTypeService.getHouseTypeListByActivityId(String.valueOf(opActivity.getId()));
    	resultMap.put("opHouseTypeList", opHouseTypeList);
    	HashMap<String,Object> opHouseTypeAreaMinToMaxMap = iOpHouseTypeService.getHouseTypeAreaMinToMax(String.valueOf(opActivity.getId()));
    	resultMap.put("opHouseTypeAreaMinToMaxMap", opHouseTypeAreaMinToMaxMap);
    	//通过项目id查询进行中的活动
    	String houseCount = iOpHousingResourcesService.getHouseCountByActivityId(opActivity.getId());
    	resultMap.put("houseCount", houseCount);
    	List<HashMap<String,Object>> opFavoriteList = iOpOrderService.getMyOrderByactivityIdAndUser(opActivity.getId().toString(),appRequstDto.getUserId(),"y");
    	if(opFavoriteList.size() == 0){
    		resultMap.put("isOrders", 0);//是否存在订单 0否 1是
    	}else{
    		resultMap.put("isOrders", 1);
    	}
    	QueryWrapper<OpUserSign> wrapperOpUserSign = new QueryWrapper<>();
    	wrapperOpUserSign.lambda().eq(OpUserSign::getActivityId, opActivity.getId());
    	wrapperOpUserSign.lambda().eq(OpUserSign::getUserId, appRequstDto.getUserId());
    	OpUserSign opUserSign = iOpUserSignService.getOne(wrapperOpUserSign);
    	if(opUserSign == null){
    		resultMap.put("isSignIn", 0);//是否签到 0否 1是
    	}else{
    		resultMap.put("isSignIn", 1);
    	}
    	long endTime = System.currentTimeMillis();    //获取结束时间
    	log.info("getHeadByProjectId+===getUserId="+appRequstDto.getUserId()+"====程序运行时间：" + (endTime - startTime) + "ms");
    	return ResponseMessage.ok(resultMap);
    }
    /**
     * 查询我的收藏
     * @param activityId
     * @param userName
     * @param isRegular
     * @return ResponseMessage
     */
    @ApiOperation(value = "我的收藏", notes = "通过活动id、用户查询我的收藏信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "AES_DATA", value = "Json {activityId,userId,isRegular}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/getMyFavoriteByactivityIdAndUser", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage getMyFavoriteByactivityIdAndUser(  @ApiIgnore @EncryptionParameters String requestParamDto
    														
    		) {//@EncryptionParameters
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
		// 定义是否需要校验 销控逻辑标识
		boolean saleControlFlag = false;
		LocalDateTime nowDate = LocalDateTime.now();
		// 定义返回结果
		Map<String, Object> valueMap = Maps.newHashMapWithExpectedSize(2);
		valueMap.put("showPriceFlag", false);
		// 获取活动对象
		OpActivity opActivity = activityService.getById(appRequstDto.getActivityId());
		// 给当前时间 加相应分钟数
		LocalDateTime localDateTime = nowDate.plusMinutes(null != opActivity.getShowPriceMinutes() ? opActivity.getShowPriceMinutes() : 0);
		// 校验 当前时间 在 模拟开盘的开始和结束时间区间内
		if (null != opActivity.getSimulationStart() && null != opActivity.getSimulationEnd()) {
			// 在 模拟开始结束时间区间内
			if (nowDate.isAfter(opActivity.getSimulationStart()) && nowDate.isBefore(opActivity.getSimulationEnd())) {
				saleControlFlag = true;
			}
			// 校验 当前是否显示房源价格
			if (localDateTime.isAfter(opActivity.getSimulationStart())) {
				valueMap.put("showPriceFlag", true);
			}
		}
		// 校验 当前时间 在 正式开盘的开始和结束时间区间内
		if (null != opActivity.getFormalStart() && null != opActivity.getFormalEnd()) {
			// 在 正式开始时间以后
			if (nowDate.isAfter(opActivity.getFormalStart())) {
				saleControlFlag = true;
			}
			// 校验 当前是否显示房源价格
			if (localDateTime.isAfter(opActivity.getFormalStart())) {
				valueMap.put("showPriceFlag", true);
			} else {
				valueMap.put("showPriceFlag", false);
			}
		}
    	List<HashMap<String,Object>> opFavoriteList = iOpFavoriteService.getMyFavoriteByactivityIdAndUser(appRequstDto.getActivityId(),appRequstDto.getUserId(),appRequstDto.getIsRegular());
		// 校验 是否需要匹配销控数据
		if (saleControlFlag) {
			// 获取该活动 对应销控的房源数据信息
			QueryWrapper<OpHousingResources> resourceQueryWrapper = new QueryWrapper<>();
			resourceQueryWrapper.eq("activity_id", appRequstDto.getActivityId()).eq("sale_control_flag", "y").eq("yn", "y");
			List<OpHousingResources> saleControlResourceList = iOpHousingResourcesService.list(resourceQueryWrapper);
			// 校验
			if (CollectionUtils.isNotEmpty(saleControlResourceList)) {
				// 匹配数据
				if (CollectionUtils.isNotEmpty(opFavoriteList)) {
					for (HashMap<String,Object> map : opFavoriteList) {
						for (OpHousingResources resources : saleControlResourceList) {
							// 校验 销控的数据，将订单状态置为1
							if (map.get("houseId").toString().equals(resources.getId().toString())
									&& resources.getSaleControlFlag().equals("y")) {
								map.put("orderStatus", 1);
							}
						}
					}
				}
			}
		}
		valueMap.put("resultList", opFavoriteList);
    	return ResponseMessage.ok(valueMap);
    }
    /**
     * 收藏订单失败查询可售房源
     * @param activityId
     * @param userName
     * @param isRegular
     * @return ResponseMessage
     */
    @ApiOperation(value = "收藏订单失败查询可售房源", notes = "通过活动id、用户查询收藏订单失败查询可售房源")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "AES_DATA", value = "Json {activityId,userId,isRegular}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/getFavoriteFailByactivityIdAndUser", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage getFavoriteFailByactivityIdAndUser(  @ApiIgnore @EncryptionParameters String requestParamDto
    														
    		) {//@EncryptionParameters
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	List<HashMap<String,Object>> opFavoriteList = iOpFavoriteService.getFavoriteFailByactivityIdAndUser(appRequstDto.getActivityId(),appRequstDto.getUserId(),appRequstDto.getIsRegular());
    	return ResponseMessage.ok(opFavoriteList);
    }
    /**
     * 查询我的订单
     * @param activityId
     * @param userName
     * @param isRegular
     * @return ResponseMessage
     */
    @ApiOperation(value = "我的订单", notes = "通过活动id、用户查询我的订单信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "AES_DATA", value = "Json {activityId,userId,isRegular}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/getMyOrderByactivityIdAndUser", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage getMyOrderByactivityIdAndUser(  @ApiIgnore @EncryptionParameters String requestParamDto
    														
    		) {//@EncryptionParameters
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	HashMap<String,Object> resultMap = new HashMap<String,Object>();
    	List<HashMap<String,Object>> opFavoriteList = iOpOrderService.getMyOrderByactivityIdAndUser(appRequstDto.getActivityId(),appRequstDto.getUserId(),appRequstDto.getIsRegular());
    	return ResponseMessage.ok(opFavoriteList);
    }
    /**
     * 收藏
     * @param activityId
     * @param userName
     * @param projectId
     * @param userId
     * @param houseId
     * @param houseName
     * @return ResponseMessage
     */
    @ApiOperation(value = "收藏", notes = "添加收藏")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "AES_DATA", value = "Json {activityId，projectId，userName，userId，houseId，houseName}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/addMyFavoriteByactivityIdAndUser", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage addMyFavoriteByactivityIdAndUser(  @ApiIgnore @EncryptionParameters String requestParamDto
  ) {//@EncryptionParameters
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	HashMap<String,Object> paramMap = new HashMap<String,Object>();
    	paramMap.put("activityId", appRequstDto.getActivityId());
    	paramMap.put("userName", appRequstDto.getUserName());
    	paramMap.put("userId", appRequstDto.getUserId());
    	paramMap.put("houseId", appRequstDto.getHouseId());
    	paramMap.put("houseName", appRequstDto.getHouseName());
    	//判断当前人员的收藏数和活动限制数目
    	HashMap<String,Object> userFavoriteAndActivityFavoriteMap =  iOpOrderService.getUserFavoriteAndActivityFavorite(paramMap);
    	if(Integer.parseInt(userFavoriteAndActivityFavoriteMap.get("collectionLimit") == null ? "0" : userFavoriteAndActivityFavoriteMap.get("collectionLimit").toString())<=Integer.parseInt(userFavoriteAndActivityFavoriteMap.get("userFavoriteCounts") == null ? "0" : userFavoriteAndActivityFavoriteMap.get("userFavoriteCounts").toString())){
    		return ResponseMessage.error("亲，添加失败，您的收藏数目已达上限！");
    	}
    	OpFavorite opFavorite = new OpFavorite();
    	opFavorite.setActivityId(appRequstDto.getActivityId());
    	opFavorite.setCreatedBy(appRequstDto.getUserName());
    	opFavorite.setCreationDate(LocalDateTime.now());
    	opFavorite.setHouseId(appRequstDto.getHouseId());
    	opFavorite.setHouseName(appRequstDto.getHouseName());
    	opFavorite.setProjectId(appRequstDto.getProjectId());
    	opFavorite.setStatus(GlobalConstants.Y);
    	opFavorite.setUserId(appRequstDto.getUserId());
    	opFavorite.setUserName(appRequstDto.getUserName());
    	opFavorite.setYn(GlobalConstants.Y);
    	opFavorite.setOrderBy("0");
    	iOpFavoriteService.save(opFavorite);
    	//List<HashMap<String,Object>> opFavoriteList = iOpOrderService.addMyOrderByactivityIdAndUser(paramMap);
    	return ResponseMessage.ok("收藏成功");
    }
    /**
     * 取消收藏
     * @param activityId
     * @param userName
     * @param houseId
     * @param houseName
     * @return ResponseMessage
     */
    @ApiOperation(value = "取消收藏", notes = "取消收藏")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "AES_DATA", value = "Json {activityId，userId，houseId，houseName}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/delMyFavoriteByactivityIdAndUser", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage delMyFavoriteByactivityIdAndUser(  @ApiIgnore @EncryptionParameters String requestParamDto
  ) {//@EncryptionParameters
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	HashMap<String,Object> paramMap = new HashMap<String,Object>();
    	paramMap.put("activity_Id", appRequstDto.getActivityId());
    	paramMap.put("user_id", appRequstDto.getUserId());
    	paramMap.put("house_Id", appRequstDto.getHouseId());
    	paramMap.put("house_Name", appRequstDto.getHouseName());
    	iOpFavoriteService.removeByMap(paramMap);
    	//List<HashMap<String,Object>> opFavoriteList = iOpOrderService.addMyOrderByactivityIdAndUser(paramMap);
    	return ResponseMessage.ok("取消收藏成功");
    }
    /**
     * 收藏排序
     * @param myFavoriteIds
     * @return ResponseMessage
     */
    @ApiOperation(value = "收藏排序", notes = "收藏排序")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "AES_DATA", value = "Json {{\"myFavoriteIds\":\"id,id,id\"}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/modifyMyFavoriteOrderByIds", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage modifMyFavoriteOrderByIds(  @ApiIgnore @EncryptionParameters String requestParamDto
    		) {//@EncryptionParameters
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	String[] myFavoriteIds = appRequstDto.getMyFavoriteIds().split(",");
    	for(int i=0; i < myFavoriteIds.length ; i++){
    		OpFavorite opFavorite = iOpFavoriteService.getById(myFavoriteIds[i]);
    		if(opFavorite != null){
    			opFavorite.setOrderBy(String.valueOf(i+1));
    			iOpFavoriteService.updateById(opFavorite);
    		}
    	}
    	return ResponseMessage.ok("收藏排序成功");
    }
    /**
     * 取消订单
     * @param orderId
     * @return ResponseMessage
     */
    @ApiOperation(value = "取消订单", notes = "取消订单")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "AES_DATA", value = "Json {{\"orderId\":\"id\"}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/delMyOrderById", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage delMyOrderById(  @ApiIgnore @EncryptionParameters String requestParamDto
    		) {//@EncryptionParameters
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	OpOrder opOrder = iOpOrderService.getById(appRequstDto.getOrderId());
    	//OpOrder opOrder = iOpOrderService.getById(appRequstDto.getOrderId());
    	if(iOpOrderService.removeById(appRequstDto.getOrderId())){
    		iOpOrderService.executeBackOrderLua(opOrder);
    		if("y".equals(opOrder.getIsRegular())){
    			//查询订单手机号和用户名
    			OpUser opUser = iOpUserService.getById(opOrder.getUserId());
    			OpHousingResources opHousingResources = iOpHousingResourcesService.getById(opOrder.getHouseId());
    			csSendSmsLogService.sendSms(opUser.getTel(),"订单已被取消,房间信息:"+opHousingResources.getBuildingName()+opHousingResources.getUnitName()+"-"+opHousingResources.getRoomNum(),opUser.getName());
    		}
    		return ResponseMessage.ok("取消订单成功！");
    	}else{
    		return ResponseMessage.error("取消订单失败，请联系置业顾问！");
    	}
    }
    /**
     * 活动分析
     * @param activityId
     * @return ResponseMessage
     */
    @ApiOperation(value = "活动分析", notes = "活动分析")
    @ApiImplicitParams({ 
    	@ApiImplicitParam(name = "AES_DATA", value = "Json {{\"activityId\":\"activityId\"}", paramType = "query", dataType = "String", required = true)
    })
    @RequestMapping(value = "/getActivityAnalysis", method = {RequestMethod.POST,RequestMethod.GET})
    public ResponseMessage getActivityAnalysis(  @ApiIgnore @EncryptionParameters String requestParamDto
    		) {//@EncryptionParameters
    	AppRequstDto appRequstDto = JSON.parseObject(requestParamDto, AppRequstDto.class);
    	HashMap<String,Object> resultMap = new HashMap<String,Object>();
    	resultMap = iOpHousingResourcesService.getActivityAnalysis(appRequstDto.getActivityId());
    	return ResponseMessage.ok(resultMap);
    }


	@ApiOperation(value = "下单", notes = "下单")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "AES_DATA", value = "", paramType = "query", dataType = "String", required = true)
	})
	@RequestMapping(value = "/getCanOrder", method = {RequestMethod.POST})
	public ResponseMessage getCanOrder(@ApiIgnore @EncryptionParameters String opOrder) {
		OpOrder order = JSON.parseObject(opOrder, OpOrder.class);
		// 获取活动
		OpActivity activity = activityService.getById(order.getActivityId());
		LocalDateTime now = LocalDateTime.now();
		Boolean allowFlag = false;
		// 校验 当前时间是否在模拟选房时间区间内，或者当前时间在正式选房时间区间内
		if ("y".equalsIgnoreCase(activity.getIsSimulation())) {
			// 该活动有模拟，需要判断两个时间区间
			if ((now.isAfter(activity.getSimulationStart()) && now.isBefore(activity.getSimulationEnd()))
					|| (now.isAfter(activity.getFormalStart()) && now.isBefore(activity.getFormalEnd()))) {
				allowFlag = true;
			}
		} else {
			// 该活动只有正式
			if (now.isAfter(activity.getFormalStart()) && now.isBefore(activity.getFormalEnd())) {
				allowFlag = true;
			}
		}
		if (false == allowFlag) {
			return ResponseMessage.error("当前时间不在抢购时间内，不允许抢购！");
		}

		// 获取用户
		OpUser opUser = iOpUserService.getById(order.getUserId());
		if (null == opUser) {
			return ResponseMessage.error("当前用户不存在！");
		}
		Date nowDate = new Date();
		// 校验当前活动类型
		if (null != activity.getCheckMode() && 1 == activity.getCheckMode().intValue()) {
			// 选房模式
			UserDto userDto = activityService.getUserDto(activity, opUser.getId());
			if (null == userDto || null == userDto.getBeginDate() || null == userDto.getEndDate()) {
				return ResponseMessage.error("当前时间您不可选租！");
			}
			if (nowDate.before(userDto.getBeginDate()) || nowDate.after(userDto.getEndDate())) {
				return ResponseMessage.error("当前时间您不可选租！");
			}
		}

		JSONResult jsonResult = iOpOrderService.getCanOrderByLua(order);
		if (GlobalConstants.E_CODE == jsonResult.getCode()){
			return ResponseMessage.error(jsonResult.getMsg());
		}else{
			return ResponseMessage.ok(jsonResult.getMsg());
		}
	}

	@ApiOperation(value = "查询成功订单", notes = "查询成功订单")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "AES_DATA", value = "", paramType = "query", dataType = "String", required = true)
	})
	@RequestMapping(value = "/getOrder", method = {RequestMethod.POST})
	public ResponseMessage getOrder(@ApiIgnore @EncryptionParameters String opOrder) {
		OpOrder order = JSON.parseObject(opOrder, OpOrder.class);
		JSONResult jsonResult = iOpOrderService.getOrder(order);
		if (GlobalConstants.E_CODE == jsonResult.getCode()){
			return ResponseMessage.error(jsonResult.getMsg());
		}else{
			return ResponseMessage.ok(jsonResult.getData());
		}
	}

	@ApiOperation(value = "去签约", notes = "去签约")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "AES_DATA", value = "", paramType = "query", dataType = "String", required = true)
	})
	@RequestMapping(value = "/goToElectronSign", method = {RequestMethod.POST})
	public ResponseMessage goToElectronSign(@ApiIgnore @EncryptionParameters String opOrder) {
		OpOrder order = JSON.parseObject(opOrder, OpOrder.class);
		// 获取活动
		OpActivity opActivity = activityService.getById(order.getActivityId());
		if (null == opActivity) {
			return ResponseMessage.error("当前活动不存在！");
		}
		// 校验是否开启了电子签
		if (ObjectUtil.isNull(opActivity.getOpenElectronFlag()) || 1 != opActivity.getOpenElectronFlag().intValue()) {
			return ResponseMessage.error("当前活动未开启电子签配置！");
		}
		// 校验是否配置了模板ID
		if (StringUtils.isBlank(opActivity.getTemplateId())) {
			return ResponseMessage.error("当前活动未配置电子签模板！");
		}
		OpOrder thisOrder = iOpOrderService.getById(order.getId());
		if (null == thisOrder) {
			return ResponseMessage.error("当前订单不存在！");
		}
		OpHousingResources housingResources = iOpHousingResourcesService.getById(thisOrder.getHouseId());
		if (null == housingResources) {
			return ResponseMessage.error("当前房源不存在！");
		}
		// 校验订单状态
		if (ObjectUtil.isNotNull(thisOrder.getOrderType()) && 1 == thisOrder.getOrderType().intValue()) {
			// 已生成电子签合同
			return ResponseMessage.ok(thisOrder.getElectronUrl());
		}
		// 生成电子签
		OpUser opUser = iOpUserService.getById(thisOrder.getUserId());
		if (null == opUser) {
			return ResponseMessage.error("当前用户不存在！");
		}
		iOpOrderService.checkElectronData(thisOrder, opActivity, opUser, housingResources);
		return ResponseMessage.ok(thisOrder.getElectronUrl());
	}

	@ApiOperation(value = "下载电子签合同", notes = "下载电子签合同")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "AES_DATA", value = "", paramType = "query", dataType = "String", required = true)
	})
	@RequestMapping(value = "/downloadElectron", method = {RequestMethod.POST})
	public ResponseMessage downloadElectron(@ApiIgnore @EncryptionParameters String opOrder) {
		OpOrder order = JSON.parseObject(opOrder, OpOrder.class);
		// 获取活动
		OpActivity opActivity = activityService.getById(order.getActivityId());
		if (null == opActivity) {
			return ResponseMessage.error("当前活动不存在！");
		}
		// 校验是否开启了电子签
		if (ObjectUtil.isNull(opActivity.getOpenElectronFlag()) || 1 != opActivity.getOpenElectronFlag().intValue()) {
			return ResponseMessage.error("当前活动未开启电子签配置！");
		}
		// 校验是否配置了模板ID
		if (StringUtils.isBlank(opActivity.getTemplateId())) {
			return ResponseMessage.error("当前活动未配置电子签模板！");
		}
		OpOrder thisOrder = iOpOrderService.getById(order.getId());
		if (null == thisOrder) {
			return ResponseMessage.error("当前订单不存在！");
		}
		if (StringUtils.isBlank(thisOrder.getElectronId())) {
			return ResponseMessage.error("合同id不存在！");
		}
		// 获取附件
		String resultStr = electronService.getElectronFile(thisOrder.getElectronId());
		if (StringUtils.isNotBlank(resultStr)) {
			JSONObject resultObj = JSONObject.parseObject(resultStr);
			if (ObjectUtil.isNotNull(resultObj.get("code")) && "0".equals(resultObj.getString("code"))) {
				// 操作成功
				// 获取data
				if (ObjectUtil.isNotNull(resultObj.get("data"))) {
					Object o = resultObj.get("data");
					byte[] decode = Base64.getDecoder().decode(o.toString());
					BufferedOutputStream bos = null;
					try {
						response.setHeader("Content-disposition", "attachment; filename=" + "contract.pdf");
						response.setContentType("multipart/form-data; charset=utf-8");
						bos = new BufferedOutputStream(response.getOutputStream());
						bos.write(decode);
					} catch (Exception e) {
						e.printStackTrace();
					} finally {
						try {
							if (bos != null)
								bos.close();
						} catch (IOException e) {
							e.printStackTrace();
						}
					}
				}
			}
		}
		return ResponseMessage.error("附件不存在！");
	}

	 @ApiOperation(value = "生成压测参数", notes = "生成压测参数")
		@RequestMapping(value = "/getLoadTestData", method = { RequestMethod.POST, RequestMethod.GET })
		public JSONResult getLoadTestData(String activityId) {
	    	String houseIds = "";
	    	HashMap<String,Object> resultMap = new HashMap<String,Object>();
	    	List<String> listAesData = new ArrayList<String>();
	    	List<HashMap<String,Object>> resultList = activityService.getLoadTestData(houseIds,activityId);
//	    	for(HashMap<String,Object> map : resultList){
//	    		String str = JSON.toJSONString(map);
//	    		System.out.println("====================str"+str);
////	    		try {
////					String strAes = AesUtils.encrypt(str, AesConstants.AES_KEY);
////					System.out.println("====================strAes"+strAes);
//					listAesData.add(str);
////				} catch (AesException e) {
////					// TODO Auto-generated catch block
////					e.printStackTrace();
////				}
//	    	}
	    	int i = (int)(0+Math.random()*(resultList.size()-1+0));
	    	System.out.println(resultList.get(i));
			JSONResult<Object> jsonResult = new JSONResult<>();
	        jsonResult.setCode(GlobalConstants.S_CODE);
	        jsonResult.setMsg("SUCCESS");
	        jsonResult.setData(resultList.get(i));
	        return jsonResult;
		}

	@GetMapping("/initHouseResourceWhiteOrder")
	public JSONResult initHouseResourceWhiteOrder(@RequestParam("activityId") String activityId, @RequestParam("createType") Integer createType) {
		Object o = iOpOrderService.initHouseResourceWhiteOrder(activityId, createType);
		JSONResult<Object> jsonResult = new JSONResult<>();
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		jsonResult.setData(o);
		return jsonResult;
	}

	/*
	 * <AUTHOR> <EMAIL>
	 * @Date         2024/11/22 17:50
	 * @Param        activityId			活动id
	 * @Param        userId				用户id
	 * @Return       com.tahoecn.opening.converter.ResponseMessage
	 * @Description  TODO 		获取当前用户最新通知信息
	 **/
	@ApiImplicitParams({
			@ApiImplicitParam(name = "AES_DATA", value = "", paramType = "query", dataType = "String", required = true)
	})
	@RequestMapping(value = "/getUserNoticeByActivityIdAndUserId", method = {RequestMethod.POST})
	@ApiOperation(value = "获取当前用户最新通知信息", notes = "获取当前用户最新通知信息")
	public ResponseMessage getUserNoticeByActivityIdAndUserId(@ApiIgnore @EncryptionParameters String param) {
		JSONObject jsonObject = JSONObject.parseObject(param);
		Long activityId = jsonObject.getLong("activityId");
		Long userId = jsonObject.getLong("userId");
		try {
			OpActivity activity = activityService.getById(activityId);
			// 校验当前活动类型
			if (null != activity.getCheckMode() && 1 == activity.getCheckMode().intValue()) {
				// 选房模式
				// 校验活动状态
				if (!GlobalConstants.STATUS_R.equals(activity.getStatusCode())) {
					return ResponseMessage.ok("当前活动" + activity.getStatusName() + "！");
				}
				LocalDateTime now = LocalDateTime.now();
				Date nowDate = new Date();
				// 校验是否有模拟活动
				if (null != activity.getSimulationStart() && null != activity.getSimulationEnd()) {
					if (now.isBefore(activity.getSimulationEnd())) {
						// 当前时间在模拟活动结束时间之前
						// 获取全量用户
						List<UserDto> userDtoList = activityService.selectAllUserDtoByActivityId(activityId.toString(), false);
						if (CollectionUtils.isEmpty(userDtoList)) {
							return ResponseMessage.error("该用户不存在！");
						}
						// 获取当前执行序号
						int currentSortNum = getCurrentSortNum(userDtoList, nowDate);
						// 获取当前用户
						for (UserDto userDto : userDtoList) {
							if (userDto.getId().longValue() == userId.longValue()) {
								String message = "当前选租序号：" + currentSortNum + "，您的选租序号：" + userDto.getUserSort() + "，您的选租时间区间：";
								if (null != userDto.getBeginDate() && null != userDto.getEndDate()) {
									message += DateUtils.getDateString(userDto.getBeginDate(), DateUtils.PATTERN_SECOND) + "至";
									message += LocalDateTimeUtils.formatDateTime(activity.getSimulationEnd(), DateUtils.PATTERN_SECOND);
								}
								return ResponseMessage.ok(message);
							}
						}
						return ResponseMessage.error("该用户不存在！");
					}
				}
				// 校验是否有正式活动
				if (null != activity.getFormalStart() && null != activity.getFormalEnd()) {
					// 当前时间在模拟活动结束时间之前
					// 获取全量用户
					List<UserDto> userDtoList = activityService.selectAllUserDtoByActivityId(activityId.toString(), true);
					if (CollectionUtils.isEmpty(userDtoList)) {
						return ResponseMessage.error("该用户不存在！");
					}
					// 获取当前执行序号
					int currentSortNum = getCurrentSortNum(userDtoList, nowDate);
					// 获取当前用户
					for (UserDto userDto : userDtoList) {
						if (userDto.getId().longValue() == userId.longValue()) {
							String message = "当前选租序号：" + currentSortNum + "，您的选租序号：" + userDto.getUserSort() + "，您的选租时间区间：";
							if (null != userDto.getBeginDate() && null != userDto.getEndDate()) {
								message += DateUtils.getDateString(userDto.getBeginDate(), DateUtils.PATTERN_SECOND) + "至";
								message += LocalDateTimeUtils.formatDateTime(activity.getFormalEnd(), DateUtils.PATTERN_SECOND);
							}
							return ResponseMessage.ok(message);
						}
					}
					return ResponseMessage.error("该用户不存在！");
				}
				return ResponseMessage.error("当前活动模式时间配置有误！");
			} else {
				return ResponseMessage.ok("当前活动模式不支持此类通知！");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("查询当前用户最新选租公告接口异常！异常活动id为:{}, 异常用户id为:{}", activityId, userId);
			return ResponseMessage.error("系统异常！");
		}
	}

	public static void main(String[] args) {
		List<BuyTimeVO> timeVOList = Lists.newArrayListWithCapacity(2);
		timeVOList.add(new BuyTimeVO("2025-01-25 07:00:00", "2025-01-25 08:00:00"));
		timeVOList.add(new BuyTimeVO("2025-01-25 14:00:00", "2025-01-25 15:00:00"));
		List<UserDto> userDtoList = Lists.newArrayListWithCapacity(10);
		userDtoList.add(new UserDto("A", 5, 1));
		userDtoList.add(new UserDto("C", 6, 2));
		userDtoList.add(new UserDto("B", 7, 2));
		userDtoList.add(new UserDto("D", 3, 5));
		userDtoList.add(new UserDto("E", 8, 6));
		userDtoList.add(new UserDto("F", 5, 7));
		userDtoList.add(new UserDto("G", 6, 9));
		userDtoList.add(new UserDto("H", 30, 13));
		userDtoList.add(new UserDto("I", 5, 15));
		userDtoList.add(new UserDto("J", 5, 16));


		List<UserDto> convertUserDtoList = convertUserDtoList(timeVOList, userDtoList, 2);
		for (UserDto userDto : convertUserDtoList) {
			System.out.println(userDto);
		}

		int currentSortNum = getCurrentSortNum(convertUserDtoList, DateUtils.getDate("2025-01-25 14:08:00", "yyyy-MM-dd HH:mm:ss"));
		System.out.println("currentSortNum = " + currentSortNum);


	}

	public static List<UserDto> convertUserDtoList(List<BuyTimeVO> timeVOList, List<UserDto> userDtoList, Integer checkMinutes) {
		List<UserDto> newUserDtoList = Lists.newArrayListWithCapacity(userDtoList.size());
		// 校验
		if (CollectionUtils.isNotEmpty(timeVOList)) {
			// 上一个执行序号、上一个执行分钟数、上一个执行开始时间、上一个执行结束时间
			int lastSortNum = 0;
			int lastMinutes = 0;
			Date lastBeginDate = null;
			// 超出分钟数
			int morethanMinutes = 0;
			// 处理数据
			for (BuyTimeVO vo : timeVOList) {
				// 定义开始时间和结束时间
				Date thisBeginDate = DateUtils.getDate(vo.getBeginDate(), DateUtils.PATTERN_SECOND);
				Date thisEndDate = DateUtils.getDate(vo.getEndDate(), DateUtils.PATTERN_SECOND);
				Date userBeginDate = null;
				Date userEndDate = null;
				// 校验当前时间
				Iterator<UserDto> iterator = userDtoList.iterator();
				while (iterator.hasNext()) {
					UserDto userDto = iterator.next();
					if (0 == userDto.getSelectCount().intValue() || 0 == userDto.getUserSort().intValue()) {
						// 该用户未配置可选房数 或 排序号
						newUserDtoList.add(userDto);
						iterator.remove();
					} else {
						// 校验是否上一区间有超出分钟数
						if (morethanMinutes > 0) {
							// 有超出
							userBeginDate = thisBeginDate;
						} else {
							// 无超出
							// 校验上一执行序号与当前序号是否相同
							if (lastSortNum == userDto.getUserSort().intValue()) {
								userBeginDate = lastBeginDate;
							} else {
								userBeginDate = thisBeginDate;
							}
						}
						// 获取该区间分钟数
						int minutes = DateUtils.getMinutes(userBeginDate, thisEndDate);
						if (0 >= minutes) {
							break;
						}
						// 获取分钟数
						Integer userCheckMinutes = getUserCheckMinutes(checkMinutes, userDto.getSelectCount());
						// 校验是否上一区间有超出分钟数
						if (morethanMinutes > 0) {
							// 获取该用户结束
							userEndDate = DateUtils.addDateMinute(userBeginDate, morethanMinutes);
						} else {
							// 获取该用户结束
							userEndDate = DateUtils.addDateMinute(userBeginDate, userCheckMinutes);
						}
						// 校验序号是否相同
						if (lastSortNum == userDto.getUserSort().intValue()) {
							// 校验是否上一区间有超出分钟数
							if (morethanMinutes > 0) {
								thisBeginDate = userEndDate;
							} else {
								// 校验是否更新上一个序号数据
								if (userCheckMinutes.intValue() > lastMinutes) {
									// 赋值上次执行时间
									lastBeginDate = userBeginDate;
									lastMinutes = userCheckMinutes;
									thisBeginDate = userEndDate;
								}
							}
						} else {
							thisBeginDate = userEndDate;
							// 赋值上次执行时间
							lastBeginDate = userBeginDate;
							lastMinutes = userCheckMinutes;
							lastSortNum = userDto.getUserSort().intValue();
						}
//						System.err.println("lastSortNum = " + lastSortNum + "==lastMinutes = " + lastMinutes + "==lastBeginDate = " + lastBeginDate + "==thisBeginDate = " + thisBeginDate + "==morethanMinutes = " + morethanMinutes);
						// 校验是否上一区间有超出分钟数
						if (morethanMinutes > 0) {
							morethanMinutes = 0;
						} else {
							userDto.setBeginDate(userBeginDate);
						}
						userDto.setEndDate(userEndDate.after(thisEndDate) ? thisEndDate : userEndDate);
						System.err.println("----" + userDto);
						// 校验是否移除
						if (userCheckMinutes.intValue() > minutes) {
							morethanMinutes = userCheckMinutes.intValue() - minutes;
						} else {
							newUserDtoList.add(userDto);
							iterator.remove();
						}
					}
				}
			}
			if (CollectionUtils.isNotEmpty(userDtoList)) {
				newUserDtoList.addAll(userDtoList);
			}
		}
		return newUserDtoList;
	}

	public static int getCurrentSortNum(List<UserDto> userDtoList, Date nowDate) {
		// 校验
		int currentSortNum = 0;
		if (CollectionUtils.isNotEmpty(userDtoList)) {
			// 定义上一个对象
			UserDto lastUserDto = null;
			// 处理数据
			for (UserDto dto : userDtoList) {
				if (null == dto.getBeginDate() || null == dto.getEndDate()) {
					continue;
				}
				if (nowDate.before(dto.getBeginDate())) {
					return currentSortNum;
				}
//				// 校验区间
//				if (nowDate.after(dto.getBeginDate()) && nowDate.before(dto.getEndDate())) {
//					return dto.getUserSort().intValue();
//				}
				currentSortNum = dto.getUserSort().intValue();
				if (null != lastUserDto) {
					// 比较日期区间
					if (nowDate.after(lastUserDto.getBeginDate()) && nowDate.before(dto.getBeginDate())) {
						return lastUserDto.getUserSort().intValue();
					}
					continue;
				}
				lastUserDto = dto;
			}
		}
		return currentSortNum;
	}


	/** 
	 * 
	 * <AUTHOR> <EMAIL>
	 * @Date         2025/1/21 17:18
	 * @Param        checkMinutes	互动配置选房分钟数
	 * @Param        selectCount 	该用户配置可选房数
	 * @Return       java.lang.Integer
	 * @Description  TODO 相乘
	 **/
	public static Integer getUserCheckMinutes(Integer checkMinutes, Integer selectCount) {
		return checkMinutes * selectCount;
	}

	/*
	 * <AUTHOR> <EMAIL>
	 * @Date         2024/11/22 17:26
	 * @Param        timeStr	选房区间字符串
	 * @Return       java.util.List<com.tahoecn.opening.model.vo.BuyTimeVO>
	 * @Description  TODO 		转换选房时间区间集合
	 **/
	public List<BuyTimeVO> convertBuyTimeByTimeStr(String timeStr) {
		if (StringUtils.isBlank(timeStr)) {
			return null;
		}
		List<BuyTimeVO> timeVOList = JSONArray.parseArray(timeStr, BuyTimeVO.class);
		// 剔除非区间数据
		Iterator<BuyTimeVO> iterator = timeVOList.iterator();
		while (iterator.hasNext()) {
			BuyTimeVO next = iterator.next();
			if (null == next.getBeginDate() || null == next.getEndDate()) {
				iterator.remove();
			}
		}
		if (CollectionUtils.isNotEmpty(timeVOList)) {
			// 按开始时间正叙排序
			timeVOList = timeVOList.stream().sorted(Comparator.comparing(BuyTimeVO::getBeginDate)).collect(Collectors.toList());
		}
		return timeVOList;
	}

	/*
	 * <AUTHOR> <EMAIL>
	 * @Date         2024/11/22 16:19
	 * @Param        timeVOList			可选时间区间集合
	 * @Param        sortNum			个人排序号
	 * @Param        checkMinutes		个人选房分钟数
	 * @Return       java.lang.String
	 * @Description  TODO 	获取个人选房开始时间
	 **/
	public String getStartDateStrByTimeStr(List<BuyTimeVO> timeVOList, Integer sortNum, Integer checkMinutes) {
		String beginDateStr = "";
		if (CollectionUtils.isNotEmpty(timeVOList)) {
			// 定义总序号
			int totalNum = 0;
			// 处理数据
			for (BuyTimeVO vo : timeVOList) {
				int thisSortNum = DateUtils.getSortNum(vo.getBeginDate(), vo.getEndDate(), checkMinutes);
				totalNum += thisSortNum;
				// 校验
				if (totalNum >= sortNum.intValue()) {
					// 获取时间区间
					// 判断第几个区间
					if (totalNum == thisSortNum) {
						// 第一个区间
						Date thisBegin = DateUtils.addDateMinute(DateUtils.getDate(vo.getBeginDate(), DateUtils.PATTERN_SECOND), (sortNum - 1) * checkMinutes);
						beginDateStr = DateUtils.getDateString(thisBegin, DateUtils.PATTERN_SECOND);
					} else {
						// 后续区间
						int stract = sortNum - (totalNum - thisSortNum);
						Date thisBegin = DateUtils.addDateMinute(DateUtils.getDate(vo.getBeginDate(), DateUtils.PATTERN_SECOND), (stract - 1) * checkMinutes);
						beginDateStr = DateUtils.getDateString(thisBegin, DateUtils.PATTERN_SECOND);
					}
					break;
				}
			}
		}
		return beginDateStr;
	}

	/*
	 * <AUTHOR> <EMAIL>
	 * @Date         2024/11/22 16:33
	 * @Param        timeVOList				可选时间区间集合
	 * @Param        checkMinutes			个人选房分钟数
	 * @Param        nowDate				当前日期
	 * @Return       java.lang.Integer
	 * @Description  TODO 	获取当前正在进行中序号
	 **/
	public Integer getCurrentSortNumByTimeStr(List<BuyTimeVO> timeVOList, Integer checkMinutes, Date nowDate) {
		int currentSortNum = 1;
		// 校验
		if (CollectionUtils.isEmpty(timeVOList)) {
			return currentSortNum;
		}
		// 校验时间
		if (nowDate.before(DateUtils.getDate(timeVOList.get(0).getBeginDate(), DateUtils.PATTERN_SECOND))) {
			return currentSortNum;
		}
		// 处理数据
		for (BuyTimeVO vo : timeVOList) {
			// 校验日期
			if (nowDate.before(DateUtils.getDate(timeVOList.get(0).getBeginDate(), DateUtils.PATTERN_SECOND))) {
				break;
			}
			if (nowDate.before(DateUtils.getDate(timeVOList.get(0).getEndDate(), DateUtils.PATTERN_SECOND))) {
				int thisSortNum = DateUtils.getSortNum(vo.getBeginDate(), DateUtils.getDateString(nowDate, DateUtils.PATTERN_SECOND), checkMinutes);
				currentSortNum += thisSortNum;
				break;
			}
			int thisSortNum = DateUtils.getSortNum(vo.getBeginDate(), vo.getEndDate(), checkMinutes);
			currentSortNum += thisSortNum;
		}
		return currentSortNum;
	}
}
