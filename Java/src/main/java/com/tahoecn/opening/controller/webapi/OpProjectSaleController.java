package com.tahoecn.opening.controller.webapi;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.ExcelUtilsTest;
import com.tahoecn.opening.controller.TahoeBaseController;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpProjectSale;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.service.IOpProjectSaleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 销售系统项目 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@Api(tags = "销售项目接口", value = "销售项目接口")
@RestController
@RequestMapping("/webapi/opProjectSale")
public class OpProjectSaleController extends TahoeBaseController {

    @Autowired
    private IOpProjectSaleService projectSaleService;

    @ApiOperation(value = "项目列表", notes = "分页获取项目列表")
    @ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "当前页数", dataType = "int") ,
            @ApiImplicitParam(name = "pageSize", value = "每页大小", dataType = "int") })
    @RequestMapping(value = "/projectPageList", method = {RequestMethod.GET})
    public JSONResult projectPageList(@RequestParam(defaultValue = "1") int pageNum, @RequestParam(defaultValue = "10")int pageSize,
                                        String areaId,String cityId,String projectId) {
        /*IPage<OpProjectSale> page = new Page<>(pageNum,pageSize);
        QueryWrapper<OpProjectSale> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpProjectSale::getYn, GlobalConstants.Y);
        wrapper.lambda().eq(StringUtils.isNotBlank(areaId),OpProjectSale::getAreaId,areaId);
        wrapper.lambda().eq(StringUtils.isNotBlank(cityId),OpProjectSale::getCityId,cityId);
        wrapper.lambda().eq(StringUtils.isNotBlank(projectId),OpProjectSale::getProjectId,projectId);
        IPage<OpProjectSale> opProjectSaleIPage = projectSaleService.page(page,wrapper);*/


        Page<OpProjectSale> page2 = new Page<>(pageNum,pageSize);
        Page<OpProjectSale> lists = projectSaleService.projectList(areaId, cityId, projectId,page2);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(lists);
        return jsonResult;
    }

    @ApiOperation(value = "项目区域", notes = "获取项目区域查询条件下拉列表")
    @RequestMapping(value = "/projectArea", method = {RequestMethod.GET})
    public JSONResult projectArea() {
        QueryWrapper<OpProjectSale> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpProjectSale::getYn, GlobalConstants.Y);
        wrapper.select("area_id","area_name");
        wrapper.groupBy("area_id","area_name");
        List<OpProjectSale> opProjectSales = projectSaleService.list(wrapper);
        List<OpProjectSale> list2 = new ArrayList<OpProjectSale>();
        list2.add(null);
        opProjectSales.removeAll(list2);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(opProjectSales);

        return jsonResult;
    }

    @ApiOperation(value = "项目城市", notes = "获取项目城市查询条件下拉列表")
    @RequestMapping(value = "/projectCity", method = {RequestMethod.GET})
    public JSONResult projectCity(String areaId) {
        QueryWrapper<OpProjectSale> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpProjectSale::getYn, GlobalConstants.Y);
        wrapper.lambda().eq(OpProjectSale::getAreaId,areaId);
        wrapper.select("city_id","city_name");
        wrapper.groupBy("city_id","city_name");
        List<OpProjectSale> opProjectSales = projectSaleService.list(wrapper);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(opProjectSales);
        return jsonResult;
    }

    @ApiOperation(value = "查询条件项目", notes = "获取项目查询条件下拉列表")
    @RequestMapping(value = "/projectConditionList", method = {RequestMethod.GET})
    public JSONResult projectConditionList(String areaId,String cityId) {
        QueryWrapper<OpProjectSale> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpProjectSale::getYn, GlobalConstants.Y);
        wrapper.lambda().eq(OpProjectSale::getAreaId,areaId);
        wrapper.lambda().eq(OpProjectSale::getCityId,cityId);
        wrapper.select("project_id","project_name");
        wrapper.groupBy("project_id","project_name");
        List<OpProjectSale> opProjectSales = projectSaleService.list(wrapper);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(opProjectSales);
        return jsonResult;
    }

    /**
     * 项目模板下载
     *
     * @param response
     * @return
     */
    @ApiOperation(value = "下载项目模板", notes = "下载项目模板")
    @RequestMapping(value = "/downLoadExcel", method = {RequestMethod.GET})
    public JSONResult downLoadExcel(HttpServletResponse response) {
        List<OpProjectSale> opProjectSales = new ArrayList<>();
        JSONResult<Object> jsonResult = new JSONResult<>();
        try {
            ExcelUtilsTest.exportExcel(opProjectSales, null, "项目信息", OpProjectSale.class, "项目导入表", response);
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("项目信息模板导出错误");
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }

    /**
     * 项目数据批量导入
     *
     * @param file
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "Excel项目导入", notes = "Excel项目导入")
    @RequestMapping(value = "/importProjectExcel", method = {RequestMethod.POST})
    public JSONResult importProjectExcel(MultipartFile file) {
        List<OpProjectSale> opProjectSaleList = null;
        JSONResult<Object> jsonResult = new JSONResult<>();
        try {
            opProjectSaleList = ExcelUtilsTest.importExcel(file, OpProjectSale.class);
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("Excel导入失败!");
            return jsonResult;
        }
        try {
            projectSaleService.saveImportData(opProjectSaleList);
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("Excel插入数据库失败!");
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }



}
