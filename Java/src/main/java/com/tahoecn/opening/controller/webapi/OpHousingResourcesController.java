package com.tahoecn.opening.controller.webapi;

import cn.hutool.db.sql.Order;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.constants.MQConstant;
import com.tahoecn.opening.common.utils.*;
import com.tahoecn.opening.controller.TahoeBaseController;
import com.tahoecn.opening.mapper.OpUserHouseResourceMapper;
import com.tahoecn.opening.model.*;
import com.tahoecn.opening.model.dto.WhiteHouseResourceDTO;
import com.tahoecn.opening.model.vo.HouseResourceImportVO;
import com.tahoecn.opening.model.vo.SalesOrderVO;
import com.tahoecn.opening.model.vo.UserResourceImportVO;
import com.tahoecn.opening.model.vo.WhiteHouseResourceParamVO;
import com.tahoecn.opening.schedule.OrgInfoTask;
import com.tahoecn.opening.schedule.UserInfoTask;
import com.tahoecn.opening.server.WebSocketServer;
import com.tahoecn.opening.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import ma.glasnost.orika.MapperFacade;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.AbstractJavaTypeMapper;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 楼栋房源 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-08
 */
@RestController
@Api(tags = "房源接口", value = "房源接口")
@RequestMapping("/webapi/opHousingResources")
public class OpHousingResourcesController extends TahoeBaseController {
	// 测到房源页面
	// 每页显示的数据是按项目id还是活动id
	@Autowired
	private IOpHousingResourcesService iOpHousingResourcesService;

	@Autowired
	private IOpHouseTypeService iOpHouseTypeService;

	@Autowired
	private IOpActivityService iOpActivityService;

	@Autowired
	private IOpOrderService iOpOrderService;

	@Value("${control_time}")
	private Double controTime;

	@Value("${control_white_minutes}")
	private Integer controlWhiteMinutes;

	@Autowired
    private IOpFavoriteService favoriteService;

	@Resource
	private OpUserHouseResourceMapper userHouseResourceMapper;

	@Autowired
	MapperFacade mapperFacade;

	@Resource
	private IOpBatchService batchService;

	/**
	 * 楼栋下拉列表
	 *
	 * @param activityIdNow
	 * @return
	 */
	@ApiOperation(value = "楼栋", notes = "获取楼栋查询条件下拉列表")
	@RequestMapping(value = "/buildingName", method = { RequestMethod.GET })
	public JSONResult buildingName(String activityIdNow) {
		QueryWrapper<OpHousingResources> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(OpHousingResources::getYn, GlobalConstants.Y);
		// 查询条件活动id
		wrapper.lambda().eq(OpHousingResources::getActivityId, activityIdNow);

		wrapper.select("project_id", "building_name");
		wrapper.groupBy("project_id", "building_name");
		List<OpHousingResources> opHousingResources = iOpHousingResourcesService.list(wrapper);
		JSONResult jsonResult = new JSONResult();
		if (opHousingResources==null){
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("楼栋列表为null");
			return jsonResult;
		}
		// 转换数据
        List<OpHousingResources> collect = opHousingResources.stream().sorted(Comparator.comparing(s -> MatcherUtils.convertStringToLong(s.getBuildingName(), ""))).collect(Collectors.toList());
        jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		jsonResult.setData(collect);
		return jsonResult;
	}

	/**
	 * 获取单元下来列表
	 *
	 * @param activityIdNow
	 * @param buildingName
	 * @return
	 */
	@ApiOperation(value = "单元", notes = "获取单元查询条件下拉列表")
	@RequestMapping(value = "/unitName", method = { RequestMethod.GET })
	public JSONResult unitName(String activityIdNow, String buildingName) {
		QueryWrapper<OpHousingResources> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(OpHousingResources::getYn, GlobalConstants.Y);
		wrapper.lambda().eq(OpHousingResources::getBuildingName, buildingName);
		QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
		activityQueryWrapper.lambda().eq(OpActivity::getId, activityIdNow);
		OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
		// 获取项目id
		wrapper.lambda().eq(OpHousingResources::getProjectId, opActivity.getProjectId());
		wrapper.select("project_id", "building_name", "unit_name");
		wrapper.groupBy("project_id", "building_name", "unit_name");
		List<OpHousingResources> opHousingResources = iOpHousingResourcesService.list(wrapper);
		JSONResult jsonResult = new JSONResult();
        if (opHousingResources==null){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("单元列表为null");
            return jsonResult;
        }
		// 转换数据
		List<OpHousingResources> collect = opHousingResources.stream().sorted(Comparator.comparing(s -> MatcherUtils.convertStringToLong(s.getUnitName(), ""))).collect(Collectors.toList());
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		jsonResult.setData(collect);
		return jsonResult;
	}

    /**
     * 获取楼层下拉列表
     *
     * @param activityIdNow
     * @param buildingName
     * @param unitName
     * @return
     */
    //楼层数为当前层数 current_floor  变量名未改
    @ApiOperation(value = "楼层", notes = "获取楼层查询条件下拉列表")
    @RequestMapping(value = "/unitFloorNumber", method = {RequestMethod.GET})
    public JSONResult unitFloorNumber(String activityIdNow,
                                      String buildingName,
                                      String unitName) {
        QueryWrapper<OpHousingResources> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpHousingResources::getYn, GlobalConstants.Y);
        wrapper.lambda().eq(OpHousingResources::getBuildingName, buildingName);
        wrapper.lambda().eq(OpHousingResources::getUnitName, unitName);
        //获取项目id
        wrapper.lambda().eq(OpHousingResources::getActivityId, activityIdNow);
        wrapper.select("project_id", "building_name",  "unit_name","current_floor");
        wrapper.groupBy("project_id", "building_name", "unit_name", "current_floor");
        List<OpHousingResources> opHousingResources = iOpHousingResourcesService.list(wrapper);
        JSONResult jsonResult = new JSONResult();
        if (opHousingResources==null){
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("当前层数列表为null");
            return jsonResult;
        }
		// 转换数据
		List<OpHousingResources> collect = opHousingResources.stream().sorted(Comparator.comparing(s -> MatcherUtils.convertStringToLong(s.getCurrentFloor(), ""))).collect(Collectors.toList());
		jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(collect);
        return jsonResult;
    }

	/**
	 * 获取产品类型下拉列表
	 *
	 * @param activityId
	 * @return
	 */
	@ApiOperation(value = "产品类型", notes = "获取产品类型查询条件下拉列表")
	@RequestMapping(value = "/roomType", method = { RequestMethod.GET })
	public JSONResult roomType(String activityId) {
		QueryWrapper<OpHousingResources> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(OpHousingResources::getYn, GlobalConstants.Y);
        wrapper.lambda().eq(OpHousingResources::getActivityId, activityId);
        wrapper.select("room_type");
        wrapper.groupBy("room_type");
        List<OpHousingResources> OpHousingResources = iOpHousingResourcesService.list(wrapper);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(OpHousingResources);
        return jsonResult;
    }

	/**
	 * 户型名称下拉
	 *
	 * @param activityIdNow
	 * @return
	 */
	@ApiOperation(value = "户型名称下拉", notes = "获取户型名称下拉列表")
	@RequestMapping(value = "/houseType", method = { RequestMethod.GET })
	public JSONResult houseType(String activityIdNow) {
		QueryWrapper<OpHouseType> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(OpHouseType::getYn, GlobalConstants.Y);
		// 获取项目id
		wrapper.lambda().eq(OpHouseType::getActivityId, activityIdNow);
		wrapper.select("house_type");
		wrapper.groupBy("house_type");
		List<OpHouseType> opHouseTypeList = iOpHouseTypeService.list(wrapper);
		JSONResult jsonResult = new JSONResult();
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		jsonResult.setData(opHouseTypeList);
		return jsonResult;
	}

	@ApiOperation(value = "户型结构下拉", notes = "获取户型结构下拉列表")
	@RequestMapping(value = "/houseDescribe", method = { RequestMethod.GET })
	public JSONResult houseDescribe(String activityIdNow, String houseType) {
		QueryWrapper<OpHouseType> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(OpHouseType::getYn, GlobalConstants.Y);
		// 获取项目id
		wrapper.lambda().eq(OpHouseType::getActivityId, activityIdNow);
		wrapper.lambda().eq(OpHouseType::getHouseType, houseType);
		wrapper.select("house_type", "house_describe");
		wrapper.groupBy("house_type", "house_describe");
		List<OpHouseType> houseDescribeList = iOpHouseTypeService.list(wrapper);
		JSONResult jsonResult = new JSONResult();
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		jsonResult.setData(houseDescribeList);
		return jsonResult;
	}

	/**
	 * 房源列表 面积范围
	 *
	 * @param pageNum
	 * @param pageSize
	 * @param opHousingResources
	 * @return
	 */
	@ApiOperation(value = "房源列表", notes = "分页获取房源列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "pageNum", value = "当前页数", dataType = "int"),
			@ApiImplicitParam(name = "pageSize", value = "每页大小", dataType = "int") })
	@RequestMapping(value = "/opHousingResourcesPageList", method = { RequestMethod.GET })
	public JSONResult selectIopHouseType(@RequestParam(defaultValue = "1") int pageNum,
			@RequestParam(defaultValue = "10") int pageSize,
			@RequestParam(name = "activityIdNow", required = false) String activityIdNow,
			@RequestParam(name = "houserAreaMin", required = false) String houserAreaMin,
			@RequestParam(name = "houserAreaMax", required = false) String houserAreaMax,
			@RequestParam(name = "totalPriceMin", required = false) String totalPriceMin,
			@RequestParam(name = "totalPriceMax", required = false) String totalPriceMax,
			@RequestParam(name = "unitPriceMin", required = false) String unitPriceMin,
			@RequestParam(name = "unitPriceMax", required = false) String unitPriceMax,
			OpHousingResourcesVo opHousingResources) {
		IPage<OpHousingResources> page = new Page<>(pageNum, pageSize);
		QueryWrapper<OpHousingResources> wrapper = new QueryWrapper<>();
		// 属性匹配 条件添加
		wrapper.lambda().eq(OpHousingResources::getYn, GlobalConstants.Y);
		if (StringUtils.isNotBlank(opHousingResources.getActivityId())) {
			wrapper.lambda().eq(OpHousingResources::getActivityId, opHousingResources.getActivityId());
		}
		// 设置项目id 查询条件
		wrapper.lambda().eq(OpHousingResources::getActivityId, activityIdNow);

        if (StringUtils.isNotBlank(opHousingResources.getBuildingName())) {
            wrapper.lambda().eq(OpHousingResources::getBuildingName, opHousingResources.getBuildingName());
        }
        if (StringUtils.isNotBlank(opHousingResources.getUnitName())) {
            wrapper.lambda().eq(OpHousingResources::getUnitName, opHousingResources.getUnitName());
        }
        //后改需验证
        if (StringUtils.isNotBlank(opHousingResources.getCurrentFloor())) {
            wrapper.lambda().eq(OpHousingResources::getCurrentFloor, opHousingResources.getCurrentFloor());
        }
        if (StringUtils.isNotBlank(opHousingResources.getRoomNum())) {
            wrapper.lambda().like(OpHousingResources::getRoomNum, opHousingResources.getRoomNum());
        }
        if (StringUtils.isNotBlank(houserAreaMin)) {
            wrapper.lambda().ge(OpHousingResources::getHouserArea, houserAreaMin);
        }
        if (StringUtils.isNotBlank(houserAreaMax)) {
            wrapper.lambda().le(OpHousingResources::getHouserArea, houserAreaMax);
        }
        if (StringUtils.isNotBlank(totalPriceMin)) {
            wrapper.lambda().ge(OpHousingResources::getTotalPrice, totalPriceMin);
        }
        if (StringUtils.isNotBlank(totalPriceMax)) {
            wrapper.lambda().le(OpHousingResources::getTotalPrice, totalPriceMax);
        }
        if (StringUtils.isNotBlank(unitPriceMin)) {
            wrapper.lambda().ge(OpHousingResources::getUnitPrice, unitPriceMin);
        }
        if (StringUtils.isNotBlank(unitPriceMax)) {
            wrapper.lambda().le(OpHousingResources::getUnitPrice, unitPriceMax);
        }
        if (StringUtils.isNotBlank(opHousingResources.getHourseType())) {
            wrapper.lambda().like(OpHousingResources::getHourseType, opHousingResources.getHourseType());
        }
        if (StringUtils.isNotBlank(opHousingResources.getRoomStru())) {
            wrapper.lambda().like(OpHousingResources::getRoomStru, opHousingResources.getRoomStru());
        }
        if (StringUtils.isNotBlank(opHousingResources.getHouseName())) {
            wrapper.lambda().like(OpHousingResources::getHouseName, opHousingResources.getHouseName());
        }
        if (StringUtils.isNotBlank(opHousingResources.getRoomType())) {

            wrapper.lambda().eq(OpHousingResources::getRoomType, opHousingResources.getRoomType());
        }
        // 是否销控
        if (StringUtils.isNotBlank(opHousingResources.getSaleControlFlag())) {
            wrapper.lambda().eq(OpHousingResources::getSaleControlFlag, opHousingResources.getSaleControlFlag());
        }
		// 是否已成单
		if (StringUtils.isNotBlank(opHousingResources.getOrderControlFlag())) {
			QueryWrapper<OpOrder> wrapperOrder = new QueryWrapper<>();
			List<SalesOrderVO> lst=iOpOrderService.getSalesOrder(activityIdNow);
			List<Integer> hostIds=new ArrayList<>();
			for(int i=0;i<lst.size();i++){
				hostIds.add(lst.get(i).getHouseId());
			}
			if(opHousingResources.getOrderControlFlag().equalsIgnoreCase("y")) {
				wrapper.lambda().in(OpHousingResources::getId, hostIds);
			}else{wrapper.lambda().notIn(OpHousingResources::getId, hostIds);}
		}
        IPage<OpHousingResources> iPage = iOpHousingResourcesService.page(page, wrapper);
        // 处理数据，房源总价，优惠总价精确到元，房源单价、优惠单价精确到小数点后两位
		List<OpHousingResources> records = iPage.getRecords();
		// 校验处理
		if (CollectionUtils.isNotEmpty(records)) {
			// 不足两位小数补0
			DecimalFormat format = new DecimalFormat("0.00#");
			for (OpHousingResources vo : records) {
				vo.setTotalPrice(null != vo.getTotalPrice() ? vo.getTotalPrice().setScale(0, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
				vo.setUnitPrice(null != vo.getUnitPrice() ? new BigDecimal(format.format(vo.getUnitPrice().setScale(2, BigDecimal.ROUND_HALF_UP))) : new BigDecimal("0.00"));
				vo.setDiscountTotalPrice(null != vo.getDiscountTotalPrice() ? vo.getDiscountTotalPrice().setScale(0, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
				vo.setDiscountUnitPrice(null != vo.getDiscountUnitPrice() ? new BigDecimal(format.format(vo.getDiscountUnitPrice().setScale(2, BigDecimal.ROUND_HALF_UP))) : new BigDecimal("0.00"));
			}
			iPage = iPage.setRecords(records);
		}
		JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(iPage);
        return jsonResult;
    }

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 10:53 2023/10/27
	 * @param activityIdNow
	 * @param houserAreaMin
	 * @param houserAreaMax
	 * @param totalPriceMin
	 * @param totalPriceMax
	 * @param unitPriceMin
	 * @param unitPriceMax
	 * @param opHousingResources
	 * @param response 
	 * @return com.tahoecn.core.json.JSONResult
	 * @description // TODO 导出房源列表
	 **/
	@ApiOperation(value = "导出房源列表", notes = "导出房源列表")
	@RequestMapping(value = "/exportHourseResources", method = { RequestMethod.GET })
	public JSONResult exportHourseResources(
			@RequestParam(name = "activityIdNow", required = false) String activityIdNow,
			@RequestParam(name = "houserAreaMin", required = false) String houserAreaMin,
			@RequestParam(name = "houserAreaMax", required = false) String houserAreaMax,
			@RequestParam(name = "totalPriceMin", required = false) String totalPriceMin,
			@RequestParam(name = "totalPriceMax", required = false) String totalPriceMax,
			@RequestParam(name = "unitPriceMin", required = false) String unitPriceMin,
			@RequestParam(name = "unitPriceMax", required = false) String unitPriceMax,
			OpHousingResourcesVo opHousingResources, HttpServletResponse response) {
		QueryWrapper<OpHousingResources> wrapper = new QueryWrapper<>();
		// 属性匹配 条件添加
		wrapper.lambda().eq(OpHousingResources::getYn, GlobalConstants.Y);
		if (StringUtils.isNotBlank(opHousingResources.getActivityId())) {
			wrapper.lambda().eq(OpHousingResources::getActivityId, opHousingResources.getActivityId());
		}
		// 设置项目id 查询条件
		wrapper.lambda().eq(OpHousingResources::getActivityId, activityIdNow);

        if (StringUtils.isNotBlank(opHousingResources.getBuildingName())) {
            wrapper.lambda().eq(OpHousingResources::getBuildingName, opHousingResources.getBuildingName());
        }
        if (StringUtils.isNotBlank(opHousingResources.getUnitName())) {
            wrapper.lambda().eq(OpHousingResources::getUnitName, opHousingResources.getUnitName());
        }
        //后改需验证
        if (StringUtils.isNotBlank(opHousingResources.getCurrentFloor())) {
            wrapper.lambda().eq(OpHousingResources::getCurrentFloor, opHousingResources.getCurrentFloor());
        }
        if (StringUtils.isNotBlank(opHousingResources.getRoomNum())) {
            wrapper.lambda().like(OpHousingResources::getRoomNum, opHousingResources.getRoomNum());
        }
        if (StringUtils.isNotBlank(houserAreaMin)) {
            wrapper.lambda().ge(OpHousingResources::getHouserArea, houserAreaMin);
        }
        if (StringUtils.isNotBlank(houserAreaMax)) {
            wrapper.lambda().le(OpHousingResources::getHouserArea, houserAreaMax);
        }
        if (StringUtils.isNotBlank(totalPriceMin)) {
            wrapper.lambda().ge(OpHousingResources::getTotalPrice, totalPriceMin);
        }
        if (StringUtils.isNotBlank(totalPriceMax)) {
            wrapper.lambda().le(OpHousingResources::getTotalPrice, totalPriceMax);
        }
        if (StringUtils.isNotBlank(unitPriceMin)) {
            wrapper.lambda().ge(OpHousingResources::getUnitPrice, unitPriceMin);
        }
        if (StringUtils.isNotBlank(unitPriceMax)) {
            wrapper.lambda().le(OpHousingResources::getUnitPrice, unitPriceMax);
        }
        if (StringUtils.isNotBlank(opHousingResources.getHourseType())) {
            wrapper.lambda().like(OpHousingResources::getHourseType, opHousingResources.getHourseType());
        }
        if (StringUtils.isNotBlank(opHousingResources.getRoomStru())) {
            wrapper.lambda().like(OpHousingResources::getRoomStru, opHousingResources.getRoomStru());
        }
        if (StringUtils.isNotBlank(opHousingResources.getHouseName())) {
            wrapper.lambda().like(OpHousingResources::getHouseName, opHousingResources.getHouseName());
        }
        if (StringUtils.isNotBlank(opHousingResources.getRoomType())) {

            wrapper.lambda().eq(OpHousingResources::getRoomType, opHousingResources.getRoomType());
        }
        // 是否销控
        if (StringUtils.isNotBlank(opHousingResources.getSaleControlFlag())) {
            wrapper.lambda().eq(OpHousingResources::getSaleControlFlag, opHousingResources.getSaleControlFlag());
        }
		// 是否已成单
		if (StringUtils.isNotBlank(opHousingResources.getOrderControlFlag())) {
			QueryWrapper<OpOrder> wrapperOrder = new QueryWrapper<>();
			List<SalesOrderVO> lst=iOpOrderService.getSalesOrder(activityIdNow);
			List<Integer> hostIds=new ArrayList<>();
			for(int i=0;i<lst.size();i++){
				hostIds.add(lst.get(i).getHouseId());
			}
			if(opHousingResources.getOrderControlFlag().equalsIgnoreCase("y")) {
				wrapper.lambda().in(OpHousingResources::getId, hostIds);
			}else{wrapper.lambda().notIn(OpHousingResources::getId, hostIds);}
		}
        List<OpHousingResources> list = iOpHousingResourcesService.list(wrapper);
		// 校验处理
		if (CollectionUtils.isNotEmpty(list)) {
			// 不足两位小数补0
			DecimalFormat format = new DecimalFormat("0.00#");
			for (OpHousingResources vo : list) {
				vo.setTotalPrice(null != vo.getTotalPrice() ? vo.getTotalPrice().setScale(0, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
				vo.setUnitPrice(null != vo.getUnitPrice() ? new BigDecimal(format.format(vo.getUnitPrice().setScale(2, BigDecimal.ROUND_HALF_UP))) : new BigDecimal("0.00"));
				vo.setDiscountTotalPrice(null != vo.getDiscountTotalPrice() ? vo.getDiscountTotalPrice().setScale(0, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
				vo.setDiscountUnitPrice(null != vo.getDiscountUnitPrice() ? new BigDecimal(format.format(vo.getDiscountUnitPrice().setScale(2, BigDecimal.ROUND_HALF_UP))) : new BigDecimal("0.00"));
			}
		}
        try {
            ExcelUtilsTest.exportExcel(mapperFacade.mapAsList(list, HouseResourceImportVO.class), null, "房源表", HouseResourceImportVO.class, "房源导出表", response);
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            return new JSONResult(GlobalConstants.E_CODE, "数据导出错误！");
        }
    }

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 14:35 2023/3/13
	 * @param paramVO 条查参数VO
	 * @return com.tahoecn.core.json.JSONResult
	 * @description // TODO 条查房源白名单列表
	 **/
	@ApiOperation(value = "条查房源白名单列表", notes = "条查房源白名单列表")
	@PostMapping(value = "/selectWhiteHouseResourceListByCondition")
	public JSONResult selectWhiteHouseResourceListByCondition(@RequestBody WhiteHouseResourceParamVO paramVO) {
		JSONResult<Object> jsonResult = new JSONResult<>();
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		// 校验 活动id不能为空
		if (StringUtils.isBlank(paramVO.getActivityId())) {
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("活动ID不能为空！");
		}
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		IPage<WhiteHouseResourceDTO> ipage = iOpHousingResourcesService.selectWhiteHouseResourceListByCondition(paramVO);
		jsonResult.setData(ipage);
		return jsonResult;
	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:45 2023/3/15
	 * @param paramVO
	 * @param response 
	 * @return com.tahoecn.core.json.JSONResult
	 * @description // TODO 导出房源白名单列表
	 **/
	@ApiOperation(value = "导出房源白名单列表", notes = "导出房源白名单列表")
	@PostMapping(value = "/exportWhiteHouseResourceListByCondition")
	public JSONResult exportWhiteHouseResourceListByCondition(@RequestBody WhiteHouseResourceParamVO paramVO, HttpServletResponse response) {
		JSONResult<Object> jsonResult = new JSONResult<>();
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		// 校验 活动id不能为空
		if (StringUtils.isBlank(paramVO.getActivityId())) {
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("活动ID不能为空！");
		}

		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		// 设置 第一页，查询9999999
		paramVO.setPageNum(1);
		paramVO.setPageSize(999999);
		IPage<WhiteHouseResourceDTO> ipage = iOpHousingResourcesService.selectWhiteHouseResourceListByCondition(paramVO);
		try {
			ExcelUtilsTest.exportExcel(ipage.getRecords(), null, "房源白名单", WhiteHouseResourceDTO.class, "房源白名单导出表", response);
		} catch (IOException e) {
			e.printStackTrace();
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("数据导出错误!");
			jsonResult.setData(ipage.getRecords());
		}
		return jsonResult;
	}



	/**
	 * 新增房源
	 *
	 *
	 * @param opHousingResources
	 * @return
	 */
	@ApiOperation(value = "新增房源", notes = "新增单个房源")
	@RequestMapping(value = "/saveOpHousingResources", method = { RequestMethod.POST })
	public JSONResult saveIopHouseType(@RequestBody OpHousingResources opHousingResources) {
		JSONResult<Object> jsonResult = new JSONResult<>();
		OpActivity opActivity = iOpActivityService.getById(opHousingResources.getActivityId());
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            if (opActivity.getFormalStart() != null) {
                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
                    jsonResult.setCode(GlobalConstants.E_CODE);
                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
                    return jsonResult;
                }
            }
        }
		if (StringUtils.isBlank(opHousingResources.getActivityId())) {
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("参数异常，请检查参数信息!");
			jsonResult.setData(opHousingResources);
			return jsonResult;
		}
		// 校验
		if (StringUtils.isBlank(opHousingResources.getBatchNameSimulation())) {
			opHousingResources.setBatchNameSimulation(GlobalConstants.COMMON);
		}
		if (StringUtils.isBlank(opHousingResources.getBatchNameFormal())) {
			opHousingResources.setBatchNameFormal(GlobalConstants.COMMON);
		}
		// 选房模式 && 开启按批次处理
		if (null != opActivity.getCheckMode() && 1 == opActivity.getCheckMode().intValue() && null != opActivity.getBatchFlag() && 1 == opActivity.getBatchFlag().intValue()) {
			// 校验批次
			// 获取该活动所有批次
			List<OpBatch> batchList = batchService.selectAllBatchListByActivityId(opHousingResources.getActivityId());
			List<String> collect = batchList.stream().map(OpBatch::getBatchName).collect(Collectors.toList());
			collect.add(GlobalConstants.COMMON);
			if (!collect.contains(opHousingResources.getBatchNameSimulation())) {
				jsonResult.setCode(GlobalConstants.E_CODE);
				jsonResult.setMsg("该房源模拟批次名称必须在以下批次中：" + collect.stream().collect(Collectors.joining(",")));
				return jsonResult;
			}
			if (!collect.contains(opHousingResources.getBatchNameFormal())) {
				jsonResult.setCode(GlobalConstants.E_CODE);
				jsonResult.setMsg("该房源正式批次名称必须在以下批次中：" + collect.stream().collect(Collectors.joining(",")));
				return jsonResult;
			}
		}

		// 新增人添加
		opHousingResources.setCreatedBy(ThreadLocalUtils.getUserName());
		// 新增时间
		opHousingResources.setCreationDate(LocalDateTimeUtils.localDateTimeToDate(LocalDateTime.now()));
		// 新增项目id活动id
        opHousingResources.setYn(GlobalConstants.Y);
		opHousingResources.setActivityId(opHousingResources.getActivityId());
		opHousingResources.setProjectId(opActivity.getProjectId());
		Integer integer = iOpHousingResourcesService.saveOne(opHousingResources);
		Integer id =opHousingResources.getId();
		if (integer==1) {
			//修改redis数据
			iOpOrderService.redisHouseModify(opHousingResources.getActivityId(),id.toString(),"plus");
			jsonResult.setCode(GlobalConstants.S_CODE);
			jsonResult.setMsg("SUCCESS");
			jsonResult.setData(opHousingResources);
			return jsonResult;
		}
		jsonResult.setCode(GlobalConstants.E_CODE);
		jsonResult.setMsg("新增失败!");
		jsonResult.setData(opHousingResources);
		return jsonResult;

	}

	/**
	 * 编辑页面展示房源
	 *
	 * @param id
	 * @return
	 */
	@ApiOperation(value = "编辑房源展示", notes = "编辑单个房源展示")
	@RequestMapping(value = "/updateOpHousingResourcesShow", method = { RequestMethod.GET })
	public JSONResult updateIopHouseTypeShow(Integer id) {
		QueryWrapper<OpHousingResources> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("ID", id);
		OpHousingResources one = iOpHousingResourcesService.getOne(queryWrapper);
		JSONResult<Object> jsonResult = new JSONResult<>();
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId, one.getActivityId());
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            if (opActivity.getFormalStart() != null) {
                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
                    jsonResult.setCode(GlobalConstants.E_CODE);
                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
                    return jsonResult;
                }
            }
        }
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		jsonResult.setData(one);
		return jsonResult;
	}

	/**
	 * 修改户型信息
	 *
	 * @param opHousingResources
	 * @return
	 */
	@ApiOperation(value = "编辑房源", notes = "编辑单个房源")
	@RequestMapping(value = "/updateOpHouseType", method = { RequestMethod.POST })
	public JSONResult updateIopHouseType(@RequestBody OpHousingResources opHousingResources) {
		JSONResult<Object> jsonResult = new JSONResult<>();
		//判断活动时间区域 是否可修改
		QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
		activityQueryWrapper.lambda().eq(OpActivity::getId, opHousingResources.getActivityId());
		OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            if (opActivity.getFormalStart() != null) {
                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
                    jsonResult.setCode(GlobalConstants.E_CODE);
                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
                    return jsonResult;
                }
            }
        }

		// 校验
		if (StringUtils.isBlank(opHousingResources.getBatchNameSimulation())) {
			opHousingResources.setBatchNameSimulation(GlobalConstants.COMMON);
		}
		if (StringUtils.isBlank(opHousingResources.getBatchNameFormal())) {
			opHousingResources.setBatchNameFormal(GlobalConstants.COMMON);
		}
		// 选房模式 && 开启按批次处理
		if (null != opActivity.getCheckMode() && 1 == opActivity.getCheckMode().intValue() && null != opActivity.getBatchFlag() && 1 == opActivity.getBatchFlag().intValue()) {
			// 校验批次
			// 获取该活动所有批次
			List<OpBatch> batchList = batchService.selectAllBatchListByActivityId(opHousingResources.getActivityId());
			List<String> collect = batchList.stream().map(OpBatch::getBatchName).collect(Collectors.toList());
			collect.add(GlobalConstants.COMMON);
			if (!collect.contains(opHousingResources.getBatchNameSimulation())) {
				jsonResult.setCode(GlobalConstants.E_CODE);
				jsonResult.setMsg("该房源模拟批次名称必须在以下批次中：" + collect.stream().collect(Collectors.joining(",")));
				return jsonResult;
			}
			if (!collect.contains(opHousingResources.getBatchNameFormal())) {
				jsonResult.setCode(GlobalConstants.E_CODE);
				jsonResult.setMsg("该房源正式批次名称必须在以下批次中：" + collect.stream().collect(Collectors.joining(",")));
				return jsonResult;
			}
		}

		// 修改人
		opHousingResources.setLastUpdateBy(ThreadLocalUtils.getUserName());
		// 修改时间
		opHousingResources.setLastUpdateDate(LocalDateTimeUtils.localDateTimeToDate(LocalDateTime.now()));
		boolean flag = iOpHousingResourcesService.updateById(opHousingResources);

		if (flag) {
			OpHousingResources houseRedis = iOpHousingResourcesService.getById(opHousingResources.getId());
			iOpOrderService.redisHouseModify(houseRedis.getActivityId(),houseRedis.getId().toString(),"plus");
			jsonResult.setCode(GlobalConstants.S_CODE);
			jsonResult.setMsg("SUCCESS");
			jsonResult.setData(opHousingResources);
			return jsonResult;
		}
		jsonResult.setCode(GlobalConstants.E_CODE);
		jsonResult.setMsg("编辑更新失败!");
		jsonResult.setData(opHousingResources);
		return jsonResult;

	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:53 2023/3/13
	 * @param activityId		活动ID
	 * @param saleControlFlag 	是否销控标识：y--是;n--否
	 * @param ids				房源ids，多个用英文逗号分隔
	 * @return com.tahoecn.core.json.JSONResult
	 * @description // TODO 更改房源销控状态
	 **/
	@ApiOperation(value = "更改房源销控状态", notes = "更改房源销控状态")
	@GetMapping(value = "/updateSaleControlFlag")
	public JSONResult updateSaleControlFlag(@RequestParam("activityId") String activityId, @RequestParam("saleControlFlag") String saleControlFlag, @RequestParam("ids") String ids) {
		JSONResult<Object> jsonResult = new JSONResult<>();
//		//判断活动时间区域 是否可修改
//		OpActivity opActivity = iOpActivityService.getById(activityId);
//        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
//            if (opActivity.getFormalStart() != null) {
//                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                    return jsonResult;
//                }
//            }
//        }

		String[] split = ids.split(",");
        if (split.length > 0) {
            return iOpHousingResourcesService.updateSaleControlFlag(activityId, saleControlFlag, ids);
		}
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		return jsonResult;
	}

	/**
	 * 删除选择的一个或多个房源
	 *
	 * @param idList
	 * @return
	 */
	@ApiOperation(value = "删除房源", notes = "批量/单个删除房源")
	@RequestMapping(value = "/deleteOpHousingResources", method = { RequestMethod.POST })
	public JSONResult deleteIopHouseType(@RequestBody List<String> idList) {
		JSONResult<Object> jsonResult = new JSONResult<>();
		OpHousingResources byId = iOpHousingResourcesService.getById(idList.get(0));
		//判断活动时间区域 是否可修改
		QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
		activityQueryWrapper.lambda().eq(OpActivity::getId, byId.getActivityId());
		OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            if (opActivity.getFormalStart() != null) {
                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
                    jsonResult.setCode(GlobalConstants.E_CODE);
                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
                    return jsonResult;
                }
            }
        }
		boolean b = iOpHousingResourcesService.canBeDelete(idList);
		if (!b) {
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("删除房源失败!,选定房源已下订单无法删除");
			return jsonResult;
		}

//		for (int i = 0; i < idList.size(); i++) {
//			OpHousingResources opHousingResource = new OpHousingResources();
//			opHousingResource.setId(Integer.parseInt(idList.get(i)));
//			opHousingResource.setYn(GlobalConstants.N);
//			opHousingResource.setLastUpdateBy(ThreadLocalUtils.getUserName());
//			opHousingResource.setLastUpdateDate(LocalDateTimeUtils.localDateTimeToDate(LocalDateTime.now()));
//			opHousingResourcesList.add(opHousingResource);
//		}

		boolean flag =iOpHousingResourcesService.removeByIds(idList);
//		boolean flag iOpHousingResourcesService.updateBatchById(opHousingResourcesList);


        QueryWrapper<OpHousingResources> queryWrapper=new QueryWrapper<>();
        queryWrapper.in("id",idList);
        List<OpHousingResources> list = iOpHousingResourcesService.list(queryWrapper);
        if (flag) {
			for (String id : idList) {
                //删除房源后删除此房源的所有收藏,同活动下
                QueryWrapper<OpFavorite> favoriteQueryWrapper=new QueryWrapper<>();
                favoriteQueryWrapper.eq("house_id",id).eq("activity_id",opActivity.getId());
                favoriteService.remove(favoriteQueryWrapper);
//                OpHousingResources houseRedis = iOpHousingResourcesService.getById(id);

				//删除房源后删除此房源的所有白名单,同活动下
				QueryWrapper<OpUserHouseResource> userHouseResourceQueryWrapper = new QueryWrapper<>();
				userHouseResourceQueryWrapper.eq("house_resource_id",id).eq("activity_id",opActivity.getId());
				userHouseResourceMapper.delete(userHouseResourceQueryWrapper);

                //修改redis数据
                for (OpHousingResources opHousingResources : list) {
                    if (id.equals(opHousingResources.getId())){
                        iOpOrderService.redisHouseModify(opHousingResources.getActivityId(),opHousingResources.getId().toString(),"minus");
                    }
                }
			}
			jsonResult.setCode(GlobalConstants.S_CODE);
			jsonResult.setMsg("SUCCESS");
			return jsonResult;
		}
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("删除户型失败!");
		return jsonResult;

	}


    /**
     * 下载模板  固定模板表头为房源属性名
     *
     * @param
     * @return
     */
    @ApiOperation(value = "下载模板", notes = "下载模板")
    @RequestMapping(value = "/downLoadExcel", method = {RequestMethod.GET})
    public JSONResult downLoadExcel(HttpServletResponse response) {
        List<OpHousingResources> opHousingResourcesList = new ArrayList<>();
        JSONResult<Object> jsonResult = new JSONResult<>();
        try {
            ExcelUtilsTest.exportExcel(opHousingResourcesList, null, "房源信息", OpHousingResources.class, "房源导入表", response);
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("房源信息模板导出错误");
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }

	/**
	 * 批量导入 通过固定模板导入
	 *
	 * @param file
	 * @return
	 * @throws Exception
	 */
	@ApiOperation(value = "批量导入", notes = "批量导入")
	@RequestMapping(value = "/importExcel", method = { RequestMethod.POST })
	public JSONResult importExcel(MultipartFile file, String activityIdNow) {
		List<OpHousingResources> opHousingResourcesList = null;
		JSONResult<Object> jsonResult = new JSONResult<>();
		QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
		activityQueryWrapper.lambda().eq(OpActivity::getId,activityIdNow);
		OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            if (opActivity.getFormalStart() != null) {
                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
                    jsonResult.setCode(GlobalConstants.E_CODE);
                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
                    return jsonResult;
                }
            }
        }
		try {
			opHousingResourcesList = ExcelUtilsTest.importExcel(file, OpHousingResources.class);
		} catch (IOException e) {
		    e.printStackTrace();
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("Excel导入失败!");
			return jsonResult;
		}
		try {
            jsonResult = iOpHousingResourcesService.saveImportData(opHousingResourcesList, activityIdNow);
        }catch (Exception e){
		    e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("Excel导入失败,请检查模板");
            return jsonResult;
        }
		return jsonResult;
	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 15:45 2023/10/26
	 * @param response 
	 * @return com.tahoecn.core.json.JSONResult
	 * @description // TODO 下载客户白名单模板
	 **/
	@ApiOperation(value = "下载客户白名单模板", notes = "下载客户白名单模板")
	@RequestMapping(value = "/downLoadWhiteExcel", method = {RequestMethod.GET})
	public JSONResult downLoadWhiteExcel(HttpServletResponse response) {
		List<UserResourceImportVO> userResourceImportVOList = new ArrayList<>();
		JSONResult<Object> jsonResult = new JSONResult<>();
		try {
			ExcelUtilsTest.exportExcel(userResourceImportVOList, null, "客户白名单信息", UserResourceImportVO.class, "客户白名单导入表", response);
		} catch (IOException e) {
			e.printStackTrace();
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("客户白名单模板导出错误");
			return jsonResult;
		}
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		return jsonResult;
	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:00 2023/10/26
	 * @param file
	 * @param activityIdNow 
	 * @return com.tahoecn.core.json.JSONResult
	 * @description // TODO 批量导入客户白名单
	 **/
	@ApiOperation(value = "批量导入客户白名单", notes = "批量导入客户白名单")
	@RequestMapping(value = "/importWhiteExcel", method = { RequestMethod.POST })
	public JSONResult importWhiteExcel(MultipartFile file, String activityIdNow) {
		JSONResult<Object> jsonResult = new JSONResult<>();
		// 获取 活动对象
		OpActivity opActivity = iOpActivityService.getById(activityIdNow);
		if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
			if (opActivity.getFormalStart() != null) {
				// 校验 当前时间是否在白名单可操作性时间区间内
				LocalDateTime now = LocalDateTime.now();
				LocalDateTime minusMinutes = opActivity.getFormalStart().minusMinutes(controlWhiteMinutes);
				if (now.isAfter(minusMinutes) && now.isBefore(opActivity.getFormalEnd())) {
					jsonResult.setCode(GlobalConstants.E_CODE);
					jsonResult.setMsg("操作失败,活动开始前" + controlWhiteMinutes + "分钟或活动开盘期间信息不可操作！");
					return jsonResult;
				}
			}
		}
		// 解析文件
		List<UserResourceImportVO> userResourceImportVOList = null;
		try {
			userResourceImportVOList = ExcelUtilsTest.importExcel(file, UserResourceImportVO.class);
		} catch (IOException e) {
			e.printStackTrace();
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("Excel导入失败!");
			return jsonResult;
		}
		// 处理数据
		try {
			// 校验
			if (CollectionUtils.isNotEmpty(userResourceImportVOList)) {
				jsonResult = iOpHousingResourcesService.saveUserResourceImportData(userResourceImportVOList, activityIdNow);
			}
		} catch (Exception e){
			e.printStackTrace();
			jsonResult.setCode(GlobalConstants.E_CODE);
			jsonResult.setMsg("Excel导入失败,请检查模板");
			return jsonResult;
		}
		return jsonResult;
	}

	/**
	 * 大屏获取房源 （魔改）
	 * @param activityId
	 * @return
	 */
	@ApiOperation(value = "大屏获取房源 （魔改）", notes = "大屏获取房源 （魔改）")
    @RequestMapping(value = "/getScreenList", method = RequestMethod.GET)
    public JSONResult getScreenList(@RequestParam(name = "activityIdNow", required = false) String activityId) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(iOpHousingResourcesService.getScreenList(activityId));
        return jsonResult;
    }
    /**
     * 大屏获取房源
     *
     * @return
     */
    @ApiOperation(value = "大屏获取房源", notes = "大屏获取房源")
    @RequestMapping(value = "/opHousingResourcesList", method = {RequestMethod.POST,RequestMethod.GET})
    public JSONResult opHousingResourcesList(
                                         @RequestParam(name = "activityIdNow", required = false) String activityId
                                        ) {
        List<OpHousingResources> houseList = iOpHousingResourcesService.getOpHousingResourcesList(activityId);
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(houseList);
        return jsonResult;
    }
    /**
     * 大屏获取基本数据
     *
     * @return
     */
    @ApiOperation(value = "大屏获取基本数据", notes = "大屏获取基本数据")
    @RequestMapping(value = "/getOpHousingBaseData", method = {RequestMethod.POST,RequestMethod.GET})
    public JSONResult getOpHousingBaseData(
                                         @RequestParam(name = "activityIdNow", required = false) String activityId
                                        ) {
		OpActivity opActivity =  iOpActivityService.getById(activityId);
		HashMap<String, Object> resultMap = iOpHousingResourcesService.getOpHousingBaseData(activityId, DateUtils.regular(opActivity, LocalDateTime.now()));
    	resultMap.put("opActivity", opActivity);
    	int todayLoginUserCount = (int)(resultMap.get("todayLoginUserCount") == null ? 0: resultMap.get("todayLoginUserCount"))+(opActivity.getJoinNumber() == null ? 0 : opActivity.getJoinNumber());
    	resultMap.put("todayLoginUserCount", todayLoginUserCount);
    	resultMap.put("sysDate", new Date().getTime());
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(resultMap);
        return jsonResult;
    }



	//推送数据接口
	@ApiOperation(value = "webSocketTest", notes = "webSocketTest")
	@GetMapping("/socket/push")
	public JSONResult pushToWeb(@RequestParam("cid") String cid) {
		JSONResult<Object> jsonResult = new JSONResult<>();
		OpActivity opActivity =  iOpActivityService.getById(cid);
		HashMap<String, Object> resultMap = iOpHousingResourcesService.getOpHousingBaseData(cid, DateUtils.regular(opActivity, LocalDateTime.now()));
		resultMap.put("opActivity", opActivity);
		int todayLoginUserCount = (int)(resultMap.get("todayLoginUserCount") == null ? 0: resultMap.get("todayLoginUserCount"))+(opActivity.getJoinNumber() == null ? 0 : opActivity.getJoinNumber());
		resultMap.put("todayLoginUserCount", todayLoginUserCount);
		resultMap.put("sysDate", new Date().getTime());
		jsonResult.setCode(GlobalConstants.S_CODE);
		jsonResult.setMsg("SUCCESS");
		jsonResult.setData(resultMap);
		try {
			WebSocketServer.sendInfo(JSON.toJSONString(jsonResult),cid);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return jsonResult;
	}

	@Autowired
	OrgInfoTask orgInfoTask;

	@Autowired
	UserInfoTask userInfoTask;

	//推送数据接口
	@ApiOperation(value = "pullOrg", notes = "pullOrg")
	@GetMapping("/pullOrg")
	public JSONResult pullOrg() {
		orgInfoTask.pullOrgInfo();
		return new JSONResult<>();
	}

	//推送数据接口
	@ApiOperation(value = "pullUser", notes = "pullUser")
	@GetMapping("/pullUser")
	public JSONResult pullUser() {
		userInfoTask.pullUserInfo();
		return new JSONResult<>();
	}

	// 推送数据接口
	@ApiOperation(value = "pushWebsocket", notes = "pushWebsocket")
	@GetMapping("/pushWebsocket")
	public JSONResult pushWebsocket(@RequestParam("activityId") String activityId, @RequestParam(value = "num", required = false) Integer num) {
		if (null == num) {
			iOpHousingResourcesService.sendWebSocket(activityId);
			return new JSONResult(500, "单次写入成功！");
		}
		for (int i = 0; i < num; i++) {
			iOpHousingResourcesService.sendWebSocket(activityId);
		}
		return new JSONResult(500, "循环写入成功！次数为:" + num);
	}
}
