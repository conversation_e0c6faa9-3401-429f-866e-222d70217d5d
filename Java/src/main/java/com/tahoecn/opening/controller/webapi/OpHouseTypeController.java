package com.tahoecn.opening.controller.webapi;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.ApiResult;
import com.tahoecn.opening.common.utils.LocalDateTimeUtils;
import com.tahoecn.opening.common.utils.ThreadLocalUtils;
import com.tahoecn.opening.common.utils.TimeControlUtil;
import com.tahoecn.opening.controller.TahoeBaseController;
import com.tahoecn.opening.converter.ShareinterJson2HttpMessageConverter;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpHouseType;
import com.tahoecn.opening.model.interfaceBean.HouseTypeData;
import com.tahoecn.opening.model.interfaceBean.HouseTypeRootBean;
import com.tahoecn.opening.service.IOpActivityService;
import com.tahoecn.opening.service.IOpHouseTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <p>
 * 户型 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@RestController
@RequestMapping("/webapi/opHouseType")
@Api(tags = "戶型接口", value = "戶型接口")
public class OpHouseTypeController extends TahoeBaseController {
    private static final Logger logger = LoggerFactory.getLogger(OpHouseTypeController.class);

//同步功能未测试

    @Autowired
    private IOpHouseTypeService iOpHouseTypeService;

    @Autowired
    private IOpActivityService iOpActivityService;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${const_HOST}")
    private String ConstHOST;

    @Value("${control_time}")
    private Double controTime;

    @Value("${tahoe.application.physicalPath}")
    private String webPath;

    @Value("${system_HOST}")
    private String system_HOST;


    /**
     * 分页获取存在房型列表
     *
     * @param pageNum
     * @param pageSize
     * @param opHouseType
     * @return
     */
    @ApiOperation(value = "户型列表", notes = "分页获取户型列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "pageNum", value = "当前页数", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", dataType = "int")})
    @RequestMapping(value = "/opHouseTypePageList", method = {RequestMethod.GET})
    public JSONResult selectIopHouseType(@RequestParam(defaultValue = "1") int pageNum,
                                         @RequestParam(defaultValue = "10") int pageSize,
                                         @RequestParam(name = "activityIdNow") String activityIdNow,
                                         OpHouseType opHouseType) {
        IPage<OpHouseType> page = new Page<>(pageNum, pageSize);
        QueryWrapper<OpHouseType> wrapper = new QueryWrapper<>();

        wrapper.lambda().eq(OpHouseType::getYn, GlobalConstants.Y);
        //添加项目id查询项
        wrapper.lambda().eq(OpHouseType::getActivityId, activityIdNow);
        if (null != opHouseType.getHouseArea()) {
            wrapper.lambda().eq(OpHouseType::getHouseArea, opHouseType.getHouseArea());
        }
        if (StringUtils.isNotBlank(opHouseType.getHouseType())) {
            wrapper.lambda().like(OpHouseType::getHouseType, opHouseType.getHouseType());
        }
        if (StringUtils.isNotBlank(opHouseType.getHouseName())) {
            wrapper.lambda().like(OpHouseType::getHouseName, opHouseType.getHouseName());
        }
        wrapper.lambda().like(StringUtils.isNotBlank(opHouseType.getHouseDescribe()), OpHouseType::getHouseDescribe,
                opHouseType.getHouseDescribe());
        IPage<OpHouseType> iPage = iOpHouseTypeService.page(page, wrapper);
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(iPage);
        return jsonResult;
    }


    /**
     * 编辑页面展示选择户型
     *
     * @param opHouseType
     * @return
     */
    @ApiOperation(value = "编辑户型展示", notes = "编辑单个户型展示")
    @RequestMapping(value = "/updateIopHouseTypeShow", method = {RequestMethod.GET})
    public JSONResult updateIopHouseTypeShow(OpHouseType opHouseType) {
        QueryWrapper<OpHouseType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ID", opHouseType.getId());
        OpHouseType one = iOpHouseTypeService.getOne(queryWrapper);
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(one);
        return jsonResult;
    }


    /**
     * 删除选择的一个或多个户型
     *
     * @param idList
     * @return
     */
    @ApiOperation(value = "删除户型", notes = "批量/单个删除户型")
    @RequestMapping(value = "/deleteOpHouseType", method = {RequestMethod.POST})
    public JSONResult deleteIopHouseType(@RequestBody List<Integer> idList) {
        List<OpHouseType> opHouseTypeList = new ArrayList<>();
        JSONResult<Object> jsonResult = new JSONResult<>();
        OpHouseType byId = iOpHouseTypeService.getById(idList.get(0));
        //判断活动时间区域 是否可修改
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId, byId.getActivityId());
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            if (opActivity.getFormalStart() != null) {
                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
                    jsonResult.setCode(GlobalConstants.E_CODE);
                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
                    return jsonResult;
                }
            }
        }

        for (Integer anIdList : idList) {
            OpHouseType opHouseType = new OpHouseType();
            opHouseType.setId(anIdList);
            opHouseType.setYn(GlobalConstants.N);
            opHouseType.setLastUpdateBy(ThreadLocalUtils.getUserName());
            opHouseType.setLastUpdateDate(LocalDateTime.now());
            opHouseTypeList.add(opHouseType);
        }
        boolean flag = iOpHouseTypeService.updateBatchById(opHouseTypeList);
        if (flag) {
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
            return jsonResult;
        } else {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("删除户型失败!");
            return jsonResult;
        }

    }


    /**
     * 同步户型
     *
     * @param activityId
     * @return
     */

    @ApiOperation(value = "同步户型", notes = "同步户型")
    @RequestMapping(value = "/updataAll", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONResult updateAll(String activityId) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        HttpHeaders headers = new HttpHeaders();
        OpActivity one = iOpActivityService.getById(activityId);
//        if (GlobalConstants.STATUS_R.equals(one.getStatusCode())) {
//            if (one.getFormalStart() != null) {
//                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(one.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(one.getFormalEnd()), controTime)) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                    return jsonResult;
//                }
//            }
//        }
        restTemplate.getMessageConverters().add(new ShareinterJson2HttpMessageConverter());

        HttpEntity<String> requestEntity = new HttpEntity<String>("{\"_datatype\":\"text\",\"_param\":{\"ProjectID\":\"" + one.getProjectId() + "\"}}", headers);

        //需要try
        ResponseEntity<HouseTypeRootBean> quote = restTemplate.postForEntity(ConstHOST + "/FrameWeb/FrameService/Api.ashx?option=func&funcid=mShareOnlineHouseList_Select", requestEntity, HouseTypeRootBean.class);

        List<HouseTypeData> houseTypeDataList = quote.getBody().getData();
        if (houseTypeDataList.size() == 0) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("未获取到户型");
            return jsonResult;
        }
        List<OpHouseType> opHouseTypeList = new ArrayList<>();
        for (HouseTypeData houseTypeData : houseTypeDataList) {
            //创建人时间 可用设置
            OpHouseType opHouseType = new OpHouseType();
            opHouseType.setYn(GlobalConstants.Y);
            opHouseType.setCreatedBy("interface");
            opHouseType.setCreationDate(LocalDateTime.now());
            opHouseType.setProjectId(one.getProjectId());
            opHouseType.setActivityId(activityId);
            opHouseType.setSyncId(houseTypeData.getId());
            opHouseType.setHouseName(houseTypeData.getName());
            opHouseType.setHouseType(houseTypeData.getTypeName());
            opHouseType.setHouseCode(houseTypeData.getHouseCode());
            opHouseType.setHouseDescribe(houseTypeData.getImgDesc());
//            opHouseType.setHouseArea(houseTypeData.getBuildArea());
            // 校验 户型图片是否配置了多个，如果配置了多个，只取第一个
            if (StringUtils.isNotBlank(houseTypeData.getImgUrl())) {
                String[] split = houseTypeData.getImgUrl().split(",");
                opHouseType.setHouseImg(split [0]);
            }
            opHouseTypeList.add(opHouseType);
        }
        QueryWrapper<OpHouseType> wrapper = new QueryWrapper<>();
        wrapper.eq("activity_id", activityId);
        boolean remove = iOpHouseTypeService.remove(wrapper);
//        //获取协议失败,服务器Nginx未配置 应为https  现在为http
//        String basePath =request.getHeader("referer");
////        String basePath ="https://"+request.getServerName();
//        String path=basePath+"opening/uploadfiles/";
        for (OpHouseType opHouseType : opHouseTypeList) {
            //把图片上传到服务器,并把服务器上地址存入对象里 之后保存
            opHouseType.setHouseImg(iOpHouseTypeService.upLoadImg(opHouseType, webPath, system_HOST));
        }
        boolean saveBatch = iOpHouseTypeService.saveBatch(opHouseTypeList);

        if (remove && saveBatch) {

            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
            jsonResult.setData(opHouseTypeList);
            return jsonResult;
        }
        //未判断删除和保存失败情况
        jsonResult.setCode(GlobalConstants.E_CODE);
        jsonResult.setMsg("同步失败!");
        jsonResult.setData(opHouseTypeList);
        return jsonResult;
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:24 2024/6/13
     * @param opHouseType
     * @return com.tahoecn.core.json.JSONResult
     * @description // TODO 
     **/
    @ApiOperation(value = "新增修改户型", notes = "新增修改户型")
    @RequestMapping(value = "/saveOrUpdateHouseType", method = {RequestMethod.POST})
    public JSONResult saveOrUpdateHouseType(@RequestBody OpHouseType opHouseType) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.E_CODE);
        // 活动id必传
        if (StringUtils.isBlank(opHouseType.getActivityId())) {
            jsonResult.setMsg("活动id不能为空！");
            return jsonResult;
        }
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId, opHouseType.getActivityId());
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            if (opActivity.getFormalStart() != null) {
                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
                    return jsonResult;
                }
            }
        }
        // 户型名称必填
        if (StringUtils.isBlank(opHouseType.getHouseName())) {
            jsonResult.setMsg("户型名称不能为空！");
            return jsonResult;
        }
        opHouseType.setHouseType(opHouseType.getHouseName());
        // 根据该户型名称查询数据
        QueryWrapper<OpHouseType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(OpHouseType::getActivityId, opHouseType.getActivityId())
                .eq(OpHouseType::getHouseName, opHouseType.getHouseName())
                .eq(OpHouseType::getYn, GlobalConstants.Y);
        List<OpHouseType> dataList = iOpHouseTypeService.list(queryWrapper);
        // 校验新增还是修改
        if (null == opHouseType.getId()) {
            // 校验是否已存在
            if (CollectionUtils.isNotEmpty(dataList)) {
                jsonResult.setMsg("户型名称已存在！");
                return jsonResult;
            }
            // 赋值基础数据
            opHouseType.setYn(GlobalConstants.Y);
            opHouseType.setCreatedBy(ThreadLocalUtils.getUserName());
            opHouseType.setCreationDate(LocalDateTime.now());
            iOpHouseTypeService.save(opHouseType);
        } else {
            // 校验是否已存在
            if (CollectionUtils.isNotEmpty(dataList) && dataList.get(0).getId().intValue() != opHouseType.getId().intValue()) {
                jsonResult.setMsg("户型名称已存在！");
                return jsonResult;
            }
            // 赋值基础数据
            opHouseType.setLastUpdateBy(ThreadLocalUtils.getUserName());
            opHouseType.setLastUpdateDate(LocalDateTime.now());
            iOpHouseTypeService.updateById(opHouseType);
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("操作成功！");
        return jsonResult;
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 17:51 2022/6/22
     * @param file          单文件
     * @return com.tyzq.website.common.vo.ApiResult
     * @description // TODO 单文件上传
     **/
    @ApiOperation(value = "单文件上传", httpMethod = "POST", notes = "单文件上传")
    @PostMapping(value = "/uploadFile")
    public ApiResult uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            // 获取源文件名
            String originalFilename =  file.getOriginalFilename();
            // 获取 文件扩展名
            String extension = "";
            if (StringUtils.isNotBlank(originalFilename)) {
                int index = originalFilename.lastIndexOf(".");
                if (index > 0) {
                    extension = originalFilename.substring(index);
                }
            }
            // 获取当前年月
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            // 拼接物理文件夹路径
            String monthPath = webPath + "/" + year+ "/" + month;
            // 校验文件夹是否存在，不存在则创建
            File dir = new File(monthPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            //获取一个UUID来作为存入服务器中的文件的名字
            String fileUuid = UUID.randomUUID().toString().replace("-", "").toUpperCase();
            String newName = fileUuid + extension;
            // 将文件转存到指定文件夹
            file.transferTo(new File(dir.getAbsolutePath(), newName));
            System.out.println("newName = " + system_HOST + year + "/" + month + "/" + newName);
            return ApiResult.getSuccessApiResponse(system_HOST + year + "/" + month + "/" + newName);
        } catch (Exception e) {
            e.printStackTrace();
            return ApiResult.getFailedApiResponse();
        }
    }
}
