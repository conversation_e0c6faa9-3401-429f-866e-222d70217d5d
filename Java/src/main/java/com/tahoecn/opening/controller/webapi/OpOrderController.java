package com.tahoecn.opening.controller.webapi;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sun.org.apache.bcel.internal.generic.NEW;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.common.utils.ExcelUtilsTest;
import com.tahoecn.opening.common.utils.FmtMicrometer;
import com.tahoecn.opening.common.utils.LocalDateTimeUtils;
import com.tahoecn.opening.common.utils.TimeControlUtil;
import com.tahoecn.opening.controller.TahoeBaseController;
import com.tahoecn.opening.converter.ShareinterJson2HttpMessageConverter;
import com.tahoecn.opening.model.*;
import com.tahoecn.opening.model.interfaceBean.HouseTypeData;
import com.tahoecn.opening.model.interfaceBean.HouseTypeRootBean;
import com.tahoecn.opening.model.vo.*;
import com.tahoecn.opening.service.IOpActivityService;
import com.tahoecn.opening.service.IOpHousingResourcesService;
import com.tahoecn.opening.service.IOpOrderService;
import com.tahoecn.opening.service.IOpUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 订单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
@RestController
@RequestMapping("/webapi/opOrder")
@Api(tags = "销售管理接口", value = "销售管理接口")
public class OpOrderController extends TahoeBaseController {

    @Autowired
    private IOpHousingResourcesService iOpHousingResourcesService;

    @Autowired
    private IOpOrderService iOpOrderService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IOpActivityService iOpActivityService;

    @Autowired
    private  IOpUserService iOpUserService;

    @Value("${const_HOST}")
    private String ConstHOST;

    @Value("${control_time}")
    private Double controTime;

    /**
     * 房间号下拉列表
     * @param activityIdNow
     * @param buildingName
     * @param unitName
     * @return
     */
    @ApiOperation(value = "房间号", notes = "获取房间号查询条件下拉列表")
    @RequestMapping(value = "/roomNum", method = {RequestMethod.GET})
    public JSONResult roomNum(String activityIdNow,
                              String buildingName,
                              String unitName) {
        QueryWrapper<OpHousingResources> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OpHousingResources::getYn, GlobalConstants.Y);
        wrapper.lambda().eq(OpHousingResources::getBuildingName, buildingName);
        wrapper.lambda().eq(OpHousingResources::getUnitName, unitName);
        wrapper.lambda().eq(OpHousingResources::getActivityId, activityIdNow);
        wrapper.select("activity_id", "building_name", "unit_name", "room_num");
        wrapper.groupBy("activity_id", "building_name", "unit_name", "room_num");
        List<OpHousingResources> opHousingResources = iOpHousingResourcesService.list(wrapper);
        JSONResult jsonResult = new JSONResult();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(opHousingResources);
        return jsonResult;
    }


    /**
     * 订单列表分页数据获取
     * @param pageNum
     * @param pageSize
     * @param activityIdNow
     * @param buildingName
     * @param unitName
     * @param roomNum
     * @param opUser
     * @return
     */

    @ApiOperation(value = "订单列表", notes = "分页获取订单列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "pageNum", value = "当前页数", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", dataType = "int")})
    @RequestMapping(value = "/opOrderPageList", method = {RequestMethod.GET})
    public JSONResult selectIopHouseType(@RequestParam(defaultValue = "1") int pageNum,
                                         @RequestParam(defaultValue = "10") int pageSize,
                                         @RequestParam(name = "activityIdNow") String activityIdNow,
                                         @RequestParam(name = "buildingName",required = false) String buildingName,
                                         @RequestParam(name = "unitName",required = false) String unitName,
                                         @RequestParam(name = "roomNum",required = false) String roomNum,
                                         @RequestParam(name = "isRegular",required = true) String isRegular,
                                         @RequestParam(name = "isAdmin",required = true) boolean isAdmin,
                                         OpUser opUser) {

        Page page=new Page(pageNum,pageSize);
        IPage<OrderInfoVO> order = iOpOrderService.getOrder(page, opUser, activityIdNow, buildingName, unitName, roomNum,isRegular,isAdmin);
        List<OrderInfoVO> records = order.getRecords();
        for (OrderInfoVO record : records) {
            if (record.getIsBeforehand()!=null){
                record.setIsBeforehand("是");
            }else {
            record.setIsBeforehand("否");
            }
            if ("ExcelImport".equals(record.getCreatedBy())){
                record.setCreatedBy("销售");
            }else {
                record.setCreatedBy("添加");
            }
            // 手机号前三后四
            if (StringUtils.isNotBlank(record.getTel())) {
                record.setTel(record.getTel().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
            }
        }
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(order);
        return jsonResult;
    }


    @ApiOperation(value = "订单导出", notes = "订单导出")
    @RequestMapping(value = "/downLoadOrderExcel", method = {RequestMethod.GET})
    public JSONResult downLoadOrderExcel(HttpServletResponse response,
                                         @RequestParam(name = "activityIdNow") String activityIdNow,
                                         @RequestParam(name = "buildingName",required = false) String buildingName,
                                         @RequestParam(name = "unitName",required = false) String unitName,
                                         @RequestParam(name = "roomNum",required = false) String roomNum,
                                         @RequestParam(name = "isRegular",required = true) String isRegular,
                                         @RequestParam(name = "isAdmin",required = true) boolean isAdmin,
                                         OpUser opUser) {
        List<OrderInfoVO> records = iOpOrderService.getOrderEx( opUser, activityIdNow, buildingName, unitName, roomNum,isRegular,isAdmin);
        JSONResult<Object> jsonResult = new JSONResult<>();
        for (OrderInfoVO record : records) {
            if (record.getIsBeforehand()!=null){
                record.setIsBeforehand("是");
            }else {
                record.setIsBeforehand("否");
            }
            if ("ExcelImport".equals(record.getCreatedBy())){
                record.setCreatedBy("销售");
            }else {
                record.setCreatedBy("添加");
            }
        }
        try {
            ExcelUtilsTest.exportExcel(records, null, "订单列表", OrderInfoVO.class, "订单列表导出信息", response);
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("订单Excel导出失败!");
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }
    @ApiOperation(value = "订单模板下载", notes = "订单模板下载")
    @RequestMapping(value = "/moudlDownload", method = {RequestMethod.GET})
    public JSONResult moudlDownload(HttpServletResponse response) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        List<OrderExcelImportVO> orderImportVO=new ArrayList<>();
        try {
            ExcelUtilsTest.exportExcel(orderImportVO, null, "订单导入模板", OrderExcelImportVO.class, "订单导入模板", response);
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("订单导入模板导出错误");
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }

    @ApiOperation(value = "订单导入", notes = "订单导入")
    @RequestMapping(value = "/importOrder", method = {RequestMethod.POST})
    public JSONResult importOrder(MultipartFile file, String activityId) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId, activityId);
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
            if (opActivity.getFormalStart() != null) {
                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
                    jsonResult.setCode(GlobalConstants.E_CODE);
                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
                    return jsonResult;
                }
            }
        }
            List<OrderExcelImportVO> orderExcelImportVOS;
        try {
             orderExcelImportVOS = ExcelUtilsTest.importExcel(file, OrderExcelImportVO.class);
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("订单导入错误,请检查模板");
            return jsonResult;
        }

        try {
            jsonResult= iOpOrderService.placeExcelOrder(orderExcelImportVOS, activityId);
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("订单导入错误!");
            return jsonResult;
        }

        return jsonResult;

    }


    @ApiOperation(value = "释放订单", notes = "释放订单")
    @RequestMapping(value = "/deleteOrderY", method = {RequestMethod.POST})
    public JSONResult deleteOrderY(@RequestBody List<String> idList) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        OpOrder order = iOpOrderService.getById(idList.get(0));
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId, order.getActivityId());
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
//        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
//            if (opActivity.getFormalStart() != null) {
//                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                    return jsonResult;
//                }
//            }
//        }

        List<OpOrder> opOrders = iOpOrderService.selectBatchIds(idList);
        String msg = iOpOrderService.deleteOrder(idList,"y");


        if ("SUCCESS".equals(msg)){
        	for(OpOrder opOrder : opOrders){
        		iOpOrderService.executeBackOrderLua(opOrder);
        	}
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.E_CODE);
        jsonResult.setMsg(msg);
        return jsonResult;

    }

    @ApiOperation(value = "释放订单", notes = "释放订单")
    @RequestMapping(value = "/deleteOrderN", method = {RequestMethod.POST})
    public JSONResult deleteOrderN(@RequestBody List<String> idList) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        OpOrder order = iOpOrderService.getById(idList.get(0));
        QueryWrapper<OpActivity> activityQueryWrapper = new QueryWrapper<>();
        activityQueryWrapper.lambda().eq(OpActivity::getId, order.getActivityId());
        OpActivity opActivity = iOpActivityService.getOne(activityQueryWrapper);
//        if (GlobalConstants.STATUS_R.equals(opActivity.getStatusCode())) {
//            if (opActivity.getFormalStart() != null) {
//                if (!TimeControlUtil.judgmentDate(LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalStart()), LocalDateTimeUtils.localDateTimeToDate(opActivity.getFormalEnd()), controTime)) {
//                    jsonResult.setCode(GlobalConstants.E_CODE);
//                    jsonResult.setMsg("操作失败,活动开始前三分钟或活动开盘期间信息不可编辑！");
//                    return jsonResult;
//                }
//            }
//        }
        String msg = iOpOrderService.deleteOrder(idList,"n");
        if ("SUCCESS".equals(msg)){
            List<OpOrder> opOrders = iOpOrderService.selectBatchIds(idList);
            for(OpOrder opOrder : opOrders){
                iOpOrderService.executeBackOrderLua(opOrder);
            }
            jsonResult.setCode(GlobalConstants.S_CODE);
            jsonResult.setMsg("SUCCESS");
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.E_CODE);
        jsonResult.setMsg(msg);
        return jsonResult;

    }







    @ApiOperation(value = "销售总览", notes = "销售总览")
    @RequestMapping(value = "/salesOverview", method = {RequestMethod.GET})
    public JSONResult salesOverview(String activityId,String isRegular) {
        SalesOverview salesOverview=iOpOrderService.getOverview(activityId,isRegular);
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(salesOverview);
        return jsonResult;
    }

    /**
     * 销售户型列表查询
     * @param pageNum
     * @param pageSize
     * @param activityIdNow
     * @return
     */
    @ApiOperation(value = "销售户型列表", notes = "销售户型列表")
    @RequestMapping(value = "/salesHouseTypeList", method = {RequestMethod.GET})
    public JSONResult salesHouseTypeList(@RequestParam(defaultValue = "1") int pageNum,
                                         @RequestParam(defaultValue = "10") int pageSize,
                                         String activityIdNow,
                                         String isRegular) {
        Page<HouseTypeList> page=new Page<>(pageNum,pageSize);
        IPage<HouseTypeList> houseTypeList=iOpOrderService.getHouseTypeList(page,activityIdNow,isRegular);
        List<HouseTypeList> records = houseTypeList.getRecords();
        for (HouseTypeList record : records) {
            record.setSalesVolume(FmtMicrometer.fmtMicrometer(record.getSalesVolume()));
            record.setSalesArea(FmtMicrometer.fmtMicrometer(record.getSalesArea()));
            record.setSalesNum(FmtMicrometer.fmtMicrometer(record.getSalesNum()));
        }
        houseTypeList.setRecords(records);
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(houseTypeList);
        return jsonResult;
    }

    /**
     * 户型列表导出
     *
     * @param response
     * @return
     */
    @ApiOperation(value = "户型列表导出", notes = "户型列表导出")
    @RequestMapping(value = "/downloadHouseTypeList", method = {RequestMethod.GET})
    public JSONResult downloadHouseTypeList(HttpServletResponse response,String activityIdNow,
                                            String isRegular) {
        List<HouseTypeList> list=iOpOrderService.getHouseTypeListEx(activityIdNow,isRegular);
        for (HouseTypeList record : list) {
            record.setSalesVolume(FmtMicrometer.fmtMicrometer(record.getSalesVolume()));
            record.setSalesArea(FmtMicrometer.fmtMicrometer(record.getSalesArea()));
            record.setSalesNum(FmtMicrometer.fmtMicrometer(record.getSalesNum()));
        }

        try {
            ExcelUtilsTest.exportExcel(list, null, "户型列表", HouseTypeList.class, "户型列表导出信息", response);
        } catch (IOException e) {
            e.printStackTrace();
        }
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }

    @ApiOperation(value = "置业顾问销售数据", notes = "置业顾问销售数据")
    @RequestMapping(value = "/saleData", method = {RequestMethod.GET})
    public JSONResult saleData(@RequestParam(defaultValue = "1") int pageNum,
                                 @RequestParam(defaultValue = "10") int pageSize,
                                 String roleName,
                                 String activityIdNow,
                                 String isRegular) {
        Page<SaleDataVO> page=new Page<>(pageNum,pageSize);
       IPage<SaleDataVO> iPage=iOpOrderService.getSaleData(page,roleName,activityIdNow,isRegular);
        List<SaleDataVO> records = iPage.getRecords();
        for (SaleDataVO record : records) {
            String s = FmtMicrometer.fmtMicrometer(record.getSalesVolume());
            record.setSalesVolume(s);
        }
        iPage.setRecords(records);
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(iPage);
        return jsonResult;
    }


    @ApiOperation(value = "置业顾问销售数据导出", notes = "置业顾问销售数据导出")
    @RequestMapping(value = "/downloadSaleData", method = {RequestMethod.GET})
    public JSONResult downloadSaleData(HttpServletResponse response,
                                       String roleName,
                                       String activityIdNow,
                                       String isRegular) {
//        IPage<SaleDataVO> iPage=iOpOrderService.getSaleData(new Page(),roleName,activityIdNow,isRegular);
        List<SaleDataVO> list=iOpOrderService.getSaleDataEx(roleName,activityIdNow,isRegular);
        JSONResult<Object> jsonResult = new JSONResult<>();
        for (SaleDataVO record : list) {
            String s = FmtMicrometer.fmtMicrometer(record.getSalesVolume());
            record.setSalesVolume(s);
        }
        try {
            ExcelUtilsTest.exportExcel(list, null, "置业顾问销售数据表", SaleDataVO.class, "置业顾问销售数据导出信息", response);
        } catch (IOException e) {
            e.printStackTrace();
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("置业顾问销售数据表导出错误");
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("导出成功!");
        return jsonResult;
    }
/*
    @ApiOperation(value = "销售订单导出", notes = "销售订单导出")
    @RequestMapping(value = "/SalesOrder", method = {RequestMethod.GET})
    public JSONResult SalesOrder(HttpServletResponse response, String activityId) {
         List<SalesOrderVO> list=iOpOrderService.getSalesOrder(activityId);

        JSONResult<Object> jsonResult = new JSONResult<>();
        try {
            ExcelUtilsTest.exportExcel(list, null, "销售订单", SalesOrderVO.class, "销售订单信息", response);
        } catch (IOException e) {
            jsonResult.setCode(GlobalConstants.E_CODE);
            jsonResult.setMsg("销售订单Excel导出错误");
            return jsonResult;
        }
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        return jsonResult;
    }*/

    /**
     * 接口 test不调用
     * @param followUpContent
     * @param opUser
     * @return
     */
    @ApiOperation(value = "接口", notes = "接口")
    @RequestMapping(value = "/returnData", method = {RequestMethod.POST,RequestMethod.GET})
    public JSONResult returnData(String followUpContent,OpUser opUser) {
        JSONResult<Object> jsonResult = new JSONResult<>();
        JSONResult jsonResult1 = iOpOrderService.returnData(followUpContent, opUser);
        jsonResult.setCode(GlobalConstants.S_CODE);
        return jsonResult1;
    }


}
