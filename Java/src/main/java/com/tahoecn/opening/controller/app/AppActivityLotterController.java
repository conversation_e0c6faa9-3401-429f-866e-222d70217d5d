package com.tahoecn.opening.controller.app;

import com.tahoecn.core.json.JSONResult;
import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.controller.TahoeBaseController;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.service.IOpActivityService;
import com.tahoecn.opening.service.IOpUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/app/activity")
@Api(tags = "活动摇号", value = "活动摇号控制器")
public class AppActivityLotterController extends TahoeBaseController {


    private static final Log log = LogFactory.get();

    @Autowired
    private IOpActivityService activityService;

    @Autowired
    private IOpUserService iOpUserService;

    /**
     * 个人摇号
     * @param activityIdNow
     * @return
     */
    @ApiOperation(value = "个人摇号", notes = "个人摇号")
    @GetMapping(value = "/personalLottery")
    public JSONResult personalLottery(@RequestParam Integer activityIdNow, Integer userId){
        return activityService.personalLottery(activityIdNow, userId);
    }

    /**
     * 查询摇号
     */
    @ApiOperation(value = "查询摇号", notes = "查询摇号")
    @GetMapping(value = "/queryLottery")
    public JSONResult queryLottery(@RequestParam Integer userId){
        OpUser byId = iOpUserService.getById(userId);
        JSONResult<Object> jsonResult = new JSONResult<>();
        jsonResult.setCode(GlobalConstants.S_CODE);
        jsonResult.setMsg("SUCCESS");
        jsonResult.setData(byId.getUserSort());
        return jsonResult;
    }

}
