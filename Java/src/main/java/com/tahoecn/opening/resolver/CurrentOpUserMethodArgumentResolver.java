package com.tahoecn.opening.resolver;

import java.util.Enumeration;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tahoecn.opening.common.annotation.CurrentOpUser;
import com.tahoecn.opening.common.constants.GlobalConstants;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.model.appDto.CurrentOpUserBean;
import com.tahoecn.opening.service.IOpUserService;

/**
 * 自定义参数解析器，基于小程序当前登录用户的相关信息解析
 * <AUTHOR>
 *
 */
@Component
public class CurrentOpUserMethodArgumentResolver implements HandlerMethodArgumentResolver {

	private static final Logger log = LoggerFactory.getLogger(CurrentOpUserMethodArgumentResolver.class);

	@Autowired
	private IOpUserService iOpUserService;
	/**
	 * 通过请求头部openid获取用户相关信息
	 */
	@Override
	public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
			NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
		HttpServletRequest request =   webRequest.getNativeRequest(HttpServletRequest.class);
		//1.小程序获取头部openid
		Enumeration<String> enumeration = request.getHeaderNames();
		while(enumeration.hasMoreElements()){
			String name = enumeration.nextElement();
			if(GlobalConstants.OPEN_ID.equals(name)){
				String value = request.getHeader(name);
				log.info("--------微信openid--------:"+value);
				//根据openId获取当前用户信息
				QueryWrapper<OpUser> queryWrapper = new QueryWrapper<OpUser>();
				queryWrapper.eq("open_id", value);
				OpUser opUser = iOpUserService.getOne(queryWrapper);
				if(opUser==null||opUser.getId()==null)
					return null;
				log.info("--------当前用户信息--------:"+opUser.toString());
				//封装参数集
				CurrentOpUserBean currentOpUserBean = new CurrentOpUserBean();
				currentOpUserBean.setOpUser(opUser);
				return currentOpUserBean;
			}
		}
		return null;
	}

	@Override
	public boolean supportsParameter(MethodParameter parameter) {
		//如果参数有CurrentOpUser注解则支持,且参数类型是CurrentOpUserBean，查询当前登录用户并返回
        if (parameter.getParameterType().isAssignableFrom(CurrentOpUserBean.class) 
        		&& parameter.hasParameterAnnotation(CurrentOpUser.class)) {
            return true;
        }
        return false;
	}

}
