package com.tahoecn.opening.resolver;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import com.tahoecn.opening.common.annotation.EncryptionParameters;
import com.tahoecn.opening.common.constants.AesConstants;
import com.tahoecn.opening.common.exception.AesException;
import com.tahoecn.opening.common.utils.AesUtils;

/**
 * 自定义参数解析器，基于前台AES方式加密的参数进行解密
 * <AUTHOR>
 *
 */
public class ProcessParametersMethodArgumentResolver implements HandlerMethodArgumentResolver {

	private static final Logger log = LoggerFactory.getLogger(ProcessParametersMethodArgumentResolver.class);

	/**
	 * 参数解析核心逻辑
	 */
	@Override
	public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
			NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
		HttpServletRequest request =   webRequest.getNativeRequest(HttpServletRequest.class);
		//获取参数集
		Map<String, String[]> parameterMap = request.getParameterMap();
		//获取AES加密参数
		String[] aesDataArr = parameterMap.get(AesConstants.AES_DATA);
		if(aesDataArr == null || aesDataArr.length == 0){
			return null;
		}
		//String encryptionParameter = aesDataArr[0];
		return aesDataArr[0];
		/*log.debug("[encryptionParameters]==========>加密数据：{}", encryptionParameter);
		//解密
        String decryptionParameter = null;
        try {
        	decryptionParameter = AesUtils.decrypt(encryptionParameter, AesConstants.AES_KEY);
            log.debug("[decryptionParameters]==========> 解密数据：{}", decryptionParameter);
        } catch (AesException e) {
            log.error("[AesException]", e);
            log.debug("[AesException]==========>", e);
        }
		return decryptionParameter;*/
	}

	@Override
	public boolean supportsParameter(MethodParameter parameter) {
		//如果参数有EncryptionParameters注解则支持,进行一系列参数解析操作并返回
        if (parameter.hasParameterAnnotation(EncryptionParameters.class)) {
            return true;
        }
        return false;
	}

}
