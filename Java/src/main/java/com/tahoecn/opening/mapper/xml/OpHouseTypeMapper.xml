<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.opening.mapper.OpHouseTypeMapper">

	<select id="getHouseTypeListByActivityId"  resultType="java.util.HashMap">
			SELECT
			  op_house_type.`id`,
			  op_house_type.`project_id` as projectId,
			  op_house_type.`house_name` as houseName,
			  op_house_type.`house_area` as houseArea,
			  op_house_type.`house_type` as houseType,
			  op_house_type.`house_img` as houseImg,
			  op_house_type.`house_describe` as houseDescribe,
			  op_house_type.`status`,
			  op_house_type.`yn`,
			  op_house_type.`creation_date` as creationDate,
			  op_house_type.`created_by` as createdBy,
			  op_house_type.`last_update_date` as lastUpdateDate,
			  op_house_type.`last_update_by` as lastUpdateBy,
			  op_house_type.`sync_id` as syncId,
			  op_house_type.`activity_id` as activityId,
				(
				SELECT
					count( * ) 
				FROM
					op_housing_resources 
				WHERE
					op_housing_resources.activity_id = op_house_type.activity_id 
					AND op_housing_resources.hourse_type = op_house_type.house_name
					AND op_housing_resources.activity_id = #{activityId} 
				) AS houseCount 
			FROM
				op_house_type 
			WHERE
				EXISTS (
				SELECT
					* 
				FROM
					op_housing_resources 
				WHERE
					op_housing_resources.activity_id = op_house_type.activity_id 
					AND op_housing_resources.hourse_type = op_house_type.house_name
					AND op_housing_resources.activity_id = #{activityId}
					
				)
				and op_house_type.yn = 'y'
			ORDER BY
				house_type;
	    </select>
	<select id="getHouseTypeAreaMinToMax" resultType="java.util.HashMap">
		SELECT
			min(op_house_type.house_area) as minArea,
			max(op_house_type.house_area) as maxArea
		FROM
			`op_house_type` 
		WHERE
			EXISTS (
			SELECT
				* 
			FROM
				op_housing_resources 
			WHERE
				op_housing_resources.activity_id = op_house_type.activity_id 
				AND op_housing_resources.hourse_type = op_house_type.house_name
			AND op_housing_resources.activity_id = #{activityId} 
			)
			and op_house_type.yn = 'y'
	    </select>
</mapper>
