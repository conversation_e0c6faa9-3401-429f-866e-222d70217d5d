package com.tahoecn.opening.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tahoecn.opening.model.OpProjectSale;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 销售系统项目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
public interface OpProjectSaleMapper extends BaseMapper<OpProjectSale> {

    List<OpProjectSale> projectList(@Param("areaId") String areaId, @Param("cityId") String cityId, @Param("projectId") String projectId, Page<OpProjectSale> page);
}
