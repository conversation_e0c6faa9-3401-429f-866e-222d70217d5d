<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.opening.mapper.OpUserMapper">

    <select id="getUserSale" resultType="com.tahoecn.opening.model.vo.SaleListVO">
		SELECT
		quan.id as id,
		quan.`name` AS saleName, quan.tel AS tel, quan.creation_date AS creationDate,
		IFNULL(zhiye.holdNum,0) AS holdUserNum,
		quan.role_name AS roleName
		FROM op_user quan
		LEFT JOIN
		(SELECT z.id AS zid,
		COUNT(u.id) AS holdNum
		FROM op_user u
		LEFT JOIN op_user z
		ON u.sale_id=z.cstguid
		AND z.activity_id=#{opUser.activityId}
		AND z.yn='y'
		AND z.role_name!='customer'
		WHERE u.activity_id=#{opUser.activityId}
		AND u.yn='y'
		AND u.role_name='customer'
		GROUP BY z.id ) zhiye
		ON quan.id= zhiye.zid
		<where> quan.activity_id=#{opUser.activityId}
			<if test="opUser.name != null and opUser.name != ''">
				AND quan.name LIKE CONCAT('%', #{opUser.name},'%')
			</if>
			<if test="opUser.tel != null and opUser.tel != ''">
				AND quan.tel LIKE CONCAT('%', #{opUser.tel},'%')
			</if>
		AND quan.yn='y'
		AND quan.role_name!='customer'
		</where>
		ORDER BY CONVERT(quan.`name` USING gbk)
	</select>

    <select id="getRoleName" resultType="string">
        SELECT role_name
        FROM op_user
        GROUP BY role_name
    </select>

	<!-- 			and op_user.tel = #{tel}-->
     <select id="getOpUserByProjectIdAndTelOrOpenId" resultType="java.util.HashMap">
        SELECT DISTINCT
			op_activity.id AS activityId,
			op_activity.project_address as projectAddress,
			op_activity.project_id AS projectId,
			op_activity.activity_name AS activityName,
			op_activity.simulation_start AS simulationStart,
			op_activity.simulation_end AS simulationEnd,
			op_activity.formal_start AS formalStart,
			op_activity.formal_end AS formalEnd,
			op_activity.is_id_val AS isIdVal,
			op_activity.is_message_val AS isMessageVal,
			( SELECT count( id ) FROM op_housing_resources WHERE op_housing_resources.activity_id = op_activity.id AND op_housing_resources.yn = 'y' ) AS houseCount,
			 op_activity.check_mode AS checkMode,
			 op_activity.check_minutes AS checkMinutes
		FROM
			op_user,
			op_activity 
		WHERE
			op_user.yn = 'y' 
			AND op_activity.yn = 'y'

			AND op_user.project_id = op_activity.project_id 
			AND op_activity.status_code IN ( 'running', 'close' )
		<if test="projectId != null and projectId != ''">
			AND op_activity.project_id  = #{projectId}
		</if>
			;
    </select>
     <select id="getActivityRunOrCloseList" resultType="java.util.HashMap">
        SELECT DISTINCT
			op_activity.id AS activityId,
			op_activity.project_address as projectAddress,
			op_activity.project_id AS projectId,
			op_activity.activity_name AS activityName,
			op_activity.simulation_start AS simulationStart,
			op_activity.simulation_end AS simulationEnd,
			op_activity.formal_start AS formalStart,
			op_activity.formal_end AS formalEnd
		FROM
			op_activity 
		WHERE
			op_activity.yn = 'y'
			AND op_activity.status_code IN ( 'running', 'close' )
		<if test="projectId != null and projectId != ''">
			AND op_activity.project_id  = #{projectId}
		</if>
			;
    </select>
     <select id="getOpUserByActivityIds" resultType="java.util.HashMap">
        SELECT DISTINCT
			op_activity.id AS activityId,
			op_activity.project_address as projectAddress,
			op_activity.project_id AS projectId,
			op_activity.activity_name AS activityName,
			op_activity.simulation_start AS simulationStart,
			op_activity.simulation_end AS simulationEnd,
			op_activity.formal_start AS formalStart,
			op_activity.formal_end AS formalEnd,
			op_activity.is_id_val AS isIdVal,
			op_activity.is_message_val AS isMessageVal,
			( SELECT count( id ) FROM op_housing_resources WHERE op_housing_resources.activity_id = op_activity.id AND op_housing_resources.yn = 'y' ) AS houseCount,
			 op_activity.check_mode AS checkMode,
			 op_activity.check_minutes AS checkMinutes
		FROM
			op_activity 
		WHERE
			op_activity.id in 
			<foreach item="item" index="index" collection="activityIds" open="(" separator="," close=")">  
		      #{item}  
		     </foreach> 
			AND op_activity.status_code IN ( 'running', 'close' )
			AND op_activity.yn = 'y'
			;
    </select>
     <select id="getOpUserListByProjectIdAndTel" resultType="com.tahoecn.opening.model.OpUser">
        SELECT DISTINCT
			op_user.* 
		FROM
			op_user,
			op_activity 
		WHERE
			op_user.yn = 'y' 
			AND op_activity.yn = 'y'
			and op_user.tel = #{tel}
			AND op_user.project_id = op_activity.project_id 
			AND op_activity.status_code IN ( 'running', 'close' )
		<if test="projectId != null and projectId != ''">
			AND op_activity.project_id  = #{projectId}
		</if>
			;
    </select>
     <select id="getOpUserListByActivityIdAndOpenId" resultType="com.tahoecn.opening.model.OpUser">
        SELECT DISTINCT
			op_user.* 
		FROM
			op_user 
		WHERE
			op_user.yn = 'y' 
			AND op_user.activity_id = #{activityId} 
			AND op_user.tel  = #{tel}
    </select>

	<select id="getUserList" resultType="com.tahoecn.opening.model.OpUser">
		SELECT id,name,id_card,sale_name,tel,select_count,booking_num,creation_date, user_sort, batch_name_str, house_str
		FROM op_user
		<where>activity_id = #{opUser.activityId}
		AND yn = 'y'
		AND role_name = 'customer'
			<if test="opUser.name != null and opUser.name != ''">
				AND name LIKE CONCAT('%',#{opUser.name},'%')
			</if>
			<if test="opUser.tel != null and opUser.tel != ''">
				AND tel LIKE CONCAT('%',#{opUser.tel},'%')
			</if>
			<if test="opUser.idCard != null and opUser.idCard != ''">
				AND  id_card LIKE CONCAT('%',#{opUser.idCard},'%')
			</if>
			<if test="opUser.selectCount != null and opUser.selectCount != ''">
				AND  select_count LIKE CONCAT('%',#{opUser.selectCount},'%')
			</if>
			<if test="opUser.saleName != null and opUser.saleName != ''">
				AND  sale_name LIKE CONCAT('%',#{opUser.saleName},'%')
			</if>
			<if test="opUser.bookingNum != null and opUser.bookingNum != ''">
				AND  booking_num LIKE CONCAT('%',#{opUser.bookingNum},'%')
			</if>
		</where>
		ORDER BY id asc
	</select>

	<select id="getBookingNum" resultType="string">
		SELECT DISTINCT IFNULL(booking_num,0) AS num
		FROM op_user WHERE activity_id=#{activityId} AND role_name='customer'
		GROUP BY booking_num
		order by num
	</select>
    <select id="getselecCount" resultType="string">
        SELECT DISTINCT IFNULL(select_count,0) AS num
        FROM op_user WHERE activity_id=#{activityId} AND role_name='customer'
        GROUP BY select_count
        order by num
    </select>
    <select id="getSaleName" resultType="com.tahoecn.opening.model.OpUser">
        SELECT
        DISTINCT cstguid as saleId, name as saleName
        FROM op_user
        WHERE activity_id=#{activityId}
        AND role_name='置业顾问'
		AND yn = 'y'
    </select>

    <!-- 根据用户id查询用户白名单房源列表 -->
    <select id="getUserHouseResourceListByUserId" resultType="com.tahoecn.opening.model.dto.UserHouseResourceDTO">
        SELECT
            uhr.id as id,
            uhr.user_id as userId,
            hr.id as houseResourceId,
            hr.house_name as hourseName
        FROM op_user_house_resource as uhr
        RIGHT JOIN op_housing_resources as hr on uhr.house_resource_id = hr.id
        where
            uhr.user_id = #{userId}
            and hr.yn = 'y'
            order by hr.building_name, hr.unit_name, current_floor, room_num desc
    </select>


	<select id="selectAllUserDtoByActivityId" resultType="com.tahoecn.opening.model.dto.UserDto">
		SELECT
			id,
			select_count as selectCount,
			user_sort as userSort
		FROM op_user
		WHERE activity_id = #{activityId}
		AND yn = 'y'
		ORDER BY user_sort asc
	</select>

	<!-- 将 数据库中 所有的sale_id是当前用户的saleName改值-->
    <update id="updateSaleNameById">
        update op_user set sale_name = #{name} where sale_id = #{cstguid} and yn = 'y'
    </update>

    <!-- 更改登录时间-->
    <update id="updateUserLoginDate">
        update op_user set open_id = "OPENID", last_login_date = null, first_login = 0 where activity_id = #{opUser.activityId} and tel = #{opUser.tel}
    </update>
	<update id="updateUserLoginDateByTel">
        update op_user set last_login_date = #{opUser.lastLoginDate} where activity_id = #{opUser.activityId} and tel = #{opUser.tel}
    </update>
</mapper>
