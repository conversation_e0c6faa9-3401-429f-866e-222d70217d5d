package com.tahoecn.opening.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tahoecn.opening.model.OpOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tahoecn.opening.model.dto.UserHouseResourceDTO;
import com.tahoecn.opening.model.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
public interface OpOrderMapper extends BaseMapper<OpOrder> {

    /**
     *
     * @param page
     * @param orderInfoVo
     * @return
     */
    IPage<OrderInfoVO> getOrder(Page page, @Param("orderInfoVo") OrderInfoVO orderInfoVo, @Param("isRegular") String isRegular, @Param("isAdmin") String isAdmin);
    List<OrderInfoVO> getOrder(@Param("orderInfoVo") OrderInfoVO orderInfoVo, @Param("isRegular") String isRegular, @Param("isAdmin") String isAdmin);

	List<HashMap<String, Object>> getMyOrderByactivityIdAndUser(@Param("activityId") String activityId, @Param("userId") String userId, @Param("isRegular") String isRegular);
    SalesOverview getOverview1(@Param("activityIdNow") String activityIdNow, @Param("isRegular") String isRegular);
    SalesOverview getOverview2(@Param("activityIdNow") String activityIdNow, @Param("isRegular") String isRegular);
    SalesOverview getOverview3(@Param("activityIdNow") String activityIdNow, @Param("isRegular") String isRegular);

    List<HashMap<String, Object>> getUserCanBuy(@Param("activityId") String activityId, @Param("isRegular") String isRegular);
    List<HashMap<String, Object>> getHouseCanBuy(@Param("activityId") String activityId);

    IPage<HouseTypeList> getHouseTypeList(Page page, @Param("activityIdNow") String activityIdNow, @Param("isRegular") String isRegular);
    List<HouseTypeList> getHouseTypeList(@Param("activityIdNow") String activityIdNow, @Param("isRegular") String isRegular);


    IPage<SaleDataVO> getSaleData(Page page, @Param("roleName") String roleName, @Param("activityIdNow") String activityIdNow, @Param("isRegular") String isRegular);
    List<SaleDataVO> getSaleData(@Param("roleName") String roleName, @Param("activityIdNow") String activityIdNow, @Param("isRegular") String isRegular);

	HashMap<String, Object> getUserFavoriteAndActivityFavorite(HashMap<String, Object> paramMap);


    List<HashMap<String, Object>> getAllHouseTypeNum(String activityIdNow);

    /**
     * 通过userid 查询此用户可购房数
     * @param userId
     * @return
     */
    Integer canBuyNum(String userId);

    void setBeforeOrderShow(String activityId);

    List<SalesOrderVO> getSalesOrder(String activityId);

    List<HashMap<String,Object>> getSalesUserNum(@Param("roleName") String roleName, @Param("activityIdNow") String activityIdNow, @Param("isRegular") String isRegular);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:45 2022/8/10
     * @param activityId    活动id
     * @return java.util.List<com.tahoecn.opening.model.dto.UserHouseResourceDTO>
     * @description // TODO 获取到该活动下所有的 白名单数据
     **/
    List<UserHouseResourceDTO> selectUserHourseResourceListByActivityId(@Param("activityId") String activityId);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:42 2023/10/23
     * @param voList 
     * @description // TODO 批量新增订单数据
     **/
    void insertList(@Param("dataList") List<OpOrder> voList);
}
