<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.opening.mapper.OpUserHouseResourceMapper">

    <!-- 批量入库 白名单集合 -->
    <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO op_user_house_resource(
            id,
            activity_id,
            user_id,
            house_resource_id,
            create_id,
            create_name,
            create_time
        )
        values
        <foreach collection="dataList" item="item" separator=",">
            (
                null, #{item.activityId},
                #{item.userId}, #{item.houseResourceId},
                #{item.createId}, #{item.createName},
                #{item.createTime}
            )
        </foreach>
    </insert>
</mapper>
