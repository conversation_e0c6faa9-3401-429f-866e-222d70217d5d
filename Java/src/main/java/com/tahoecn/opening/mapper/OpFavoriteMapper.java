package com.tahoecn.opening.mapper;

import com.tahoecn.opening.model.OpFavorite;

import java.util.HashMap;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 收藏 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
public interface OpFavoriteMapper extends BaseMapper<OpFavorite> {

	List<HashMap<String, Object>> getMyFavoriteByactivityIdAndUser(@Param("activityId") String activityId, @Param("userId") String userId, @Param("isRegular") String isRegular);

	List<HashMap<String, Object>> getFavoriteFailByactivityIdAndUser(@Param("activityId") String activityId, @Param("userId") String userId, @Param("isRegular") String isRegular);

}
