<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.opening.mapper.OpOrderMapper">

    <select id="getOrder" resultType="com.tahoecn.opening.model.vo.OrderInfoVO">
        SELECT
        a.id AS orderId,
        a.user_name AS name,
        b.cstguid AS cstguid,
        b.sale_id AS saleId,
        b.tel AS tel,
        b.id_card AS idCard,
        d.building_name AS buildingName,
        d.unit_name AS unitName,
        d.room_num AS roomNum,
        d.house_name AS houseName,
        a.creation_date AS creationDate,
        b.created_by AS createdBy,
        a.is_beforehand AS isBeforehand,
        b.sale_name AS saleName,
        b.booking_num AS bookingNum,
        d.house_sync_id AS houseSyncId,
        a.electron_code as electronCode,
        a.electron_name as electronName,
        a.order_type as orderType,
        a.user_id as userId,
        a.electron_id as electronId,
        a.electron_url as electronUrl,
        a.electron_result as electronResult,
        a.order_status as orderStatus,
        a.electron_file_url as electronFileUrl
        FROM op_order a
        LEFT JOIN op_user b
        ON a.user_id=b.id
        AND b.activity_id=#{orderInfoVo.activityIdNow}
        LEFT JOIN op_housing_resources d
        ON a.house_id=d.id
        <where>
            a.activity_id=#{orderInfoVo.activityIdNow}
            <if test="orderInfoVo.name!= null and orderInfoVo.name!= ''">
        AND a.user_name LIKE CONCAT('%',#{orderInfoVo.name},'%')
            </if>
            <if test="orderInfoVo.tel!= null and orderInfoVo.tel!= ''">
                AND b.tel LIKE CONCAT('%',#{orderInfoVo.tel},'%')
            </if>
            <if test="orderInfoVo.saleName!= null and orderInfoVo.saleName!= ''">
        AND b.sale_name LIKE CONCAT('%',#{orderInfoVo.saleName},'%')
            </if>
            <if test="orderInfoVo.buildingName!= null and orderInfoVo.buildingName!= ''">
        AND d.building_name LIKE CONCAT('%',#{orderInfoVo.buildingName},'%')
            </if>
            <if test="orderInfoVo.unitName!= null and orderInfoVo.unitName!= ''">
        AND d.unit_name LIKE CONCAT('%',#{orderInfoVo.unitName},'%')
            </if>
            <if test="orderInfoVo.roomNum!= null and orderInfoVo.roomNum!= ''">
        AND d.room_num LIKE CONCAT('%',#{orderInfoVo.roomNum},'%')
            </if>
            <if test="isAdmin!= 'true'">
        AND (is_beforehand = 's' or is_beforehand is null)
            </if>
        AND a.yn='y'
        AND a.is_regular=#{isRegular}
        </where>

    </select>
    <select id="getMyOrderByactivityIdAndUser"  resultType="java.util.HashMap">
     SELECT
			op_order.id,
			op_housing_resources.id as houseId,
			op_housing_resources.house_sync_id as houseSyncId,
			op_housing_resources.project_id as projectId,
			op_housing_resources.activity_id AS activityId,
			op_housing_resources.building_name AS buildingName,
			op_housing_resources.unit_name AS unitName,
			op_housing_resources.current_floor AS currentFloor,
			op_housing_resources.room_num AS roomNum,
			CONCAT_WS('-',op_housing_resources.building_name,op_housing_resources.unit_name ,op_housing_resources.room_num)  as houseName,
			op_housing_resources.house_name,
			op_housing_resources.unit_price AS unitPrice,
			replace(FORMAT( op_housing_resources.total_price / 10000, 2 ),',','') AS totalPrice,
			op_housing_resources.hourse_type AS hourseType,
			op_house_type.house_name as hourseTypeName,
			op_house_type.house_describe as houseDescribe,
			op_housing_resources.houser_area as houserArea,
			op_house_type.house_img as houseImg,
			op_order.creation_date as creationDate,
			op_order.is_regular as isRegular,
			op_order.user_id as userId,
			op_order.electron_code as electronCode,
			op_order.electron_name as electronName,
			op_order.order_type as orderType,
			op_order.electron_id as electronId,
			op_order.electron_url as electronUrl,
			op_order.electron_result as electronResult,
			op_order.order_status as orderStatus,
			op_order.electron_file_url as electronFileUrl
		FROM
			op_order
			LEFT JOIN op_housing_resources ON op_housing_resources.activity_id = op_order.activity_id and op_order.house_id =  op_housing_resources.id
			LEFT JOIN op_house_type ON op_housing_resources.activity_id = op_house_type.activity_id AND op_housing_resources.hourse_type = op_house_type.house_name
		WHERE
		op_order.activity_id = #{activityId}
		and op_order.is_regular = #{isRegular}
		and op_order.user_id = #{userId}
		and op_order.yn = 'y'
		and op_order.effective_flag = 0
		and (op_order.is_beforehand is null or op_order.is_beforehand = 's')
		ORDER BY op_order.creation_date;
    </select>

    <select id="getUserCanBuy"  resultType="java.util.HashMap">
        SELECT
        *
        FROM
        op_user c
        LEFT JOIN
        (
        SELECT
        o.user_id ,
        COUNT(o.user_id) AS order_cound
        FROM
        op_user u
        LEFT JOIN
        op_order o
        ON
        u.id = o.user_id and o.is_regular = #{isRegular}
        GROUP BY
        o.user_id) d
        ON
        c.id = d.user_id
        where c.activity_id = #{activityId} and c.yn='y' and c.role_name = 'customer';
    </select>

    <select id="getHouseCanBuy"  resultType="java.util.HashMap">
        select *,b.is_regular from op_housing_resources a left join op_order b on a.id = b.house_id where a.activity_id = #{activityId} and a.yn='y' ;
    </select>

    <select id="getOverview1" resultType="com.tahoecn.opening.model.vo.SalesOverview">
        SELECT  COUNT(distinct b.id) AS joinNum,
        COUNT( distinct a.user_id) AS orderNum,
        IFNULL(COUNT(distinct a.user_id)/COUNT(distinct b.id)*100,0) AS percentage
        FROM op_order a
        RIGHT JOIN op_user b
        ON 1=1
        AND a.is_regular =#{isRegular}
        AND a.activity_id=#{activityIdNow}
        WHERE
         b.activity_id=#{activityIdNow}
        AND b.role_name='customer'
        AND a.effective_flag = 0
    </select>
    <select id="getOverview2" resultType="com.tahoecn.opening.model.vo.SalesOverview">
        SELECT
        COUNT(z.id) AS totalNum,
        COUNT(z.house_id) AS soldNum,
        SUM(z.houser_area) AS totalHouseResources
        FROM(
        SELECT
        b.id AS id,a.house_id AS house_id,b.houser_area AS houser_area,b.activity_id AS activity_id
        FROM op_order a
        RIGHT JOIN op_housing_resources b
        ON a.house_id=b.id
        AND a.is_regular =#{isRegular}
        AND a.yn='y'
        AND a.effective_flag = 0
        AND  a.activity_id=#{activityIdNow}) z
        WHERE z.activity_id=#{activityIdNow}
    </select>
    <select id="getOverview3" resultType="com.tahoecn.opening.model.vo.SalesOverview">
        SELECT
        IFNULL(SUM(b.houser_area),0)  AS soldHouseResources
        FROM
        op_order a
        JOIN
        op_housing_resources b
        ON a.house_id=b.id
        AND a.is_regular =#{isRegular}
        where  a.yn='y'
        AND a.effective_flag = 0
        AND a.activity_id=#{activityIdNow}
    </select>

    <select id="getHouseTypeList" resultType="com.tahoecn.opening.model.vo.HouseTypeList">
        SELECT
        type.house_type AS typeName,IFNULL(house.price,0) AS salesVolume,IFNULL(house.area,0) AS salesArea,
        IFNULL(house.num,0) AS salesNum
        FROM op_house_type type
        LEFT JOIN (SELECT
        SUM(b.total_price) price,SUM(b.houser_area) area,b.hourse_type ht,COUNT(*) num
        FROM op_order a
        JOIN op_housing_resources b
        ON a.house_id=b.id
        AND a.activity_id=#{activityIdNow}
        AND a.is_regular=#{isRegular}
        AND a.effective_flag = 0
        GROUP BY b.hourse_type) house
        ON type.house_name=house.ht
        WHERE type.activity_id=#{activityIdNow}
    </select>


    <update id="setBeforeOrderShow" parameterType="java.lang.String">
        update op_order set is_beforehand = 's' where activity_id = #{activityId} and is_beforehand = 'h' and yn = 'y'
    </update>

    <select id="getAllHouseTypeNum" resultType="hashmap">
        SELECT
        type.house_type AS typeName ,IFNULL(COUNT(*),0) AS Num
        FROM op_house_type type
        JOIN op_housing_resources hr
        ON hr.hourse_type=type.house_name
        AND hr.activity_id=#{activityIdNow}
        where type.activity_id = #{activityIdNow}

        GROUP BY type.house_type
    </select>
    <select id="getUserFavoriteAndActivityFavorite" parameterType="map" resultType="java.util.HashMap">
        SELECT
			( SELECT count( * ) FROM op_favorite WHERE op_favorite.activity_id = #{activityId} AND op_favorite.user_id = #{userId} AND op_favorite.yn = 'y' ) AS userFavoriteCounts,
			( SELECT collection_limit FROM op_activity WHERE op_activity.id = #{activityId} ) AS collectionLimit ,
			(select max(order_by)+1 from op_favorite WHERE op_favorite.activity_id = #{activityId} AND op_favorite.user_id = #{userId} AND op_favorite.yn = 'y') as maxOrder
		FROM
		DUAL;
    </select>

    <select id="getSaleData" resultType="com.tahoecn.opening.model.vo.SaleDataVO">
        SELECT
        z.cstguid AS id,z.`name` AS saleName,COUNT(u.id) AS salesHouseNum,SUM(hr.total_price) AS salesVolume
        FROM op_user u
        JOIN op_user z
        ON u.sale_id=z.cstguid
        JOIN op_order o
        ON o.user_id=u.id
        JOIN op_housing_resources hr
        ON o.house_id=hr.id
        <where>  o.activity_id=#{activityIdNow}
        AND u.activity_id=#{activityIdNow}
        AND z.activity_id=#{activityIdNow}
        AND hr.activity_id=#{activityIdNow}
        AND o.is_regular=#{isRegular}
        AND (o.is_beforehand='s' OR o.is_beforehand IS NULL)
        AND o.effective_flag = 0
        AND z.role_name != 'customer'
        <if test="roleName!= null and roleName!= ''">
        AND z.`name` LIKE CONCAT('%',#{roleName},'%')
        </if>
        </where>
        GROUP BY z.`name`,z.cstguid
    </select>

    <select id="getSalesUserNum" resultType="java.util.HashMap">
        SELECT
        a.zid, COUNT(DISTINCT a.uid) AS NUM
        FROM
        (
        SELECT
        z.cstguid AS zid,u.id AS uid
        FROM op_user u
        JOIN op_user z ON u.sale_id=z.cstguid
        JOIN op_order o ON o.user_id=u.id
        <where>
            o.activity_id=#{activityIdNow}
        AND o.is_regular=#{isRegular}
        AND (o.is_beforehand='s' OR o.is_beforehand IS NULL)
        AND o.effective_flag = 0
        AND z.role_name != 'customer'
            <if test="roleName!= null and roleName!= ''">
                AND z.`name` LIKE CONCAT('%',#{roleName},'%')
            </if>
        </where>) a

        GROUP BY a.zid
    </select>

    <select id="canBuyNum" resultType="integer">
        SELECT
        IFNULL(z.totil-z.num,0)
        FROM(
        SELECT
        COUNT(*) AS num,
        b.select_count AS totil
        FROM op_order a
        JOIN op_user b
        ON a.user_id=b.id
        WHERE a.user_id=#{userId}) z

    </select>


    <select id="getSalesOrder" resultType="com.tahoecn.opening.model.vo.SalesOrderVO">
        SELECT
        b.oppguid AS oppguid,
        c.house_sync_id AS houseSyncId,
        a.house_id as houseId,
        c.house_name AS houseName,
        b.cstguid AS cstguid,
        b.`name` AS name,
        b.tel AS tel,
        b.id_card AS idCard,
        c.total_price AS totalPrice,
        a.creation_date AS creationDate
        FROM op_order a
        JOIN op_user b
        ON a.user_id=b.id
        JOIN op_housing_resources c
        ON a.house_id=c.id
        WHERE a.activity_id=#{activityId}
    </select>

    <!-- 获取到该活动下所有的 白名单数据-->
    <select id="selectUserHourseResourceListByActivityId" resultType="com.tahoecn.opening.model.dto.UserHouseResourceDTO">
        SELECT
            uhr.id AS id,
            uhr.user_id AS userId,
            uhr.house_resource_id AS houseResourceId,
            u.cstguid AS userSyncId,
            u.name AS userName,
            CONCAT(hr.building_name, "-", hr.unit_name, "-", hr.room_num) AS hourseName,
            hr.house_sync_id AS hourseSyncId
        FROM
            op_user_house_resource AS uhr
            LEFT JOIN op_user AS u ON uhr.user_id = u.id
            LEFT JOIN op_housing_resources AS hr ON uhr.house_resource_id = hr.id
        WHERE
            uhr.activity_id = #{activityId}
            AND u.yn = 'y'
            AND hr.yn = 'y';
    </select>

    <!-- 批量入库 订单集合 -->
    <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO op_order(
            id,
            project_id,
            activity_id,
            user_id,
            user_name,
            house_id,
            house_name,
            is_regular,
            yn,
            creation_date,
            user_sync_id,
            house_sync_id,
            is_return,
            effective_flag
        )
        values
        <foreach collection="dataList" item="item" separator=",">
            (
                null, #{item.projectId},
                #{item.activityId}, #{item.userId},
                #{item.userName}, #{item.houseId},
                #{item.houseName}, #{item.isRegular},
                #{item.yn}, #{item.creationDate},
                #{item.userSyncId}, #{item.houseSyncId},
                #{item.isReturn}, #{item.effectiveFlag}
            )
        </foreach>
    </insert>
</mapper>
