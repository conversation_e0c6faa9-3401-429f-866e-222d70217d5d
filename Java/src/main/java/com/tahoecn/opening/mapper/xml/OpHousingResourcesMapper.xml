<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.opening.mapper.OpHousingResourcesMapper">

	<insert id="saveOne">
		INSERT INTO op_housing_resources(
		id,
		activity_id,
		project_id,
		project_name,
		house_sync_id,
		house_name,
		pro_child_id,
		pro_child_name,
		building_name,
		unit_name,
		unit_floor_number,
		current_floor,
		room_type,
		room_num,
		houser_area,
		total_price,
		unit_price,
		discount_total_price,
		discount_unit_price,
		hourse_type,
		room_stru,
		house_img,
		remark,
		status,
		yn,
		sale_control_flag,
		creation_date,
		created_by,
		last_update_date,
		last_update_by
		) VALUES (
		#{id},
		#{activityId},
		#{projectId},
		#{projectName},
		#{houseSyncId},
		#{houseName},
		#{proChildId},
		#{proChildName},
		#{buildingName},
		#{unitName},
		#{unitFloorNumber},
		#{currentFloor},
		#{roomType},
		#{roomNum},
		#{houserArea},
		#{totalPrice},
		#{unitPrice},
		#{discountTotalPrice},
		#{discountUnitPrice},
		#{hourseType},
		#{roomStru},
		#{houseImg},
		#{remark},
		#{status},
		#{yn},
		#{saleControlFlag},
		#{creationDate},
		#{createdBy},
		#{lastUpdateDate},
		#{lastUpdateBy}
		)
		<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
			SELECT @@IDENTITY AS id
		</selectKey>
	</insert>


	<select id="getBuildingListByActivityId"  resultType="java.util.HashMap">
        SELECT DISTINCT
			op_housing_resources.building_name as buildingName
		FROM
			`op_housing_resources` 
		WHERE
			op_housing_resources.activity_id = #{activityId} 
			AND op_housing_resources.building_name IS NOT NULL
			<if test="batchFlag == 1">
				<if test="formalFlag == 1">
					AND op_housing_resources.batch_name_formal IN
					<foreach collection="batchNameList" item="item" separator="," open="(" close=")">
						#{item}
					</foreach>
				</if>
				<if test="formalFlag == 0">
					AND op_housing_resources.batch_name_simulation IN
					<foreach collection="batchNameList" item="item" separator="," open="(" close=")">
						#{item}
					</foreach>
				</if>
			</if>
		ORDER BY
			op_housing_resources.building_name *1;
    </select>
	<select id="getUnitListByActivityIdAndBuildingName"  resultType="java.util.HashMap">
       SELECT DISTINCT
			op_housing_resources.unit_name as  unitName
		FROM
			`op_housing_resources` 
		WHERE
			op_housing_resources.activity_id = #{activityId} 
			AND op_housing_resources.unit_name IS NOT NULL 
			<if test="buildingName != null and buildingName != ''">
				and op_housing_resources.building_name = #{buildingName}
			</if>
			<if test="batchFlag == 1">
				<if test="formalFlag == 1">
					AND op_housing_resources.batch_name_formal IN
					<foreach collection="batchNameList" item="item" separator="," open="(" close=")">
						#{item}
					</foreach>
				</if>
				<if test="formalFlag == 0">
					AND op_housing_resources.batch_name_simulation IN
					<foreach collection="batchNameList" item="item" separator="," open="(" close=")">
						#{item}
					</foreach>
				</if>
			</if>
		ORDER BY
			op_housing_resources.unit_name desc ;
    </select>
	<select id="getFloorListByActivityIdAndBuildingAndUnit"  resultType="java.util.HashMap">
       SELECT DISTINCT
			op_housing_resources.current_floor AS currentFloor
		FROM
			`op_housing_resources` 
		WHERE
			op_housing_resources.activity_id = #{activityId} 
			AND op_housing_resources.current_floor IS NOT NULL 
			<if test="buildingName != null and buildingName != ''">
				and op_housing_resources.building_name = #{buildingName}
			</if>
			<if test="unitName != null and unitName != ''">
				and op_housing_resources.unit_name = #{unitName}
			</if>
			
		ORDER BY
			op_housing_resources.current_floor *1 desc;
    </select>
	<select id="getRoomMsgByActivityIdAndBuildingAndUnit"  resultType="java.util.HashMap">
      SELECT
			op_housing_resources.id,
			op_housing_resources.house_sync_id as houseSyncId,
			op_housing_resources.project_id as projectId,
			op_housing_resources.activity_id AS activityId,
			op_housing_resources.building_name AS buildingName,
			op_housing_resources.unit_name AS unitName,
			op_housing_resources.current_floor AS currentFloor,
			op_housing_resources.room_num AS roomNum,
			CONCAT_WS('-',op_housing_resources.building_name,op_housing_resources.unit_name ,op_housing_resources.room_num)  as houseName,
			op_housing_resources.house_name,
			op_housing_resources.unit_price AS unitPrice,
			replace(FORMAT( op_housing_resources.total_price / 10000, 2 ),',','') AS totalPrice,
			op_housing_resources.hourse_type AS hourseType,
			op_house_type.house_name as hourseTypeName,
			op_house_type.house_describe as houseDescribe,
			op_housing_resources.houser_area as houserArea,
			op_house_type.house_img as houseImg,
			IF(EXISTS(SELECT * FROM op_favorite WHERE op_favorite.activity_id=op_housing_resources.activity_id and op_favorite.user_id =#{userId} and op_favorite.house_id = op_housing_resources.id),1,0) AS isCollect,
			IF(EXISTS(SELECT * FROM op_order WHERE op_order.activity_id=op_housing_resources.activity_id and op_order.house_id = op_housing_resources.id and is_regular = #{isRegular} and (op_order.is_beforehand is null or op_order.is_beforehand = 's')  and yn = 'y'),1,0) AS orderStatus
		FROM
			op_housing_resources
			LEFT JOIN op_house_type ON op_housing_resources.activity_id = op_house_type.activity_id 
			AND op_housing_resources.hourse_type = op_house_type.house_type 
		WHERE
			op_housing_resources.activity_id = #{activityId} 
			<if test="buildingName != null and buildingName != ''">
				and op_housing_resources.building_name = #{buildingName}
			</if>
			<if test="unitName != null and unitName != ''">
				and op_housing_resources.unit_name = #{unitName}
			</if>
			<if test="currentFloor != null and currentFloor != ''">
				and op_housing_resources.current_floor = #{currentFloor}
			</if>
		ORDER BY
			current_floor,
			room_num ;
    </select>
    
    <resultMap type="java.util.HashMap" id="houseResultMap">
    	<id column="currentFloor" property="currentFloor"/>
    	<collection property="houseList"  ofType="java.util.HashMap" javaType="ArrayList">
   			<id column="id" property="id"/>
   			<result column="houseSyncId" property="houseSyncId"/>
   			<result column="projectId" property="projectId"/>
   			<result column="activityId" property="activityId"/>
   			<result column="buildingName" property="buildingName"/>
   			<result column="unitName" property="unitName"/>
   			<result column="currentFloor" property="currentFloor"/>
   			<result column="roomNum" property="roomNum"/>
   			<result column="saleControlFlag" property="saleControlFlag"/>
   			<result column="roomType" property="roomType"/>
   			<result column="houseName" property="houseName"/>
   			<result column="house_name" property="house_name"/>
   			<result column="unitPrice" property="unitPrice"/>
   			<result column="totalPrice" property="totalPrice"/>
   			<result column="discountUnitPrice" property="discountUnitPrice"/>
   			<result column="discountTotalPrice" property="discountTotalPrice"/>
   			<result column="hourseType" property="hourseType"/>
   			<result column="hourseTypeName" property="hourseTypeName"/>
   			<result column="houseDescribe" property="houseDescribe"/>
   			<result column="houserArea" property="houserArea"/>
   			<result column="houseImg" property="houseImg"/>
   			<result column="isCollect" property="isCollect"/>
   			<result column="orderStatus" property="orderStatus"/>
        </collection>
    </resultMap>
    
    <select id="getRoomMsgByActivityIdAndBuildingAndUnitMg"  resultMap="houseResultMap">
      SELECT
			op_housing_resources.id,
			op_housing_resources.house_sync_id as houseSyncId,
			op_housing_resources.project_id as projectId,
			op_housing_resources.activity_id AS activityId,
			op_housing_resources.building_name AS buildingName,
			op_housing_resources.unit_name AS unitName,
			op_housing_resources.current_floor AS currentFloor,
			op_housing_resources.room_num AS roomNum,
			op_housing_resources.sale_control_flag AS saleControlFlag,
			op_housing_resources.room_type AS roomType,
			CONCAT_WS('-',op_housing_resources.building_name,op_housing_resources.unit_name ,op_housing_resources.room_num)  as houseName,
			op_housing_resources.house_name,
			CONVERT(op_housing_resources.unit_price, DECIMAL(10, 2)) AS unitPrice,
			replace(FORMAT( op_housing_resources.total_price, 0),',','') AS totalPrice,
			CONVERT(op_housing_resources.discount_unit_price, DECIMAL(10, 2)) AS discountUnitPrice,
			replace(FORMAT( op_housing_resources.discount_total_price, 0),',','') AS discountTotalPrice,
			op_housing_resources.hourse_type AS hourseType,
			op_housing_resources.room_stru as hourseTypeName,
			op_house_type.house_describe as houseDescribe,
			op_housing_resources.houser_area as houserArea,
			op_house_type.house_img as houseImg,
			IF(EXISTS(SELECT * FROM op_favorite WHERE op_favorite.activity_id=op_housing_resources.activity_id and op_favorite.user_id =#{userId} and op_favorite.house_id = op_housing_resources.id),1,0) AS isCollect,
			IF(EXISTS(SELECT * FROM op_order WHERE op_order.activity_id=op_housing_resources.activity_id and op_order.house_id = op_housing_resources.id and op_order.is_regular = #{isRegular} and (op_order.is_beforehand is null or op_order.is_beforehand = 's') and op_order.effective_flag = 0 and yn = 'y'),1,0) AS orderStatus
		FROM
			op_housing_resources
			LEFT JOIN op_house_type ON op_housing_resources.activity_id = op_house_type.activity_id 
			AND op_housing_resources.hourse_type = op_house_type.house_name
		WHERE
			op_housing_resources.activity_id = #{activityId} 
			<if test="buildingName != null and buildingName != ''">
				and op_housing_resources.building_name = #{buildingName}
			</if>
			<if test="unitName != null and unitName != ''">
				and op_housing_resources.unit_name = #{unitName}
			</if>
			<if test="batchFlag == 1">
				<if test="formalFlag == 1">
					AND op_housing_resources.batch_name_formal IN
					<foreach collection="batchNameList" item="item" separator="," open="(" close=")">
						#{item}
					</foreach>
				</if>
				<if test="formalFlag == 0">
					AND op_housing_resources.batch_name_simulation IN
					<foreach collection="batchNameList" item="item" separator="," open="(" close=")">
						#{item}
					</foreach>
				</if>
			</if>
		ORDER BY
		REGEXP_REPLACE(current_floor, '[^0-9]+', '')*1   asc,
		REGEXP_REPLACE(room_num, '[^0-9]+', '')*1  asc;
    </select>

	<!-- 递归查询房源信息根据活动ID-->
	<select id="selectHouseResourceListByActivityId"  resultType="com.tahoecn.opening.model.dto.HouseResourceDTO">
		SELECT
		op_housing_resources.id as houseResourceId,
		op_housing_resources.activity_id AS activityId,
		op_housing_resources.house_sync_id as houseSyncId,
		op_housing_resources.project_id as projectId,
		op_housing_resources.building_name AS buildingName,
		op_housing_resources.unit_name AS unitName,
		op_housing_resources.current_floor AS currentFloor,
		op_housing_resources.room_num AS roomNum,
		op_housing_resources.house_name AS houseName,
		op_housing_resources.unit_price AS unitPrice,
		replace(FORMAT( op_housing_resources.total_price / 10000, 2 ),',','') AS totalPrice,
		op_housing_resources.hourse_type AS hourseType,
		op_housing_resources.room_stru as hourseTypeName,
		op_housing_resources.houser_area as houserArea
		FROM
		op_housing_resources
		WHERE
		op_housing_resources.activity_id = #{activityId}
		AND op_housing_resources.building_name IS NOT NULL
		AND op_housing_resources.unit_name IS NOT NULL
		AND op_housing_resources.sale_control_flag = 'n'
		AND op_housing_resources.yn = 'y'
	</select>
    
    
	<select id="getHouseCountByActivityId"  resultType="java.lang.String">
		SELECT
			count( id )
		FROM
			op_housing_resources
		WHERE
			op_housing_resources.activity_id = #{activityId}
			AND op_housing_resources.yn = 'y';
    </select>
	<select id="getOpenDataByActivityId"  resultType="java.util.HashMap">
		SELECT
			count( * ) AS totalCount,
			CONVERT(SUM(op_housing_resources.houser_area), DECIMAL(10,2)) AS totalAreaCount,
			FORMAT(SUM( op_housing_resources.total_price )/10000,2) AS totalPriceCount,
			count( DISTINCT op_housing_resources.hourse_type ) AS totalHouseType 
		FROM
			op_housing_resources 
		WHERE
			op_housing_resources.activity_id = #{activityId};
    </select>
	<select id="getTotalUserByActivityId"  resultType="java.lang.Integer">
		select count(*) from op_user where op_user.activity_id = #{activityId} and op_user.role_name = 'customer';
    </select>
	<select id="getHisLoginUserCountByActivityId"  resultType="java.lang.Integer">
		select count(*) from op_user where op_user.activity_id = #{activityId} and op_user.role_name = 'customer' and op_user.last_login_date is not null ;
    </select>
	<select id="getTodayLoginUserCountByActivityId"  resultType="java.lang.Integer">
		select count(*) from op_user where op_user.activity_id = #{activityId} and op_user.role_name = 'customer' and op_user.last_login_date &gt;= date_format(SYSDATE(),'%Y-%m-%d') ;
    </select>
    <select id="getTodayLoginUserCountByActivityIdTwo"  resultType="java.lang.Integer">
		select count(*) from op_user where op_user.activity_id = #{activityId} and op_user.role_name = 'customer' and op_user.last_login_date <![CDATA[<=]]> #{endDate};
    </select>
	<select id="getRoomCountByActivityId"  resultType="java.lang.Integer">
		select count(*) from op_housing_resources where op_housing_resources.activity_id = #{activityId};
    </select>
	<select id="getRoomFavoriteCountByActivityId"  resultType="java.lang.Integer">
		SELECT
			count( * ) 
		FROM
			(
			SELECT
				op_housing_resources.id,
				count( op_favorite.id ) AS favoriteCount 
			FROM
				op_housing_resources
				LEFT JOIN op_favorite ON op_housing_resources.id = op_favorite.house_id 
			WHERE
				op_housing_resources.activity_id = #{activityId} 
			GROUP BY
				op_housing_resources.id 
			HAVING
				count( op_favorite.id ) &gt;= #{minTotal}
			<if test="maxtotal != 0 and maxtotal != minTotal">
				AND count( op_favorite.id ) &lt;  #{maxtotal} 
			</if> 
			<if test="maxtotal == minTotal ">
				AND count( op_favorite.id ) &lt;=  #{maxtotal} 
			</if> 
			
			) a;
    </select>
	<select id="getFavoriteMaxCountByActivityId"  resultType="java.lang.Integer">
		SELECT
			max( a.favoriteCount ) AS favoriteMaxCount 
		FROM
			(
			SELECT
				op_housing_resources.id,
				count( op_favorite.id ) AS favoriteCount 
			FROM
				op_housing_resources
				LEFT JOIN op_favorite ON op_housing_resources.id = op_favorite.house_id 
			WHERE
				op_housing_resources.activity_id = #{activityId}
			GROUP BY
				op_housing_resources.id 
			) a;
    </select>
	<!--<select id="getHouseCountByActivityId"  resultType="java.lang.Integer">-->
		<!--SELECT-->
			<!--count( * ) AS houseCount-->
		<!--FROM-->
			<!--op_housing_resources-->
		<!--WHERE-->
			<!--op_housing_resources.yn = 'y'-->
			<!--AND op_housing_resources.activity_id = #{activityId};-->
    <!--</select>-->
	<select id="getOpHousingResourcesList"  resultType="com.tahoecn.opening.model.OpHousingResources">
		SELECT
			* 
		FROM
			op_housing_resources 
		WHERE
			op_housing_resources.yn = 'y' 
			AND op_housing_resources.activity_id = #{activityId} 
		ORDER BY
			op_housing_resources.building_name * 1 ASC,
			op_housing_resources.unit_name DESC,
			op_housing_resources.current_floor * 1 DESC,
			op_housing_resources.room_num * 1 DESC
    </select>
    
    <resultMap type="com.tahoecn.opening.model.screenDto.Building" id="screenMap">
    	<id column="building_name" property="buildingName"/>
    	<collection property="units"  ofType="com.tahoecn.opening.model.screenDto.Unit" >
	    	<id column="unit_name" property="unitName"/>
	    	<result column="uNum" property="uNum"/>
	    	<collection property="floors"  ofType="com.tahoecn.opening.model.screenDto.Floor"
	    		select="getScreenFloor" column="{actId=activity_id,bName=building_name,uName=unit_name}">
	        </collection>
        </collection>
    </resultMap>
    
    <resultMap type="com.tahoecn.opening.model.screenDto.Floor" id="screenFloorMap">
    	<id column="current_floor" property="currentFloor"/>
    	<collection property="rooms"  ofType="com.tahoecn.opening.model.screenDto.Room">
   			<id column="id" property="id"/>
   			<result column="building_name" property="buildingName"/>
   			<result column="unit_name" property="unitName"/>
   			<result column="room_num" property="roomNum"/>
        </collection>
    </resultMap>
    
    <select id="getScreenList" resultMap="screenMap">
    	SELECT
			o.activity_id,
			o.building_name,
			o.unit_name,
			MAX( o.fnum ) uNum 
		FROM
			(
			SELECT
				ohr.activity_id,
				ohr.building_name,
				ohr.unit_name,
				ohr.current_floor,
				COUNT( 1 ) fnum 
			FROM
				op_housing_resources ohr 
			WHERE
				ohr.yn = 'y' 
				AND ohr.activity_id = #{activityId}
			GROUP BY
				ohr.building_name,
				ohr.unit_name,
				ohr.current_floor 
			) o 
		GROUP BY
			o.building_name,
			o.unit_name 
		ORDER BY
			o.building_name * 1 ASC,
			o.unit_name ASC
    </select>
    
    <select id="getScreenFloor" resultMap="screenFloorMap">
    	SELECT
			* 
		FROM
			op_housing_resources 
		WHERE
			op_housing_resources.yn = 'y' 
			AND op_housing_resources.activity_id = #{actId} 
			AND op_housing_resources.building_name = #{bName} 
			AND op_housing_resources.unit_name = #{uName} 
		ORDER BY
			op_housing_resources.current_floor * 1 DESC,
			op_housing_resources.room_num * 1 ASC
    </select>
    
    <select id="getSalesDataByActivityId"  resultType="com.tahoecn.opening.model.OpHousingResources">
		SELECT
			op_order.house_id as id,
			op_housing_resources.house_name as houseName,
			op_housing_resources.building_name as buildingName,
			op_housing_resources.unit_name as unitName,
			op_housing_resources.room_num as roomNum,
			op_housing_resources.total_price as totalPrice,
			op_order.creation_date as saleControlDate,
			op_user.`name` as remark
		FROM
			op_order
		LEFT JOIN op_housing_resources on op_order.house_id = op_housing_resources.id
		LEFT JOIN op_user on op_order.user_id = op_user.id
		WHERE
			op_order.activity_id = #{activityId}
			AND op_order.is_regular = #{regular}
			AND op_order.yn = 'y'
			AND (op_order.is_beforehand = 's' or op_order.is_beforehand is null )
			ORDER BY op_order.creation_date DESC
    </select>
<select id="getOptionalProperties" resultType="java.lang.Integer">
	select (select count(id) from op_housing_resources where activity_id=21)-(select count(id) as optionalProperties
from
(select DISTINCT id from
(SELECT
			op_order.house_id as id
		FROM
			op_order
		LEFT JOIN op_housing_resources on op_order.house_id = op_housing_resources.id
		LEFT JOIN op_user on op_order.user_id = op_user.id
		WHERE
			op_order.activity_id = #{activityId}
			AND op_order.is_regular = #{regular}
			AND op_order.yn = 'y'
			AND (op_order.is_beforehand = 's' or op_order.is_beforehand is null)
union
select id
from op_housing_resources where activity_id=#{activityId} and sale_control_flag='y') a) b) from DUAL
</select>
	<!-- 获取当前活动下所有的白名单人员数量 -->
	<select id="countOpUserHouseResourceByActivityId" resultType="java.lang.Integer">
		SELECT
			count( z.num ) AS countNum
		FROM
			( SELECT count( * ) AS num FROM op_user_house_resource WHERE activity_id = #{activityId} GROUP BY user_id ) z;
    </select>

	<!-- 递归查询房源信息根据活动ID-->
	<select id="selectWhiteHouseResourceListByCondition"  resultType="com.tahoecn.opening.model.dto.WhiteHouseResourceDTO">
		SELECT
			ohr.id as id,
			ohr.activity_id AS activityId,
			ohr.project_id AS projectId,
			ohr.project_name AS projectName,
			ohr.house_sync_id AS houseSyncId,
			ohr.house_name AS houseName,
			ohr.pro_child_id AS proChildId,
			ohr.pro_child_name AS proChildName,
			ohr.building_name AS buildingName,
			ohr.unit_name AS unitName,
			ohr.unit_floor_number AS unitFloorNumber,
			ohr.current_floor AS currentFloor,
			ohr.room_type AS roomType,
			ohr.room_num AS roomNum,
			ohr.houser_area AS houserArea,
			ohr.total_price AS totalPrice,
			ohr.unit_price AS unitPrice,
			ohr.discount_total_price AS discountTotalPrice,
			ohr.discount_unit_price AS discountUnitPrice,
			ohr.hourse_type AS hourseType,
			ohr.room_stru AS roomStru,
			ohr.house_img AS houseImg,
			ohr.remark AS remark,
			ohr.status AS status,
			ohr.yn AS yn,
			ouhr.create_id AS createId,
			ouhr.create_name AS createName,
			ouhr.create_time AS createTime,
			ou.name AS userName,
			ou.id_card AS userIdCard,
			ou.tel AS userMobile
		FROM op_housing_resources AS ohr
		LEFT JOIN op_user_house_resource AS ouhr ON ohr.id = ouhr.house_resource_id
		LEFT JOIN op_user AS ou ON ouhr.user_id = ou.id
		<where>
			ohr.activity_id = #{param.activityId}
			AND ouhr.activity_id = #{param.activityId}
			AND ou.activity_id = #{param.activityId}
			<if test="param.buildingName != null and param.buildingName != ''">
				AND ohr.building_name = #{param.buildingName}
			</if>
			<if test="param.unitName != null and param.unitName != ''">
				AND ohr.unit_name = #{param.unitName}
			</if>
			<if test="param.currentFloor != null and param.currentFloor != ''">
				AND ohr.current_floor = #{param.currentFloor}
			</if>
			<if test="param.roomNum != null and param.roomNum != ''">
				AND ohr.room_num LIKE CONCAT('%', #{param.roomNum},'%')
			</if>
			<if test="param.hourseType != null and param.hourseType != ''">
				AND ohr.hourse_type LIKE CONCAT('%', #{param.hourseType},'%')
			</if>
			<if test="param.roomStru != null and param.roomStru != ''">
				AND ohr.room_stru LIKE CONCAT('%', #{param.roomStru},'%')
			</if>
			<if test="param.houseName != null and param.houseName != ''">
				AND ohr.house_name LIKE CONCAT('%', #{param.houseName},'%')
			</if>
			<if test="param.roomType != null and param.roomType != ''">
				AND ohr.room_type = #{param.roomType}
			</if>
			<if test="param.userName != null and param.userName != ''">
				AND ou.name LIKE CONCAT('%', #{param.userName},'%')
			</if>
			<if test="param.userMobile != null and param.userMobile != ''">
				AND ou.tel LIKE CONCAT('%', #{param.userMobile},'%')
			</if>
			AND ohr.yn = 'y'
			AND ou.yn = 'y'
			order by ouhr.create_time desc
		</where>
	</select>

    <update id="updateSaleControlFlagByIdList">
        update op_housing_resources set sale_control_flag = 'n', sale_control_user_account = '', sale_control_user_name = '', sale_control_date = null
        WHERE
            id in
        <foreach item="item" index="index" collection="deleteIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>
