<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.opening.mapper.OpFavoriteMapper">

	<select id="getMyFavoriteByactivityIdAndUser"  resultType="java.util.HashMap">
		SELECT
			op_favorite.id,
			op_housing_resources.id as houseId,
			op_housing_resources.house_sync_id as houseSyncId,
			op_housing_resources.project_id as projectId,
			op_housing_resources.activity_id AS activityId,
			op_housing_resources.building_name AS buildingName,
			op_housing_resources.unit_name AS unitName,
			op_housing_resources.current_floor AS currentFloor,
			op_housing_resources.room_num AS roomNum,
			CONCAT_WS('-',op_housing_resources.building_name,op_housing_resources.unit_name ,op_housing_resources.room_num)  as houseName,
			op_housing_resources.house_name,
			CONVERT(op_housing_resources.unit_price, DECIMAL(10, 2)) AS unitPrice,
			replace(FORMAT( op_housing_resources.total_price, 0),',','') AS totalPrice,
			CONVERT(op_housing_resources.discount_unit_price, DECIMAL(10, 2)) AS discountUnitPrice,
			replace(FORMAT( op_housing_resources.discount_total_price, 0),',','') AS discountTotalPrice,
			op_house_type.house_name AS hourseType,
			op_housing_resources.room_stru as hourseTypeName,
			op_house_type.house_describe as houseDescribe,
			op_housing_resources.houser_area as houserArea,
			op_house_type.house_img as houseImg,
			1 AS isCollect,
			IF(EXISTS(SELECT * FROM op_order WHERE op_order.activity_id=op_housing_resources.activity_id and op_order.house_id = op_housing_resources.id and is_regular = #{isRegular} and (op_order.is_beforehand is null or op_order.is_beforehand = 's') and op_order.effective_flag = 0 and yn = 'y'),1,0) AS orderStatus,
			(select count(*) from op_favorite a where op_favorite.house_id = a.house_id and op_favorite.activity_id = a.activity_id) as favoriteCounts
		FROM
			op_favorite
			left join op_housing_resources on op_favorite.house_id = op_housing_resources.id and op_housing_resources.yn = 'y'
			LEFT JOIN op_house_type ON op_housing_resources.activity_id = op_house_type.activity_id  and op_house_type.yn = 'y'
			AND op_housing_resources.hourse_type = op_house_type.house_name
		WHERE
			op_favorite.activity_id = #{activityId} 
			and op_favorite.user_id = #{userId}
			and op_housing_resources.id is not null
			order by op_favorite.order_by asc,op_favorite.creation_date desc;
	 </select>
	<select id="getFavoriteFailByactivityIdAndUser"  resultType="java.util.HashMap">
		SELECT
			op_favorite.id,
			op_housing_resources.id as houseId,
			op_housing_resources.house_sync_id as houseSyncId,
			op_housing_resources.project_id as projectId,
			op_housing_resources.activity_id AS activityId,
			op_housing_resources.building_name AS buildingName,
			op_housing_resources.unit_name AS unitName,
			op_housing_resources.current_floor AS currentFloor,
			op_housing_resources.room_num AS roomNum,
			CONCAT_WS('-',op_housing_resources.building_name,op_housing_resources.unit_name ,op_housing_resources.room_num)  as houseName,
			op_housing_resources.house_name,
			op_housing_resources.unit_price AS unitPrice,
			replace(FORMAT( op_housing_resources.total_price / 10000, 2 ),',','') AS totalPrice,
			op_housing_resources.discount_unit_price AS discountUnitPrice,
			replace(FORMAT( op_housing_resources.discount_total_price / 10000, 2 ),',','') AS discountTotalPrice,
			op_house_type.house_name AS hourseType,
			op_housing_resources.room_stru as hourseTypeName,
			op_house_type.house_describe as houseDescribe,
			op_housing_resources.houser_area as houserArea,
			op_house_type.house_img as houseImg,
			1 AS isCollect,
			IF(EXISTS(SELECT * FROM op_order WHERE op_order.activity_id=op_housing_resources.activity_id and op_order.house_id = op_housing_resources.id and is_regular = #{isRegular} and (op_order.is_beforehand is null or op_order.is_beforehand = 's') and yn = 'y'),1,0) AS orderStatus,
			(select count(*) from op_favorite a where op_favorite.house_id = a.house_id and op_favorite.activity_id = a.activity_id) as favoriteCounts
		FROM
			op_favorite
			left join op_housing_resources on op_favorite.house_id = op_housing_resources.id and op_housing_resources.yn = 'y'
			LEFT JOIN op_house_type ON op_housing_resources.activity_id = op_house_type.activity_id  and op_house_type.yn = 'y'
			AND op_housing_resources.hourse_type = op_house_type.house_name
		WHERE
			op_favorite.activity_id = #{activityId} 
			and op_favorite.user_id = #{userId}
			AND NOT EXISTS (SELECT * FROM op_order WHERE op_order.activity_id=op_housing_resources.activity_id and op_order.house_id = op_housing_resources.id and is_regular = #{isRegular} and (op_order.is_beforehand is null or op_order.is_beforehand = 's') and yn = 'y')
			order by op_favorite.order_by asc,op_favorite.creation_date desc;
	 </select>
</mapper>
