package com.tahoecn.opening.mapper;

import com.tahoecn.opening.model.OpHouseType;

import java.util.HashMap;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 户型 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-08
 */
public interface OpHouseTypeMapper extends BaseMapper<OpHouseType> {
	
	List<HashMap<String, Object>> getHouseTypeListByActivityId(@Param("activityId") String activityId);
	
	HashMap<String,Object> getHouseTypeAreaMinToMax(@Param("activityId") String activityId);
}
