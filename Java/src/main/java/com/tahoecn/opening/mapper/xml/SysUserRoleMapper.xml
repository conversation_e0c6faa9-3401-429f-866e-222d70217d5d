<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.opening.mapper.SysUserRoleMapper">


    <select id="myActivityList" resultType="com.tahoecn.opening.model.SysUserRole">
        SELECT
        b.id as  activity_id,
        b.activity_name
        FROM
        sys_user_role a
        LEFT JOIN
        op_activity b
        ON
        a.activity_id = b.id
        WHERE
        b.yn = 'y'
        AND b.status_code != 'end'
        AND a.user_code = #{userName}
        GROUP BY
        b.id,
        b.activity_name
        order by b.creation_date desc
    </select>

    <select id="myActivityListAll" resultType="com.tahoecn.opening.model.SysUserRole">
        SELECT
        id as  activity_id,
        activity_name
        from op_activity WHERE yn = 'y'
        order by b.creation_date desc
    </select>
</mapper>
