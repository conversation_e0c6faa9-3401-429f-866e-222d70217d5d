package com.tahoecn.opening.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tahoecn.opening.model.OpActivity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tahoecn.opening.model.OpHousingResources;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.model.dto.OpActivityDTO;
import com.tahoecn.opening.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 活动 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-03
 */
public interface OpActivityMapper extends BaseMapper<OpActivity> {


    IPage<CustomerAnalysisVO> getActivityAnalysisCustomer(Page page,
                                                          @Param("activityId") String activityId,
                                                          @Param("loginNum") Integer loginNum,
                                                          @Param("orderNum") Integer orderNum,
                                                          @Param("opUser") OpUser opUser);

    List<CustomerAnalysisVO> getActivityAnalysisCustomer(@Param("activityId") String activityId,
                                                         @Param("loginNum") Integer loginNum,
                                                         @Param("orderNum") Integer orderNum,
                                                         @Param("opUser") OpUser opUser);

    IPage<FavoriteDetil> getFavoriteUserList(Page page, @Param("houseId") String houseId);
    List<FavoriteDetil> getFavoriteUserList(@Param("houseId") String houseId);

    IPage<HouseResAnalysisVO> getHouseFavoriteNum(Page page, @Param("opHousingResources") OpHousingResources opHousingResources, @Param("minFavo") String minFavo, @Param("maxFavo") String maxFavo);
    List<HouseResAnalysisVO> getHouseFavoriteNum(@Param("opHousingResources") OpHousingResources opHousingResources, @Param("minFavo") String minFavo, @Param("maxFavo") String maxFavo);

    Long getMaxFavoriteNum(String activityId);

    IPage<HashMap<String, Object>> getHouseFavoriteType(Page page, @Param("opHousingResources") OpHousingResources opHousingResources);

    Long getMaxFavoriteTypeNum(String activityId);

    IPage<SginInVO> getSginInList(Page page, @Param("sginOrNot") String sginOrNot, @Param("opUser") OpUser opUser);

    int userSginIn(@Param("activityId") String activityId, @Param("userId") String userId, @Param("address") String address, @Param("now") LocalDateTime now);

    int modifyUserSelectCount(Integer id, Integer buyNumber);

	List<HashMap<String, Object>> getLoadTestData(@Param("houseIds") String houseIds, @Param("activityId") String activityId);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 10:19 2022/8/11
	 * @param startTime     区间开始
	 * @param endTime       区间结束
	 * @param queryFlag     查询类型：1--正式；2--模拟；必传
	 * @return java.util.List<com.tahoecn.opening.model.dto.OpActivityDTO>
	 * @description // TODO 查询活动表中为生成房源白名单的活动数据根据所传时间和查询类型
	 **/
    List<OpActivityDTO> selectListByTime(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("queryFlag") Integer queryFlag);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:54 2022/8/11
     * @param id    数据id
     * @param orderResultFlag 订单生成状态
     * @description // TODO 修改活动的 白名单生成状态
     **/
    void updateAutoOrderResultFlagById(@Param("id") Integer id, @Param("orderResultFlag") Integer orderResultFlag);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 15:56 2022/9/19
     * @param startTime     区间开始
     * @param endTime       区间结束
     * @return java.util.List<com.tahoecn.opening.model.dto.OpActivityDTO>
     * @description // TODO 查询活动表中模拟选房结束时间为当前时间的数据根据所传时间和查询类型
     **/
    List<OpActivityDTO> selectActivityListByTime(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:02 2023/10/23
     * @param id                活动id
     * @param effectiveFlag     有效无效标识：0--有效；1--无效
     * @param regular           正式订单：y--是；n--否
     * @description // TODO 更改订单有效无效标识
     **/
    void updateOrderByData(@Param("id") String id, @Param("effectiveFlag") Integer effectiveFlag, @Param("regular") String regular);

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/27 18:03
     * @Param        activityId 活动id
     * @Param        dataList   批次名称集合
     * @Return       void
     * @Description  TODO 修改活动模拟批次为公共
     **/
    void updateBatchNameSimulation(@Param("activityId") String activityId, @Param("dataList") List<String> dataList);

    /**
     *
     * <AUTHOR> <EMAIL>
     * @Date         2025/4/27 18:03
     * @Param        activityId 活动id
     * @Param        dataList   批次名称集合
     * @Return       void
     * @Description  TODO 修改活动正式批次为公共
     **/
    void updateBatchNameFormal(@Param("activityId") String activityId, @Param("dataList") List<String> dataList);
}
