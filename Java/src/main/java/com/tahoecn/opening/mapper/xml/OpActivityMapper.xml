<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.opening.mapper.OpActivityMapper">


    <select id="getActivityAnalysisCustomer" resultType="com.tahoecn.opening.model.vo.CustomerAnalysisVO">
        SELECT
        a.`name` AS name,
        a.tel AS tel,
        a.id_card AS idCard,
        date_format(a.last_login_date, '%Y-%m-%d %T') AS lastLoginDate,
        c.name AS saleName,
        b.num AS orderYN
        FROM op_user a
        LEFT JOIN op_user c
        ON a.sale_id=c.cstguid
        AND c.yn='y'
        AND c.activity_id=#{activityId}
        LEFT JOIN (SELECT user_id,COUNT(user_id) AS num FROM op_order WHERE yn='y' AND effective_flag = 0 and is_regular = 'y' AND activity_id=#{activityId} GROUP BY user_id) b
        ON a.id=b.user_id
        <where> a.activity_id=#{activityId}
            AND a.yn='y'
            AND a.role_name='customer'
            And c.role_name = '置业顾问'
            <if test="opUser.name != null and opUser.name != ''">
            AND a.`name` LIKE CONCAT('%',#{opUser.name},'%')
            </if>
            <if test="opUser.tel != null and opUser.tel != ''">
            AND a.tel LIKE CONCAT('%',#{opUser.tel},'%')
            </if>
            <if test="opUser.idCard != null and opUser.idCard != ''">
            AND a.id_card LIKE CONCAT('%',#{opUser.idCard},'%')
            </if>
            <if test="opUser.saleName != null and opUser.saleName != ''">
            AND a.sale_name LIKE CONCAT('%',#{opUser.saleName},'%')
            </if>

            <if test = "orderNum == 0">
                AND b.num is not null
            </if>
            <if test = "orderNum == 1">
                AND b.num is null
            </if>

            <if test = "loginNum == 1">
                AND a.last_login_date IS NULL
            </if>
            <if test = "loginNum == 2">
                AND a.last_login_date IS NOT NULL
            </if>
            <if test = "loginNum == 3">
                AND (a.last_login_date IS NULL OR date_format(a.last_login_date,'%Y-%m-%d') != date_format(SYSDATE(),'%Y-%m-%d'))
            </if>
            <if test = "loginNum == 4">
                AND date_format(a.last_login_date,'%Y-%m-%d') = date_format(SYSDATE(),'%Y-%m-%d')
            </if>
            <if test = "loginNum == 5">
                AND b.num is null
                AND a.last_login_date IS NOT NULL
            </if>
        </where>
    </select>

    <select id="getFavoriteUserList" resultType="com.tahoecn.opening.model.vo.FavoriteDetil">
        SELECT
        b.`name` AS name,
        b.tel AS tel,
        b.id_card AS idCard,
        b.sale_name AS saleName
        FROM
        op_favorite a
        JOIN op_user b
        ON b.id=a.user_id
        WHERE a.house_id=#{houseId}
    </select>

    <select id="getHouseFavoriteNum" resultType="com.tahoecn.opening.model.vo.HouseResAnalysisVO">
        SELECT *
        FROM(
        SELECT
        b.id AS houseId,
        b.building_name AS buildingName,
        b.unit_name AS unitName,
        b.current_floor AS currentFloor,
        b.room_num AS roomNum,
        b.house_name AS houseName,
        COUNT(a.house_id) AS favoNum
        FROM op_favorite a
        RIGHT JOIN op_housing_resources b
        ON a.house_id=b.id
        AND a.activity_id=#{opHousingResources.activityId}

        <where>b.activity_id=#{opHousingResources.activityId}
        <if test="opHousingResources.houseName!=null and opHousingResources.houseName!=''">
            AND b.house_name LIKE CONCAT('%',#{opHousingResources.houseName},'%')
        </if>
        <if test="opHousingResources.buildingName!=null and opHousingResources.buildingName!=''">
            AND b.building_name LIKE CONCAT('%',#{opHousingResources.buildingName},'%')
        </if>
        <if test="opHousingResources.unitName!=null and opHousingResources.unitName!=''">
            AND b.unit_name LIKE CONCAT('%',#{opHousingResources.unitName},'%')
        </if>
        <if test="opHousingResources.roomNum!=null and opHousingResources.roomNum!=''">
            AND b.room_num LIKE CONCAT('%',#{opHousingResources.roomNum},'%')
        </if>
        </where>
        GROUP BY b.id) z
        where 1=1
        <if test="minFavo!=null and minFavo!=''">
            AND z.favoNum <![CDATA[>= ]]> #{minFavo}
        </if>
        <if test="maxFavo!=null and maxFavo!=''">
            AND z.favoNum <![CDATA[<=]]> #{maxFavo}
        </if>
        ORDER BY z.favoNum DESC
    </select>
    <select id="getMaxFavoriteNum" resultType="long">
        SELECT MAX(countNum.num) AS max
        FROM (
        SELECT COUNT(a.house_id) AS num FROM op_favorite a
        JOIN op_housing_resources b
        ON a.house_id=b.id
        WHERE
        a.activity_id=#{activityId}
        GROUP BY a.house_id
        ) countNum
    </select>

    <select id="getHouseFavoriteType" resultType="java.util.HashMap">
        SELECT
        b.HouseType AS houseType,
        IFNULL(a.con,0) AS totilHouseRes,
        b.Num AS num
        FROM
        (
        SELECT
        hourse_type,COUNT(*) AS con
        FROM op_housing_resources
        WHERE activity_id=#{opHousingResources.activityId}
        GROUP BY hourse_type) a
        RIGHT JOIN
        (
        SELECT ht.house_type AS HouseType,
        IFNULL(hr.th,0) AS TotilHouseRes,
        IFNULL(hr.numid,0) AS Num
        FROM
        (SELECT b.hourse_type as hoursetype, COUNT(b.id) AS th,
        COUNT(a.house_id) AS numid
        FROM op_favorite a
        RIGHT JOIN op_housing_resources b
        ON a.house_id=b.id
        AND a.activity_id=#{opHousingResources.activityId}
        WHERE b.activity_id=#{opHousingResources.activityId}
        AND a.yn='y' AND b.yn='y'
        GROUP BY b.hourse_type) hr
        RIGHT JOIN op_house_type ht
        ON ht.house_type=hr.hoursetype
        WHERE ht.activity_id=#{opHousingResources.activityId} AND ht.yn='y'
        <if test="opHousingResources.hourseType!=null and opHousingResources.hourseType!=''">
            AND ht.house_type LIKE CONCAT("%",#{opHousingResources.hourseType},"%")
        </if>
        ) b
        ON a.hourse_type=b.HouseType
        ORDER BY b.Num DESC

    </select>
    <select id="getLoadTestData" resultType="java.util.HashMap">
        SELECT
			op_housing_resources.project_id as projectId,
			op_housing_resources.activity_id as activityId,
			CONCAT_WS('-',op_housing_resources.building_name,op_housing_resources.unit_name ,op_housing_resources.room_num)  as houseName,
			op_housing_resources.id as houseId,
			op_housing_resources.house_sync_id as houseSyncId,
			op_user.id as userId,
			op_user.`name` as userName,
			op_user.cstguid as userSyncId,
			'y' as isRegular
		FROM
			op_housing_resources,
			op_user 
		WHERE
			op_housing_resources.activity_id = #{activityId} 
			<!-- AND op_housing_resources.id IN ( ${houseIds} )  -->
			AND op_user.activity_id = op_housing_resources.activity_id 
			AND op_user.role_name = 'customer'
			ORDER BY op_user.id ,op_housing_resources.id
    </select>

    <select id="getMaxFavoriteTypeNum" resultType="long">
        SELECT
        MAX(z.num) AS max
        FROM(
        SELECT
        COUNT(a.id) AS num
        FROM
        op_favorite a
        JOIN op_housing_resources b
        ON a.house_id=b.id
        WHERE a.activity_id=#{activityId}
        GROUP BY b.hourse_type) z
    </select>

    <select id="getSginInList" resultType="com.tahoecn.opening.model.vo.SginInVO">
        SELECT
        a.`name` AS name,
        a.tel AS tel,
        a.id_card AS idCard,
        b.sign_in_date AS sginInDate,
        a.sale_name AS saleName
        FROM
        op_user a
        LEFT JOIN op_user_sign b
        ON a.id=b.user_id
        AND b.activity_id=#{opUser.activityId}
        <where> a.activity_id=#{opUser.activityId}
            <if test='sginOrNot=="y"'>
                AND b.sign_in_date IS NOT NULL
            </if>
            <if test='sginOrNot=="n"'>
                AND b.sign_in_date IS  NULL
            </if>
            AND a.yn='y'
            <if test="opUser.name!=null and opUser.name!=''">
                AND a.name LIKE CONCAT('%',#{opUser.name},'%')
            </if>
            <if test="opUser.tel!=null and opUser.tel!=''">
                AND a.tel LIKE CONCAT('%',#{opUser.tel},'%')
            </if>
            <if test="opUser.idCard!=null and opUser.idCard!=''">
                AND a.id_card LIKE CONCAT('%',#{opUser.idCard},'%')
            </if>
            <if test="opUser.saleName!=null and opUser.saleName!=''">
                AND a.sale_name LIKE CONCAT('%',#{opUser.saleName},'%')
            </if>
        </where>
        order by sginInDate desc
    </select>

    <insert id="userSginIn" >
        <selectKey keyProperty="count" resultType="int" order="BEFORE">
            select count(*) from op_user_sign where user_id = #{userId} and activity_id=#{activityId}
        </selectKey>
        <if test="count > 0">
            update op_user_sign
            <set>
                    sign_in_date= #{now},address=#{address}
            </set>
            <where>
                user_id = #{userId} and activity_id=#{activityId}
            </where>
        </if>
        <if test="count==0">
            insert into op_user_sign  values (null,#{activityId},#{userId},#{now},#{address})
        </if>
    </insert>

    <update id="modifyUserSelectCount">
        UPDATE op_user SET select_count=#{buyNumber}
        WHERE activity_id=#{id}
    </update>

    <!-- 查询活动表中为生成房源白名单的活动数据根据所传时间和查询类型-->
    <select id="selectListByTime" resultType="com.tahoecn.opening.model.dto.OpActivityDTO">
        SELECT
            a.id AS id,
            a.auto_order_flag AS autoOrderFlag,
            a.order_type AS orderType,
            a.auto_order_result_flag AS autoOrderResultFlag
        FROM
            op_activity AS a
        <where>
            <if test="queryFlag != null and queryFlag == 1">
                AND a.formal_start <![CDATA[>=]]> #{startTime} AND a.formal_start <![CDATA[<=]]> #{endTime}
                AND (a.order_type = 0 OR a.order_type = 1)
                AND a.auto_order_result_flag != 1 AND a.auto_order_result_flag != 3
            </if>
            <if test="queryFlag != null and queryFlag == 2">
                AND a.simulation_start <![CDATA[>=]]> #{startTime} AND a.simulation_start <![CDATA[<=]]> #{endTime}
                AND (a.order_type = 0 OR a.order_type = 2)
                AND a.auto_order_result_flag != 2 AND a.auto_order_result_flag != 3
            </if>
            AND a.status_code = 'running'
            AND a.auto_order_flag = 1
            AND a.yn = 'y';
        </where>
    </select>

    <!-- 查询活动表中模拟选房结束时间为当前时间的数据根据所传时间和查询类型 -->
    <select id="selectActivityListByTime" resultType="com.tahoecn.opening.model.dto.OpActivityDTO">
        SELECT
            a.id AS id,
            a.is_simulation AS isSimulation
        FROM
            op_activity AS a
        where
            ((a.simulation_start <![CDATA[>=]]> #{startTime} AND a.simulation_start <![CDATA[<=]]> #{endTime})
            OR (a.formal_start <![CDATA[>=]]> #{startTime} AND a.formal_start <![CDATA[<=]]> #{endTime}))
            AND a.status_code = 'running'
            AND a.yn = 'y';
    </select>

    <!-- 修改活动的 白名单生成状态-->
    <update id="updateAutoOrderResultFlagById">
        UPDATE op_activity SET auto_order_result_flag = #{orderResultFlag}
        WHERE id = #{id}
    </update>

    <!-- 修改活动表的订单有效状态-->
    <update id="updateOrderByData">
        UPDATE op_order SET effective_flag = #{effectiveFlag}
        WHERE activity_id = #{id} AND is_regular = #{regular}
    </update>

    <!-- 修改活动模拟批次为公共-->
    <update id="updateBatchNameSimulation">
        UPDATE op_housing_resources AS ohr
            LEFT JOIN op_order AS oo ON oo.activity_id = #{activityId} AND oo.house_id = ohr.id
            SET ohr.batch_name_simulation = 'common'
        WHERE ohr.activity_id = #{activityId}
          AND ohr.batch_name_simulation IN
            <foreach collection="dataList" item="item" separator="," open="(" close=")">
                (#{item})
            </foreach>
          AND oo.id IS NULL;
    </update>

    <!-- 修改活动正式批次为公共-->
    <update id="updateBatchNameFormal">
        UPDATE op_housing_resources AS ohr
            LEFT JOIN op_order AS oo ON oo.activity_id = #{activityId} AND oo.house_id = ohr.id
            SET ohr.batch_name_formal = 'common'
        WHERE ohr.activity_id = #{activityId}
          AND ohr.batch_name_simulation IN
            <foreach collection="dataList" item="item" separator="," open="(" close=")">
                (#{item})
            </foreach>
          AND oo.id IS NULL;
    </update>
</mapper>
