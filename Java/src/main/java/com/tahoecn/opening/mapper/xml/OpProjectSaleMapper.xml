<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tahoecn.opening.mapper.OpProjectSaleMapper">

    <select id="projectList" resultType="com.tahoecn.opening.model.OpProjectSale">
        SELECT
        *
        FROM
        op_project_sale hr
        WHERE
        hr.id IN
        (
        SELECT
        MAX(id)
        FROM
        op_project_sale b
        <where>
            <if test="areaId != null and '' != areaId">
                and b.area_id = #{areaId}
            </if>
            <if test="cityId != null and '' != cityId">
                and b.city_id = #{cityId}
            </if>
            <if test="projectId != null and '' != projectId">
                and b.project_id = #{projectId}
            </if>
        </where>
        GROUP BY
        project_id)
        order by hr.area_id, hr.city_id, hr.project_id asc
    </select>
</mapper>
