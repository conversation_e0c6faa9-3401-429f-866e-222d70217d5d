package com.tahoecn.opening.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tahoecn.opening.model.OpUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tahoecn.opening.model.dto.UserDto;
import com.tahoecn.opening.model.dto.UserHouseResourceDTO;
import com.tahoecn.opening.model.vo.SaleListVO;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 客户 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-09
 */
public interface OpUserMapper extends BaseMapper<OpUser> {

    IPage<SaleListVO> getUserSale(Page page, @Param("opUser") OpUser opUser);
    List<String> getRoleName();
    List<HashMap<String, String>> getOpUserByProjectIdAndTelOrOpenId(@Param("projectId") String projectId, @Param("tel") String tel, @Param("openId") String openId);
	List<OpUser> getOpUserListByProjectIdAndTel(@Param("tel") String tel, @Param("projectId") String projectId);
	List<HashMap<String, String>> getOpUserByActivityIds(@Param("activityIds") List activityIds);
	List<OpUser> getOpUserListByActivityIdAndOpenId(@Param("activityId") String activityId, @Param("openId") String openId, @Param("tel") String tel);
	List<HashMap<String, String>> getActivityRunOrCloseList(@Param("projectId") String projectId);

    IPage<OpUser> getUserList(Page page, @Param("opUser") OpUser opUser);

    List<String> getBookingNum(String activityId);

    List<String> getselecCount(String activityId);

    List<OpUser> getSaleName(@Param("activityId") String activityId);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:35 2022/7/19
     * @param userId    用户id 必传
     * @return java.util.List<com.tahoecn.opening.model.dto.UserHouseResourceDTO>
     * @description // TODO 根据用户id查询用户白名单房源列表
     **/
    List<UserHouseResourceDTO> getUserHouseResourceListByUserId(@Param("userId") Long userId);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 11:02 2022/8/12
     * @param cstguid
     * @param name
     * @description // TODO  将 数据库中 所有的sale_id是当前用户的saleName改值
     **/
    void updateSaleNameById(@Param("cstguid") String cstguid, @Param("name") String name);

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:27 2023/9/18
     * @param opUser 
     * @description // TODO 更改退出登录逻辑
     **/
    void updateUserLoginDate(@Param("opUser") OpUser opUser);
    void updateUserLoginDateByTel(@Param("opUser") OpUser opUser);

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/1/21 15:50
     * @Param        activityId 活动id
     * @Return       java.util.List<com.tahoecn.opening.model.dto.UserDto>
     * @Description  TODO 查询全部未删除用户、根据活动id
     **/
    List<UserDto> selectAllUserDtoByActivityId(@Param("activityId") String activityId);
}
