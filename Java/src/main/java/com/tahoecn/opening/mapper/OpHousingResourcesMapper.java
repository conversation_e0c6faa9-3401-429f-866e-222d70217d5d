package com.tahoecn.opening.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tahoecn.opening.model.OpHousingResources;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tahoecn.opening.model.dto.HouseResourceDTO;
import com.tahoecn.opening.model.dto.WhiteHouseResourceDTO;
import com.tahoecn.opening.model.vo.WhiteHouseResourceParamVO;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 楼栋房源 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-08
 */
public interface OpHousingResourcesMapper extends BaseMapper<OpHousingResources> {
	List<HashMap<String,Object>> getBuildingListByActivityId(@Param("activityId") String activityId, @Param("batchFlag") Integer batchFlag, @Param("formalFlag") Integer formalFlag, @Param("batchNameList") List<String> batchNameList);
	List<HashMap<String,Object>> getUnitListByActivityIdAndBuildingName(@Param("activityId") String activityId, @Param("buildingName") String buildingName, @Param("batchFlag") Integer batchFlag, @Param("formalFlag") Integer formalFlag, @Param("batchNameList") List<String> batchNameList);
	List<HashMap<String,Object>> getRoomMsgByActivityIdAndBuildingAndUnit(@Param("activityId") String activityId, @Param("buildingName") String buildingName, @Param("unitName") String unitName,
                                                                          @Param("currentFloor") String currentFloor, @Param("isRegular") String isRegular, @Param("userId") String userId);
	List<HashMap<String,Object>> getRoomMsgByActivityIdAndBuildingAndUnitMg(@Param("activityId") String activityId, @Param("buildingName") String buildingName, @Param("unitName") String unitName,
                                                                            @Param("currentFloor") String currentFloor, @Param("isRegular") String isRegular, @Param("userId") String userId,
																			@Param("batchFlag") Integer batchFlag, @Param("formalFlag") Integer formalFlag, @Param("batchNameList") List<String> batchNameList);
	List<HashMap<String,Object>> getFloorListByActivityIdAndBuildingAndUnit(@Param("activityId") String activityId, @Param("buildingName") String buildingName, @Param("unitName") String unitName);
	String getHouseCountByActivityId(@Param("activityId") Integer activityId);
	HashMap<String, Object> getOpenDataByActivityId(@Param("activityId") String activityId);
	int getTotalUserByActivityId(@Param("activityId") String activityId);
	int getHisLoginUserCountByActivityId(@Param("activityId") String activityId);
	int getTodayLoginUserCountByActivityId(@Param("activityId") String activityId);
	int getTodayLoginUserCountByActivityIdTwo(@Param("activityId") String activityId, @Param("beginDate") String beginDate, @Param("endDate") String endDate);
	Integer getFavoriteMaxCountByActivityId(@Param("activityId") String activityId);
	int getRoomCountByActivityId(@Param("activityId") String activityId);
	int getRoomFavoriteCountByActivityId(@Param("activityId") String activityId, @Param("minTotal") Integer minTotal, @Param("maxtotal") Integer maxtotal);
	List<OpHousingResources> getOpHousingResourcesList(@Param("activityId") String activityId);
//	int getHouseCountByActivityId(@Param("activityId") String activityId);  重名相同方法 删除一个
	List<OpHousingResources> getSalesDataByActivityId(@Param("activityId") String activityId, @Param("regular") String regular);
	Integer getOptionalProperties(@Param("activityId") String activityId, @Param("regular") String regular);
	Integer saveOne(OpHousingResources opHousingResources);

//    Integer saveOne(@Param("opHousingResources")OpHousingResources  opHousingResources);
	
	/**
	 * 大屏获取列表
	 * @param activityId
	 * @return
	 */
	List<Map<String, Object>> getScreenList(@Param("activityId") String activityId);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 16:36 2022/7/19
	 * @param activityId 	活动id  必传
	 * @return java.util.List<com.tahoecn.opening.model.dto.HouseResourceDTO>
	 * @description // TODO 递归查询房源信息根据活动ID
	 **/
	List<HouseResourceDTO> selectHouseResourceListByActivityId(@Param("activityId") Long activityId);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 15:34 2022/9/19
	 * @param activityId 
	 * @return java.lang.Integer
	 * @description // TODO 获取当前活动下所有的白名单人员数量
	 **/
	Integer countOpUserHouseResourceByActivityId(@Param("activityId") String activityId);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 14:35 2023/3/13
	 * @param paramVO 条查参数VO
	 * @return com.tahoecn.core.json.JSONResult
	 * @description // TODO 条查房源白名单列表
	 **/
	IPage<WhiteHouseResourceDTO> selectWhiteHouseResourceListByCondition(Page page, @Param("param") WhiteHouseResourceParamVO paramVO);

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 15:20 2023/3/28
	 * @param deleteIdList 
	 * @description // TODO 批量更改销控数据
	 **/
	void updateSaleControlFlagByIdList(@Param("deleteIdList") List<Integer> deleteIdList);
}
