package com.tahoecn.opening.mapper;

import com.tahoecn.opening.model.OpUserHouseResource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客户房源关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-19
 */
public interface OpUserHouseResourceMapper extends BaseMapper<OpUserHouseResource> {

    /**
     * <AUTHOR>  <EMAIL>
     * @date 17:45 2023/10/26
     * @param insertList 
     * @description // TODO 批量新增房源白名单数据
     **/
    void insertList(@Param("dataList") List<OpUserHouseResource> insertList);
}
