package com.tahoecn.opening.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.common.utils
 * @ClassName: TTTT
 * @Description:// TODO SSSS
 * @Date: 2022/7/5 16:39
 * @Version: 1.0
 */
public class TTTT {


    public static void main(String[] args) {
        String wxqrCode = createWXQRCode(1 + "");

        System.out.println("wxqrCode = " + wxqrCode);

        String card = "199502056537";
        String param = "056537";

        if (card.endsWith(param)) {
            System.out.println("param = " + param);
        } else {
            System.out.println("card = " + card);
        }


    }



    // todo zwc 20220705  小程序 生成二维码
    public static String createWXQRCode(String activitiId) {
        String accessToken = getAccessToken();//获取二维码需要的Token
        String wxQRCode = "";
        String WXACodeUrl = "https://api.weixin.qq.com/wxa/getwxacodeunlimit";//获取微信小程序二维码的链接
        String getWXCodeUrl = WXACodeUrl + "?access_token=" + accessToken;
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("scene", activitiId);
        paramMap.put("width", 200);
        paramMap.put("page", "pages/opening/projectEnter/index");
//        paramMap.put("page", "pages/opening/paramsEnter/index");
        paramMap.put("auto_color", false);
        paramMap.put("is_hyaline", false);
        Map<String, Object> colorMap = Maps.newHashMap();
        colorMap.put("r", 0);
        colorMap.put("g", 0);
        colorMap.put("b", 0);
        paramMap.put("line_color", JSONObject.toJSON(colorMap));

        wxQRCode = PostMoths(getWXCodeUrl, JSONObject.toJSON(paramMap).toString());
        return wxQRCode;
    }

    public static String getAccessToken() {
        String accessToken = "";

        String accessTokenUrl = "https://api.weixin.qq.com/cgi-bin/token";//获取小程序TokenUrl
        String appID = "wxc37523fb55b9457b";
        String secret = "81a75ca16db5ea597de13133ff02932c";
        String getTokenUrl = accessTokenUrl + "?grant_type=client_credential&appid=" + appID + "&secret=" + secret;

        try {
            String result = HttpClientUtil.HttpGet(getTokenUrl);
            System.out.println("resultToken ======================== " + result);
            // 校验
            if (null != result) {
                JSONObject parse = JSONObject.parseObject(result, JSONObject.class);
                // 校验
                if (null != parse.get("access_token")) {
                    accessToken = parse.get("access_token").toString();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return accessToken;
    }

    //请求处理，返回二维码图片
    public static String PostMoths(String url, String param) {
        try {
            String result = HttpClientUtil.HttpPostJson(url, param, "/filedata/");
            System.out.println("result二维码 ======================== " + result);
            // 校验
            if (null != result) {
                JSONObject parse = JSONObject.parseObject(result, JSONObject.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        return "";
    }
}
