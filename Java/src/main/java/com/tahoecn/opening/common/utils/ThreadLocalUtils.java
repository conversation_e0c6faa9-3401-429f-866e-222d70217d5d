/**
 * 
 */
package com.tahoecn.opening.common.utils;

import com.tahoecn.opening.model.CsUcUser;
import com.tahoecn.opening.model.vo.BusLoginResponseVO;

/**
 * <AUTHOR>
 * @date 2018年9月5日 下午5:18:08
 * @desc
 */
public class ThreadLocalUtils {

	private static final ThreadLocal<BusLoginResponseVO> LOCAL = new ThreadLocal<BusLoginResponseVO>();

	public static void setUser(BusLoginResponseVO csUcUser) {
		LOCAL.set(csUcUser);
	}

	/**
	 * 获取对象
	 * 
	 * @return
	 */
	public static BusLoginResponseVO get() {
		return LOCAL.get();
	}

	/**
	 * 获取当前登陆用户名
	 * 
	 * @return
	 */
	public static String getUserName() {
		return LOCAL.get().getUserAccount();
	}

	/**
	 * 获取当前登陆用户中文名
	 * 
	 * @return
	 */
	public static String getRealName() {
		return LOCAL.get().getUserName();
	}

	/**
	 * 获取当前登陆用户ID
	 * 
	 * @return
	 */
	public static String getUserId() {
		return LOCAL.get().getFid();
	}

	/**
	 * 清理线程
	 */
	public static void remove() {
		LOCAL.remove();
	}

}
