package com.tahoecn.opening.common.utils;

import java.io.Serializable;

/**
 * @ProJectName: salary
 * @Author: zwc  <EMAIL>
 * @CreateTime: 2020-09-07 11:26
 * @Description: //TODO 结果响应类
 **/
public class ApiResult implements Serializable {

    private static final long serialVersionUID = -1283570981723235L;

    public static final Integer SUCCESS = 200;

    public static final String SUCCESS_MSG = "请求成功！";

    public static final Integer ERROR = 500;

    public static final String ERROR_MSG = "请求失败！";

    /**
     *  接口响应 code (默认值请参考状态码枚举ApiCodeEnum)
     */
    private Integer code;

    /**
     *  接口响应 提示信息
     */
    private String message;

    /**
     *  接口响应 数据信息
     */
    private Object data;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public ApiResult() {}

    public ApiResult(Integer code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public ApiResult(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:33 2022/6/20
     * @return com.tyzq.bpm.common.vo.ApiResult
     * @description // TODO 请求成功
     **/
    public static ApiResult getSuccessApiResponse() {
        return new ApiResult(SUCCESS, SUCCESS_MSG);
    }

    public static ApiResult getSuccessApiResponse(Object data) {
        return new ApiResult(SUCCESS, SUCCESS_MSG, data);
    }

    public static ApiResult getSuccessApiResponse(Integer code, String message) {
        return new ApiResult(code, message);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:33 2022/6/20
     * @return com.tyzq.bpm.common.vo.ApiResult
     * @description // TODO 请求失败
     **/
    public static ApiResult getFailedApiResponse() {
        return new ApiResult(ERROR, ERROR_MSG);
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 13:33 2022/6/20
     * @return com.tyzq.bpm.common.vo.ApiResult
     * @description // TODO 请求失败
     **/
    public static ApiResult getFailedApiResponse(Object data) {
        return new ApiResult(ERROR, ERROR_MSG, data);
    }

    public static ApiResult getFailedApiResponse(Integer code, String message) {
        return new ApiResult(code, message);
    }

    public static ApiResult getFailedApiResponse(Integer code, String message, Object data) {
        return new ApiResult(code, message, data);
    }

    @Override
    public String toString() {
        return "ApiResult{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
