package com.tahoecn.opening.common.utils;

import java.util.UUID;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.common.utils
 * @ClassName: UUIDUtil
 * @Description:// TODO UUID
 * @Date: 2022/8/12 10:37
 * @Version: 1.0
 */
public class UUIDUtil {

    /**
     * <AUTHOR>  <EMAIL>
     * @date 10:38 2022/8/12
     * @return java.lang.String
     * @description // TODO 获取32位UUID
     **/
    public static String getUUID32() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 10:38 2022/8/12
     * @return java.lang.String
     * @description // TODO 获取36位UUID
     **/
    public static String getUUID36() {
        return UUID.randomUUID().toString();
    }
}
