package com.tahoecn.opening.common.utils;


import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.Key;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;


public class RSAUtil {


	public static final String KEY_ALGORITHM = "RSA";

	public static final String SIGNATURE_ALGORITHM = "MD5withRSA";

	// ��Կ
	public static final String PASSWORD_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCgKEtllxrAZ1izOvtQbsv89sTp1KwNiqe3zoVEWu49CV80CntN2P42xi+kwx+DvrJx75WW0LpXOKkuAUUoBWrBgMh+Kx5+XwasPX36WfWtQ3OpJzmUPTvDD60w/gVpVc1xCYzRGNqSP/OBZyT/SxJDitD4UpTranpklRsIlv/+vwIDAQAB";
	// ˽Կ
	public static final String PASSWORD_PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKAoS2WXGsBnWLM6+1Buy/z2xOnUrA2Kp7fOhURa7j0JXzQKe03Y/jbGL6TDH4O+snHvlZbQulc4qS4BRSgFasGAyH4rHn5fBqw9ffpZ9a1Dc6knOZQ9O8MPrTD+BWlVzXEJjNEY2pI/84FnJP9LEkOK0PhSlOtqemSVGwiW//6/AgMBAAECgYBuhaASSUZyxF7XwLoxdWRb+0dEPjlFVwwKsgARfdauVEtYtGsG+9ninr2+K9ERr4WhTr6iRwRwsG8/iizZQ3O5X1aLK7UeZmzxspd0c+2LSp+kGWmmt2Lutx5S7FH1igBT1XyillhxXhSSj+zP4bhBX5GPPawS4qGavrDKdBh08QJBAOd7YqG7rjm0Ns2Ozr6aq9W6US2UnGp329PR60eqlSHx518cmRZFjR1eF46uvfRjEAcpheYSD2hB0hLQoDCor8sCQQCxHvMEhaK3rsTSefKWPQskGUjMlqtD7mD3p0ZsAVxHv8qGPiJ1+G5CGfdsFDeWmf2vLYE2nbtsTntYPG5q4CZdAkEAl1w5qTGUgczSDOU1ULLIa29yB1DmWkPXbREVPH5RF6oatTl47HQ8Cpjs5HJkaPmhkF1ZXZAvnOd1GrJqHIcgswJAexv37fy0zNc9Fs6MPsDPLZglcY6IG/9khr5NxGRtr7EvI3+slDLI8Ej8ILMssBD4svn+Beg8jVs/gGUAmmiSQQJAcc3MX5fIuK8PyvKFgwBDwk2E6QLx55NYiCQfHScBzUwXGInYR8VbxEhDWKO8/K5UEQIlu95W46y2pOT++D7/zw==";

	public static byte[] decryptBASE64(String data) {

		return Base64.decodeBase64(data);
	}

	public static String encryptBASE64(byte[] bytes) {

		return Base64.encodeBase64String(bytes);
	}

	public static String sign(byte[] data, String privateKey) throws Exception {

		// ������base64�����˽Կ
		byte[] keyBytes = decryptBASE64(privateKey);

		// ����PKCS8EncodedKeySpec����
		PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);

		// KEY_ALGORITHM ָ���ļ����㷨
		KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);

		// ȡ˽Կ�׶���
		PrivateKey priKey = keyFactory.generatePrivate(pkcs8KeySpec);

		// ��˽Կ����Ϣ��������ǩ��
		Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
		signature.initSign(priKey);
		signature.update(data);
		return encryptBASE64(signature.sign());
	}

	public static byte[] decryptByPrivateKey(byte[] data) throws Exception {

		return decryptByPrivateKey(PASSWORD_PRIVATE_KEY, data);
	}

	public static byte[] decryptByPrivateKey(String privateKeyString, byte[] data) throws Exception {

		// 1 ��˽Կ����
		byte[] keyBytes = decryptBASE64(privateKeyString);

		// 2 ȡ��˽Կ
		PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
		KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
		Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);

		// 3 �����ݽ���
		Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
		cipher.init(Cipher.DECRYPT_MODE, privateKey);
		return cipher.doFinal(data);
	}

	public static byte[] decryptByPrivateKey(String data) throws Exception {

		try {
			return decryptByPrivateKey(decryptBASE64(data));
		} catch (Exception e) {
			throw new Exception("RSA���ܳ���");
		}
	}

	public static byte[] decryptByPublicKey(byte[] data) throws Exception {

		// ����Կ����
		byte[] keyBytes = decryptBASE64(PASSWORD_PUBLIC_KEY);

		// ȡ�ù�Կ
		X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
		KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
		Key publicKey = keyFactory.generatePublic(x509KeySpec);

		// �����ݽ���
		Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
		cipher.init(Cipher.DECRYPT_MODE, publicKey);
		return cipher.doFinal(data);
	}

	public static byte[] encryptByPublicKey(String publicKeyString, String data) throws Exception {

		// �Թ�Կ����
		byte[] keyBytes = decryptBASE64(publicKeyString);

		// ȡ�ù�Կ
		X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
		KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
		Key publicKey = keyFactory.generatePublic(x509KeySpec);
		// �����ݼ���
		Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
		cipher.init(Cipher.ENCRYPT_MODE, publicKey);
		return cipher.doFinal(data.getBytes());
	}

	public static byte[] encryptByPrivateKey(byte[] data) throws Exception {

		// ����Կ����
		byte[] keyBytes = decryptBASE64(PASSWORD_PRIVATE_KEY);

		// ȡ��˽Կ
		PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
		KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
		Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);

		// �����ݼ���
		Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
		cipher.init(Cipher.ENCRYPT_MODE, privateKey);
		return cipher.doFinal(data);
	}

	public static void main(String[] args) throws Exception {
		byte[] bytes = encryptByPublicKey(PASSWORD_PUBLIC_KEY, "Aa123456!@");
		String encryptBASE64 = encryptBASE64(bytes);
		System.out.println(encryptBASE64);
	}
}