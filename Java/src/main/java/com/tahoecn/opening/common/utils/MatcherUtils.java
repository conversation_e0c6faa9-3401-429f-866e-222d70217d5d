package com.tahoecn.opening.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author:-----wenchaoZheng <EMAIL>
 * @ProjectName:custom
 * @Package:----com.tahoecn.xkc.common.utils
 * @ClassName:--MatcherUtils
 * @Date:-------2022/4/12 19:10
 * @Version:----1.0
 * @Description://TODO 正则表达式
 */
public class MatcherUtils {

    /**
     * 匹配数字
     */
    public static final String PATTERN = "[^0-9]";

    /**
    * @Author:  wenchaozheng <EMAIL>
    * @Date: 19:20 2022/4/12
     * @param str
     * @param pattern
    * @return: 
    * @Description: //TODO  根据字符和正则规则，转换数据
    **/
    public static long convertStringToLong(Object str, String pattern) {
        long result = 1;
        // 校验
        if (null == str || "".equals(str.toString().trim())) {
            return result;
        }
        try {
            Pattern p = null;
            // 校验
            if (null == pattern || "".equals(pattern.trim())) {
                p = Pattern.compile(PATTERN);
            } else {
                p = Pattern.compile(pattern);
            }
            Matcher matcher = p.matcher(str.toString());
            String trim = matcher.replaceAll("").trim();
            return Long.valueOf(trim).longValue();
        } catch (Exception e) {
            e.printStackTrace();
            return result;
        }
    }
}
