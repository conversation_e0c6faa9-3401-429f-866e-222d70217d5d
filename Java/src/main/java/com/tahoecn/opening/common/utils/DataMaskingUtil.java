package com.tahoecn.opening.common.utils;

import org.apache.commons.lang3.StringUtils;

public class DataMaskingUtil {

    // 名字脱敏
    public static String maskName(String name) {
        if (StringUtils.isBlank(name)) return name;

        int length = name.length();

        if (length == 1) {
            return "*";
        } else if (length == 2) {
            return name.charAt(0) + "*";
        } else {
            // 多字姓名：保留首尾，中间替换为 *
            return name.charAt(0) + "*" + name.charAt(length - 1);
        }
    }

    // 手机号脱敏：保留前3位和后4位，中间用 **** 替代
    public static String maskPhone(String phone) {
        if (StringUtils.isBlank(phone) || phone.length() < 11) {
            return phone;
        }
        return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

}
