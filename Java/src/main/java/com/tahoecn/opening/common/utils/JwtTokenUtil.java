package com.tahoecn.opening.common.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;

import java.util.Date;

/**
 * <p>
 * jwt token������
 * </p>
 * 
 * <pre>
 *     jwt��claim��һ��������¼�������:
 *         1. iss -- token�ķ�����
 *         2. sub -- ��JWT��������û�
 *         3. aud -- ���ո�JWT��һ��
 *         4. exp -- token��ʧЧʱ��
 *         5. nbf -- �ڴ�ʱ���֮ǰ,���ᱻ����
 *         6. iat -- jwt����ʱ��
 *         7. jti -- jwtΨһ��ʶ,��ֹ�ظ�ʹ��
 * </pre>
 */
public class JwtTokenUtil {

	public static String jwtSecret = "nFXa66wzfzhY7lkm";

	/**
	 * ��token�л�ȡ�û���
	 */
	public static String getSubjectFromToken(String token, String jwtSecret) {

		return getClaimFromToken(token, jwtSecret).getSubject();
	}

	/**
	 * ��ȡjwt����ʱ��
	 */
	public Date getIssuedAtDateFromToken(String token, String jwtSecret) {

		return getClaimFromToken(token, jwtSecret).getIssuedAt();
	}

	/**
	 * ��ȡjwtʧЧʱ��
	 */
	public static Date getExpirationDateFromToken(String token, String jwtSecret) {

		return getClaimFromToken(token, jwtSecret).getExpiration();
	}

	/**
	 * ��ȡjwt������
	 */
	public String getAudienceFromToken(String token, String jwtSecret) {

		return getClaimFromToken(token, jwtSecret).getAudience();
	}

	/**
	 * ��ȡ˽�е�jwt claim
	 */
	public String getPrivateClaimFromToken(String token, String key, String jwtSecret) {

		return getClaimFromToken(token, jwtSecret).get(key).toString();
	}

	/**
	 * ��ȡjwt��payload����
	 */
	public static Claims getClaimFromToken(String token, String jwtSecret) {

		return Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token).getBody();
	}

	/**
	 * ����token�Ƿ���ȷ(true-��ȷ, false-����)<br>
	 */
	public static Boolean checkToken(String token, String jwtSecret) throws JwtException {

		try {
			Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token).getBody();
			return true;
		} catch (JwtException e) {
			return false;
		}
	}

	/**
	 * <pre>
	 *  ��֤token�Ƿ�ʧЧ
	 *  true:����   false:û����
	 * </pre>
	 */
	public static Boolean isTokenExpired(String token, String jwtSecret) {

		try {
			final Date expiration = getExpirationDateFromToken(token, jwtSecret);
			return expiration.before(new Date());
		} catch (ExpiredJwtException expiredJwtException) {
			return true;
		}
	}

	public static void main(String[] args) {
		String token="eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************.KnQ8iC9qMDHGZR_kzR7TerVe_sbtfVcA6ypOzgtoJlfpXXpxf52PnoDgeBJOfvp35lQvUHxSKt3dJBlNUII8Ww";

		//��token�н������ʺ�
		String account=getSubjectFromToken(token,jwtSecret);
		
		System.out.println(account);
		//��֤token�Ƿ�ʧЧ
		Boolean flag=isTokenExpired(token,jwtSecret);
		
		System.out.println(flag);
		
		//��ȡjwtʧЧʱ��
		Date date=getExpirationDateFromToken(token,jwtSecret);
		
		System.out.println(date);
	}

}