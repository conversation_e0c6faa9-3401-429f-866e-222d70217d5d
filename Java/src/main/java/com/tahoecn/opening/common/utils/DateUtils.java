package com.tahoecn.opening.common.utils;

import com.tahoecn.opening.model.OpActivity;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: tyzq-dzkp
 * @Package: com.tahoecn.opening.common.utils
 * @ClassName: DateUtils
 * @Description:// TODO 日期工具类
 * @Date: 2022/9/19 14:56
 * @Version: 1.0
 */
public class DateUtils {

    /**
     *  日期格式：yyyy-MM-dd HH:mm:ss
     */
    public static final String PATTERN_SECOND = "yyyy-MM-dd HH:mm:ss";

    /**
     *  日期格式：yyyy-MM-dd HH:mm
     */
    public static final String PATTERN_MINUTE = "yyyy-MM-dd HH:mm";

    /**
     *  日期格式：yyyy-MM-dd HH
     */
    public static final String PATTERN_HOUR = "yyyy-MM-dd HH";

    /**
     *  日期格式：yyyy-MM-dd
     */
    public static final String PATTERN_DAY = "yyyy-MM-dd";

    public static void main(String[] args) {
        //获取当前时间
        LocalDateTime nowTime= LocalDateTime.now();
        System.out.println("nowTime = " + nowTime);
        //自定义时间
        LocalDateTime endTime = LocalDateTime.of(2021, 10, 23, 14, 10, 10);
        System.out.println("endTime = " + endTime);
        //比较  如今的时间 在 设定的时间 之后  返回的类型是Boolean类型
        System.out.println(nowTime.isAfter(endTime));
        //比较   如今的时间 在 设定的时间 之前  返回的类型是Boolean类型
        System.out.println(nowTime.isBefore(endTime));
        //比较   如今的时间 和 设定的时候  相等  返回类型是Boolean类型
        System.out.println(nowTime.equals(endTime));


    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 15:14 2022/9/19
     * @param activity  活动对象
     * @param nowTime   当前时间
     * @return String
     * @description // TODO 获取当前查询订单类型，y为正式，n为模拟
     **/
    public static String regular(OpActivity activity, LocalDateTime nowTime) {
        // 校验：活动为空 || 是否模拟为空 || 是否模拟为n
        if (null == activity || StringUtils.isBlank(activity.getIsSimulation()) || "n".equalsIgnoreCase(activity.getIsSimulation())) {
            return "y";
        }
        // 校验模拟时间是否在当前时间以后，在的话返回false，否则返回true
        return nowTime.isAfter(activity.getSimulationEnd()) ? "y" : "n";
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 14:15 2023/10/23
     * @param paramDate         时间
     * @param num               数量
     * @return java.util.Date
     * @description // TODO 给时间提前或延后几分钟
     **/
    public static Date addDateMinute(Date paramDate, int num) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(paramDate);
        calendar.add(Calendar.MINUTE, num);
        return calendar.getTime();
    }

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/7 10:21
     * @Param        paramDate      时间
     * @Param        num            数量
     * @Return       java.util.Date
     * @Description  TODO 给时间提前或延后几秒钟
     **/
    public static Date addDateSecond(Date paramDate, int num) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(paramDate);
        calendar.add(Calendar.SECOND, num);
        return calendar.getTime();
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 17:37 2024/8/13
     * @param beginDate         开始时间
     * @param endDate           结束时间
     * @param checkMinutes      时间间隔
     * @return int
     * @description // TODO     获取排序号
     **/
    public static int getSortNum(String beginDate, String endDate, Integer checkMinutes) {
        SimpleDateFormat formatter = new SimpleDateFormat(PATTERN_MINUTE);
        int sortNum = 0;
        try {
            Date begin = formatter.parse(beginDate);
            Date end = formatter.parse(endDate);
            long times = (end.getTime() - begin.getTime()) / (1000 * 60);
            sortNum = Integer.valueOf(times + "");
            sortNum = sortNum / checkMinutes;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sortNum;
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 17:37 2024/8/13
     * @param beginDate         开始时间
     * @param endDate           结束时间
     * @return int
     * @description // TODO     获取两个时间中间分钟数
     **/
    public static int getMinutes(String beginDate, String endDate) {
        SimpleDateFormat formatter = new SimpleDateFormat(PATTERN_MINUTE);
        int sortNum = 0;
        try {
            Date begin = formatter.parse(beginDate);
            Date end = formatter.parse(endDate);
            long times = (end.getTime() - begin.getTime()) / (1000 * 60);
            sortNum = Integer.valueOf(times + "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sortNum;
    }


    /**
     * <AUTHOR>  <EMAIL>
     * @date 17:37 2024/8/13
     * @param beginDate         开始时间
     * @param endDate           结束时间
     * @return int
     * @description // TODO     获取两个时间中间分钟数
     **/
    public static int getMinutes(Date beginDate, Date endDate) {
        int sortNum = 0;
        try {
            long times = (endDate.getTime() - beginDate.getTime()) / (1000 * 60);
            sortNum = Integer.valueOf(times + "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sortNum;
    }

    /**
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/7 10:19
     * @Param        beginDate  开始时间
     * @Param        endDate    结束时间
     * @Return       int
     * @Description  TODO       获取两个时间中间秒数
     **/
    public static int getSeconds(Date beginDate, Date endDate) {
        int sortNum = 0;
        try {
            long times = (endDate.getTime() - beginDate.getTime()) / 1000;
            sortNum = Integer.valueOf(times + "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sortNum;
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 10:41 2024/5/16
     * @param dateStr			时间字符串
     * @return java.util.Date
     * @description // TODO
     **/
    public static Date getDate(String dateStr, String pattern) {
        Date dtReturn = null;

        if (StringUtils.isNotBlank(dateStr)) {
            if (StringUtils.isBlank(pattern)) {
                pattern = PATTERN_SECOND;
            }

            try {
                SimpleDateFormat formatter = new SimpleDateFormat(pattern);
                dtReturn = formatter.parse(dateStr);
            } catch (Exception e) {
            }
        }

        return dtReturn;
    }

    /**
     * <AUTHOR>  <EMAIL>
     * @date 10:14 2024/8/14
     * @param startDate
     * @param pattern 
     * @return java.lang.String
     * @description // TODO         获取日期
     **/
    public static String getDateString(Date startDate, String pattern) {
        String sReturn = null;

        if (startDate != null && StringUtils.isNotBlank(pattern)) {
            SimpleDateFormat formatter = new SimpleDateFormat(pattern);
            sReturn = formatter.format(startDate);
        }

        return sReturn;
    }

    /** 
     * 
     * <AUTHOR> <EMAIL>
     * @Date         2025/5/7 10:26
     * @Param        startDate  时间
     * @Return       java.lang.String
     * @Description  TODO   时间类型转字符串类型
     **/
    public static String getDateString(Date startDate) {
        String sReturn = null;
        if (startDate != null) {
            SimpleDateFormat formatter = new SimpleDateFormat(PATTERN_SECOND);
            sReturn = formatter.format(startDate);
        }
        return sReturn;
    }
}
