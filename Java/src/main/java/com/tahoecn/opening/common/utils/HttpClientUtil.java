/**
 * 
 */
package com.tahoecn.opening.common.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.AllowAllHostnameVerifier;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.*;
import java.net.URL;
import java.nio.charset.Charset;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version
 */
public class HttpClientUtil {

	private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

	public static String HttpGet(String url) {
		String result = null;
		CloseableHttpClient httpclient = HttpClients.createDefault();
		try {
			// 创建httpget.
			HttpGet httpget = new HttpGet(url);
			logger.debug("executing request " + httpget.getURI());
			//修改org.apache.http的主机名验证解决问题
			SSLSocketFactory.getSocketFactory().setHostnameVerifier(new AllowAllHostnameVerifier());
			// 执行get请求.
			CloseableHttpResponse response = httpclient.execute(httpget);
			try {
				// 获取响应实体
				HttpEntity entity = response.getEntity();
				logger.debug("--------------------------------------");
				// 打印响应状态
				logger.debug(response.getStatusLine().toString());
				if (entity != null) {
					// 打印响应内容长度
					logger.debug("Response content length: " + entity.getContentLength());
					// 打印响应内容
					result = EntityUtils.toString(entity);
					logger.debug("Response content: " + result);
				}
				logger.debug("------------------------------------");
			} finally {
				response.close();
			}
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (ParseException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			// 关闭连接,释放资源
			try {
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return result;
	}

	public static String HttpPost(String url,Map<String, String> params) {
		String result = null;
		// 创建默认的httpClient实例.
		CloseableHttpClient httpclient = HttpClients.createDefault();
		// 创建httppost
		HttpPost post = new HttpPost(url);
		// 创建参数队列
		List<BasicNameValuePair> formparams = new ArrayList<>();
		for (Map.Entry<String, String> entry : params.entrySet()) {
			logger.debug("key= " + entry.getKey() + " and value= " + entry.getValue());
			formparams.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
		}
		UrlEncodedFormEntity uefEntity;
		try {
			uefEntity = new UrlEncodedFormEntity(formparams, "UTF-8");
			post.setEntity(uefEntity);
			logger.debug("executing request " + post.getURI());
			CloseableHttpResponse response = httpclient.execute(post);
			try {
				HttpEntity entity = response.getEntity();
				if (entity != null) {
					result = EntityUtils.toString(entity, "UTF-8");
					logger.debug("--------------------------------------");
					logger.debug("Response content: " + result);
					logger.debug("--------------------------------------");
					return result;
				}
			} finally {
				response.close();
			}
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			// 关闭连接,释放资源
			try {
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return result;
	}

	public static String HttpPostJson(String url, JSONObject json){
		String result = null;
		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpPost post = new HttpPost(url);
		// 构造消息头
		post.setHeader("Content-type", "application/json; charset=utf-8");
		post.setHeader("Connection", "Close");
		// 构建消息实体
		StringEntity entity = new StringEntity(json.toString(), Charset.forName("UTF-8"));
		entity.setContentEncoding("UTF-8");
		// 发送Json格式的数据请求
		entity.setContentType("application/json");
		post.setEntity(entity);
		try {
			CloseableHttpResponse response = httpclient.execute(post);
			HttpEntity httpEntity = response.getEntity();
			if (entity != null) {
				result = EntityUtils.toString(httpEntity, "UTF-8");
			}
		} catch(Exception e) {
			e.printStackTrace();
		} finally {
			// 关闭连接,释放资源
			try {
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return result;
	}

	public static String HttpPostJson(String url, Map<String, Object> paramMap){
		String result = null;
		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpPost post = new HttpPost(url);
		// 构造消息头
		post.setHeader("Content-type", "application/json; charset=utf-8");
		post.setHeader("Connection", "Close");
		// 构建消息实体
		StringEntity entity = new StringEntity(JSONObject.toJSON(paramMap).toString(), Charset.forName("UTF-8"));
		entity.setContentEncoding("UTF-8");
		// 发送Json格式的数据请求
		entity.setContentType("application/json");
		post.setEntity(entity);
		try {
			CloseableHttpResponse response = httpclient.execute(post);
			HttpEntity httpEntity = response.getEntity();
			if (entity != null) {
				result = EntityUtils.toString(httpEntity, "UTF-8");
			}
		} catch(Exception e) {
			e.printStackTrace();
		} finally {
			// 关闭连接,释放资源
			try {
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return result;
	}

	/**
	 * <AUTHOR>  <EMAIL>
	 * @date 14:34 2023/9/25
	 * @param url				请求地址
	 * @param paramMap 			入参
	 * @return java.lang.String
	 * @description // TODO 	发起HttpsPost请求
	 **/
	public static String HttpsPostJson(String url, Map<String, Object> paramMap) {
		String result = null;
		BufferedReader br = null;
		PrintStream ps = null;
		HttpsURLConnection connection = null;
		try {
			// 创建信任管理器
			TrustManager[] trustAllCerts = new TrustManager[] { new X509TrustManager() {
				public void checkClientTrusted(X509Certificate[] certs, String authType) {}
				public void checkServerTrusted(X509Certificate[] certs, String authType) {}
				public X509Certificate[] getAcceptedIssuers() { return null; }
			}};

			// 获取默认的SSL上下文
			SSLContext sslContext = SSLContext.getInstance("TLS");
			sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
			HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
			// 创建URL对象
			URL sendUrl = new URL(url);
			// 打开HTTPS连接
			connection = (HttpsURLConnection) sendUrl.openConnection();
			// 设置请求方法为GET
			connection.setRequestMethod("POST");
			connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
			connection.setDoOutput(true);
			// 写入数据
			ps = new PrintStream(connection.getOutputStream());
			ps.print(JSONObject.toJSON(paramMap).toString());
			ps.close();

			br = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
			String line;
			StringBuilder sb = new StringBuilder();
			while ((line = br.readLine()) != null ) {
				sb.append(line);
			}
			br.close();
			connection.disconnect();
			result = sb.toString();
		} catch(Exception e) {
			e.printStackTrace();
		} finally {
			// 关闭连接,释放资源
			try {
				if (null != ps) {
					ps.close();
				}
				if (null != br) {
					br.close();
				}
				if (null != connection) {
					connection.disconnect();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return result;
	}

	public static String HttpPostJson(String url, String json, String filePath){
		String result = null;
		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpPost post = new HttpPost(url);
		// 构造消息头
		post.setHeader("Content-type", "application/json; charset=utf-8");
		post.setHeader("Connection", "Close");
		// 构建消息实体
		StringEntity entity = new StringEntity(json, Charset.forName("UTF-8"));
		entity.setContentEncoding("UTF-8");
		// 发送Json格式的数据请求
		entity.setContentType("application/json");
		post.setEntity(entity);
		try{
			CloseableHttpResponse response = httpclient.execute(post);
			HttpEntity httpEntity = response.getEntity();
			InputStream content = httpEntity.getContent();
			DataInputStream dataInputStream = new DataInputStream(content);
			// 声明一个用来存储文件的文件夹
			if (StringUtils.isBlank(filePath)) {
				filePath = "/fileData";
			}
			String fileName = "/wxcode/" + UUID.randomUUID().toString().toLowerCase() + ".jpg";
			filePath = filePath + fileName;
			File file = new File(filePath);
			File fileParent = file.getParentFile();
			if(!fileParent.exists()){
				fileParent.mkdirs();
			}
			FileOutputStream fileOutputStream = new FileOutputStream(file);
			ByteArrayOutputStream output = new ByteArrayOutputStream();

			byte[] buffer = new byte[1024];
			int length;

			while ((length = dataInputStream.read(buffer)) > 0) {
				output.write(buffer, 0, length);
			}
			fileOutputStream.write(output.toByteArray());
			content.close();
			dataInputStream.close();
			dataInputStream.close();
			fileOutputStream.close();

			return fileName;

		}catch(Exception e){
			e.printStackTrace();
		}finally{
			// 关闭连接,释放资源
			try {
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return result;
	}
}
