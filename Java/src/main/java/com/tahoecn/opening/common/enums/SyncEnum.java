package com.tahoecn.opening.common.enums;

/**
 * @Author: zwc  <EMAIL>
 * @ProjectName: chengben
 * @Package: com.telehot.co.appframe.config
 * @ClassName: SyncEnum
 * @Description:// TODO 同步数据枚举类
 * @Date: 2023/11/21 9:40
 * @Version: 1.0
 */
public enum SyncEnum {


    DELETE_TRUE(1, "1", "删除"),
    DELETE_FALSE(0, "0", "未删除"),

    USE_TRUE(0, "1", "启用"),
    USE_FALSE(1, "2", "禁用"),


    ORG_TYPE_01(0, "1", "集团"),
    ORG_TYPE_02(0, "2", "公司"),
    ORG_TYPE_03(0, "3", "部门"),
    ORG_TYPE_04(0, "4", "项目"),
    ORG_TYPE_05(0, "5", "分期"),
    ORG_TYPE_06(0, "6", "板块"),

    CONSTANT_001(0, "company", "公司"),
    CONSTANT_002(0, "department", "部门"),
    CONSTANT_003(0, "user", "用户"),
    CONSTANT_004(0, "role", "角色"),
    CONSTANT_005(0, "business", "板块"),

    DICT_001(0, "HOUSE_TYPE", "精装毛坯"),
    DICT_002(1, "PROPERTY_TYPE", "物业属性"),


    NONE(10086, "ALL", "所有");


    /**
     *  枚举数值
     */
    private int num;

    /**
     *  枚举编码
     */
    private String code;

    /**
     *  枚举名称
     */
    private String name;

    SyncEnum(int num, String code, String name) {
        this.num = num;
        this.code = code;
        this.name = name;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
