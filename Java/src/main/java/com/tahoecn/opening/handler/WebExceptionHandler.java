package com.tahoecn.opening.handler;

import com.tahoecn.log.Log;
import com.tahoecn.log.LogFactory;
import com.tahoecn.opening.converter.ResponseMessage;
import org.springframework.dao.DataAccessException;
import org.springframework.transaction.TransactionTimedOutException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>Description: [统一异常处理类]<p>
 * Created on 2018年9月11日 下午6:15:22
 *
 * <AUTHOR> href="mailto: <EMAIL>">息阳</a>
 * @version 1.0
 * Copyright (c) 2018年 北京柯莱特科技有限公司
 */
//@ControllerAdvice
@ResponseBody
public class WebExceptionHandler {

    private static final Log log = LogFactory.get();


    /** 数据库抛出的其他异常 */
    @ExceptionHandler(value = DataAccessException.class)
    public final ResponseMessage handlerDataAccessException(Throwable e) {
        DataAccessException exception = (DataAccessException) e;
        log.error("数据库抛出的其他异常:", exception);
        return ResponseMessage.error("", 000);
    }

    /**
     * 运行超时
     */
    @ExceptionHandler(value = TransactionTimedOutException.class)
    public final ResponseMessage handleTransactionTimedOutException(Exception e) {
        log.warn(e.getMessage(), e);
        return ResponseMessage.error("运行超时", 000);
    }

    /** 运行时异常 */
    @ExceptionHandler(value = RuntimeException.class)
    public final ResponseMessage handleRuntimeException(Exception e) {
        log.warn(e.getMessage(), e);
        return ResponseMessage.error(e.getMessage(), 000);
    }

    /** 空指针异常 */
    @ExceptionHandler(NullPointerException.class)
    public final ResponseMessage handlerNullPointerException(NullPointerException e) {
        log.warn("发生空指针异常,异常内容为:{}", e);
        return ResponseMessage.error("空指针异常", 000);
    }

    /** 数据类型转换异常 */
    @ExceptionHandler(BindException.class)
    public final ResponseMessage handlerBeanPropertyBindingResult(BindException e) {
        log.error(e.getMessage(), e);
        return ResponseMessage.error("数据绑定异常", 000);
    }

    /** 其他异常捕获并处理 */
    @ExceptionHandler({Exception.class})
    public final ResponseMessage handleException(Exception exception) {
        log.error(exception.getMessage(), exception);
        return ResponseMessage.error("服务器内部错误", 000);
    }

}
