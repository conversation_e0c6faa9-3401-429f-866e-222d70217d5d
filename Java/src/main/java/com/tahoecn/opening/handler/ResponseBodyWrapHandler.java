package com.tahoecn.opening.handler;

import com.alibaba.fastjson.JSONObject;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.utils.ApiResult;
import com.tahoecn.opening.converter.ResponseMessage;
import org.springframework.core.MethodParameter;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;

/**
 * ResponseBody 统一参数返回处理
 *
 * <AUTHOR>
 */
public class ResponseBodyWrapHandler implements HandlerMethodReturnValueHandler {

    private final HandlerMethodReturnValueHandler delegate;

    public ResponseBodyWrapHandler(HandlerMethodReturnValueHandler delegate) {
        this.delegate = delegate;
    }

    /** 若返回值为true则对返回值进行包装,若为false则不处理 */
    @Override
    public boolean supportsReturnType(MethodParameter returnType) {
        return delegate.supportsReturnType(returnType);
    }

    @Override
    public void handleReturnValue(Object returnValue, MethodParameter returnType, ModelAndViewContainer mavContainer, NativeWebRequest webRequest) throws Exception {
        Object resultModel = changeResult(returnValue);
        delegate.handleReturnValue(resultModel, returnType, mavContainer, webRequest);
    }

    /**
     * 对返回值类型进行判断并包装为Http最外层获取的数据格式
     *
     * @param body
     * @return
     * <AUTHOR>
     */
    private Object changeResult(Object body) {
        // 对返回值类型进行判断,如果不需要包装则直接返回
        if (isResult(body)) {
            return body;
        }

        return ResponseMessage.ok(body);
    }

    private boolean isResult(Object body) {
        return body instanceof JSONObject || body instanceof ResponseMessage || body instanceof JSONResult || body instanceof ApiResult;
    }

    /**
     * 获取请求类型
     *
     * @return 求情类型
     */
    private String getRequestMethodStr() {
        String requestMethodStr = "";

        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        requestMethodStr = request.getMethod();

        return requestMethodStr;
    }
}
