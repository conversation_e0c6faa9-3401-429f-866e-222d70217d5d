######################################
##  电子开盘系统 develop
######################################
spring.application.name=opening
server.servlet.context-path= /opening

#server.port=8181
server.port=443
server.ssl.key-store=classpath:open.sky-dome.com.cn.jks
server.ssl.enabled=true
server.ssl.key-store-password=lx2ocowm
server.ssl.key-store-type=JKS

spring.jackson.time-zone=GMT+8
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss

#Rdis Sentinel 配置
spring.redis.database=8
spring.redis.host=************
spring.redis.port=6379
spring.redis.password=Tyzq@123!

spring.task.execution.pool.core-size=32
spring.task.execution.pool.max-size=64
spring.task.execution.pool.queue-capacity=1000
spring.task.execution.pool.keep-alive=10000
spring.task.execution.thread-name-prefix=myThread



spring.rabbitmq.addresses=*************:5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/
spring.rabbitmq.template.exchange=exchange_fanout
spring.rabbitmq.listener.concurrency=10
spring.rabbitmq.listener.max-concurrency=20
spring.rabbitmq.listener.prefetch=5



# MULTIPART (MultipartProperties)
tahoe.application.mappingPath=/uploadfiles/**
tahoe.application.physicalPath=/filedata/
server.tomcat.basedir=/home/<USER>
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=10MB

#MySQL 多数据源
spring.datasource.dynamic.primary=master
spring.datasource.dynamic.datasource.master.url=************************************************************************************************
#spring.datasource.dynamic.datasource.master.url=*************************************************************************************************
spring.datasource.dynamic.datasource.master.username=root
spring.datasource.dynamic.datasource.master.password=fk123456
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver
spring.quartz.job-store-type=jdbc
spring.quartz.jdbc.initialize-schema=never
spring.quartz.properties.org.quartz.scheduler.instanceName=PScheduler
spring.quartz.properties.org.quartz.jobStore.tablePrefix=QRTZ_


#spring.datasource.dynamic.datasource.slave_01.url=**************************************************************************
#spring.datasource.dynamic.datasource.slave_01.username=root
#spring.datasource.dynamic.datasource.slave_01.password=mysql
#spring.datasource.dynamic.datasource.slave_01.driver-class-name=com.mysql.jdbc.Driver
#
#spring.datasource.dynamic.datasource.slave_02.url=**************************************************************************
#spring.datasource.dynamic.datasource.slave_02.username=root
#spring.datasource.dynamic.datasource.slave_02.password=mysql
#spring.datasource.dynamic.datasource.slave_02.driver-class-name=com.mysql.jdbc.Driver


#MybatisPlus 如果使用注解的方式，这里可以不用配置
mybatis-plus.mapper-locations=classpath*:com/tahoecn/opening/mapper/xml/*Mapper.xml 
mybatis-plus.typeAliasesPackage=com.tahoecn.opening.model

bind_host=127.0.0.1
uc_api_url=http://zwcucapi.tzdctest.com:8080
uc_sysId=ZXKAIPAN
uc_priv_key=luanqibazaod_zhend_buzhid


#sms
sms_uri=http://hy.qixinhl.com/msg/
sms_account=taihe99
sms_pwd=taihe@123
sms_needstatus=true
sms_product=459367
sms_extno=001
#ceshi off pro on
sms_switch=on
#环境dev不发短信
sms_environment=prod

uc_api_url_userlist=http://zwcucapi.tzdctest.com:8080/v3/user/list
uc_api_url_orglist=http://zwcucapi.tzdctest.com:8080/v3/org/list

APP_ID=wxccdcd04e02d8c7f6
APP_SECRET=8e330d9442355dfd967ac5ddec447a5f

#活动开始前不许修改数据的时间
control_time=0.05 
#活动开始前不许修改白名单数据的时间
control_white_minutes=3

const_HOST=https://zwcfxapi.tzdc.com
#system_HOST=http://dev.tyzq.com:8181/opening/uploadfiles/
system_HOST=https://open.sky-dome.com.cn/opening/uploadfiles/


sso.loginUrl=http://zwcucsso.tzdctest.com:9988/login?sysId=ZXKAIPAN
sso.logoutUrl=http://zwcucsso.tzdctest.com:9988/logout
sso.ucwebUrl=http://zwckp.tzdctest.com
sso.cookieDomain=tzdctest.com
sso.ucssowebUrl=http://zwcucsso.tzdctest.com:9988

weixin.appId=wxccdcd04e02d8c7f6
weixin.appSecret=8e330d9442355dfd967ac5ddec447a5f

# 同步数据地址
syncData.address=http://***********:8280
syncData.key=dbbf3f16-a82c-ad42-0521-9cec6770b421
#诚东统一登录平台
chengdong.url: http://fengmai.javaguns.com:10066
chengdong.accessCode: /river-system/auth/accessCode
chengdong.login: /river-system/auth/login
chengdong.clientId: 04011652544780835

# ???
# ????appId?appSecret
electron.sign.basePath=http://************:18088/contract
electron.sign.appId=CUWcamXm
electron.sign.appSecret=cb2e9b8889edc45db88bda3928fadc3c22fefc92
#创建的电子签属于哪个组织下
electron.sign.orgId=b638a187226e68cad6a75c9d6b33d91d
#接口调用获取token需要密码
electron.sign.password=Azt123456
electron.sign.version=1.0
electron.sign.createLoginName=412722199502056537
electron.sign.notifyUrl=https://open.sky-dome.com.cn/opening/electron/callBackSignResult
electron.sign.statusNotifyUrl=https://open.sky-dome.com.cn/opening/electron/callBackContractStatus
electron.sign.contractDataNotifyUrl=https://open.sky-dome.com.cn/opening/electron/callBackContractFile
# ??access_token
electron.sign.apiOauthToken=/api/oauth/token
# ?????????
electron.sign.queryOrgOrUserInfo=/platform/user/queryOrgOrUserInfo
# ?????????
electron.sign.syncInnerUser=/platform/syncInnerUser
# ?????????
electron.sign.getContractVariableInfoList=/platform/contract/getContractVariableInfoList
# ?????????
electron.sign.createContract=/platform/contract/createContract
# ?????????
electron.sign.getContracteBase64=/platform/contract/getContracteBase64

aliyun.accessKeyId=LTAI5tQYoRWBcJdmmWVCbGDh
aliyun.accessKeySecret=******************************
aliyun.signName=阿里云短信测试专用
aliyun.openSms=true
aliyun.specialId=21
aliyun.templates.activity=SMS_486185074
aliyun.templates.person=SMS_486195049
aliyun.templates.openLottery=SMS_486295058
aliyun.templates.lotteryResult=SMS_486285047
aliyun.templates.verifyCode=SMS_486140063