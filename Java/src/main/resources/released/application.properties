######################################
##  \u5F00\u53D1\u73AF\u5883 develop
######################################
spring.application.name=project
server.port=8181

#Redis
spring.redis.host=127.0.0.1
spring.redis.port=6379

#SpringMVC
spring.mvc.view.suffix=.jsp
spring.mvc.view.prefix=/WEB-INF/view/

# MULTIPART (MultipartProperties)
file.relativePath=/uploadfiles/**
file.physicalPath=d:/uploadfiles/
spring.servlet.multipart.max-file-size=1MB
spring.servlet.multipart.max-request-size=10MB

#Durid
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.url = **************************************************************************
spring.datasource.username = root
spring.datasource.password = mysql
spring.datasource.druid.initial-size=10
spring.datasource.druid.max-active=100
spring.datasource.druid.min-idle=10
spring.datasource.druid.max-wait=10000
spring.datasource.druid.web-stat-filter.enabled=true
spring.datasource.druid.stat-view-servlet.enabled=true

#Zipkin \u8BBF\u95EE\u8DEF\u7531\u8DDF\u8E2A
spring.zipkin.service.name=project
spring.zipkin.base-url=http://***********:9411
spring.sleuth.sampler.probability=1

#MybatisPlus \u5982\u679C\u4F7F\u7528\u6CE8\u89E3\u7684\u65B9\u5F0F\uFF0C\u8FD9\u91CC\u53EF\u4EE5\u4E0D\u7528\u914D\u7F6E
mybatis-plus.mapper-locations=classpath*:com/tahoecn/project/mapper/xml/*Mapper.xml
mybatis-plus.typeAliasesPackage=com.tahoecn.project.model
