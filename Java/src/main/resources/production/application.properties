######################################
##  电子开盘系统 develop
######################################
spring.application.name=opening
server.servlet.context-path= /opening
server.port=8000
#server.port=443
#server.ssl.key-store=classpath:kp.tzdc.com.jks
#server.ssl.enabled=true
#server.ssl.key-store-password=u18amrs8
#server.ssl.key-store-type=JKS

spring.jackson.time-zone=GMT+8
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss

#server.tomcat.accept-count=100
#server.tomcat.max-connections=8192
#server.tomcat.threads.min-spare=10
#server.tomcat.threads.max=200
#server.tomcat.connection-timeout=20000
#server.tomcat.keep-alive-timeout=20000
#server.tomcat.max-keep-alive-requests=100

spring.task.execution.pool.core-size=32
spring.task.execution.pool.max-size=64
spring.task.execution.pool.queue-capacity=1000
spring.task.execution.pool.keep-alive=10000
spring.task.execution.thread-name-prefix=myThread


#Rdis Sentinel 配置
spring.redis.host=cd-dbserver.redis.cnceccloud.cn
spring.redis.port=30101
spring.redis.password=Y0l4g@0gwq10jIFIT
spring.redis.database=1



spring.rabbitmq.addresses=127.0.0.1:5672
spring.rabbitmq.username=root
spring.rabbitmq.password=TYZQ123456qweasdzxc
spring.rabbitmq.virtual-host=/
spring.rabbitmq.template.exchange=exchange_fanout
spring.rabbitmq.listener.concurrency=10
spring.rabbitmq.listener.max-concurrency=20
spring.rabbitmq.listener.prefetch=5



# MULTIPART (MultipartProperties)
tahoe.application.mappingPath=/uploadfiles/**
tahoe.application.physicalPath=/Data/uploadfiles_nas/dzkp/
server.tomcat.basedir=/home/<USER>
spring.servlet.multipart.max-file-size=1MB
spring.servlet.multipart.max-request-size=10MB

#MySQL 多数据源
spring.datasource.dynamic.primary=master
#spring.datasource.dynamic.datasource.master.url=jdbc:mysql://**************:3306/opening_prod?useUnicode=true&characterEncoding=utf8&useSSL=false
#spring.datasource.dynamic.datasource.master.username=root
#spring.datasource.dynamic.datasource.master.password=<EMAIL>
#spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver

spring.datasource.dynamic.datasource.master.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.dynamic.datasource.master.url=***************************************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=cd_dzkp_user
spring.datasource.dynamic.datasource.master.password=Cn28_S*23ac_S6bc
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver

spring.datasource.dynamic.datasource.master.druid.initial-size=100
spring.datasource.dynamic.datasource.master.druid.min-idle=100
spring.datasource.dynamic.datasource.master.druid.max-active=3000
spring.datasource.dynamic.datasource.master.druid.max-wait=60000
spring.datasource.dynamic.datasource.master.druid.time-between-eviction-runs-millis=60000
spring.datasource.dynamic.datasource.master.druid.min-evictable-idle-time-millis=300000
spring.datasource.dynamic.datasource.master.druid.validation-query=SELECT 0
spring.datasource.dynamic.datasource.master.druid.test-while-idle=true
spring.datasource.dynamic.datasource.master.druid.test-on-borrow=false
spring.datasource.dynamic.datasource.master.druid.test-on-return=false
spring.datasource.dynamic.datasource.master.druid.pool-prepared-statements=true
spring.datasource.dynamic.datasource.master.druid.max-pool-prepared-statement-per-connection-size=20
spring.datasource.dynamic.datasource.master.druid.filter=stat,wall,log4j
spring.datasource.dynamic.datasource.master.druid.connection-properties=druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
spring.datasource.dynamic.datasource.master.druid.stat-view-servlet.login-username=root
spring.datasource.dynamic.datasource.master.druid.stat-view-servlet.login-password=123


#spring.datasource.dynamic.datasource.slave_01.url=jdbc:mysql://************:3306/test?useUnicode=true&characterEncoding=utf8
#spring.datasource.dynamic.datasource.slave_01.username=root
#spring.datasource.dynamic.datasource.slave_01.password=mysql
#spring.datasource.dynamic.datasource.slave_01.driver-class-name=com.mysql.jdbc.Driver
#
#spring.datasource.dynamic.datasource.slave_02.url=jdbc:mysql://************:3306/test?useUnicode=true&characterEncoding=utf8
#spring.datasource.dynamic.datasource.slave_02.username=root
#spring.datasource.dynamic.datasource.slave_02.password=mysql
#spring.datasource.dynamic.datasource.slave_02.driver-class-name=com.mysql.jdbc.Driver


#MybatisPlus 如果使用注解的方式，这里可以不用配置
mybatis-plus.mapper-locations=classpath*:com/tahoecn/opening/mapper/xml/*Mapper.xml 
mybatis-plus.typeAliasesPackage=com.tahoecn.opening.model

bind_host=127.0.0.1
uc_api_url=http://ucapi.tzdc.com:8080
uc_sysId=ZXKAIPAN
uc_priv_key=luanqibazaod_zhend_buzhid

logging.config=classpath:logback.xml
logging.level.com.tahoecn.opening.mapper=DEBUG


#sms
sms_uri=http://hy.qixinhl.com/msg/
sms_account=taihe99
sms_pwd=taihe@123
sms_needstatus=true
sms_product=459367
sms_extno=001
#ceshi off pro on
sms_switch=on
#环境dev不发短信
sms_environment=prod

uc_api_url_userlist=http://ucapi.tzdc.com:8080/v3/user/list
uc_api_url_orglist=http://ucapi.tzdc.com:8080/v3/org/list

APP_ID=wxccdcd04e02d8c7f6
APP_SECRET=8e330d9442355dfd967ac5ddec447a5f

#活动开始前不许修改数据的时间
control_time=0.05 
#活动开始前不许修改白名单数据的时间
control_white_minutes=3

const_HOST=https://fxapi.tzdc.com
system_HOST=http://plan.cncec-amc.com/opening/uploadfiles/


sso.loginUrl=http://ucsso.tzdc.com:9988/login?sysId=ZXKAIPAN
sso.logoutUrl=http://ucsso.tzdc.com:9988/logout
sso.ucwebUrl=https://kp.tzdc.com
sso.cookieDomain=tzdc.com
sso.ucssowebUrl=http://ucsso.tzdc.com:9988

weixin.appId=wxc37523fb55b9457b
weixin.appSecret=81a75ca16db5ea597de13133ff02932c

# 同步数据地址
syncData.address=http://mdm.cncec-amc.com/bo-prod
syncData.key=dbbf3f16-a82c-ad42-0521-9cec6770b421
#诚东统一登录平台
chengdong.url: https://cncec-amc.com
chengdong.accessCode: /river-system/auth/accessCode
chengdong.login: /river-system/auth/login
chengdong.clientId: 12221409589130635

# ???
# ????appId?appSecret
electron.sign.basePath=https://dzq.bjzhxzy.com:18002/contract
electron.sign.appId=4iXI3o5a
electron.sign.appSecret=efcc167a7a2dd541604900c93894f7f20b086fac
#创建的电子签属于哪个组织下
electron.sign.orgId=92935372315f549f05b97a46dee7bbf8
#接口调用获取token需要密码
electron.sign.password=Azt@123456
electron.sign.version=1.0
electron.sign.createLoginName=13910980287 
electron.sign.notifyUrl=https://cdxf.cncec-amc.com/opening/electron/callBackSignResult
electron.sign.statusNotifyUrl=https://cdxf.cncec-amc.com/opening/electron/callBackContractStatus
electron.sign.contractDataNotifyUrl=https://cdxf.cncec-amc.com/opening/electron/callBackContractFile
# ??access_token
electron.sign.apiOauthToken=/api/oauth/token
# ?????????
electron.sign.queryOrgOrUserInfo=/platform/user/queryOrgOrUserInfo
# ?????????
electron.sign.syncInnerUser=/platform/syncInnerUser
# ?????????
electron.sign.getContractVariableInfoList=/platform/contract/getContractVariableInfoList
# ?????????
electron.sign.createContract=/platform/contract/createContract
# ?????????
electron.sign.getContracteBase64=/platform/contract/getContracteBase64
