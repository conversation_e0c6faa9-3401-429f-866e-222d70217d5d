<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="300000">
	<jmxConfigurator/>

	<contextName>open</contextName>

	<!-- For Run -->
	<property name="root.logger.level" value="INFO"/>

	<property name="log.path" value="/appdata/dzkp/java/openLogs"/>
	<property name="app.name" value="open"/>
	<property name="app.version" value="1.0"/>

	<property name="log.file" value="${log.path}/${app.name}-${app.version}/info/${app.name}.%d{yyyyMMdd}.info.%i.log.gz"/>

	<property name="log.file.error" value="${log.path}/${app.name}-${app.version}/error/${app.name}.%d{yyyyMMdd}.error.%i.log.gz"/>

	<property name="log.file.operate" value="${log.path}/${app.name}-${app.version}/operate/${app.name}.%d{yyyyMMdd}.operate.%i.log.gz"/>

	<property name="log.file.all" value="${log.path}/${app.name}-${app.version}/debug/${app.name}.%d{yyyyMMdd}.debug.%i.log.gz"/>

	<!-- 控制台输出的日志级别 -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<!--时间戳|应用标识|版本标识|类名方法名行号|日志级别|[请求/响应标识]|来源IP/日志详细内容-->
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%logger{10}-%line|%5p|%msg%n</pattern>
		</encoder>
	</appender>

	<!-- 程序日志输出Info的信息 -->
	<appender name="fileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<ImmediateFlush>true</ImmediateFlush>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%logger{10}-%line|%5p|%msg%n</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>DENY</onMatch>  <!-- 如果命中就禁止这条日志 -->
			<onMismatch>ACCEPT</onMismatch>  <!-- 如果没有命中就使用这条规则 -->
		</filter>

		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>${log.file}</FileNamePattern>
			<MaxHistory>30</MaxHistory>

			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
	</appender>

	<appender name="asyncFileAppender" class="ch.qos.logback.classic.AsyncAppender">
		<queueSize>1000</queueSize>
		<discardingThreshold>0</discardingThreshold>
		<includeCallerData>false</includeCallerData>
		<appender-ref ref="fileAppender"/>
	</appender>

	<!-- 程序error输出的信息 -->
	<appender name="errorFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<ImmediateFlush>true</ImmediateFlush>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%logger{10}-%line|%5p|%msg%n</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<!--设置日志级别,过滤掉info日志,只输入error日志-->
			<level>ERROR</level>
		</filter>

		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>${log.file.error}</FileNamePattern>
			<MaxHistory>60</MaxHistory>

			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
	</appender>

	<appender name="errorAsyncFileAppender" class="ch.qos.logback.classic.AsyncAppender">
		<queueSize>1000</queueSize>
		<discardingThreshold>0</discardingThreshold>
		<includeCallerData>false</includeCallerData>
		<appender-ref ref="errorFileAppender"/>
	</appender>

	<!-- 操作拦截器记录日志 -->
	<!--<appender name="file.other.operate" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <ImmediateFlush>true</ImmediateFlush>
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{56}.%method:%L -%msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>info</level>
        </filter>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${log.file.operate}</FileNamePattern>
            <MaxHistory>60</MaxHistory>

            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender> -->

	<!-- 如果要输出业务日志, 请修改为TRACE -->
	<logger name="org.springframework" level="ERROR"></logger>
	<logger name="ch.qos.logback" level="OFF"></logger>
	<logger name="org.apache.catalina" level="OFF"></logger>
	<logger name="java.sql.Connection" level="OFF"/>
	<logger name="java.sql.Statement" level="OFF"/>
	<logger name="java.sql.PreparedStatement" level="OFF"/>
	<!-- <logger name="com.tahoecn.planManagement.interceptors.OperateLogInterceptor">
		<level value="${root.logger.level}" />
		<appender-ref ref="file.other.operate" />
	</logger> -->

	<root level="INFO">
		<appender-ref ref="asyncFileAppender"/>
		<appender-ref ref="errorAsyncFileAppender"/>
		<appender-ref ref="console"/>
	</root>


	<!--  <root level="DEBUG">
         <appender-ref ref="allAsyncFileAppender"/>
     </root> -->
</configuration>