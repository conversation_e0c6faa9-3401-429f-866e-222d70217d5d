package com.tahoecn.opening;

import com.google.common.collect.Maps;
import com.tahoecn.opening.common.utils.DateUtils;
import com.tahoecn.opening.service.impl.AliyunServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.Map;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>  <EMAIL>
 * @ProjectName: dzkp_cddc
 * @Package: com.tahoecn.opening
 * @Description:// TODO 短信测试类
 * @Date: 2025/5/9 17:00
 * @Version: 1.0
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class SmsTest {

    @Autowired
    AliyunServiceImpl aliyunService;

    @Test
    public void test() {
        Map<String, String> params = Maps.newHashMapWithExpectedSize(2);
        params.put("time", DateUtils.getDateString(new Date()));
//        params.put("content", "1234");
//        params.put("code", "1234");
//        aliyunService.sendSms("18238967745", "activity", params);

    }
}
