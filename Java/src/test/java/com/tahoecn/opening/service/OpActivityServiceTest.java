package com.tahoecn.opening.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.tahoecn.opening.converter.ResponseMessage;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpHousingResources;
import com.tahoecn.opening.model.OpOrder;
import com.tahoecn.opening.model.OpUser;
import com.tahoecn.opening.model.dto.UserDto;
import com.tahoecn.opening.model.electronVo.ContractParamVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;
import java.util.UUID;



@RunWith(SpringRunner.class)
@SpringBootTest
public class OpActivityServiceTest {

    @Autowired
    private IOpActivityService activityService;
    @Autowired
    private IOpHouseTypeService iOpHouseTypeService;
    @Autowired
    private IOpFavoriteService iOpFavoriteService;
    @Autowired
    private IOpOrderService iOpOrderService;
    @Autowired
    private IOpUserSignService iOpUserSignService;
    @Autowired
    private IOpUserService iOpUserService;

    @Autowired
    private ElectronService electronService;

    @Autowired
    private IOpHousingResourcesService iOpHousingResourcesService;

    @Test
    public void test() {
//        String electronToken = electronService.getElectronToken();
//        System.out.println("electronToken = " + electronToken);
////        String s = electronService.queryOrgOrUserInfo();
//        String s1 = electronService.syncInnerUser();
////
////        String s2 = electronService.queryOrgOrUserInfo();
//
//        electronService.getContractVariableInfoList();

//        // 获取活动
//        OpActivity opActivity = activityService.getById(21);
//        OpOrder thisOrder = iOpOrderService.getById(1104);
//        // 生成电子签
//        OpUser opUser = iOpUserService.getById(2880);
//        iOpOrderService.checkElectronData(thisOrder, opActivity, opUser);

//        List<UserDto> userDtoList = activityService.selectAllUserDtoByActivityId("28", false);
//        for (UserDto userDto : userDtoList) {
//            System.out.println("userDto = " + userDto);
//        }


        OpUser opUser = iOpUserService.getById(2881);
        OpHousingResources housingResources = iOpHousingResourcesService.getById(20743);
        List<ContractParamVo> contractParamVoList = electronService.getContractParamVoList(opUser, housingResources);
        System.out.println(JSON.toJSONString(contractParamVoList));

    }

}
