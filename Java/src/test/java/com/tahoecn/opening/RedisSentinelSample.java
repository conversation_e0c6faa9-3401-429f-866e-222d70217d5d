package com.tahoecn.opening;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tahoecn.core.json.JSONResult;
import com.tahoecn.opening.common.constants.MQConstant;
import com.tahoecn.opening.mapper.CsUcUserMapper;
import com.tahoecn.opening.model.CsUcUser;
import com.tahoecn.opening.model.OpActivity;
import com.tahoecn.opening.model.OpOrder;
import com.tahoecn.opening.service.IMessageService;
import com.tahoecn.opening.service.IOpOrderService;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;

@RunWith(SpringRunner.class)
@SpringBootTest
public class RedisSentinelSample{

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private IOpOrderService iOpOrderService;

    private OpOrder opOrder = new OpOrder();

    @Autowired
    private IMessageService messageService;

    @Autowired
    private CsUcUserMapper csUcUserMapper;

    @Test
    public void sentinelTest(){
        // 获取所有用户
        LambdaQueryWrapper<CsUcUser> userWrapper = new QueryWrapper<CsUcUser>().lambda();
        List<CsUcUser> userList = csUcUserMapper.selectList(userWrapper);
        if (null != userList) {
            for (CsUcUser csUcUser : userList) {
                csUcUser.setFdPassword(DigestUtils.md5Hex(csUcUser.getFdUsername() + "_" + "123456" + "_" + csUcUser.getFdUsername()).toLowerCase());
                csUcUserMapper.updateById(csUcUser);
            }
        }


//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        this.redisTemplate.opsForValue().set(String.valueOf(System.currentTimeMillis()),simpleDateFormat.format(new Date()));

        Set<String> keys = redisTemplate.keys("*Order*");
        //redisTemplate.delete(keys);
//        redisTemplate.opsForValue().set("abc",500);

        /*String keyp[] = {"1Activity_T_House10","1Activity_T_User85"}, value = "";

        DefaultRedisScript<String> redisScript = new DefaultRedisScript<>();
        String script = "if " +
                " tonumber(redis.call('get', KEYS[1])) <= 0 " +
                "then " +
                " return \"Houses_sold\" " +
                "end " +
                "if " +
                " tonumber(redis.call('get', KEYS[2])) <= 0 " +
                "then " +
                " return \"maximum\"  " +
                "end " +
                "redis.call('DECR', KEYS[1]) " +
                "redis.call('DECR', KEYS[2])" +
                " return \"success\"";
        redisScript.setScriptText(script);
        redisScript.setResultType(String.class);


        String str = redisTemplate.execute(redisScript, Arrays.asList(keyp), value);
        System.out.println(str);*/
    }


    @Test
    public void iMassageTest(){
        System.out.println("发送时间:" + new Date());
        String message = "测试延迟消息";
        messageService.send(MQConstant.MASSAGE_QUEUE_NAME, message, 60);
        message = "测试普通消息";
        messageService.send(MQConstant.MASSAGE_QUEUE_NAME, message);
    }


    public void run() {
        /*try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }*/


    }

    @Test
    public void redisConcurrentTest(){



        CountDownLatch countDownLatch = new CountDownLatch(1);
        Long s = System.currentTimeMillis();
        for(int i=2000;i>0;i--){
            //new Thread(new RedisSentinelSample(countDownLatch)).start();

            MyThread thread = new MyThread(countDownLatch);
            Thread tt = new Thread(thread);
            tt.start();

        }
        Long c = System.currentTimeMillis();
        countDownLatch.countDown();
        Long e = System.currentTimeMillis();
        System.out.println(s.toString()+"--"+e.toString()+"---"+(s-e)+"==="+c.toString());
    }


    class MyThread implements Runnable {


        private final CountDownLatch countDownLatch;

        public MyThread() {
            opOrder.setActivityId("1");
            opOrder.setUserId("82");
            opOrder.setHouseId("6");
            countDownLatch = null;
        }

        private MyThread(CountDownLatch countDownLatch) {
            super();
            this.countDownLatch = countDownLatch;

            opOrder.setActivityId("1");
            opOrder.setUserId("82");
            opOrder.setHouseId("7");
        }


        @Override
        public void run() {
            try {
                countDownLatch.await();
                //JSONResult jsonResult = iOpOrderService.getCanOrder(opOrder);
                //System.out.println(jsonResult.toString()+"---"+System.currentTimeMillis());

                //System.out.println(redisTemplate.getConnectionFactory().getConnection().decr(redisTemplate.getKeySerializer().serialize("abc")));
                String keyp[] = {"1Activity_T_House10","1Activity_T_User85"}, value = "";

                DefaultRedisScript<String> redisScript = new DefaultRedisScript<>();
                String script = "if " +
                        " tonumber(redis.call('get', KEYS[1])) <= 0 " +
                        "then " +
                        " return \"Houses_sold\" " +
                        "end " +
                        "if " +
                        " tonumber(redis.call('get', KEYS[2])) <= 0 " +
                        "then " +
                        " return \"maximum\"  " +
                        "end " +
                        "redis.call('DECR', KEYS[1]) " +
                        "redis.call('DECR', KEYS[2])" +
                        " return \"success\"";
                redisScript.setScriptText(script);
                redisScript.setResultType(String.class);


                System.out.println("---"+System.currentTimeMillis());
                String str = redisTemplate.execute(redisScript, Arrays.asList(keyp), value);
                //System.out.println(str+"---"+System.currentTimeMillis());
                //System.out.println(redisTemplate.opsForValue().get("abc"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
