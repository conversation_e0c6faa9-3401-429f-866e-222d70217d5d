package com.zxx.test;

import com.alibaba.fastjson.JSONObject;
import com.tahoecn.opening.common.utils.AesUtils;
import com.tahoecn.opening.common.utils.ParseSystemUtil;

public class AesTest {
	public static void main(String[] args) throws Exception {
		JSONObject jo = new JSONObject();
		jo.put("code", "200");
		jo.put("message", "成功");
	    String content = "AES---加密";
	    content = jo.toJSONString();
	    String password = "1234567891234567";
	    System.out.println("加密之前：" + content);
	    
	    String encrypt = AesUtils.encrypt(content, password);
	    System.out.println("加密后的内容：" + new String(encrypt));
	    
	    String hexStrResult = ParseSystemUtil.parseByte2HexStr(encrypt.getBytes("utf-8"));
	    System.out.println("16进制的密文："  + hexStrResult);
	    
	    byte[] twoStrResult = ParseSystemUtil.parseHexStr2Byte(hexStrResult);
	    System.out.println("二进制的密文："  + twoStrResult); 
	    // 解密
	    String decrypt = AesUtils.decrypt(encrypt, password);
	    System.out.println("解密后的内容：" + new String(decrypt));    
	}
}
