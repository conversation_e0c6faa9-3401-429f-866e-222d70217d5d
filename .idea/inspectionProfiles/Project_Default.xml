<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="49">
            <item index="0" class="java.lang.String" itemvalue="openai" />
            <item index="1" class="java.lang.String" itemvalue="chainlit" />
            <item index="2" class="java.lang.String" itemvalue="fastapi" />
            <item index="3" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="4" class="java.lang.String" itemvalue="starlette" />
            <item index="5" class="java.lang.String" itemvalue="pillow" />
            <item index="6" class="java.lang.String" itemvalue="jinja2" />
            <item index="7" class="java.lang.String" itemvalue="influxdb-client" />
            <item index="8" class="java.lang.String" itemvalue="pydantic" />
            <item index="9" class="java.lang.String" itemvalue="paddleocr" />
            <item index="10" class="java.lang.String" itemvalue="pdf2image" />
            <item index="11" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="12" class="java.lang.String" itemvalue="uvicorn" />
            <item index="13" class="java.lang.String" itemvalue="python-multipart" />
            <item index="14" class="java.lang.String" itemvalue="python-jose" />
            <item index="15" class="java.lang.String" itemvalue="httpx" />
            <item index="16" class="java.lang.String" itemvalue="PyJWT" />
            <item index="17" class="java.lang.String" itemvalue="PyYAML" />
            <item index="18" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="19" class="java.lang.String" itemvalue="minio" />
            <item index="20" class="java.lang.String" itemvalue="aerich" />
            <item index="21" class="java.lang.String" itemvalue="requests" />
            <item index="22" class="java.lang.String" itemvalue="literalai" />
            <item index="23" class="java.lang.String" itemvalue="sqlparse" />
            <item index="24" class="java.lang.String" itemvalue="docling" />
            <item index="25" class="java.lang.String" itemvalue="aiomysql" />
            <item index="26" class="java.lang.String" itemvalue="pymilvus" />
            <item index="27" class="java.lang.String" itemvalue="passlib" />
            <item index="28" class="java.lang.String" itemvalue="Flask" />
            <item index="29" class="java.lang.String" itemvalue="pydantic-settings" />
            <item index="30" class="java.lang.String" itemvalue="llama-index-core" />
            <item index="31" class="java.lang.String" itemvalue="pytest" />
            <item index="32" class="java.lang.String" itemvalue="aiofiles" />
            <item index="33" class="java.lang.String" itemvalue="autogen-ext" />
            <item index="34" class="java.lang.String" itemvalue="plotly" />
            <item index="35" class="java.lang.String" itemvalue="loguru" />
            <item index="36" class="java.lang.String" itemvalue="python-pptx" />
            <item index="37" class="java.lang.String" itemvalue="autogen-agentchat" />
            <item index="38" class="java.lang.String" itemvalue="pandas" />
            <item index="39" class="java.lang.String" itemvalue="DBUtils" />
            <item index="40" class="java.lang.String" itemvalue="aiohttp" />
            <item index="41" class="java.lang.String" itemvalue="allure-python-commons" />
            <item index="42" class="java.lang.String" itemvalue="vanna" />
            <item index="43" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="44" class="java.lang.String" itemvalue="fitz" />
            <item index="45" class="java.lang.String" itemvalue="python_pptx" />
            <item index="46" class="java.lang.String" itemvalue="Pillow" />
            <item index="47" class="java.lang.String" itemvalue="anyio" />
            <item index="48" class="java.lang.String" itemvalue="rich" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>