## 技术

- vue3
- threejs
- indexdb
- pinia
- daisyui

## 开发

安装依赖
node version  20.12.0
```bash
pnpm i
or
npm install
```

开发运行

```bash
pnpm dev
or
npm run dev
```

打包
打包前请修改 src/api/request.ts baseURL
```bash
pnpm build
or
npm run build
```

预览

```bash
pnpm preview
or
npm run preview
```

若想直接以打开 html 文件的方式运行，请执行以下命令进行打包。打包完成后在 dist 目录中直接打开 index.html 即可。

```bash
pnpm build:file
or
npm run build:file
```

> 项目思路来源于 <https://github.com/moshang-xc/lottery>

## Docker 支持

构建镜像

```bash
docker build -t log-lottery .
```

运行容器

```bash
docker run -d -p 9279:80 log-lottery
```
