{"name": "lotterySystem", "private": true, "version": "0.0.3", "type": "module", "license": "MIT", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vue-tsc --noEmit && vite build", "build:pre": "vue-tsc --noEmit && vite build --mode prebuild", "build:file": "vue-tsc --noEmit && vite build --mode file", "test": "vitest", "test:ui": "vitest --ui", "preview": "vite preview", "lint": "eslint ./src", "lint:fix": "eslint ./src --fix"}, "dependencies": {"@tweenjs/tween.js": "^23.1.2", "@vueuse/core": "^11.3.0", "axios": "^1.7.8", "canvas-confetti": "^1.9.3", "dayjs": "^1.11.13", "github-markdown-css": "^5.8.0", "localforage": "^1.10.0", "markdown-it": "^14.1.0", "pinia": "^2.2.6", "pinia-plugin-persist": "^1.0.0", "sparticles": "^1.3.1", "three": "^0.166.0", "three-css3d": "^1.0.6", "vue": "^3.5.13", "vue-dompurify-html": "^5.2.0", "vue-i18n": "^10.0.4", "vue-router": "^4.5.0", "vue-toast-notification": "^3", "vue3-colorpicker": "^2.3.0", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@antfu/eslint-config": "^3.9.2", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.15.0", "@iconify-json/ep": "^1.2.1", "@iconify-json/fluent": "^1.2.8", "@tailwindcss/typography": "^0.5.15", "@testing-library/vue": "^8.1.0", "@types/canvas-confetti": "^1.6.4", "@types/markdown-it": "^14.1.2", "@types/node": "^22.9.4", "@types/three": "^0.166.0", "@typescript-eslint/eslint-plugin": "^8.16.0", "@typescript-eslint/parser": "^8.16.0", "@vitejs/plugin-legacy": "^6.0.0", "@vitejs/plugin-vue": "^5.2.0", "@vitest/ui": "^2.1.5", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.20", "daisyui": "^4.12.14", "eslint": "^9.15.0", "eslint-plugin-vue": "^9.31.0", "globals": "^15.12.0", "happy-dom": "^15.11.6", "husky": "^9.1.7", "jsdom": "^25.0.1", "path": "^0.12.7", "postcss": "^8.4.49", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.81.0", "sass-loader": "^16.0.3", "tailwindcss": "^3.4.15", "terser": "^5.36.0", "typescript": "5.5.3", "unplugin-auto-import": "^0.18.5", "unplugin-icons": "^0.20.1", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.11", "vite-plugin-compression": "^0.5.1", "vite-plugin-inspect": "^0.8.8", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.6.4", "vitest": "^2.1.5", "vue-tsc": "^2.1.10"}}