export default {
  button: {
    continue: 'Continue',
    confirm: 'Confirm',
    cancel: 'Cancel',
    setting: 'Setting',
    delete: 'Delete',
    allDelete: 'Delete All',
    downloadTemplate: 'Download Template',
    importData: 'Import Data',
    resetData: 'Reset Data',
    exportResult: 'Export Result',
    add: 'Add',
    resetDefault: 'Reset Default',
    resetAllData: 'Reset All Data',
    clearPattern: 'Clear Pattern',
    DefaultPattern: 'Default Pattern',
    upload: 'Upload',
    reset: 'Reset',
    play: 'Play',
    setLayout: 'Set Layout',
    close: 'Close',
    noInfoAndImport: 'No Info and import it',
    useDefault: 'Use Default Data',
  },
  sidebar: {
    personConfiguration: 'Person Configuration',
    personList: 'Person List',
    winnerList: 'Winner List',
    prizeConfiguration: 'Prize Configuration',
    globalSetting: 'Global Configuration',
    viewSetting: 'View Setting',
    imagesManagement: 'Images Management',
    musicManagement: 'Music Management',
    operatingInstructions: 'Operating Instructions',
  },
  viewTitle: {
    personManagement: 'Person Management',
    winnerManagement: 'Winner Management',
    prizeManagement: 'Prize Management',
    globalSetting: 'Global Setting',
    operatingInstructions: 'Operating Instructions',
  },
  table: {
    // person configuration
    number: 'Number',
    name: 'Name',
    prizeName: 'Name',
    department: 'Department',
    identity: 'Identity',
    isLucky: 'Is Lucky',
    operation: 'Operation',
    setLuckyNumber: 'Set Lucky Number',
    luckyPeopleNumber: 'Lucky People Number',

    detail: 'Detail',
    noneData: 'No Data',
    // prize configuration
    fullParticipation: 'FullParticipation',
    numberParticipants: 'NumberParticipants',
    isDone: 'is Done',
    image: 'Image',
    onceNumber: 'Once Number',
    time: 'Time',
    // view setting
    title: 'Main Title',
    columnNumber: 'Column Number',
    theme: 'Theme',
    language: 'Language',
    cardColor: 'Card Color',
    winnerColor: 'Winner Color',
    textColor: 'Text Color',
    cardWidth: 'Card Width',
    cardHeight: 'Card Height',
    textSize: 'Text Size',
    highlightColor: 'HighLight Color',
    patternSetting: 'Pattern Setting',
    alwaysDisplay: 'Always Display Prize List',
    avatarDisplay: 'Show avatars or not',
    selectPicture: 'Select a Picture',
    backgroundImage: 'Select Background Image',
  },
  dialog: {
    titleTip: 'Tip!',
    titleTemporary: 'Add Temporary Activity',
    dialogPCWeb: 'Please use a PC browser to access for optimal display performance',
    dialogDelAllPerson: 'This operation will delete all personnel list data. Do you want to continue?',
    dialogResetWinner: 'This operation will clear the winning information of personnel. Do you want to continue?',
    dialogResetAllData: 'This operation will reset all data. Do you want to continue?',
    dialogSingleDrawLimit: 'Only 10 characters can be extracted in a single draw',
    dialogLatestBrowser: 'Please use the latest version of Chrome or Edge browser',
    tipResetPrize: 'Performing operations may reset data, please proceed with caution',
  },
  tooltip: {
    settingConfiguration: 'Setting/Configuration',
    nextSong: 'Right Click to Next Song',
    noSongPlay: 'No Song to Play',
    prizeList: 'Prize List',
    addActivity: 'Add Activity',
    downloadTemplateTip: 'After downloading the file, please fill in the data in Excel and save it in xlsx format',
    uploadExcelTip: 'Upload the modified Excel file',
    leftClick: 'Left Click to Slice',
    toHome: 'to Home',
    resetLayout: 'This item is time-consuming and performance intensive',
    defaultLayout: 'The default pattern setting is valid for 17 columns, please set the number of other columns yourself',
    doneCount: 'Number of winners',
    edit: 'Edit',
    delete: 'Delete',
  },
  error: {
    require: 'required field',
    requireNumber: 'please enter a number',
    minNumber1: 'the minimum is 1',
    maxNumber100: 'the maximum is 100',
    uploadSuccess: 'Upload Success',
    uploadFail: 'Upload Failed',
    notImage: 'Not Image',
    personIsAllDone: 'All Person Is Done',
    personNotEnough: 'Person Is Not Enough',
    startDraw: 'Now Draw {count} {leftover} people',
    completeInformation: 'Please provide complete information',
  },
  placeHolder: {
    enterTitle: 'Enter Title',
    name: 'Name',
    winnerCount: 'Lucky Person Count',
  },
  data: {
    yes: 'Yes',
    no: 'No',
    number: 'Number',
    isWin: 'isWin',
    avatar: 'avatar',
    department: 'Department',
    name: 'Name',
    identity: 'Identity',
    prizeName: 'Prize Name',
    prizeTime: 'Prize Time',
    operation: 'Operation',
    delete: 'Delete',
    removePerson: 'Remove the Person',
    xlsxName: 'personListTemplate-en.xlsx',
    readmeName: 'readme-en.md',
  },
}
