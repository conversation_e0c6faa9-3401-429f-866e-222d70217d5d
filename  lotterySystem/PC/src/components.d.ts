/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AutoScroll: typeof import('./components/autoScroll/index.vue')['default']
    DaiysuiTable: typeof import('./components/DaiysuiTable/index.vue')['default']
    EditSeparateDialog: typeof import('./components/NumberSeparate/EditSeparateDialog.vue')['default']
    ImageSync: typeof import('./components/ImageSync/index.vue')['default']
    PlayMusic: typeof import('./components/PlayMusic/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StarsBackground: typeof import('./components/StarsBackground/index.vue')['default']
    SvgIcon: typeof import('./components/SvgIcon/index.vue')['default']
    ToTop: typeof import('./components/ToTop/index.vue')['default']
  }
}
