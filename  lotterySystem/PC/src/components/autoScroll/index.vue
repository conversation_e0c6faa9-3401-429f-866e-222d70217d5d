<template>
  <div
    id="textScroll"
    class="text-scroll"
    @mousemove="inBox"
    @mouseleave="leaveBox"
  >
    <div
      v-for="i in 2"
      :id="'scrollItem' + i"
      :key="'scrollItem' + i"
      class="scroll-item"
      :style="{ display: i === 1 ? 'flex' : 'none' }"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      left: 0,
      textScrollDiv: null,
      timer: null,
      scrollItemWidth: 0,
      isScroll: false,
    };
  },

  computed: {},
  destroyed() {
    clearInterval(this.timer);
  },
  mounted() {},
  methods: {
    startScroll() {
      const that = this;
      this.$nextTick(() => {
        that.textScrollDiv = document.querySelector("#textScroll");
        that.scrollItemWidth =
          document.querySelector("#scrollItem1").clientWidth;
        const outerBoxWidth = that.textScrollDiv.clientWidth;
        if (outerBoxWidth < that.scrollItemWidth) {
          this.isScroll = true;
          that.textScrollDiv.style.width = `${that.scrollItemWidth * 2}px`;
          that.timer = setInterval(function () {
            that.moveLeft();
          }, 30);
          document.querySelector("#scrollItem2").style.display = "flex";
        }
      });
    },
    moveLeft() {
      if (this.textScrollDiv) {
        this.left -= 1;
        if (Math.abs(this.left) > this.scrollItemWidth) {
          this.left = 0;
        }
        this.textScrollDiv.style.transform = `translate(${this.left}px, 0px)`;
      }
    },
    // 鼠标划入区域
    inBox() {
      if (this.isScroll) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    // 鼠标离开区域
    leaveBox() {
      if (this.isScroll) {
        const that = this;
        this.timer = setInterval(function () {
          that.moveLeft();
        }, 30);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.text-scroll {
  display: flex;
  flex-wrap: nowrap;
  transition: all 0ms ease-in 0s;
  .scroll-item {
    display: flex;
    flex-wrap: nowrap;
  }
}
</style>
