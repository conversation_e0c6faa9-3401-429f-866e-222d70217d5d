@tailwind base;
@tailwind components;
@tailwind utilities;

body,
html {
    margin: 0;
    padding: 0;
    overflow-y: overlay;
    overflow-y: hidden;
    overflow-x: hidden;
}

ul {
    list-style: none;
}

/* 优化滚动条 */
::-webkit-scrollbar {
    /*滚动条整体样式*/
    height: 10px;
    width: 10px;
    background: rgba(12, 9, 9, 0.1);
    float: right;
}

::-webkit-scrollbar:hover {
    background: rgba(0, 0, 0, 0.2);
}

/* .tables::-webkit-scrollbar-track {
    background: rgb(239, 239, 239);
    border-radius: 2px;
  } */
::-webkit-scrollbar-thumb {
    /* 滚动条 */
    background: #909399;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -ms-border-radius: 6px;
    -o-border-radius: 6px;
    border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
    -webkit-box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.25);
    /* Webkit browsers */
    -moz-box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.25);
    /* Firefox */
    -ms-box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.25);
    /* IE9 */
    -o-box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.25);
    /* Opera(Old) */
    box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.25);
    /* IE9+, News */
}