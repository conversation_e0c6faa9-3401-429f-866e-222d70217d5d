<svg width="100%" height="100%" id="svg" viewBox="0 0 1440 690" xmlns="http://www.w3.org/2000/svg" class="transition duration-300 ease-in-out delay-150"><style>
          .path-0{
            animation:pathAnim-0 4s;
            animation-timing-function: linear;
            animation-iteration-count: infinite;
          }
          @keyframes pathAnim-0{
            0%{
              d: path("M 0,700 L 0,131 C 90.40769230769229,160.85641025641027 180.81538461538457,190.71282051282054 261,188 C 341.1846153846154,185.28717948717946 411.1461538461539,150.0051282051282 486,144 C 560.8538461538461,137.9948717948718 640.5999999999999,161.26666666666668 709,157 C 777.4000000000001,152.73333333333332 834.4538461538461,120.92820512820514 927,101 C 1019.5461538461539,81.07179487179486 1147.5846153846153,73.02051282051282 1239,80 C 1330.4153846153847,86.97948717948718 1385.2076923076925,108.9897435897436 1440,131 L 1440,700 L 0,700 Z");
            }
            25%{
              d: path("M 0,700 L 0,131 C 72.08205128205125,98.84102564102564 144.1641025641025,66.68205128205128 230,70 C 315.8358974358975,73.31794871794872 415.4256410256411,112.11282051282052 509,130 C 602.5743589743589,147.88717948717948 690.1333333333332,144.86666666666665 752,144 C 813.8666666666668,143.13333333333335 850.0410256410258,144.4205128205128 923,134 C 995.9589743589742,123.57948717948719 1105.702564102564,101.45128205128205 1198,99 C 1290.297435897436,96.54871794871795 1365.148717948718,113.77435897435898 1440,131 L 1440,700 L 0,700 Z");
            }
            50%{
              d: path("M 0,700 L 0,131 C 82.97692307692304,91.78461538461539 165.9538461538461,52.56923076923077 236,74 C 306.0461538461539,95.43076923076923 363.16153846153856,177.5076923076923 436,196 C 508.83846153846144,214.4923076923077 597.3999999999999,169.4 683,146 C 768.6000000000001,122.6 851.2384615384616,120.8923076923077 928,113 C 1004.7615384615384,105.1076923076923 1075.6461538461538,91.03076923076922 1160,93 C 1244.3538461538462,94.96923076923078 1342.1769230769232,112.98461538461538 1440,131 L 1440,700 L 0,700 Z");
            }
            75%{
              d: path("M 0,700 L 0,131 C 87.95128205128205,141.17435897435897 175.9025641025641,151.34871794871793 241,163 C 306.0974358974359,174.65128205128207 348.34102564102557,187.77948717948718 435,190 C 521.6589743589744,192.22051282051282 652.7333333333335,183.53333333333333 737,160 C 821.2666666666665,136.46666666666667 858.725641025641,98.08717948717948 921,104 C 983.274358974359,109.91282051282052 1070.3641025641025,160.11794871794874 1161,172 C 1251.6358974358975,183.88205128205126 1345.8179487179486,157.44102564102565 1440,131 L 1440,700 L 0,700 Z");
            }
            100%{
              d: path("M 0,700 L 0,131 C 90.40769230769229,160.85641025641027 180.81538461538457,190.71282051282054 261,188 C 341.1846153846154,185.28717948717946 411.1461538461539,150.0051282051282 486,144 C 560.8538461538461,137.9948717948718 640.5999999999999,161.26666666666668 709,157 C 777.4000000000001,152.73333333333332 834.4538461538461,120.92820512820514 927,101 C 1019.5461538461539,81.07179487179486 1147.5846153846153,73.02051282051282 1239,80 C 1330.4153846153847,86.97948717948718 1385.2076923076925,108.9897435897436 1440,131 L 1440,700 L 0,700 Z");
            }
          }</style><defs><linearGradient id="gradient" x1="0%" y1="50%" x2="100%" y2="50%"><stop offset="5%" stop-color="#7bdcb5"></stop><stop offset="95%" stop-color="#8ED1FC"></stop></linearGradient></defs><path d="M 0,700 L 0,131 C 90.40769230769229,160.85641025641027 180.81538461538457,190.71282051282054 261,188 C 341.1846153846154,185.28717948717946 411.1461538461539,150.0051282051282 486,144 C 560.8538461538461,137.9948717948718 640.5999999999999,161.26666666666668 709,157 C 777.4000000000001,152.73333333333332 834.4538461538461,120.92820512820514 927,101 C 1019.5461538461539,81.07179487179486 1147.5846153846153,73.02051282051282 1239,80 C 1330.4153846153847,86.97948717948718 1385.2076923076925,108.9897435897436 1440,131 L 1440,700 L 0,700 Z" stroke="none" stroke-width="0" fill="url(#gradient)" fill-opacity="0.4" class="transition-all duration-300 ease-in-out delay-150 path-0" transform="rotate(-180 720 350)"></path><style>
          .path-1{
            animation:pathAnim-1 4s;
            animation-timing-function: linear;
            animation-iteration-count: infinite;
          }
          @keyframes pathAnim-1{
            0%{
              d: path("M 0,700 L 0,306 C 73.36923076923077,283.31794871794875 146.73846153846154,260.63589743589745 217,254 C 287.26153846153846,247.36410256410255 354.4153846153846,256.77435897435896 436,279 C 517.5846153846154,301.22564102564104 613.6,336.26666666666665 708,351 C 802.4,365.73333333333335 895.1846153846154,360.15897435897443 971,349 C 1046.8153846153846,337.84102564102557 1105.6615384615386,321.09743589743584 1181,313 C 1256.3384615384614,304.90256410256416 1348.1692307692306,305.4512820512821 1440,306 L 1440,700 L 0,700 Z");
            }
            25%{
              d: path("M 0,700 L 0,306 C 72.90000000000003,299.77435897435896 145.80000000000007,293.548717948718 232,306 C 318.19999999999993,318.451282051282 417.69999999999993,349.5794871794872 489,343 C 560.3000000000001,336.4205128205128 603.4000000000001,292.1333333333333 683,289 C 762.5999999999999,285.8666666666667 878.7,323.88717948717954 971,325 C 1063.3,326.11282051282046 1131.8,290.3179487179487 1206,281 C 1280.2,271.6820512820513 1360.1,288.8410256410257 1440,306 L 1440,700 L 0,700 Z");
            }
            50%{
              d: path("M 0,700 L 0,306 C 102.84615384615384,278.4230769230769 205.69230769230768,250.84615384615387 274,272 C 342.3076923076923,293.15384615384613 376.0769230769231,363.03846153846155 458,364 C 539.9230769230769,364.96153846153845 670.0000000000001,297 744,299 C 817.9999999999999,301 835.9230769230769,372.96153846153845 918,370 C 1000.0769230769231,367.03846153846155 1146.3076923076924,289.15384615384613 1244,266 C 1341.6923076923076,242.84615384615387 1390.8461538461538,274.4230769230769 1440,306 L 1440,700 L 0,700 Z");
            }
            75%{
              d: path("M 0,700 L 0,306 C 101.82564102564103,310.93589743589746 203.65128205128207,315.87179487179486 277,326 C 350.34871794871793,336.12820512820514 395.2205128205129,351.448717948718 469,357 C 542.7794871794871,362.551282051282 645.4666666666666,358.3333333333333 735,341 C 824.5333333333334,323.6666666666667 900.9128205128204,293.21794871794873 972,284 C 1043.0871794871796,274.78205128205127 1108.8820512820512,286.79487179487177 1186,294 C 1263.1179487179488,301.20512820512823 1351.5589743589744,303.6025641025641 1440,306 L 1440,700 L 0,700 Z");
            }
            100%{
              d: path("M 0,700 L 0,306 C 73.36923076923077,283.31794871794875 146.73846153846154,260.63589743589745 217,254 C 287.26153846153846,247.36410256410255 354.4153846153846,256.77435897435896 436,279 C 517.5846153846154,301.22564102564104 613.6,336.26666666666665 708,351 C 802.4,365.73333333333335 895.1846153846154,360.15897435897443 971,349 C 1046.8153846153846,337.84102564102557 1105.6615384615386,321.09743589743584 1181,313 C 1256.3384615384614,304.90256410256416 1348.1692307692306,305.4512820512821 1440,306 L 1440,700 L 0,700 Z");
            }
          }</style><defs><linearGradient id="gradient" x1="0%" y1="50%" x2="100%" y2="50%"><stop offset="5%" stop-color="#7bdcb5"></stop><stop offset="95%" stop-color="#8ED1FC"></stop></linearGradient></defs><path d="M 0,700 L 0,306 C 73.36923076923077,283.31794871794875 146.73846153846154,260.63589743589745 217,254 C 287.26153846153846,247.36410256410255 354.4153846153846,256.77435897435896 436,279 C 517.5846153846154,301.22564102564104 613.6,336.26666666666665 708,351 C 802.4,365.73333333333335 895.1846153846154,360.15897435897443 971,349 C 1046.8153846153846,337.84102564102557 1105.6615384615386,321.09743589743584 1181,313 C 1256.3384615384614,304.90256410256416 1348.1692307692306,305.4512820512821 1440,306 L 1440,700 L 0,700 Z" stroke="none" stroke-width="0" fill="url(#gradient)" fill-opacity="0.53" class="transition-all duration-300 ease-in-out delay-150 path-1" transform="rotate(-180 720 350)"></path><style>
          .path-2{
            animation:pathAnim-2 4s;
            animation-timing-function: linear;
            animation-iteration-count: infinite;
          }
          @keyframes pathAnim-2{
            0%{
              d: path("M 0,700 L 0,481 C 83.87692307692308,501.28205128205127 167.75384615384615,521.5641025641025 243,530 C 318.24615384615385,538.4358974358975 384.86153846153843,535.025641025641 459,514 C 533.1384615384616,492.974358974359 614.8,454.33333333333337 695,455 C 775.2,455.66666666666663 853.9384615384615,495.6410256410257 931,494 C 1008.0615384615385,492.3589743589743 1083.446153846154,449.1025641025641 1168,440 C 1252.553846153846,430.8974358974359 1346.2769230769231,455.94871794871796 1440,481 L 1440,700 L 0,700 Z");
            }
            25%{
              d: path("M 0,700 L 0,481 C 100.42051282051281,464.76666666666665 200.84102564102562,448.53333333333336 276,458 C 351.1589743589744,467.46666666666664 401.05641025641023,502.6333333333333 459,510 C 516.9435897435898,517.3666666666667 582.9333333333334,496.93333333333334 679,472 C 775.0666666666666,447.06666666666666 901.2102564102565,417.6333333333333 991,420 C 1080.7897435897435,422.3666666666667 1134.225641025641,456.53333333333336 1203,472 C 1271.774358974359,487.46666666666664 1355.8871794871795,484.23333333333335 1440,481 L 1440,700 L 0,700 Z");
            }
            50%{
              d: path("M 0,700 L 0,481 C 80.45897435897433,478.6948717948718 160.91794871794866,476.38974358974355 227,489 C 293.08205128205134,501.61025641025645 344.7871794871796,529.1358974358975 439,515 C 533.2128205128204,500.8641025641025 669.9333333333332,445.06666666666666 762,426 C 854.0666666666668,406.93333333333334 901.4794871794875,424.59743589743584 976,431 C 1050.5205128205125,437.40256410256416 1152.1487179487178,432.5435897435898 1234,439 C 1315.8512820512822,445.4564102564102 1377.9256410256412,463.2282051282051 1440,481 L 1440,700 L 0,700 Z");
            }
            75%{
              d: path("M 0,700 L 0,481 C 91.47179487179488,453.0820512820512 182.94358974358977,425.1641025641025 260,429 C 337.05641025641023,432.8358974358975 399.69743589743587,468.4256410256411 485,497 C 570.3025641025641,525.5743589743589 678.2666666666668,547.1333333333333 750,532 C 821.7333333333332,516.8666666666667 857.2358974358974,465.04102564102567 933,442 C 1008.7641025641026,418.95897435897433 1124.7897435897435,424.7025641025641 1216,436 C 1307.2102564102565,447.2974358974359 1373.6051282051283,464.14871794871794 1440,481 L 1440,700 L 0,700 Z");
            }
            100%{
              d: path("M 0,700 L 0,481 C 83.87692307692308,501.28205128205127 167.75384615384615,521.5641025641025 243,530 C 318.24615384615385,538.4358974358975 384.86153846153843,535.025641025641 459,514 C 533.1384615384616,492.974358974359 614.8,454.33333333333337 695,455 C 775.2,455.66666666666663 853.9384615384615,495.6410256410257 931,494 C 1008.0615384615385,492.3589743589743 1083.446153846154,449.1025641025641 1168,440 C 1252.553846153846,430.8974358974359 1346.2769230769231,455.94871794871796 1440,481 L 1440,700 L 0,700 Z");
            }
          }</style><defs><linearGradient id="gradient" x1="0%" y1="50%" x2="100%" y2="50%"><stop offset="5%" stop-color="#7bdcb5"></stop><stop offset="95%" stop-color="#8ED1FC"></stop></linearGradient></defs><path d="M 0,700 L 0,481 C 83.87692307692308,501.28205128205127 167.75384615384615,521.5641025641025 243,530 C 318.24615384615385,538.4358974358975 384.86153846153843,535.025641025641 459,514 C 533.1384615384616,492.974358974359 614.8,454.33333333333337 695,455 C 775.2,455.66666666666663 853.9384615384615,495.6410256410257 931,494 C 1008.0615384615385,492.3589743589743 1083.446153846154,449.1025641025641 1168,440 C 1252.553846153846,430.8974358974359 1346.2769230769231,455.94871794871796 1440,481 L 1440,700 L 0,700 Z" stroke="none" stroke-width="0" fill="url(#gradient)" fill-opacity="1" class="transition-all duration-300 ease-in-out delay-150 path-2" transform="rotate(-180 720 350)"></path></svg>