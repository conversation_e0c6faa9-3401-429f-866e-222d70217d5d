import Layout from "@/layout/index.vue";
import i18n from "@/locales/i18n";
import Home from "@/views/Home/index.vue";
import {
  createRouter,
  createWebHistory,
  createWebHashHistory,
} from "vue-router";

export const configRoutes = {
  path: "/config",
  name: "Config",
  component: () => import("@/views/Config/index.vue"),
  children: [
    {
      path: "",
      redirect: "/config/person",
    },
    {
      path: "/config/person",
      name: "PersonConfig",
      component: () => import("@/views/Config/Person/PersonConfig.vue"),
      meta: {
        title: i18n.global.t("sidebar.personConfiguration"),
        icon: "person",
      },
      children: [
        {
          path: "",
          redirect: "/config/person/all",
        },
        {
          path: "/config/person/all",
          name: "AllPersonConfig",
          component: () => import("@/views/Config/Person/PersonAll.vue"),
          meta: {
            title: i18n.global.t("sidebar.personList"),
            icon: "all",
          },
        },
        {
          path: "/config/person/already",
          name: "Already<PERSON>erson",
          component: () => import("@/views/Config/Person/PersonAlready.vue"),
          meta: {
            title: i18n.global.t("sidebar.winnerList"),
            icon: "already",
          },
        },
        // {
        //     path:'other',
        //     name:'OtherPersonConfig',
        //     component:()=>import('@/views/Config/Person/OtherPersonConfig.vue'),
        //     meta:{
        //         title:'其他配置',
        //         icon:'other'
        //     }
        // }
      ],
    },
    {
      path: "/config/prize",
      name: "PrizeConfig",
      component: () => import("@/views/Config/Prize/PrizeConfig.vue"),
      meta: {
        title: i18n.global.t("sidebar.prizeConfiguration"),
        icon: "prize",
      },
    },
    {
      path: "/config/global",
      name: "GlobalConfig",
      redirect: "/config/global/all",
      meta: {
        title: i18n.global.t("sidebar.globalSetting"),
        icon: "global",
      },
      children: [
        {
          path: "/config/global/face",
          name: "FaceConfig",
          component: () => import("@/views/Config/Global/FaceConfig.vue"),
          meta: {
            title: i18n.global.t("sidebar.viewSetting"),
            icon: "face",
          },
        },
        {
          path: "/config/global/image",
          name: "ImageConfig",
          component: () => import("@/views/Config/Global/ImageConfig.vue"),
          meta: {
            title: i18n.global.t("sidebar.imagesManagement"),
            icon: "image",
          },
        },
        {
          path: "/config/global/music",
          name: "MusicConfig",
          component: () => import("@/views/Config/Global/MusicConfig.vue"),
          meta: {
            title: i18n.global.t("sidebar.musicManagement"),
            icon: "music",
          },
        },
      ],
    },
    {
      path: "/config/readme",
      name: "Readme",
      component: () => import("@/views/Config/Readme/index.vue"),
      meta: {
        title: i18n.global.t("sidebar.operatingInstructions"),
        icon: "readme",
      },
    },
  ],
};
const routes = [
  {
    path: "",
    component: Layout,
    redirect: "/home",
    children: [
      {
        path: "/home",
        name: "Home",
        component: Home,
      },
      configRoutes,
    ],
  },
];
const envMode = import.meta.env.MODE;
console.log(envMode === "file");
const router = createRouter({
  // 读取环境变量
  history:
    envMode === "file"
      ? createWebHashHistory("/web")
      : createWebHistory("/web"),
  routes,
});

export default router;
