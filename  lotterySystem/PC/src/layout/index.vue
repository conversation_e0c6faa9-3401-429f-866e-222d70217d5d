<script setup lang="ts">
import ToTop from '@/components/ToTop/index.vue'
import { useScroll } from '@vueuse/core'
import { ref } from 'vue'

const mainContainer = ref<HTMLElement | null>(null)

const { y } = useScroll(mainContainer)

function scrollToTop() {
  y.value = 0
}
</script>

<template>
  <div class="w-screen">
    <ToTop v-if="y > 400" @click="scrollToTop" />
    <main ref="mainContainer" class="box-content w-screen h-screen overflow-x-hidden overflow-y-auto main-container">
      <router-view class="h-full main-container-content" />
    </main>
  </div>
</template>

<style scoped lang="scss">

</style>
