# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Node.js CI

on:
  pull_request:
    branches: [ "main" ]

jobs:
  build:

    runs-on: ubuntu-latest
    permissions:
      contents: read
    strategy:
      matrix:
        node-version: [20.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
            - uses: actions/checkout@v3
            - uses: pnpm/action-setup@v2
              with:
                version: 8
            - name: Use Node.js ${{ matrix.node-version }}
              uses: actions/setup-node@v3
              with:
                  node-version: ${{ matrix.node-version }}
                  cache: 'pnpm'
            - run: pnpm install
            - run: pnpm build
            - name: Copy and Rename index.html to dist
              run: cp dist/index.html dist/404.html

            - name: Deploy to gh-pages
              uses: crazy-max/ghaction-github-pages@v2
              with:
                  target_branch: gh-pages
                  build_dir: dist
              env:
                  GITHUB_TOKEN: ${{ secrets.ACCESS_TOKEN }}
              

