import axios from 'axios'
// 创建一个axios实例
const service = axios.create({
    baseURL: process.env.VUE_APP_BASE_URL,
})

// 请求拦截器
service.interceptors.request.use(
    config => {
        config.headers['content-type'] = 'application/x-www-form-urlencoded'
        let token = {
            loginToken: sessionStorage.getItem("loginToken")
        }
        config.headers['token'] = JSON.stringify(token)
        return config
    },
    error => {
        // 处理请求错误
        // console.log(error) // for debug
        return Promise.reject(error)
    }
)

// 响应拦截器
service.interceptors.response.use(
    response => {
        if (response.data.code != 200) {
            let url = location.href.replace(/detail/g, 'login'); // 替换所有匹配项
            location.href = url
        } else {
            return response.data;
        }
    },
    error => {
        return Promise.reject(error)
    }
)

export default service
