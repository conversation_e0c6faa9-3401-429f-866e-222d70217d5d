import Vue from 'vue'
import App from './App.vue'
import router from './router'
import 'lib-flexible'
import { Form, Field, Icon, CellGroup, Divider, Button, Toast, Notify, Loading, Overlay } from 'vant';

Vue.use(Form);
Vue.use(Field);
Vue.use(Icon);
Vue.use(CellGroup);
Vue.use(Divider)
Vue.use(Button)
Vue.use(Toast);
Vue.use(Notify);
Vue.use(Loading);
Vue.use(Overlay);

Vue.config.productionTip = false

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')
