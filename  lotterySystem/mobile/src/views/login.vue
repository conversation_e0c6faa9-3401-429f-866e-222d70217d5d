<template>
  <div class="container">
    <div class="bg">{{ activityData.lotteryTitle }}</div>
    <div class="login_form">
      <div class="title">
        <img src="../assets/title.png" />
        用户登录
        <img src="../assets/title.png" />
      </div>
      <div class="form_container">
        <van-form validate-first @failed="onFailed" @submit="onSubmit">
          <van-field
            v-model="loginForm.userFullName"
            label=""
            placeholder="请输入售楼处预留真实姓名"
            name="validatorName"
            :rules="[{ validator: validatorName, message: '请输入真实姓名' }]"
          >
            <template slot="left-icon">
              <img
                src="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzQ1OTg0MTg2MzQ1IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9Ijk0NzYiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMzIiIGhlaWdodD0iMzIiPjxwYXRoIGQ9Ik01MTIgNTA2YzEwNC40IDAgMTg5LTg0LjYgMTg5LTE4OXMtODQuNi0xODktMTg5LTE4OS0xODkgODQuNi0xODkgMTg5IDg0LjYgMTg5IDE4OSAxODl6IG0wIDY0Yy0xMzkuNyAwLTI1My0xMTMuMy0yNTMtMjUzUzM3Mi4zIDY0IDUxMiA2NHMyNTMgMTEzLjMgMjUzIDI1My0xMTMuMyAyNTMtMjUzIDI1M3pNMjI3IDY5OGMtNTQuNyAwLTk5IDQ0LjMtOTkgOTlzNDQuMyA5OSA5OSA5OWg1NzBjNTQuNyAwIDk5LTQ0LjMgOTktOTlzLTQ0LjMtOTktOTktOTlIMjI3eiBtMC02NGg1NzBjOTAgMCAxNjMgNzMgMTYzIDE2M3MtNzMgMTYzLTE2MyAxNjNIMjI3Yy05MCAwLTE2My03My0xNjMtMTYzczczLTE2MyAxNjMtMTYzeiBtMTcyLjktMjUwLjVjLTUuNi0xNi44IDMuNS0zNC45IDIwLjMtNDAuNSAxNi44LTUuNiAzNC45IDMuNSA0MC41IDIwLjMgNy43IDIzLjIgMjIuNyAzMy4zIDUyLjMgMzMuMyAyOS40IDAgNDQuMi05LjkgNTEuNy0zMi44IDUuNS0xNi44IDIzLjYtMjUuOSA0MC40LTIwLjQgMTYuOCA1LjUgMjUuOSAyMy42IDIwLjQgNDAuNC0xNi41IDUwLjEtNTYuNCA3Ni44LTExMi41IDc2LjgtNTYuMyAwLTk2LjQtMjYuNy0xMTMuMS03Ny4xeiIgcC1pZD0iOTQ3NyI+PC9wYXRoPjwvc3ZnPg=="
              />
            </template>
          </van-field>
          <van-field
            v-model="loginForm.tel"
            type="tel"
            label=""
            placeholder="授权手机号码"
            name="validator"
            :rules="[{ validator, message: '请输入正确的手机号码' }]"
          >
            <template slot="left-icon">
              <img
                src="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzQ1OTg5Nzc3MDcwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjEwNjM2IiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIj48cGF0aCBkPSJNODIwLjQwOTQ0OSA3OTcuMjI4MzQ2cTAgMjUuMTk2ODUtMTAuMDc4NzQgNDYuODY2MTQydC0yNy43MTY1MzUgMzguMjk5MjEzLTQxLjMyMjgzNSAyNi4yMDQ3MjQtNTAuODk3NjM4IDkuNTc0ODAzbC0zNTcuNzk1Mjc2IDBxLTI3LjIxMjU5OCAwLTUwLjg5NzYzOC05LjU3NDgwM3QtNDEuMzIyODM1LTI2LjIwNDcyNC0yNy43MTY1MzUtMzguMjk5MjEzLTEwLjA3ODc0LTQ2Ljg2NjE0MmwwLTY3NS4yNzU1OTFxMC0yNS4xOTY4NSAxMC4wNzg3NC00Ny4zNzAwNzl0MjcuNzE2NTM1LTM4LjgwMzE1IDQxLjMyMjgzNS0yNi4yMDQ3MjQgNTAuODk3NjM4LTkuNTc0ODAzbDM1Ny43OTUyNzYgMHEyNy4yMTI1OTggMCA1MC44OTc2MzggOS41NzQ4MDN0NDEuMzIyODM1IDI2LjIwNDcyNCAyNy43MTY1MzUgMzguODAzMTUgMTAuMDc4NzQgNDcuMzcwMDc5bDAgNjc1LjI3NTU5MXpNNzM4Ljc3MTY1NCAxNzAuMzMwNzA5bC00NTUuNTU5MDU1IDAgMCA1NzcuNTExODExIDQ1NS41NTkwNTUgMCAwLTU3Ny41MTE4MTF6TTUxMC45OTIxMjYgNzc2LjA2Mjk5MnEtMjEuMTY1MzU0IDAtMzYuNzg3NDAyIDE1LjExODExdC0xNS42MjIwNDcgMzcuMjkxMzM5cTAgMjEuMTY1MzU0IDE1LjYyMjA0NyAzNi43ODc0MDJ0MzYuNzg3NDAyIDE1LjYyMjA0N3EyMi4xNzMyMjggMCAzNy4yOTEzMzktMTUuNjIyMDQ3dDE1LjExODExLTM2Ljc4NzQwMnEwLTIyLjE3MzIyOC0xNS4xMTgxMS0zNy4yOTEzMzl0LTM3LjI5MTMzOS0xNS4xMTgxMXpNNTkxLjYyMjA0NyA4NC42NjE0MTdxMC04LjA2Mjk5Mi01LjAzOTM3LTEyLjU5ODQyNXQtMTEuMDg2NjE0LTQuNTM1NDMzbC0xMjggMHEtNS4wMzkzNyAwLTEwLjU4MjY3NyA0LjUzNTQzM3QtNS41NDMzMDcgMTIuNTk4NDI1IDUuMDM5MzcgMTIuNTk4NDI1IDExLjA4NjYxNCA0LjUzNTQzM2wxMjggMHE2LjA0NzI0NCAwIDExLjA4NjYxNC00LjUzNTQzM3Q1LjAzOTM3LTEyLjU5ODQyNXoiIHAtaWQ9IjEwNjM3Ij48L3BhdGg+PC9zdmc+"
              />
            </template>
          </van-field>
          <van-field
            v-model="loginForm.idCard"
            type="number"
            label=""
            placeholder="请输入身份证号码后六位"
            name="validatorId"
            :rules="[{ validator: validatorId, message: '请输入正确内容' }]"
          >
            <template slot="left-icon">
              <img
                src="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzQ1OTg5ODU1Njc3IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjExNzExIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIj48cGF0aCBkPSJNODYxLjA5MDkwOSAxMTYuMzYzNjM2bC02OTguMTgxODE4IDBDOTguNjI5ODE4IDExNi4zNjM2MzYgNDYuNTQ1NDU1IDE2OC40NDggNDYuNTQ1NDU1IDIzMi43MjcyNzNsMCA1NTguNTQ1NDU1YzAgNjQuMjc5MjczIDUyLjA4NDM2NCAxMTYuMzYzNjM2IDExNi4zNjM2MzYgMTE2LjM2MzYzNmw2OTguMTgxODE4IDBjNjQuMjc5MjczIDAgMTE2LjM2MzYzNi01Mi4wODQzNjQgMTE2LjM2MzYzNi0xMTYuMzYzNjM2TDk3Ny40NTQ1NDUgMjMyLjcyNzI3M0M5NzcuNDU0NTQ1IDE2OC40NDggOTI1LjM3MDE4MiAxMTYuMzYzNjM2IDg2MS4wOTA5MDkgMTE2LjM2MzYzNnpNOTMwLjkwOTA5MSA3OTEuMjcyNzI3YzAgMzguNTM5NjM2LTMxLjI3ODU0NSA2OS44MTgxODItNjkuODE4MTgyIDY5LjgxODE4MmwtNjk4LjE4MTgxOCAwQzEyNC4zNjk0NTUgODYxLjA5MDkwOSA5My4wOTA5MDkgODI5LjgxMjM2NCA5My4wOTA5MDkgNzkxLjI3MjcyN0w5My4wOTA5MDkgMjMyLjcyNzI3M2MwLTM4LjUzOTYzNiAzMS4yNzg1NDUtNjkuODE4MTgyIDY5LjgxODE4Mi02OS44MTgxODJsNjk4LjE4MTgxOCAwQzg5OS42MzA1NDUgMTYyLjkwOTA5MSA5MzAuOTA5MDkxIDE5NC4xODc2MzYgOTMwLjkwOTA5MSAyMzIuNzI3MjczTDkzMC45MDkwOTEgNzkxLjI3MjcyN3pNMTg2LjE4MTgxOCA2OTguMTgxODE4bDI1NiAwIDAtNDYuNTQ1NDU1TDE4Ni4xODE4MTggNjUxLjYzNjM2NCAxODYuMTgxODE4IDY5OC4xODE4MTh6TTE4Ni4xODE4MTggNTM1LjI3MjcyN2wyNTYgMCAwLTQ2LjU0NTQ1NUwxODYuMTgxODE4IDQ4OC43MjcyNzMgMTg2LjE4MTgxOCA1MzUuMjcyNzI3ek0xODYuMTgxODE4IDM3Mi4zNjM2MzZsMjU2IDBMNDQyLjE4MTgxOCAzMjUuODE4MTgyIDE4Ni4xODE4MTggMzI1LjgxODE4MiAxODYuMTgxODE4IDM3Mi4zNjM2MzZ6TTc0OS40NzQ5MDkgNTMwLjg1MDkwOUM3NzQuNzk1NjM2IDUwOS40ODY1NDUgNzkxLjI3MjcyNyA0NzcuOTI4NzI3IDc5MS4yNzI3MjcgNDQyLjE4MTgxOCA3OTEuMjcyNzI3IDM3Ny45MDI1NDUgNzM5LjE4ODM2NCAzMjUuODE4MTgyIDY3NC45MDkwOTEgMzI1LjgxODE4MiA2MTAuNjI5ODE4IDMyNS44MTgxODIgNTU4LjU0NTQ1NSAzNzcuOTAyNTQ1IDU1OC41NDU0NTUgNDQyLjE4MTgxOGMwIDM1Ljc0NjkwOSAxNi40NzcwOTEgNjcuMzA0NzI3IDQxLjc5NzgxOCA4OC42NjkwOTFDNTQ4LjA3MjcyNyA1NTcuOTg2OTA5IDUxMiA2MTEuOTc5NjM2IDUxMiA2NzQuOTA5MDkxYzAgMTIuODQ2NTQ1IDEwLjQyNjE4MiAyMy4yNzI3MjcgMjMuMjcyNzI3IDIzLjI3MjcyNyAxMi44NDY1NDUgMCAyMy4yNzI3MjctMTAuNDI2MTgyIDIzLjI3MjcyNy0yMy4yNzI3MjcgMC02NC4yNzkyNzMgNTIuMDg0MzY0LTExNi4zNjM2MzYgMTE2LjM2MzYzNi0xMTYuMzYzNjM2IDY0LjI3OTI3MyAwIDExNi4zNjM2MzYgNTIuMDg0MzY0IDExNi4zNjM2MzYgMTE2LjM2MzYzNiAwIDEyLjg0NjU0NSAxMC40MjYxODIgMjMuMjcyNzI3IDIzLjI3MjcyNyAyMy4yNzI3MjcgMTIuODQ2NTQ1IDAgMjMuMjcyNzI3LTEwLjQyNjE4MiAyMy4yNzI3MjctMjMuMjcyNzI3QzgzNy44MTgxODIgNjExLjk3OTYzNiA4MDEuNzQ1NDU1IDU1Ny45ODY5MDkgNzQ5LjQ3NDkwOSA1MzAuODUwOTA5ek02NzQuOTA5MDkxIDUxMmMtMzguNTM5NjM2IDAtNjkuODE4MTgyLTMxLjI3ODU0NS02OS44MTgxODItNjkuODE4MTgyQzYwNS4wOTA5MDkgNDAzLjY0MjE4MiA2MzYuMzY5NDU1IDM3Mi4zNjM2MzYgNjc0LjkwOTA5MSAzNzIuMzYzNjM2IDcxMy40NDg3MjcgMzcyLjM2MzYzNiA3NDQuNzI3MjczIDQwMy42NDIxODIgNzQ0LjcyNzI3MyA0NDIuMTgxODE4IDc0NC43MjcyNzMgNDgwLjcyMTQ1NSA3MTMuNDQ4NzI3IDUxMiA2NzQuOTA5MDkxIDUxMnoiIGZpbGw9IiMwNDAwMDAiIHAtaWQ9IjExNzEyIj48L3BhdGg+PC9zdmc+"
              />
            </template>
          </van-field>
          <van-button class="login_btn" type="info" native-type="submit"
            >登录</van-button
          >
        </van-form>
      </div>
    </div>
    <van-overlay :show="loading">
      <div
        style="
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <van-loading type="spinner" />
      </div>
    </van-overlay>
  </div>
</template>

<script>
import Request from "@/api/index";
export default {
  name: "login",
  components: {},
  data() {
    return {
      loading: false,
      loginForm: {
        userFullName: null,
        tel: null,
        idCard: null,
      },
      activityData: {},
    };
  },
  methods: {
    validatorName(val) {
      return val;
    },
    validator(val) {
      return /1\d{10}/.test(val); // 当前正则验证11位以1开头的数字
    },
    validatorId(val) {
      return /^\d{6}$/.test(val);
    },
    onFailed(errorInfo) {
      if (
        this.loginForm.userFullName &&
        errorInfo.errors[0].name == "validatorName"
      ) {
        this.$toast(errorInfo.errors[0].message);
      }
      if (this.loginForm.tel && errorInfo.errors[0].name == "validator") {
        this.$toast(errorInfo.errors[0].message);
      }
      if (this.loginForm.idCard && errorInfo.errors[0].name == "validatorId") {
        this.$toast(errorInfo.errors[0].message);
      }
    },
    async onSubmit() {
      this.loading = true;
      let data = JSON.stringify({
        ...this.loginForm,
        activityId: this.$route.query.id,
      });
      let res = await Request({
        url: `/app/auth/lotteryLogin`,
        method: "POST",
        data: { AES_DATA: data },
      });
      this.loading = false;
      if (res.code == 200) {
        this.$notify({ type: "success", message: '登录成功' });
        sessionStorage.setItem("loginToken", res.data.loginToken);
        sessionStorage.setItem("userId", res.data.userId);
        this.$router.push(`/detail?id=${this.$route.query.id}`);
      } else {
        this.$notify({ type: "danger", message: res.message });
      }
    },
  },
  mounted() {
    const setRealVH = () => {
      document.documentElement.style.setProperty(
        "--vh",
        `${window.innerHeight * 0.01}px`
      );
    };
    window.addEventListener("resize", setRealVH);
    setRealVH();
  },
  async created() {
    if (this.$route.query.id) {
      let res = await Request({
        url: `/webapi/opActivity/getLotteryActivityConfigById?activityId=${this.$route.query.id}`,
        method: "GET",
      });
      if (res.code == 200) {
        this.activityData = res.data;
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.container {
  width: 100vw;
  height: calc(var(--vh, 1vh) * 100);
  background: linear-gradient(
    to bottom,
    rgb(33, 134, 242) 0%,
    rgb(62, 153, 244) 20%,
    rgb(94, 176, 251) 100%
  );
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.bg {
  width: 100%;
  height: 180px;
  margin-bottom: 15px;
  background-image: url("../assets/bg.png");
  background-size: cover; /* 关键属性：保持宽高比并填满容器 */
  background-position: center; /* 居中显示 */
  background-repeat: no-repeat; /* 禁止重复 */
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  font-size: 30px;
  color: #fff;
  font-weight: bold;
  white-space: pre-wrap; /* 保留空格和换行 */
  overflow-wrap: break-word; /* 长单词或URL换行 */
}
.login_form {
  width: 100%;
  height: 400px;
  background: #fff;
  border-radius: 10px;
}
.title {
  font-size: 26px;
  font-family: cursive;
  margin: 20px 0;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 20px;
    height: 20px;
    margin: 0 20px;
  }
  img:last-child {
    transform: scaleX(-1); /* 水平翻转 */
    filter: FlipH;
  }
}
.form_container {
  padding: 0 30px;
  padding-top: 20px;
}
.van-field {
  height: 60px;
  line-height: 60px;
  padding: 0;
}
.van-cell::after {
}
.login_btn {
  margin-top: 20px;
  width: 200px;
  border-radius: 10px;
  font-size: 20px;
  background: rgb(52, 183, 255);
  border: none;
}
:deep(.van-field__left-icon) {
  width: 32px;
  display: flex;
  align-items: center;
  > img {
    width: 30px;
    height: 28px;
  }
}
</style>
