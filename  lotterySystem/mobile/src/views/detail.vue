<template>
  <div class="container">
    <div class="bg">{{ activityData.lotteryTitle }}</div>
    <div class="login_form">
      <div class="title">
        <img src="../assets/title.png" />
        摇号结果
        <img src="../assets/title.png" />
      </div>
      <div v-if="!num" class="noLotteryDraw">未摇号</div>
      <div v-else class="lotteryHasBeenHeld">{{ num }}</div>
      <div class="tip">- 请及时截图保存使用 -</div>
      <van-button
        v-if="lotteryDateFlag"
        class="btn"
        type="info"
        :disabled="!!num"
        @click="startLottery"
        >立即摇号</van-button
      >
    </div>
    <div class="info">
      <div class="info_bg">
        <div class="info_bg_top"></div>
        <div class="info_bg_bottom">
          <div style="width: 100%; height: 100%; background: #fff"></div>
        </div>
      </div>
      <div class="info_container">
        <div class="infoItem">
          <div class="top">
            <img
              src="data:image/svg+xml;base64,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"
            />
            活动规则
          </div>
          <div class="bottom">
            {{ activityData.lotteryExplain }}
          </div>
        </div>
        <div class="infoItem">
          <div class="top">
            <img
              src="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzQ1OTk4MDg1MzEzIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjI4OTMiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMzIiIGhlaWdodD0iMzIiPjxwYXRoIGQ9Ik04NTcuMDM0NzUyIDE2NC40MDUyNDhDNzY0LjczNTQ4OCA3Mi4xMDU5ODQgNjQyLjAxNzI4IDIxLjI3NDYyNCA1MTEuNDg4IDIxLjI3NDYyNGMtMTMwLjUzMTMyOCAwLTI1My4yNDc0ODggNTAuODMxMzYtMzQ1LjU0Njc1MiAxNDMuMTMwNjI0UzIyLjgxMDYyNCAzNzkuNDIwNjcyIDIyLjgxMDYyNCA1MDkuOTUyYzAgMTMwLjUyOTI4IDUwLjgzMTM2IDI1My4yNDc0ODggMTQzLjEzMDYyNCAzNDUuNTQ2NzUyczIxNS4wMTU0MjQgMTQzLjEzMDYyNCAzNDUuNTQ2NzUyIDE0My4xMzA2MjRjMTMwLjUyOTI4IDAgMjUzLjI0NzQ4OC01MC44MzEzNiAzNDUuNTQ2NzUyLTE0My4xMzA2MjRTMTAwMC4xNjUzNzYgNjQwLjQ4MTI4IDEwMDAuMTY1Mzc2IDUwOS45NTJjMC0xMzAuNTMxMzI4LTUwLjgzMTM2LTI1My4yNDc0ODgtMTQzLjEzMDYyNC0zNDUuNTQ2NzUyek01MTEuNDg4IDk1My43MTY3MzZjLTI0NC42OTI5OTIgMC00NDMuNzY0NzM2LTE5OS4wNzE3NDQtNDQzLjc2NDczNi00NDMuNzY0NzM2UzI2Ni43OTUwMDggNjYuMTg3MjY0IDUxMS40ODggNjYuMTg3MjY0IDk1NS4yNTI3MzYgMjY1LjI1OTAwOCA5NTUuMjUyNzM2IDUwOS45NTIgNzU2LjE4MDk5MiA5NTMuNzE2NzM2IDUxMS40ODggOTUzLjcxNjczNnoiIGZpbGw9IiMyRDMzM0EiIHAtaWQ9IjI4OTQiPjwvcGF0aD48cGF0aCBkPSJNNTAwLjY0MTc5MiAyNDMuNTM5OTY4aC00NC45MTI2NHYzMjIuMTcwODhoMzIyLjE3MDg4di00NC45MTI2NGgtMjc3LjI1ODI0eiIgZmlsbD0iIzJEMzMzQSIgcC1pZD0iMjg5NSI+PC9wYXRoPjwvc3ZnPg=="
            />
            摇号报名时间
          </div>
          <div class="bottom">
            {{ activityData.lotteryStartDate }} -
            {{ activityData.lotteryEndDate }}
          </div>
        </div>
      </div>
    </div>
    <van-overlay :show="loading">
      <div
        style="
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <van-loading type="spinner" />
      </div>
    </van-overlay>
  </div>
</template>

<script>
import Request from "@/api/index";
export default {
  name: "detail",
  components: {},
  data() {
    return {
      loading: false,
      num: null,
      lotteryDateFlag: false,
      activityData: {},
    };
  },
  methods: {
    async startLottery() {
      this.loading = true;
      let userId = sessionStorage.getItem("userId");
      let res = await Request({
        url: `/app/activity/personalLottery?activityIdNow=${this.$route.query.id}&userId=${userId}`,
        method: "GET",
      });
      if (res.code == 200) {
        let userId = sessionStorage.getItem("userId");
        let lotteryRes = await Request({
          url: `/app/activity/queryLottery?userId=${userId}`,
          method: "GET",
        });
        this.num = lotteryRes.data;
      }
      this.loading = false;
    },
  },
  async created() {
    if (this.$route.query.id) {
      this.loading = true;
      let res = await Request({
        url: `/webapi/opActivity/getLotteryActivityConfigById?activityId=${this.$route.query.id}`,
        method: "GET",
      });
      if (res.code == 200) {
        this.activityData = res.data;
        this.lotteryDateFlag = res.data.lotteryDateFlag;
      }
      this.num = null;
      let userId = sessionStorage.getItem("userId");
      let lotteryRes = await Request({
        url: `/app/activity/queryLottery?userId=${userId}`,
        method: "GET",
      });
      this.num = lotteryRes.data;
      this.loading = false;
    }
  },
  mounted() {
    const setRealVH = () => {
      document.documentElement.style.setProperty(
        "--vh",
        `${window.innerHeight * 0.01}px`
      );
    };
    window.addEventListener("resize", setRealVH);
    setRealVH();
  },
};
</script>
<style lang="scss" scoped>
.container {
  width: 100vw;
  height: 100vh;
  // height: calc(var(--vh, 1vh) * 100);
  background: linear-gradient(
    to bottom,
    rgb(33, 134, 242) 0%,
    rgb(62, 153, 244) 20%,
    rgb(94, 176, 251) 100%
  );
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.bg {
  width: 100%;
  height: 180px;
  margin-bottom: 15px;
  background-image: url("../assets/bg.png");
  background-size: cover; /* 关键属性：保持宽高比并填满容器 */
  background-position: center; /* 居中显示 */
  background-repeat: no-repeat; /* 禁止重复 */
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  font-size: 30px;
  color: #fff;
  font-weight: bold;
  white-space: pre-wrap; /* 保留空格和换行 */
  overflow-wrap: break-word; /* 长单词或URL换行 */
}
.login_form {
  width: 100%;
  height: 350px;
  background: #fff;
  border-radius: 10px;
}
.title {
  font-size: 26px;
  font-family: cursive;
  padding: 20px 0;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 20px;
    height: 20px;
    margin: 0 20px;
  }
  img:last-child {
    transform: scaleX(-1); /* 水平翻转 */
    filter: FlipH;
  }
}
.noLotteryDraw {
  padding: 30px 0;
  font-size: 32px;
  font-weight: bold;
  color: rgb(209, 0, 27);
  font-family: cursive;
}
.lotteryHasBeenHeld {
  padding: 30px 0;
  font-size: 32px;
  font-weight: bold;
  color: rgb(51, 140, 212);
}
.tip {
  font-size: 14px;
  color: rgb(140, 140, 143);
}
.btn {
  margin-top: 40px;
  width: 200px;
  border-radius: 10px;
  font-size: 20px;
  background: rgb(52, 183, 255);
  border: none;
}
.info {
  width: 100%;
  height: 150px;
  margin-top: 15px;
  // flex: 1;
  // background-image: url("../assets/tip.png");
  // background-size: contain; /* 关键属性：保持宽高比并填满容器 */
  // background-position: center; /* 居中显示 */
  // background-repeat: no-repeat; /* 禁止重复 */
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.info_container {
  z-index: 10;
  width: 90%;
  height: 150px;
  margin-top: 15px;
  overflow-y: scroll;
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}
/* Chrome/Safari 的隐藏方式 */
.info_container::-webkit-scrollbar {
  display: none;
}

.infoItem {
  width: 100%;
  .top {
    width: 100%;
    padding-left: 20px;
    height: 40px;
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    img {
      width: 20px;
      height: 20px;
      color: rgb(116, 151, 175);
      margin-right: 10px;
    }
  }
  .bottom {
    width: 100%;
    padding-left: 20px;
    padding-right: 10px;
    font-size: 16px;
    text-align: left;
    word-wrap: break-word; /* 长单词或URL换行 */
    overflow-wrap: break-word; /* 更现代的属性 */
    white-space: normal; /* 覆盖可能的nowrap设置 */
  }
}
.info_bg {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
.info_bg_top {
  width: 100%;
  height: 15px;
  // background: rgb(4, 104, 200);
  background: rgb(44, 127, 215);
  border-radius: 15px;
  border: 1px solid rgb(102, 178, 250);
  // 102 178 250
}
.info_bg_bottom {
  width: 100%;
  height: 100%;
  padding: 0 20px;
  // padding-top: 8px;
  position: absolute;
  top: 8px;
}
</style>
