{"name": "mobile", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"axios": "^1.9.0", "core-js": "^3.8.3", "vant": "^2.13.7", "vue": "^2.6.14", "vue-router": "^3.5.1"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-import": "^1.13.8", "lib-flexible": "^0.3.2", "postcss-pxtorem": "^6.1.0", "sass": "^1.32.7", "sass-loader": "^12.0.0", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}