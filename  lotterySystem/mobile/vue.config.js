// const { defineConfig } = require('@vue/cli-service')
// module.exports = defineConfig({
//   transpileDependencies: true
// })
module.exports = {
  publicPath: process.env.NODE_ENV === 'development' ? '' : '/mobile',
  devServer: {
    proxy: {
      '/opening': {
        target: 'https://open.sky-dome.com.cn',
        changeOrigin: true,
        pathRewrite: {
          '^/opening': ''
        }
      },
      '/api': {
        target: 'https://open.sky-dome.com.cn/opening',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  }
}
